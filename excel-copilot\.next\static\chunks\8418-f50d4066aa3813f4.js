(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8418,4930],{42480:function(){},24654:function(){},93543:function(e,t,a){"use strict";a.d(t,{J0:function(){return R}});var r=a(18473),i=a(58743),n=a(80420),o=a(25566);let s=n.s.fromEnv();async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60,r="ratelimit:".concat(e),i=Date.now(),n=1e3*a;try{let e=await s.pipeline().zremrangebyscore(r,0,i-n).zadd(r,{score:i,member:i.toString()}).zcount(r,i-n,"+inf").pexpire(r,n).exec(),a=(null==e?void 0:e[2])||0,o=await s.zrange(r,0,0,{withScores:!0}),c=o.length>0?o[0].score+n:i+n;return{success:a<=t,limit:t,remaining:Math.max(0,t-a),reset:c}}catch(e){return console.error("[RATE_LIMIT_ERROR]",e),{success:!1,limit:t,remaining:0,reset:i+n,error:"Erro ao verificar limites de taxa. Limite tempor\xe1rio aplicado por seguran\xe7a."}}}let l=new Map,d=o.env.REDIS_URL||o.env.UPSTASH_REDIS_REST_URL?c:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60,r=Date.now(),i=1e3*a;l.has(e)||l.set(e,{timestamps:[],reset:r+i});let n=l.get(e);n.timestamps=n.timestamps.filter(e=>e>r-i),n.timestamps.push(r),1===n.timestamps.length&&(n.reset=r+i);let o=n.timestamps.length<=t,s=Math.max(0,t-n.timestamps.length);if(l.size>1e4)for(let e of[...l.entries()].filter(e=>{let[t,a]=e;return a.reset<r}).map(e=>{let[t]=e;return t}))l.delete(e);return{success:o,limit:t,remaining:s,reset:n.reset}};var u=a(64930);let m={MAX_WORKBOOKS:{[i.Xf.FREE]:5,[i.Xf.PRO_MONTHLY]:1/0,[i.Xf.PRO_ANNUAL]:1/0},MAX_CELLS:{[i.Xf.FREE]:1e3,[i.Xf.PRO_MONTHLY]:5e4,[i.Xf.PRO_ANNUAL]:1/0},MAX_CHARTS:{[i.Xf.FREE]:1,[i.Xf.PRO_MONTHLY]:1/0,[i.Xf.PRO_ANNUAL]:1/0},ADVANCED_AI_COMMANDS:{[i.Xf.FREE]:!1,[i.Xf.PRO_MONTHLY]:!0,[i.Xf.PRO_ANNUAL]:!0},RATE_LIMITS:{[i.Xf.FREE]:30,[i.Xf.PRO_MONTHLY]:120,[i.Xf.PRO_ANNUAL]:240}},f=new Map;async function p(e){try{let t=f.get(e),a=Date.now();if(t&&a-t.timestamp<18e5)return t.plan;let r=await u.prisma.subscription.findFirst({where:{userId:e,OR:[{status:"active"},{status:"trialing"}],AND:[{OR:[{currentPeriodEnd:{gt:new Date}},{currentPeriodEnd:null}]}]},orderBy:{createdAt:"desc"}});await g(e);let n=(null==r?void 0:r.plan)||i.Xf.FREE;return f.set(e,{plan:n,timestamp:a}),n}catch(t){throw r.logger.error("[GET_USER_PLAN_ERROR]",t),await _(e,"plan_verification_failure",t),Error("N\xe3o foi poss\xedvel verificar seu plano de assinatura. Tente novamente mais tarde.")}}async function g(e){try{let t=await u.prisma.user.findUnique({where:{id:e}});if(!t)return;if("lastIpAddress"in t&&t.lastIpAddress){let a=await u.prisma.user.findMany({where:{lastIpAddress:t.lastIpAddress,id:{not:e}}}),r=new Date;r.setDate(r.getDate()-30);let i=a.filter(e=>"createdAt"in e&&e.createdAt&&e.createdAt>r).length;if(i>3){let r=a.map(e=>"email"in e&&e.email&&e.email.split("@")[1]||""),n=new Set(r),o=n.size>1&&n.size<=3;await _(e,"multiple_accounts_same_ip",{ipAddress:t.lastIpAddress,accountCount:a.length,recentAccountsCount:i,suspiciousDomains:o,creationDates:a.map(e=>"createdAt"in e?e.createdAt:null).filter(Boolean),severity:i>5?"high":"medium"});let s={};if(s.isSuspicious=!0,await u.prisma.user.update({where:{id:e},data:s}),i>5){let t={isBanned:!0,banReason:"M\xfaltiplas contas com mesmo IP detectadas (poss\xedvel abuso)",banDate:new Date};await u.prisma.user.update({where:{id:e},data:t}),await u.prisma.session.deleteMany({where:{userId:e}})}}}if("createdAt"in t&&t.createdAt){let a=t.createdAt.getTime(),r=(Date.now()-a)/36e5;if(r<24){let a=await u.prisma.userActionLog.count({where:{userId:e,action:{in:["attempt_create_workbook","attempt_add_cells","attempt_advanced_ai"]},timestamp:{gte:new Date(Date.now()-72e5)}}});a>50&&(await _(e,"high_activity_new_account",{accountAgeHours:r,recentActivityCount:a,ipAddress:t.lastIpAddress,severity:"high"}),await u.prisma.user.update({where:{id:e},data:{isSuspicious:!0}}))}}}catch(t){r.logger.error("[ABUSE_DETECTION_ERROR]",{userId:e,error:t})}}async function _(e,t,a){try{await u.prisma.securityLog.create({data:{userId:e,eventType:t,details:JSON.stringify(a),timestamp:new Date}})}catch(i){r.logger.error("[SECURITY_LOG_ERROR]",{userId:e,eventType:t,details:a,error:i})}}async function R(e,t,a){try{var n,o,s;let t=await p(e),r=null!==(n=m.MAX_CHARTS[i.Xf.FREE])&&void 0!==n?n:1,c=null!==(o=m.MAX_CHARTS[t])&&void 0!==o?o:r,l=a<c,d=await A(e,t,"add_chart");if(!d.allowed)return{allowed:!1,message:"Limite de taxa excedido. Tente novamente em ".concat(null!==(s=d.timeRemaining)&&void 0!==s?s:60," segundos."),limit:c===1/0?-1:c};return{allowed:l,message:l?void 0:"Voc\xea atingiu o limite de ".concat(c," ").concat(1===c?"gr\xe1fico":"gr\xe1ficos"," para este plano. Fa\xe7a upgrade para adicionar mais."),limit:c===1/0?-1:c}}catch(a){throw r.logger.error("[CAN_ADD_CHART_ERROR]",{userId:e,sheetId:t,error:a}),Error("N\xe3o foi poss\xedvel verificar seus limites de uso. Tente novamente mais tarde.")}}async function A(e,t,a){try{let r=m.RATE_LIMITS[i.Xf.FREE]||10,n=m.RATE_LIMITS[t]||r,{success:o,limit:s,remaining:c,reset:l,error:u}=await d(e,n);if(!o){if(u)return await _(e,"rate_limit_error",{action:a,error:u}),{allowed:!1,message:u};let t=Math.ceil((l-Date.now())/1e3);return{allowed:!1,message:"Voc\xea excedeu o limite de a\xe7\xf5es por minuto. Tente novamente em ".concat(t," segundos."),timeRemaining:t}}return c<=Math.floor(.2*n)&&c>0&&await w(e,"approaching_rate_limit",{action:a,remaining:c,limit:n,percentRemaining:Math.round(c/n*100)}),{allowed:!0}}catch(i){return r.logger.error("[RATE_LIMIT_CHECK_ERROR]",{userId:e,userPlan:t,action:a,error:i}),await _(e,"rate_limit_verification_error",{action:a,error:String(i)}),{allowed:!1,message:"N\xe3o foi poss\xedvel verificar limites de uso. Tente novamente em alguns instantes."}}}async function w(e,t,a){try{await u.prisma.userActionLog.create({data:{userId:e,action:t,details:JSON.stringify(a),timestamp:new Date}})}catch(i){r.logger.error("[USER_ACTION_LOG_ERROR]",{userId:e,action:t,details:a,error:i})}}},64930:function(e,t,a){"use strict";a.d(t,{prisma:function(){return c}});var r=a(17090),i=a(25566);let n={info:function(e){for(var t=arguments.length,a=Array(t>1?t-1:0),r=1;r<t;r++)a[r-1]=arguments[r]},error:function(e){for(var t=arguments.length,a=Array(t>1?t-1:0),r=1;r<t;r++)a[r-1]=arguments[r];console.error("[DB ERROR] ".concat(e),...a)},warn:function(e){for(var t=arguments.length,a=Array(t>1?t-1:0),r=1;r<t;r++)a[r-1]=arguments[r];console.warn("[DB WARNING] ".concat(e),...a)}},o={totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null},s=[],c=a.g.prisma||new r.PrismaClient({log:["error"],datasources:{db:{url:i.env.DB_DATABASE_URL||""}}});async function l(){try{await c.$disconnect(),n.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){n.error("Erro ao desconectar do banco de dados",e)}}c.$on("query",e=>{o.totalQueries++,e.duration&&(s.push(e.duration),s.length>100&&s.shift(),o.averageQueryTime=s.reduce((e,t)=>e+t,0)/s.length),e.duration&&e.duration>500&&n.warn("Consulta lenta detectada: ".concat(Math.round(e.duration),"ms - Query: ").concat(e.query||"Query desconhecida"))}),c.$on("error",e=>{o.failedQueries++,o.connectionFailures++,o.lastConnectionFailure=new Date().toISOString(),n.error("Erro na conex\xe3o com o banco de dados: ".concat(e.message||"Erro desconhecido"))}),void 0!==i&&i.on("beforeExit",()=>{l()})},56498:function(e,t,a){"use strict";function r(e,t){if(e&&Array.isArray(e)&&!(t<0)&&!(t>=e.length))return e[t]}a.d(t,{g_:function(){return r}}),a(12314),a(18473)},57392:function(e,t,a){"use strict";function r(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(!e||!Array.isArray(e)||t<0||t>=e.length)return a;let r=e[t];return void 0!==r?r:a}a.d(t,{A0:function(){return r}})}}]);