"use strict";(()=>{var e={};e.id=9269,e.ids=[9269],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},85477:e=>{e.exports=require("punycode")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},24404:e=>{e.exports=require("tls")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},56908:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>k,patchFetch:()=>_,requestAsyncStorage:()=>y,routeModule:()=>S,serverHooks:()=>v,staticGenerationAsyncStorage:()=>b});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>f});var s=r(49303),o=r(88716),i=r(60670),n=r(87070),u=r(75571),c=r(95456),l=r(33035),d=r(63841);async function p(e){try{let t=await (0,u.getServerSession)(c.Lz);if(!t?.user)return n.NextResponse.json({error:"N\xe3o autorizado"},{status:401});if(!await d.prisma.user.findUnique({where:{id:t.user.id},select:{id:!0}}))return n.NextResponse.json({error:"Usu\xe1rio n\xe3o encontrado"},{status:404});let{searchParams:r}=new URL(e.url);switch(r.get("action")||"overview"){case"overview":{let e=await m();return n.NextResponse.json({success:!0,data:e})}case"buckets":{let e=await h();return n.NextResponse.json({success:!0,data:e})}case"health":{let e=await w();return n.NextResponse.json({success:!0,data:e})}default:return n.NextResponse.json({error:"A\xe7\xe3o n\xe3o suportada"},{status:400})}}catch(e){return console.error("Erro na API de admin storage:",e),n.NextResponse.json({error:"Erro interno do servidor"},{status:500})}}async function f(e){try{let t=await (0,u.getServerSession)(c.Lz);if(!t?.user)return n.NextResponse.json({error:"N\xe3o autorizado"},{status:401});if(!await d.prisma.user.findUnique({where:{id:t.user.id},select:{id:!0}}))return n.NextResponse.json({error:"Usu\xe1rio n\xe3o encontrado"},{status:404});let{action:r,options:a}=await e.json();switch(r){case"cleanup":{let e=await g(a);return n.NextResponse.json({success:!0,message:"Limpeza executada com sucesso",data:e})}case"refresh":{let e=await x();return n.NextResponse.json({success:!0,message:"Estat\xedsticas atualizadas",data:e})}case"validate":{let e=await E();return n.NextResponse.json({success:!0,message:"Valida\xe7\xe3o conclu\xedda",data:e})}default:return n.NextResponse.json({error:"A\xe7\xe3o n\xe3o suportada"},{status:400})}}catch(e){return console.error("Erro na opera\xe7\xe3o de admin storage:",e),n.NextResponse.json({error:"Erro interno do servidor"},{status:500})}}async function m(){return{totalWorkbooks:25,totalFiles:150,totalSize:524288e3,averageFileSize:3460300.8,quotaUsage:50,recentActivity:{uploadsToday:12,downloadsToday:35,deletionsToday:3},topUsers:[{userId:"user1",fileCount:25,totalSize:83886080},{userId:"user2",fileCount:18,totalSize:68157440},{userId:"user3",fileCount:15,totalSize:47185920}]}}async function h(){return[{name:l.xk.EXCEL_FILES,public:!1,fileCount:120,totalSize:471859200,lastModified:new Date().toISOString(),status:"healthy"},{name:l.xk.EXPORTS,public:!1,fileCount:25,totalSize:83886080,lastModified:new Date(Date.now()-72e5).toISOString(),status:"healthy"},{name:l.xk.TEMPLATES,public:!0,fileCount:8,totalSize:15728640,lastModified:new Date(Date.now()-864e5).toISOString(),status:"warning"},{name:l.xk.BACKUPS,public:!1,fileCount:5,totalSize:26214400,lastModified:new Date(Date.now()-6048e5).toISOString(),status:"healthy"}]}async function w(){let e=await h(),t=[];e.forEach(e=>{let r=new Date(e.lastModified),a=(Date.now()-r.getTime())/864e5;a>7&&t.push(`Bucket "${e.name}" n\xe3o foi atualizado h\xe1 ${Math.floor(a)} dias`)});let r=e.reduce((e,t)=>e+t.fileCount,0),a=e.reduce((e,t)=>e+t.totalSize,0),s=a/***********100,o="healthy";return t.length>0&&(o="warning"),s>90&&(o="critical"),{overall:o,buckets:e,totalFiles:r,totalSize:a,quotaUsage:s,issues:t}}async function g(e={}){return{filesRemoved:15,spaceFreed:47185920,daysOld:e.daysOld||30}}async function x(){return await m()}async function E(){let e=(await h()).map(e=>({name:e.name,valid:!0,issues:[]}));return{allValid:e.every(e=>e.valid),results:e}}let S=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/storage/route",pathname:"/api/admin/storage",filename:"route",bundlePath:"app/api/admin/storage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\admin\\storage\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:b,serverHooks:v}=S,k="/api/admin/storage/route";function _(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:b})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var a={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var n=s?Object.getOwnPropertyDescriptor(e,o):null;n&&(n.get||n.set)?Object.defineProperty(a,o,n):a[o]=e[o]}return a.default=e,r&&r.set(e,a),a}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})},95456:(e,t,r)=>{r.d(t,{Lz:()=>s.L,rc:()=>i});var a=r(75571),s=r(81628);async function o(){return await (0,a.getServerSession)(s.L)}async function i(){let e=await o();return e?.user?.id||null}},33035:(e,t,r)=>{r.d(t,{xk:()=>o,Hw:()=>n});var a=r(31518);process.env.SUPABASE_URL||console.warn("SUPABASE_URL n\xe3o est\xe1 configurada - usando NEXT_PUBLIC_SUPABASE_URL"),process.env.SUPABASE_SERVICE_ROLE_KEY||console.warn("SUPABASE_SERVICE_ROLE_KEY n\xe3o est\xe1 configurada - cliente admin n\xe3o estar\xe1 dispon\xedvel"),(0,a.eI)("https://eliuoignzzxnjkcmmtml.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk",{auth:{autoRefreshToken:!1,persistSession:!1,detectSessionInUrl:!1},db:{schema:"public"}});let s=process.env.SUPABASE_SERVICE_ROLE_KEY?(0,a.eI)(process.env.SUPABASE_URL||"https://eliuoignzzxnjkcmmtml.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"}}):null,o={EXCEL_FILES:"excel-files",EXPORTS:"exports",TEMPLATES:"templates",BACKUPS:"backups"};class i{async uploadExcelFile(e,t,r,a={}){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");let o=a.bucket||this.defaultBucket,i=a.folder||`users/${t}/workbooks/${r}`,n=a.fileName||`workbook_${Date.now()}.xlsx`,u=`${i}/${n}`;try{let t=await this.validateBucketPermissions(o,a.isPublic);if(!t.isValid)throw Error(`Bucket validation failed: ${t.error}`);await this.ensureBucketExists(o,a.isPublic);let{data:r,error:i}=await s.storage.from(o).upload(u,e,{upsert:a.upsert||!1,contentType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});if(i)throw i;let n=e instanceof File?e.size:Buffer.byteLength(e),c={path:r.path,fullPath:r.fullPath,size:n};if(a.isPublic){let{data:e}=s.storage.from(o).getPublicUrl(r.path);c.publicUrl=e.publicUrl}return c}catch(e){throw Error(`Erro no upload: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async downloadExcelFile(e,t=this.defaultBucket){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:r,error:a}=await s.storage.from(t).download(e);if(a)throw a;return r}catch(e){throw Error(`Erro no download: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async getSignedUrl(e,t=3600,r=this.defaultBucket){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:a,error:o}=await s.storage.from(r).createSignedUrl(e,t);if(o)throw o;return a.signedUrl}catch(e){throw Error(`Erro ao gerar URL: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async deleteFile(e,t=this.defaultBucket){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{error:r}=await s.storage.from(t).remove([e]);if(r)throw r}catch(e){throw Error(`Erro ao deletar: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async listUserFiles(e,t=this.defaultBucket,r){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let a=r||`users/${e}`,{data:o,error:i}=await s.storage.from(t).list(a,{limit:100,sortBy:{column:"updated_at",order:"desc"}});if(i)throw i;return o.map(e=>({name:e.name,size:e.metadata?.size||0,lastModified:e.updated_at||e.created_at,path:`${a}/${e.name}`}))}catch(e){throw Error(`Erro ao listar arquivos: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async createBackup(e,t,r){let a=JSON.stringify({workbook:e,timestamp:new Date().toISOString(),userId:t,workbookId:r}),s=Buffer.from(a,"utf-8"),i=`backup_${r}_${Date.now()}.json`;return this.uploadExcelFile(s,t,r,{bucket:o.BACKUPS,fileName:i,folder:`backups/${t}/${r}`})}async validateBucketPermissions(e,t=!1){if(!s)return{isValid:!1,error:"Supabase admin client n\xe3o est\xe1 configurado"};try{let{data:t,error:r}=await s.storage.listBuckets();if(r)return{isValid:!1,error:`Sem permiss\xe3o para listar buckets: ${r.message}`};if(t?.some(t=>t.name===e))try{let t=`test_permissions_${Date.now()}.txt`,{error:r}=await s.storage.from(e).upload(t,"test",{upsert:!0});if(r)return{isValid:!1,error:`Sem permiss\xe3o de escrita no bucket ${e}: ${r.message}`};return await s.storage.from(e).remove([t]),{isValid:!0}}catch(e){return{isValid:!1,error:`Erro ao testar permiss\xf5es do bucket: ${e}`}}else{let e=`test_permissions_${Date.now()}`,{error:t}=await s.storage.createBucket(e,{public:!1});if(t)return{isValid:!1,error:`Sem permiss\xe3o para criar buckets: ${t.message}`};return await s.storage.deleteBucket(e),{isValid:!0}}}catch(e){return{isValid:!1,error:`Erro na valida\xe7\xe3o de permiss\xf5es: ${e}`}}}async ensureBucketExists(e,t=!1){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:r}=await s.storage.listBuckets();if(!r?.some(t=>t.name===e)){let{error:r}=await s.storage.createBucket(e,{public:t,allowedMimeTypes:["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/json","text/csv"],fileSizeLimit:52428800});if(r)throw r}}catch(t){console.warn(`Aviso: N\xe3o foi poss\xedvel verificar/criar bucket ${e}:`,t)}}async getStorageStats(e){let t={totalFiles:0,totalSize:0,bucketStats:{}};try{for(let r of Object.values(o)){let a=await this.listUserFiles(e,r),s=a.reduce((e,t)=>e+t.size,0);t.bucketStats[r]={files:a.length,size:s},t.totalFiles+=a.length,t.totalSize+=s}}catch(e){console.warn("Erro ao obter estat\xedsticas de storage:",e)}return t}constructor(){this.defaultBucket=o.EXCEL_FILES}}let n=new i}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,9557,7410,330,5609,1518,2972,1628],()=>r(56908));module.exports=a})();