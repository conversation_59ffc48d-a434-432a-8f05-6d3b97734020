(()=>{var e={};e.id=3591,e.ids=[3591],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},98188:e=>{"use strict";e.exports=require("module")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},85477:e=>{"use strict";e.exports=require("punycode")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},39512:e=>{"use strict";e.exports=require("timers")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},59796:e=>{"use strict";e.exports=require("zlib")},39697:()=>{},62302:()=>{},30107:()=>{},47524:()=>{},36064:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>U,patchFetch:()=>H,requestAsyncStorage:()=>j,routeModule:()=>_,serverHooks:()=>M,staticGenerationAsyncStorage:()=>D});var i,s,o={};r.r(o),r.d(o,{GET:()=>P,dynamic:()=>C});var n=r(49303),a=r(88716),l=r(60670),c=r(87070),u=r(90117),p=r(43895);class h{async addOperation(e,t){try{let r=this.pendingOperations.get(e)||[],i=this.detectConflicts(t,r);if(0===i.length)return r.push(t),this.pendingOperations.set(e,r),this.addToHistory(e,t),{operation:t,resolution:"accept",reason:"Nenhum conflito detectado"};let s=await this.resolveConflict(t,i);if("accept"===s.resolution||"transform"===s.resolution){let i=s.transformedOperation||t;r.push(i),this.pendingOperations.set(e,r),this.addToHistory(e,i)}return s}catch(r){return p.kg.error("Erro na resolu\xe7\xe3o de conflitos",{workbookId:e,operationId:t.id,error:r instanceof Error?r.message:"Erro desconhecido"}),{operation:t,resolution:"reject",reason:"Erro interno na resolu\xe7\xe3o de conflitos"}}}detectConflicts(e,t){let r=[],i=e.timestamp-this.CONFLICT_WINDOW_MS;for(let s of t)s.timestamp<i||s.cellId!==e.cellId||s.sheetId!==e.sheetId||s.userId===e.userId||r.push(s);return r}async resolveConflict(e,t){let r=t.reduce((e,t)=>t.timestamp>e.timestamp?t:e);if(e.timestamp>r.timestamp)return{operation:e,resolution:"accept",reason:`Opera\xe7\xe3o mais recente (${e.timestamp} > ${r.timestamp})`};let i={update:3,format:2,insert:1,delete:0};if((i[e.type]||0)>(i[r.type]||0))return{operation:e,resolution:"accept",reason:`Prioridade de opera\xe7\xe3o (${e.type} > ${r.type})`};if(this.canTransform(e,r)){let t=this.transformOperation(e,r);return{operation:e,resolution:"transform",transformedOperation:t,reason:"Opera\xe7\xe3o transformada para evitar conflito"}}if(this.canMerge(e,r)){let t=this.mergeOperations(e,r);return{operation:e,resolution:"merge",transformedOperation:t,reason:"Opera\xe7\xf5es mescladas"}}return{operation:e,resolution:"reject",reason:"Conflito irreconcili\xe1vel detectado"}}canTransform(e,t){return"format"===e.type||"format"===t.type}transformOperation(e,t){return{...e,id:`${e.id}_transformed`,timestamp:Date.now(),priority:(e.priority||0)+1}}canMerge(e,t){return"update"===e.type&&"update"===t.type&&"number"==typeof e.value&&"number"==typeof t.value}mergeOperations(e,t){let r=e.value+t.value;return{...e,id:`${e.id}_merged_${t.id}`,value:r,timestamp:Math.max(e.timestamp,t.timestamp)}}addToHistory(e,t){let r=this.operationHistory.get(e)||[];r.push(t),r.length>this.MAX_HISTORY_SIZE&&r.splice(0,r.length-this.MAX_HISTORY_SIZE),this.operationHistory.set(e,r)}cleanupOldOperations(e,t){let r=(this.pendingOperations.get(e)||[]).filter(e=>e.timestamp>t);this.pendingOperations.set(e,r);let i=(this.operationHistory.get(e)||[]).filter(e=>e.timestamp>t);this.operationHistory.set(e,i)}getConflictStats(e){let t=this.pendingOperations.get(e)||[],r=this.operationHistory.get(e)||[],i=r[r.length-1],s={pendingOperations:t.length,historySize:r.length};return i&&(s.lastOperation=i),s}resetWorkbook(e){this.pendingOperations.delete(e),this.operationHistory.delete(e)}constructor(){this.pendingOperations=new Map,this.operationHistory=new Map,this.MAX_HISTORY_SIZE=1e3,this.CONFLICT_WINDOW_MS=5e3}}let f=new h;var d=r(41482),m=r(63841);class E{constructor(){this.TOKEN_EXPIRY="1h",this.REFRESH_THRESHOLD=9e5,this.blockedTokens=new Set,this.activeConnections=new Map,this.JWT_SECRET=process.env.NEXTAUTH_SECRET||process.env.JWT_SECRET||"fallback-secret",process.env.NEXTAUTH_SECRET||process.env.JWT_SECRET||p.kg.warn("JWT_SECRET n\xe3o configurado, usando fallback inseguro"),setInterval(()=>{this.cleanupBlockedTokens()},36e5)}async generateWebSocketToken(e,t,r,i){try{let s=await this.getUserPermissions(e,i),o={userId:e,userName:t,userEmail:r,workbookId:i,permissions:this.serializePermissions(s)},n=d.sign(o,this.JWT_SECRET,{expiresIn:this.TOKEN_EXPIRY,issuer:"excel-copilot",audience:"websocket"});return p.kg.info("Token WebSocket gerado",{userId:e,workbookId:i,permissions:s}),n}catch(t){throw p.kg.error("Erro ao gerar token WebSocket",{userId:e,workbookId:i,error:t instanceof Error?t.message:"Erro desconhecido"}),Error("Falha na gera\xe7\xe3o do token")}}async validateWebSocketToken(e,t){try{if(this.blockedTokens.has(e))return{success:!1,error:"Token bloqueado",shouldBlock:!0};let r=d.verify(e,this.JWT_SECRET,{issuer:"excel-copilot",audience:"websocket"}),i=Date.now()/1e3;if(r.exp-i<this.REFRESH_THRESHOLD/1e3&&p.kg.warn("Token WebSocket pr\xf3ximo do vencimento",{userId:r.userId,expiresIn:r.exp-i}),!await this.verifyWorkbookAccess(r.userId,r.workbookId))return{success:!1,error:"Acesso \xe0 planilha revogado",shouldBlock:!0};return this.registerActiveConnection(r.userId,t),p.kg.info("Token WebSocket validado com sucesso",{userId:r.userId,workbookId:r.workbookId,socketId:t}),{success:!0,payload:r}}catch(e){if(e instanceof d.JsonWebTokenError)return p.kg.warn("Token WebSocket inv\xe1lido",{error:e.message,socketId:t}),{success:!1,error:"Token inv\xe1lido",shouldBlock:!0};return p.kg.error("Erro na valida\xe7\xe3o do token WebSocket",{error:e instanceof Error?e.message:"Erro desconhecido",socketId:t}),{success:!1,error:"Erro interno de autentica\xe7\xe3o"}}}blockToken(e,t){this.blockedTokens.add(e),p.kg.info("Token WebSocket bloqueado",{reason:t})}removeActiveConnection(e,t){let r=this.activeConnections.get(e);r&&(r.delete(t),0===r.size&&this.activeConnections.delete(e))}getActiveConnections(e){let t=this.activeConnections.get(e);return t?Array.from(t):[]}async getUserPermissions(e,t){try{let r=await m.prisma.workbook.findFirst({where:{id:t,OR:[{userId:e},{shares:{some:{sharedWithUserId:e}}}]},include:{shares:{where:{sharedWithUserId:e}}}});if(!r)return{canRead:!1,canWrite:!1,canShare:!1,canAdmin:!1};if(r.userId===e)return{canRead:!0,canWrite:!0,canShare:!0,canAdmin:!0};let i=r.shares[0],s=i?.permissionLevel||"READ";return{canRead:!0,canWrite:"WRITE"===s||"ADMIN"===s,canShare:"ADMIN"===s,canAdmin:"ADMIN"===s}}catch(r){return p.kg.error("Erro ao verificar permiss\xf5es",{userId:e,workbookId:t,error:r instanceof Error?r.message:"Erro desconhecido"}),{canRead:!1,canWrite:!1,canShare:!1,canAdmin:!1}}}async verifyWorkbookAccess(e,t){try{return!!await m.prisma.workbook.findFirst({where:{id:t,OR:[{userId:e},{shares:{some:{sharedWithUserId:e}}}]}})}catch(r){return p.kg.error("Erro ao verificar acesso \xe0 planilha",{userId:e,workbookId:t,error:r instanceof Error?r.message:"Erro desconhecido"}),!1}}serializePermissions(e){let t=[];return e.canRead&&t.push("read"),e.canWrite&&t.push("write"),e.canShare&&t.push("share"),e.canAdmin&&t.push("admin"),t}registerActiveConnection(e,t){this.activeConnections.has(e)||this.activeConnections.set(e,new Set),this.activeConnections.get(e).add(t)}cleanupBlockedTokens(){let e=this.blockedTokens.size;this.blockedTokens.clear(),e>0&&p.kg.info("Limpeza de tokens bloqueados realizada",{tokensRemovidos:e})}}let g=new E;var y=r(31518),v=r(23811);class S{constructor(){this.CLEANUP_INTERVAL=18e5,this.INACTIVE_THRESHOLD=36e5,this.MAX_INACTIVE_CHANNELS=100,this.cleanupTimer=null,this.activeChannels=new Map;let e="https://eliuoignzzxnjkcmmtml.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk";if(!e||!t){p.kg.warn("Supabase n\xe3o configurado para cleanup de channels");return}this.supabaseClient=(0,y.eI)(e,t),this.startCleanupScheduler(),p.kg.info("Sistema de cleanup de channels Supabase inicializado")}startCleanupScheduler(){this.cleanupTimer&&clearInterval(this.cleanupTimer),this.cleanupTimer=setInterval(()=>{this.performCleanup()},this.CLEANUP_INTERVAL),setTimeout(()=>{this.performCleanup()},3e5)}stopCleanupScheduler(){this.cleanupTimer&&(clearInterval(this.cleanupTimer),this.cleanupTimer=null)}registerChannelActivity(e){let t=`workbook:${e}`,r=Date.now();this.activeChannels.set(t,{channelName:t,workbookId:e,lastActivity:r,subscriberCount:0,isActive:!0})}unregisterChannel(e){let t=`workbook:${e}`;this.activeChannels.delete(t)}async performCleanup(){try{p.kg.info("Iniciando limpeza de channels Supabase");let e=Date.now()-this.INACTIVE_THRESHOLD,t=0,r=0,i=await v.H.listActiveWorkbooks();for(let[s,o]of Array.from(this.activeChannels.entries()))r++,o.lastActivity<e&&!i.includes(o.workbookId)&&(await this.cleanupChannel(o),this.activeChannels.delete(s),t++);await this.cleanupOrphanChannels(i),this.activeChannels.size>this.MAX_INACTIVE_CHANNELS&&await this.pruneInactiveChannels(),p.kg.info("Limpeza de channels conclu\xedda",{totalChannels:r,channelsLimpos:t,channelsAtivos:this.activeChannels.size})}catch(e){p.kg.error("Erro na limpeza de channels",{error:e instanceof Error?e.message:"Erro desconhecido"})}}async cleanupChannel(e){try{let t=this.supabaseClient?.channel(e.channelName);t&&(await t.unsubscribe(),p.kg.debug("Channel desconectado",{channelName:e.channelName,workbookId:e.workbookId})),await v.H.removeCollaborationState(e.workbookId)}catch(t){p.kg.warn("Erro ao limpar channel espec\xedfico",{channelName:e.channelName,error:t instanceof Error?t.message:"Erro desconhecido"})}}async cleanupOrphanChannels(e){try{let t=Array.from(this.activeChannels.values()).map(e=>e.workbookId).filter(t=>!e.includes(t));for(let e of t){let t=`workbook:${e}`,r=this.activeChannels.get(t);r&&(await this.cleanupChannel(r),this.activeChannels.delete(t))}t.length>0&&p.kg.info("Channels \xf3rf\xe3os limpos",{channelsOrfaos:t.length})}catch(e){p.kg.error("Erro na limpeza de channels \xf3rf\xe3os",{error:e instanceof Error?e.message:"Erro desconhecido"})}}async pruneInactiveChannels(){let e=Array.from(this.activeChannels.entries());e.sort((e,t)=>e[1].lastActivity-t[1].lastActivity);let t=e.length-this.MAX_INACTIVE_CHANNELS,r=e.slice(0,t);for(let[e,t]of r)await this.cleanupChannel(t),this.activeChannels.delete(e);r.length>0&&p.kg.info("Channels inativos em excesso removidos",{channelsRemovidos:r.length})}getChannelStats(){let e=Date.now(),t=e-this.INACTIVE_THRESHOLD,r=0,i=0,s=0;for(let o of Array.from(this.activeChannels.values())){o.lastActivity>t?r++:i++;let n=e-o.lastActivity;n>s&&(s=n)}return{totalChannels:this.activeChannels.size,activeChannels:r,inactiveChannels:i,oldestChannelAge:s}}async forceCleanup(){await this.performCleanup()}healthCheck(){return{isRunning:null!==this.cleanupTimer,nextCleanup:this.cleanupTimer?Date.now()+this.CLEANUP_INTERVAL:0,channelCount:this.activeChannels.size}}}let b=new S;var I=r(92647);(function(e){e.LOGIN_SUCCESS="LOGIN_SUCCESS",e.LOGIN_FAILURE="LOGIN_FAILURE",e.OAUTH_ERROR="OAUTH_ERROR",e.SESSION_EXPIRED="SESSION_EXPIRED",e.SUSPICIOUS_ACTIVITY="SUSPICIOUS_ACTIVITY",e.RATE_LIMIT_EXCEEDED="RATE_LIMIT_EXCEEDED",e.INVALID_TOKEN="INVALID_TOKEN",e.PROVIDER_ERROR="PROVIDER_ERROR"})(i||(i={})),function(e){e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH",e.CRITICAL="CRITICAL"}(s||(s={}));class w{constructor(){this.events=[],this.MAX_EVENTS=1e4,this.CLEANUP_INTERVAL=3e5,this.config={enabled:!0,thresholds:{failuresPerMinute:10,failuresPerHour:100,suspiciousActivityWindow:3e5},notifications:{email:!1,slack:!1,sentry:!0}},setInterval(()=>{this.cleanupOldEvents()},this.CLEANUP_INTERVAL)}recordEvent(e){let t={...e,timestamp:Date.now()};this.events.push(t),p.kg.info("Auth event recorded",{type:e.type,userId:e.userId,email:e.email,ip:e.ip,provider:e.provider,error:e.error}),this.checkForAlerts(t),this.events.length>this.MAX_EVENTS&&(this.events=this.events.slice(-this.MAX_EVENTS))}checkForAlerts(e){if(!this.config.enabled)return;let t=Date.now(),r=t-6e4,i=t-36e5,s=this.events.filter(e=>e.timestamp>=r&&("LOGIN_FAILURE"===e.type||"OAUTH_ERROR"===e.type||"PROVIDER_ERROR"===e.type)),o=this.events.filter(e=>e.timestamp>=i&&("LOGIN_FAILURE"===e.type||"OAUTH_ERROR"===e.type||"PROVIDER_ERROR"===e.type));s.length>=this.config.thresholds.failuresPerMinute&&this.sendAlert({severity:"HIGH",title:"Alto n\xfamero de falhas de autentica\xe7\xe3o",message:`${s.length} falhas de autentica\xe7\xe3o no \xfaltimo minuto`,details:{failuresPerMinute:s.length,threshold:this.config.thresholds.failuresPerMinute,recentEvents:s.slice(-5)}}),o.length>=this.config.thresholds.failuresPerHour&&this.sendAlert({severity:"MEDIUM",title:"Padr\xe3o de falhas de autentica\xe7\xe3o detectado",message:`${o.length} falhas de autentica\xe7\xe3o na \xfaltima hora`,details:{failuresPerHour:o.length,threshold:this.config.thresholds.failuresPerHour}}),"LOGIN_FAILURE"===e.type&&this.checkSuspiciousActivity(e),"PROVIDER_ERROR"===e.type&&this.sendAlert({severity:"CRITICAL",title:"Erro no provedor de autentica\xe7\xe3o",message:`Erro no provedor ${e.provider}: ${e.error}`,details:{provider:e.provider,error:e.error,timestamp:e.timestamp}})}checkSuspiciousActivity(e){let t=e.timestamp-this.config.thresholds.suspiciousActivityWindow,r=this.events.filter(r=>r.ip===e.ip&&r.timestamp>=t&&"LOGIN_FAILURE"===r.type);r.length>=5&&(this.sendAlert({severity:"HIGH",title:"Atividade suspeita detectada",message:`${r.length} tentativas de login falharam do IP ${e.ip}`,details:{ip:e.ip,failures:r.length,timeWindow:this.config.thresholds.suspiciousActivityWindow/1e3/60,userAgent:e.userAgent}}),this.recordEvent({type:"SUSPICIOUS_ACTIVITY",ip:e.ip,userAgent:e.userAgent,metadata:{failureCount:r.length,timeWindow:this.config.thresholds.suspiciousActivityWindow}}))}sendAlert(e){p.kg.warn("Auth security alert",{severity:e.severity,title:e.title,message:e.message,details:e.details}),this.config.notifications.sentry&&this.sendToSentry(e)}sendToSentry(e){}severityToSentryLevel(e){switch(e){case"LOW":return"info";case"MEDIUM":default:return"warning";case"HIGH":return"error";case"CRITICAL":return"fatal"}}cleanupOldEvents(){let e=Date.now()-864e5,t=this.events.length;this.events=this.events.filter(t=>t.timestamp>=e);let r=t-this.events.length;r>0&&p.kg.debug(`Cleaned up ${r} old auth events`)}getStats(e=36e5){let t=Date.now()-e,r=this.events.filter(e=>e.timestamp>=t),i=r.filter(e=>"LOGIN_SUCCESS"===e.type).length,s=r.filter(e=>"LOGIN_FAILURE"===e.type).length,o=r.filter(e=>"OAUTH_ERROR"===e.type).length,n=r.filter(e=>"SUSPICIOUS_ACTIVITY"===e.type).length,a=new Map;r.filter(e=>e.error).forEach(e=>{let t=e.error||"Unknown";a.set(t,(a.get(t)||0)+1)});let l=new Map;return r.filter(e=>"LOGIN_FAILURE"===e.type).forEach(e=>{l.set(e.ip,(l.get(e.ip)||0)+1)}),{totalEvents:r.length,successfulLogins:i,failedLogins:s,oauthErrors:o,suspiciousActivity:n,topFailureReasons:Array.from(a.entries()).map(([e,t])=>({reason:e,count:t})).sort((e,t)=>t.count-e.count).slice(0,5),topFailureIPs:Array.from(l.entries()).map(([e,t])=>({ip:e,count:t})).sort((e,t)=>t.count-e.count).slice(0,5)}}updateConfig(e){this.config={...this.config,...e},p.kg.info("Auth monitor configuration updated",{config:this.config})}}let R=new w,A={rateLimitExceeded:(e,t)=>{R.recordEvent({type:"RATE_LIMIT_EXCEEDED",ip:e,userAgent:t})}};class k{constructor(){this.redis=null,this.memoryStore=new Map,this.CLEANUP_INTERVAL=3e5,process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN&&(this.redis=new I.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN})),setInterval(()=>{this.cleanupMemoryStore()},this.CLEANUP_INTERVAL)}async checkLimit(e,t){let r=t.keyGenerator?t.keyGenerator(e):`rate_limit:${e}`,i=Date.now(),s=i-t.windowMs;try{if(this.redis)return await this.checkLimitRedis(r,t,i,s);return this.checkLimitMemory(r,t,i,s)}catch(r){return p.kg.error("Rate limiter error, blocking request for security",{error:r,identifier:e}),{allowed:!1,remaining:0,resetTime:i+t.windowMs,totalHits:t.maxRequests,isBlocked:!0,error:"Rate limiter temporarily unavailable"}}}async checkLimitRedis(e,t,r,i){let s=`${e}:blocked`,o=await this.redis.get(s);if(o&&parseInt(o)>r)return{allowed:!1,remaining:0,resetTime:parseInt(o),totalHits:t.maxRequests,isBlocked:!0,blockExpiresAt:parseInt(o)};let n=this.redis.pipeline();n.zremrangebyscore(e,0,i),n.zcard(e),n.zadd(e,{score:r,member:`${r}-${Math.random()}`}),n.expire(e,Math.ceil(t.windowMs/1e3));let a=await n.exec(),l=a&&a[1]&&Array.isArray(a[1])?a[1][1]:0,c=("number"==typeof l?l:0)+1,u=Math.max(0,t.maxRequests-c),p=r+t.windowMs;if(c>t.maxRequests){let e=r+t.blockDurationMs;return await this.redis.setex(s,Math.ceil(t.blockDurationMs/1e3),e.toString()),{allowed:!1,remaining:0,resetTime:e,totalHits:c,isBlocked:!0,blockExpiresAt:e}}return{allowed:!0,remaining:u,resetTime:p,totalHits:c,isBlocked:!1}}checkLimitMemory(e,t,r,i){let s=this.memoryStore.get(e);if(s?.blocked&&s.blocked>r)return{allowed:!1,remaining:0,resetTime:s.blocked,totalHits:t.maxRequests,isBlocked:!0,blockExpiresAt:s.blocked};if(!s||s.resetTime<=r)return this.memoryStore.set(e,{count:1,resetTime:r+t.windowMs}),{allowed:!0,remaining:t.maxRequests-1,resetTime:r+t.windowMs,totalHits:1,isBlocked:!1};s.count++;let o=Math.max(0,t.maxRequests-s.count);if(s.count>t.maxRequests){let e=r+t.blockDurationMs;return s.blocked=e,{allowed:!1,remaining:0,resetTime:e,totalHits:s.count,isBlocked:!0,blockExpiresAt:e}}return{allowed:!0,remaining:o,resetTime:s.resetTime,totalHits:s.count,isBlocked:!1}}cleanupMemoryStore(){let e=Date.now(),t=0;for(let[r,i]of this.memoryStore.entries())i.resetTime<=e&&(!i.blocked||i.blocked<=e)&&(this.memoryStore.delete(r),t++);t>0&&p.kg.debug(`Cleaned up ${t} expired rate limit entries`)}async resetLimit(e){let t=`rate_limit:${e}`;try{this.redis?await this.redis.del(t,`${t}:blocked`):this.memoryStore.delete(t),p.kg.info("Rate limit reset",{identifier:e})}catch(t){p.kg.error("Failed to reset rate limit",{error:t,identifier:e})}}}let T=new k,O={WEBSOCKET_CONNECT:{windowMs:6e4,maxRequests:5,blockDurationMs:3e5},CURSOR_UPDATE:{windowMs:6e4,maxRequests:500,blockDurationMs:3e4},CELL_EDIT:{windowMs:6e4,maxRequests:100,blockDurationMs:6e4}};async function $(e,t,r){let i=await T.checkLimit(e,t);return!i.allowed&&(p.kg.warn("Rate limit exceeded",{identifier:e,totalHits:i.totalHits,maxRequests:t.maxRequests,isBlocked:i.isBlocked,blockExpiresAt:i.blockExpiresAt}),r&&(e.includes("auth")||e.includes("login"))&&A.rateLimitExceeded(r.ip||"unknown",r.headers?.["user-agent"]||"unknown")),i}let x={websocketConnect:async(e,t)=>$(`websocket:connect:${e}:${t}`,O.WEBSOCKET_CONNECT),cursorUpdate:async(e,t)=>$(`cursor:${t}:${e}`,O.CURSOR_UPDATE),cellEdit:async(e,t)=>$(`cell_edit:${t}:${e}`,O.CELL_EDIT)},C="force-dynamic";class L{constructor(){this.activeCollaborators=new Map,this.userSockets=new Map,this.socketUsers=new Map,this.workbookSockets=new Map}static getInstance(){return L.instance||(L.instance=new L),L.instance}addCollaborator(e,t){let r=this.activeCollaborators.get(e)||[],i=r.findIndex(e=>e.id===t.id);i>=0?r[i]=t:r.push(t),this.activeCollaborators.set(e,r),this.userSockets.set(t.id,t.socket),this.socketUsers.set(t.socket,t.id);let s=this.workbookSockets.get(e)||new Set;s.add(t.socket),this.workbookSockets.set(e,s)}removeCollaborator(e){let t=this.socketUsers.get(e);if(!t)return[];let r=[];return this.activeCollaborators.forEach((i,s)=>{let o=i.findIndex(e=>e.id===t);if(o>=0){i.splice(o,1),this.activeCollaborators.set(s,i),r.push(s);let t=this.workbookSockets.get(s);t&&(t.delete(e),0===t.size?this.workbookSockets.delete(s):this.workbookSockets.set(s,t))}}),this.userSockets.delete(t),this.socketUsers.delete(e),r}getCollaborators(e){return this.activeCollaborators.get(e)||[]}updateCollaboratorPosition(e,t){let r=this.socketUsers.get(e);if(!r)return null;let i=[];return this.activeCollaborators.forEach((e,s)=>{let o=e.find(e=>e.id===r);o&&(o.position=t,o.lastActive=new Date,o.status="active",i.push(s))}),{userId:r,workbookIds:i}}getWorkbookSockets(e){let t=this.workbookSockets.get(e);return t?Array.from(t):[]}}let N=null;async function P(e,t){try{if(!N){let e=t.socket.server;N=new u.xF(e,{path:"/api/socket",cors:{origin:process.env.AUTH_NEXTAUTH_URL||"*",methods:["GET","POST"],credentials:!0}});let r=L.getInstance();N.on("connection",async e=>{let{workbookId:t,userId:i,userName:s,userEmail:o,token:n}=e.handshake.auth,a=function(e){let t=e.headers,r=e.connection,i=e.socket,s=t?.["x-forwarded-for"],o=t?.["x-real-ip"],n=r?.remoteAddress,a=i?.remoteAddress;return s?.split(",")[0]||o||n||a||"unknown"}(e.handshake);if(!t||!i){e.emit("error","Informa\xe7\xf5es de autentica\xe7\xe3o incompletas"),e.disconnect();return}let l=await x.websocketConnect(i,a);if(!l.allowed){p.kg.warn("Rate limit excedido para conex\xe3o WebSocket",{userId:i,clientIP:a,remaining:l.remaining}),e.emit("error","Muitas tentativas de conex\xe3o. Tente novamente em alguns minutos."),e.disconnect();return}if(n){let t=await g.validateWebSocketToken(n,e.id);if(!t.success){p.kg.warn("Falha na autentica\xe7\xe3o JWT WebSocket",{userId:i,error:t.error,shouldBlock:t.shouldBlock}),e.emit("error",t.error||"Falha na autentica\xe7\xe3o"),e.disconnect();return}}try{if(!await m.prisma.workbook.findFirst({where:{id:t,OR:[{userId:i},{shares:{some:{sharedWithUserId:i}}}]}})){e.emit("error","Sem permiss\xe3o para acessar esta planilha"),e.disconnect();return}}catch(t){p.kg.error("Erro ao verificar permiss\xe3o de planilha:",t),e.emit("error","Erro ao verificar permiss\xe3o"),e.disconnect();return}p.kg.info(`Usu\xe1rio ${s} (${i}) conectado \xe0 planilha ${t}`),e.join(`workbook:${t}`),b.registerChannelActivity(t);let c={id:i,name:s||"Usu\xe1rio",email:o||"",socket:e.id,status:"active",lastActive:new Date};await r.addCollaborator(t,c),e.to(`workbook:${t}`).emit("collaborator_joined",{id:i,name:s,status:"active"});let u=r.getCollaborators(t);e.emit("collaborators",u),e.on("cursor_position",async s=>{if(!(await x.cursorUpdate(i,t)).allowed)return;let o=r.updateCollaboratorPosition(e.id,s);if(o){let{userId:t,workbookIds:r}=o;r.forEach(r=>{e.to(`workbook:${r}`).emit("cursor_position",{userId:t,position:s,timestamp:Date.now()})})}}),e.on("cell_changed",async r=>{if(!(await x.cellEdit(i,t)).allowed){e.emit("error","Muitas edi\xe7\xf5es muito rapidamente. Aguarde um momento.");return}try{let o={id:`${Date.now()}_${i}_${Math.random().toString(36).substr(2,9)}`,type:"update",cellId:r.cellId,sheetId:r.sheetId,value:r.value,oldValue:r.oldValue,timestamp:Date.now(),userId:i,userName:s||"Usu\xe1rio"},n=await f.addOperation(t,o);if("reject"===n.resolution){e.emit("operation_rejected",{operationId:o.id,reason:n.reason});return}let a=n.transformedOperation||o,l={...r,operationId:a.id,userId:i,userName:s,timestamp:a.timestamp,resolution:n.resolution};e.to(`workbook:${t}`).emit("cell_changed",l),e.emit("operation_confirmed",{operationId:a.id,resolution:n.resolution})}catch(r){p.kg.error("Erro ao processar altera\xe7\xe3o de c\xe9lula",{workbookId:t,userId:i,error:r instanceof Error?r.message:"Erro desconhecido"}),e.emit("error","Erro ao processar altera\xe7\xe3o")}}),e.on("disconnect",async()=>{(await r.removeCollaborator(e.id)).forEach(t=>{e.to(`workbook:${t}`).emit("collaborator_left",i)}),n&&g.removeActiveConnection(i,e.id),f.cleanupOldOperations(t,Date.now()-6e4);let o=r.getCollaborators(t);0===o.length&&b.unregisterChannel(t),p.kg.info(`Usu\xe1rio ${s} (${i}) desconectado`)})})}return new c.NextResponse("Socket.IO server is running",{status:200})}catch(e){return p.kg.error("Erro ao inicializar Socket.IO",e),new c.NextResponse("Failed to start Socket.IO server",{status:500})}}let _=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/socket/route",pathname:"/api/socket",filename:"route",bundlePath:"app/api/socket/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\socket\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:j,staticGenerationAsyncStorage:D,serverHooks:M}=_,U="/api/socket/route";function H(){return(0,l.patchFetch)({serverHooks:M,staticGenerationAsyncStorage:D})}},89121:(e,t,r)=>{"use strict";var i=r(14300).Buffer,s=r(14300).SlowBuffer;function o(e,t){if(!i.isBuffer(e)||!i.isBuffer(t)||e.length!==t.length)return!1;for(var r=0,s=0;s<e.length;s++)r|=e[s]^t[s];return 0===r}e.exports=o,o.install=function(){i.prototype.equal=s.prototype.equal=function(e){return o(this,e)}};var n=i.prototype.equal,a=s.prototype.equal;o.restore=function(){i.prototype.equal=n,s.prototype.equal=a}},43183:(e,t)=>{"use strict";/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},s=e.length;if(s<2)return r;var o=t&&t.decode||u,n=0,a=0,p=0;do{if(-1===(a=e.indexOf("=",n)))break;if(-1===(p=e.indexOf(";",n)))p=s;else if(a>p){n=e.lastIndexOf(";",a-1)+1;continue}var h=l(e,n,a),f=c(e,a,h),d=e.slice(h,f);if(!i.call(r,d)){var m=l(e,a+1,p),E=c(e,p,m);34===e.charCodeAt(m)&&34===e.charCodeAt(E-1)&&(m++,E--);var g=e.slice(m,E);r[d]=function(e,t){try{return t(e)}catch(t){return e}}(g,o)}n=p+1}while(n<s);return r},t.serialize=function(e,t,i){var l=i&&i.encode||encodeURIComponent;if("function"!=typeof l)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var c=l(t);if(!o.test(c))throw TypeError("argument val is invalid");var u=e+"="+c;if(!i)return u;if(null!=i.maxAge){var p=Math.floor(i.maxAge);if(!isFinite(p))throw TypeError("option maxAge is invalid");u+="; Max-Age="+p}if(i.domain){if(!n.test(i.domain))throw TypeError("option domain is invalid");u+="; Domain="+i.domain}if(i.path){if(!a.test(i.path))throw TypeError("option path is invalid");u+="; Path="+i.path}if(i.expires){var h=i.expires;if("[object Date]"!==r.call(h)||isNaN(h.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+h.toUTCString()}if(i.httpOnly&&(u+="; HttpOnly"),i.secure&&(u+="; Secure"),i.partitioned&&(u+="; Partitioned"),i.priority)switch("string"==typeof i.priority?i.priority.toLowerCase():i.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var r=Object.prototype.toString,i=Object.prototype.hasOwnProperty,s=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,o=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/;function l(e,t,r){do{var i=e.charCodeAt(t);if(32!==i&&9!==i)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var i=e.charCodeAt(--t);if(32!==i&&9!==i)return t+1}return r}function u(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},97145:(e,t,r)=>{"use strict";var i=r(18243).Buffer,s=r(41457);function o(e){if(i.isBuffer(e))return e;if("string"==typeof e)return i.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function n(e,t,r){for(var i=0;t+i<r&&0===e[t+i];)++i;return e[t+i]>=128&&--i,i}e.exports={derToJose:function(e,t){e=o(e);var r=s(t),n=r+1,a=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var c=e[l++];if(129===c&&(c=e[l++]),a-l<c)throw Error('"seq" specified length of "'+c+'", only "'+(a-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var u=e[l++];if(a-l-2<u)throw Error('"r" specified length of "'+u+'", only "'+(a-l-2)+'" available');if(n<u)throw Error('"r" specified length of "'+u+'", max of "'+n+'" is acceptable');var p=l;if(l+=u,2!==e[l++])throw Error('Could not find expected "int" for "s"');var h=e[l++];if(a-l!==h)throw Error('"s" specified length of "'+h+'", expected "'+(a-l)+'"');if(n<h)throw Error('"s" specified length of "'+h+'", max of "'+n+'" is acceptable');var f=l;if((l+=h)!==a)throw Error('Expected to consume entire buffer, but "'+(a-l)+'" bytes remain');var d=r-u,m=r-h,E=i.allocUnsafe(d+u+m+h);for(l=0;l<d;++l)E[l]=0;e.copy(E,l,p+Math.max(-d,0),p+u),l=r;for(var g=l;l<g+m;++l)E[l]=0;return e.copy(E,l,f+Math.max(-m,0),f+h),E=(E=E.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,t){e=o(e);var r=s(t),a=e.length;if(a!==2*r)throw TypeError('"'+t+'" signatures must be "'+2*r+'" bytes, saw "'+a+'"');var l=n(e,0,r),c=n(e,r,e.length),u=r-l,p=r-c,h=2+u+1+1+p,f=h<128,d=i.allocUnsafe((f?2:3)+h),m=0;return d[m++]=48,f?d[m++]=h:(d[m++]=129,d[m++]=255&h),d[m++]=2,d[m++]=u,l<0?(d[m++]=0,m+=e.copy(d,m,0,r)):m+=e.copy(d,m,l,r),d[m++]=2,d[m++]=p,c<0?(d[m++]=0,e.copy(d,m,r)):e.copy(d,m,r+c),d}}},41457:e=>{"use strict";function t(e){return(e/8|0)+(e%8==0?0:1)}var r={ES256:t(256),ES384:t(384),ES512:t(521)};e.exports=function(e){var t=r[e];if(t)return t;throw Error('Unknown algorithm "'+e+'"')}},42857:(e,t,r)=>{var i=r(87126);e.exports=function(e,t){t=t||{};var r=i.decode(e,t);if(!r)return null;var s=r.payload;if("string"==typeof s)try{var o=JSON.parse(s);null!==o&&"object"==typeof o&&(s=o)}catch(e){}return!0===t.complete?{header:r.header,payload:s,signature:r.signature}:s}},41482:(e,t,r)=>{e.exports={decode:r(42857),verify:r(88061),sign:r(73601),JsonWebTokenError:r(453),NotBeforeError:r(37565),TokenExpiredError:r(32658)}},453:e=>{var t=function(e,t){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,t&&(this.inner=t)};t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,e.exports=t},37565:(e,t,r)=>{var i=r(453),s=function(e,t){i.call(this,e),this.name="NotBeforeError",this.date=t};s.prototype=Object.create(i.prototype),s.prototype.constructor=s,e.exports=s},32658:(e,t,r)=>{var i=r(453),s=function(e,t){i.call(this,e),this.name="TokenExpiredError",this.expiredAt=t};s.prototype=Object.create(i.prototype),s.prototype.constructor=s,e.exports=s},91440:(e,t,r)=>{let i=r(90799);e.exports=i.satisfies(process.version,">=15.7.0")},33051:(e,t,r)=>{var i=r(90799);e.exports=i.satisfies(process.version,"^6.12.0 || >=8.0.0")},41790:(e,t,r)=>{let i=r(90799);e.exports=i.satisfies(process.version,">=16.9.0")},73258:(e,t,r)=>{var i=r(13974);e.exports=function(e,t){var r=t||Math.floor(Date.now()/1e3);if("string"==typeof e){var s=i(e);if(void 0===s)return;return Math.floor(r+s/1e3)}if("number"==typeof e)return r+e}},59279:(e,t,r)=>{let i=r(91440),s=r(41790),o={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},n={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,t){if(!e||!t)return;let r=t.asymmetricKeyType;if(!r)return;let a=o[r];if(!a)throw Error(`Unknown key type "${r}".`);if(!a.includes(e))throw Error(`"alg" parameter for "${r}" key type must be one of: ${a.join(", ")}.`);if(i)switch(r){case"ec":let l=t.asymmetricKeyDetails.namedCurve,c=n[e];if(l!==c)throw Error(`"alg" parameter "${e}" requires curve "${c}".`);break;case"rsa-pss":if(s){let r=parseInt(e.slice(-3),10),{hashAlgorithm:i,mgf1HashAlgorithm:s,saltLength:o}=t.asymmetricKeyDetails;if(i!==`sha${r}`||s!==i)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==o&&o>r>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},91853:(e,t,r)=>{var i,s=r(18243).Buffer,o=r(6113),n=r(97145),a=r(73837),l="secret must be a string or buffer",c="key must be a string or a buffer",u="function"==typeof o.createPublicKey;function p(e){if(!s.isBuffer(e)&&"string"!=typeof e&&(!u||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw m(c)}function h(e){if(!s.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw m("key must be a string, a buffer or an object")}function f(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function d(e){var t=4-(e=e.toString()).length%4;if(4!==t)for(var r=0;r<t;++r)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function m(e){var t=[].slice.call(arguments,1);return TypeError(a.format.bind(a,e).apply(null,t))}function E(e){var t;return t=e,s.isBuffer(t)||"string"==typeof t||(e=JSON.stringify(e)),e}function g(e){return function(t,r){(function(e){if(!s.isBuffer(e)&&"string"!=typeof e&&(!u||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export))throw m(l)})(r),t=E(t);var i=o.createHmac("sha"+e,r);return f((i.update(t),i.digest("base64")))}}u&&(c+=" or a KeyObject",l+="or a KeyObject");var y="timingSafeEqual"in o?function(e,t){return e.byteLength===t.byteLength&&o.timingSafeEqual(e,t)}:function(e,t){return i||(i=r(89121)),i(e,t)};function v(e){return function(t,r,i){var o=g(e)(t,i);return y(s.from(r),s.from(o))}}function S(e){return function(t,r){h(r),t=E(t);var i=o.createSign("RSA-SHA"+e);return f((i.update(t),i.sign(r,"base64")))}}function b(e){return function(t,r,i){p(i),t=E(t),r=d(r);var s=o.createVerify("RSA-SHA"+e);return s.update(t),s.verify(i,r,"base64")}}function I(e){return function(t,r){h(r),t=E(t);var i=o.createSign("RSA-SHA"+e);return f((i.update(t),i.sign({key:r,padding:o.constants.RSA_PKCS1_PSS_PADDING,saltLength:o.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function w(e){return function(t,r,i){p(i),t=E(t),r=d(r);var s=o.createVerify("RSA-SHA"+e);return s.update(t),s.verify({key:i,padding:o.constants.RSA_PKCS1_PSS_PADDING,saltLength:o.constants.RSA_PSS_SALTLEN_DIGEST},r,"base64")}}function R(e){var t=S(e);return function(){var r=t.apply(null,arguments);return n.derToJose(r,"ES"+e)}}function A(e){var t=b(e);return function(r,i,s){return t(r,i=n.joseToDer(i,"ES"+e).toString("base64"),s)}}function k(){return function(){return""}}function T(){return function(e,t){return""===t}}e.exports=function(e){var t=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!t)throw m('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var r=(t[1]||t[3]).toLowerCase(),i=t[2];return{sign:({hs:g,rs:S,ps:I,es:R,none:k})[r](i),verify:({hs:v,rs:b,ps:w,es:A,none:T})[r](i)}}},87126:(e,t,r)=>{var i=r(26993),s=r(21642);t.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],t.sign=i.sign,t.verify=s.verify,t.decode=s.decode,t.isValid=s.isValid,t.createSign=function(e){return new i(e)},t.createVerify=function(e){return new s(e)}},83853:(e,t,r)=>{var i=r(18243).Buffer,s=r(12781);function o(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=i.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=i.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}r(73837).inherits(o,s),o.prototype.write=function(e){this.buffer=i.concat([this.buffer,i.from(e)]),this.emit("data",e)},o.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=o},26993:(e,t,r)=>{var i=r(18243).Buffer,s=r(83853),o=r(91853),n=r(12781),a=r(42164),l=r(73837);function c(e,t){return i.from(e,t).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function u(e){var t,r,i,s=e.header,n=e.payload,u=e.secret||e.privateKey,p=e.encoding,h=o(s.alg),f=(t=(t=p)||"utf8",r=c(a(s),"binary"),i=c(a(n),t),l.format("%s.%s",r,i)),d=h.sign(f,u);return l.format("%s.%s",f,d)}function p(e){var t=new s(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=t,this.payload=new s(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(p,n),p.prototype.sign=function(){try{var e=u({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},p.sign=u,e.exports=p},42164:(e,t,r)=>{var i=r(14300).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||i.isBuffer(e)?e.toString():JSON.stringify(e)}},21642:(e,t,r)=>{var i=r(18243).Buffer,s=r(83853),o=r(91853),n=r(12781),a=r(42164),l=r(73837),c=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function u(e){var t=e.split(".",1)[0];return function(e){if("[object Object]"===Object.prototype.toString.call(e))return e;try{return JSON.parse(e)}catch(e){return}}(i.from(t,"base64").toString("binary"))}function p(e){return e.split(".")[2]}function h(e){return c.test(e)&&!!u(e)}function f(e,t,r){if(!t){var i=Error("Missing algorithm parameter for jws.verify");throw i.code="MISSING_ALGORITHM",i}var s=p(e=a(e)),n=e.split(".",2).join(".");return o(t).verify(n,s,r)}function d(e,t){if(t=t||{},!h(e=a(e)))return null;var r,s,o=u(e);if(!o)return null;var n=(r=r||"utf8",s=e.split(".")[1],i.from(s,"base64").toString(r));return("JWT"===o.typ||t.json)&&(n=JSON.parse(n,t.encoding)),{header:o,payload:n,signature:p(e)}}function m(e){var t=new s((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=t,this.signature=new s(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(m,n),m.prototype.verify=function(){try{var e=f(this.signature.buffer,this.algorithm,this.key.buffer),t=d(this.signature.buffer,this.encoding);return this.emit("done",e,t),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},m.decode=d,m.isValid=h,m.verify=f,e.exports=m},73601:(e,t,r)=>{let i=r(73258),s=r(33051),o=r(59279),n=r(87126),a=r(22086),l=r(21724),c=r(54591),u=r(59366),p=r(29080),h=r(71380),f=r(32144),{KeyObject:d,createSecretKey:m,createPrivateKey:E}=r(6113),g=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];s&&g.splice(3,0,"PS256","PS384","PS512");let y={expiresIn:{isValid:function(e){return c(e)||h(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return c(e)||h(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return h(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:a.bind(null,g),message:'"algorithm" must be a valid string enum value'},header:{isValid:p,message:'"header" must be an object'},encoding:{isValid:h,message:'"encoding" must be a string'},issuer:{isValid:h,message:'"issuer" must be a string'},subject:{isValid:h,message:'"subject" must be a string'},jwtid:{isValid:h,message:'"jwtid" must be a string'},noTimestamp:{isValid:l,message:'"noTimestamp" must be a boolean'},keyid:{isValid:h,message:'"keyid" must be a string'},mutatePayload:{isValid:l,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:l,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:l,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},v={iat:{isValid:u,message:'"iat" should be a number of seconds'},exp:{isValid:u,message:'"exp" should be a number of seconds'},nbf:{isValid:u,message:'"nbf" should be a number of seconds'}};function S(e,t,r,i){if(!p(r))throw Error('Expected "'+i+'" to be a plain object.');Object.keys(r).forEach(function(s){let o=e[s];if(!o){if(!t)throw Error('"'+s+'" is not allowed in "'+i+'"');return}if(!o.isValid(r[s]))throw Error(o.message)})}let b={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},I=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,t,r,s){var a,l;"function"==typeof r?(s=r,r={}):r=r||{};let c="object"==typeof e&&!Buffer.isBuffer(e),u=Object.assign({alg:r.algorithm||"HS256",typ:c?"JWT":void 0,kid:r.keyid},r.header);function p(e){if(s)return s(e);throw e}if(!t&&"none"!==r.algorithm)return p(Error("secretOrPrivateKey must have a value"));if(null!=t&&!(t instanceof d))try{t=E(t)}catch(e){try{t=m("string"==typeof t?Buffer.from(t):t)}catch(e){return p(Error("secretOrPrivateKey is not valid key material"))}}if(u.alg.startsWith("HS")&&"secret"!==t.type)return p(Error(`secretOrPrivateKey must be a symmetric key when using ${u.alg}`));if(/^(?:RS|PS|ES)/.test(u.alg)){if("private"!==t.type)return p(Error(`secretOrPrivateKey must be an asymmetric key when using ${u.alg}`));if(!r.allowInsecureKeySizes&&!u.alg.startsWith("ES")&&void 0!==t.asymmetricKeyDetails&&t.asymmetricKeyDetails.modulusLength<2048)return p(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${u.alg}`))}if(void 0===e)return p(Error("payload is required"));if(c){try{a=e,S(v,!0,a,"payload")}catch(e){return p(e)}r.mutatePayload||(e=Object.assign({},e))}else{let t=I.filter(function(e){return void 0!==r[e]});if(t.length>0)return p(Error("invalid "+t.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==r.expiresIn)return p(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==r.notBefore)return p(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{l=r,S(y,!1,l,"options")}catch(e){return p(e)}if(!r.allowInvalidAsymmetricKeyTypes)try{o(u.alg,t)}catch(e){return p(e)}let h=e.iat||Math.floor(Date.now()/1e3);if(r.noTimestamp?delete e.iat:c&&(e.iat=h),void 0!==r.notBefore){try{e.nbf=i(r.notBefore,h)}catch(e){return p(e)}if(void 0===e.nbf)return p(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==r.expiresIn&&"object"==typeof e){try{e.exp=i(r.expiresIn,h)}catch(e){return p(e)}if(void 0===e.exp)return p(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(b).forEach(function(t){let i=b[t];if(void 0!==r[t]){if(void 0!==e[i])return p(Error('Bad "options.'+t+'" option. The payload already has an "'+i+'" property.'));e[i]=r[t]}});let g=r.encoding||"utf8";if("function"==typeof s)s=s&&f(s),n.createSign({header:u,privateKey:t,payload:e,encoding:g}).once("error",s).once("done",function(e){if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(u.alg)&&e.length<256)return s(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${u.alg}`));s(null,e)});else{let i=n.sign({header:u,payload:e,secret:t,encoding:g});if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(u.alg)&&i.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${u.alg}`);return i}}},88061:(e,t,r)=>{let i=r(453),s=r(37565),o=r(32658),n=r(42857),a=r(73258),l=r(59279),c=r(33051),u=r(87126),{KeyObject:p,createSecretKey:h,createPublicKey:f}=r(6113),d=["RS256","RS384","RS512"],m=["ES256","ES384","ES512"],E=["RS256","RS384","RS512"],g=["HS256","HS384","HS512"];c&&(d.splice(d.length,0,"PS256","PS384","PS512"),E.splice(E.length,0,"PS256","PS384","PS512")),e.exports=function(e,t,r,c){let y,v,S;if("function"!=typeof r||c||(c=r,r={}),r||(r={}),r=Object.assign({},r),y=c||function(e,t){if(e)throw e;return t},r.clockTimestamp&&"number"!=typeof r.clockTimestamp)return y(new i("clockTimestamp must be a number"));if(void 0!==r.nonce&&("string"!=typeof r.nonce||""===r.nonce.trim()))return y(new i("nonce must be a non-empty string"));if(void 0!==r.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof r.allowInvalidAsymmetricKeyTypes)return y(new i("allowInvalidAsymmetricKeyTypes must be a boolean"));let b=r.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return y(new i("jwt must be provided"));if("string"!=typeof e)return y(new i("jwt must be a string"));let I=e.split(".");if(3!==I.length)return y(new i("jwt malformed"));try{v=n(e,{complete:!0})}catch(e){return y(e)}if(!v)return y(new i("invalid token"));let w=v.header;if("function"==typeof t){if(!c)return y(new i("verify must be called asynchronous if secret or public key is provided as a callback"));S=t}else S=function(e,r){return r(null,t)};return S(w,function(t,n){let c;if(t)return y(new i("error in secret or public key callback: "+t.message));let S=""!==I[2].trim();if(!S&&n)return y(new i("jwt signature is required"));if(S&&!n)return y(new i("secret or public key must be provided"));if(!S&&!r.algorithms)return y(new i('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=n&&!(n instanceof p))try{n=f(n)}catch(e){try{n=h("string"==typeof n?Buffer.from(n):n)}catch(e){return y(new i("secretOrPublicKey is not valid key material"))}}if(r.algorithms||("secret"===n.type?r.algorithms=g:["rsa","rsa-pss"].includes(n.asymmetricKeyType)?r.algorithms=E:"ec"===n.asymmetricKeyType?r.algorithms=m:r.algorithms=d),-1===r.algorithms.indexOf(v.header.alg))return y(new i("invalid algorithm"));if(w.alg.startsWith("HS")&&"secret"!==n.type)return y(new i(`secretOrPublicKey must be a symmetric key when using ${w.alg}`));if(/^(?:RS|PS|ES)/.test(w.alg)&&"public"!==n.type)return y(new i(`secretOrPublicKey must be an asymmetric key when using ${w.alg}`));if(!r.allowInvalidAsymmetricKeyTypes)try{l(w.alg,n)}catch(e){return y(e)}try{c=u.verify(e,v.header.alg,n)}catch(e){return y(e)}if(!c)return y(new i("invalid signature"));let R=v.payload;if(void 0!==R.nbf&&!r.ignoreNotBefore){if("number"!=typeof R.nbf)return y(new i("invalid nbf value"));if(R.nbf>b+(r.clockTolerance||0))return y(new s("jwt not active",new Date(1e3*R.nbf)))}if(void 0!==R.exp&&!r.ignoreExpiration){if("number"!=typeof R.exp)return y(new i("invalid exp value"));if(b>=R.exp+(r.clockTolerance||0))return y(new o("jwt expired",new Date(1e3*R.exp)))}if(r.audience){let e=Array.isArray(r.audience)?r.audience:[r.audience];if(!(Array.isArray(R.aud)?R.aud:[R.aud]).some(function(t){return e.some(function(e){return e instanceof RegExp?e.test(t):e===t})}))return y(new i("jwt audience invalid. expected: "+e.join(" or ")))}if(r.issuer&&("string"==typeof r.issuer&&R.iss!==r.issuer||Array.isArray(r.issuer)&&-1===r.issuer.indexOf(R.iss)))return y(new i("jwt issuer invalid. expected: "+r.issuer));if(r.subject&&R.sub!==r.subject)return y(new i("jwt subject invalid. expected: "+r.subject));if(r.jwtid&&R.jti!==r.jwtid)return y(new i("jwt jwtid invalid. expected: "+r.jwtid));if(r.nonce&&R.nonce!==r.nonce)return y(new i("jwt nonce invalid. expected: "+r.nonce));if(r.maxAge){if("number"!=typeof R.iat)return y(new i("iat required when maxAge is specified"));let e=a(r.maxAge,R.iat);if(void 0===e)return y(new i('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(b>=e+(r.clockTolerance||0))return y(new o("maxAge exceeded",new Date(1e3*e)))}return!0===r.complete?y(null,{header:w,payload:R,signature:v.signature}):y(null,R)})}},22086:e=>{var t,r,i=1/0,s=0/0,o=/^\s+|\s+$/g,n=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,c=/^(?:0|[1-9]\d*)$/,u=parseInt;function p(e){return e!=e}var h=Object.prototype,f=h.hasOwnProperty,d=h.toString,m=h.propertyIsEnumerable,E=(t=Object.keys,r=Object,function(e){return t(r(e))}),g=Math.max,y=Array.isArray;function v(e){var t,r;return null!=e&&"number"==typeof(t=e.length)&&t>-1&&t%1==0&&t<=9007199254740991&&!("[object Function]"==(r=S(e)?d.call(e):"")||"[object GeneratorFunction]"==r)}function S(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function b(e){return!!e&&"object"==typeof e}e.exports=function(e,t,r,I){e=v(e)?e:(w=e)?function(e,t){for(var r=-1,i=e?e.length:0,s=Array(i);++r<i;)s[r]=t(e[r],r,e);return s}(v(w)?function(e,t){var r,i=y(e)||b(e)&&v(e)&&f.call(e,"callee")&&(!m.call(e,"callee")||"[object Arguments]"==d.call(e))?function(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}(e.length,String):[],s=i.length,o=!!s;for(var n in e)f.call(e,n)&&!(o&&("length"==n||(r=null==(r=s)?9007199254740991:r)&&("number"==typeof n||c.test(n))&&n>-1&&n%1==0&&n<r))&&i.push(n);return i}(w):function(e){if(t=e&&e.constructor,e!==("function"==typeof t&&t.prototype||h))return E(e);var t,r=[];for(var i in Object(e))f.call(e,i)&&"constructor"!=i&&r.push(i);return r}(w),function(e){return w[e]}):[],r=r&&!I?(k=(A=(R=r)?(R=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||b(t)&&"[object Symbol]"==d.call(t))return s;if(S(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=S(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(o,"");var i=a.test(e);return i||l.test(e)?u(e.slice(2),i?2:8):n.test(e)?s:+e}(R))===i||R===-i?(R<0?-1:1)*17976931348623157e292:R==R?R:0:0===R?R:0)%1,A==A?k?A-k:A:0):0;var w,R,A,k,T,O=e.length;return r<0&&(r=g(O+r,0)),"string"==typeof(T=e)||!y(T)&&b(T)&&"[object String]"==d.call(T)?r<=O&&e.indexOf(t,r)>-1:!!O&&function(e,t,r){if(t!=t)return function(e,t,r,i){for(var s=e.length,o=r+-1;++o<s;)if(t(e[o],o,e))return o;return -1}(e,p,r);for(var i=r-1,s=e.length;++i<s;)if(e[i]===t)return i;return -1}(e,t,r)>-1}},21724:e=>{var t=Object.prototype.toString;e.exports=function(e){return!0===e||!1===e||!!e&&"object"==typeof e&&"[object Boolean]"==t.call(e)}},54591:e=>{var t=1/0,r=0/0,i=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,n=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function c(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){var u,p,h;return"number"==typeof e&&e==(h=(p=(u=e)?(u=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(c(e)){var t,u="function"==typeof e.valueOf?e.valueOf():e;e=c(u)?u+"":u}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var p=o.test(e);return p||n.test(e)?a(e.slice(2),p?2:8):s.test(e)?r:+e}(u))===t||u===-t?(u<0?-1:1)*17976931348623157e292:u==u?u:0:0===u?u:0)%1,p==p?h?p-h:p:0)}},59366:e=>{var t=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==t.call(e)}},29080:e=>{var t,r,i=Object.prototype,s=Function.prototype.toString,o=i.hasOwnProperty,n=s.call(Object),a=i.toString,l=(t=Object.getPrototypeOf,r=Object,function(e){return t(r(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=a.call(e)||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(e))return!1;var t=l(e);if(null===t)return!0;var r=o.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&s.call(r)==n}},71380:e=>{var t=Object.prototype.toString,r=Array.isArray;e.exports=function(e){var i;return"string"==typeof e||!r(e)&&!!(i=e)&&"object"==typeof i&&"[object String]"==t.call(e)}},32144:e=>{var t=1/0,r=0/0,i=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,n=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function c(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){return function(e,u){var p,h,f,d;if("function"!=typeof u)throw TypeError("Expected a function");return d=(f=(h=e)?(h=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(c(e)){var t,u="function"==typeof e.valueOf?e.valueOf():e;e=c(u)?u+"":u}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var p=o.test(e);return p||n.test(e)?a(e.slice(2),p?2:8):s.test(e)?r:+e}(h))===t||h===-t?(h<0?-1:1)*17976931348623157e292:h==h?h:0:0===h?h:0)%1,e=f==f?d?f-d:f:0,function(){return--e>0&&(p=u.apply(this,arguments)),e<=1&&(u=void 0),p}}(2,e)}},18243:(e,t,r)=>{/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var i=r(14300),s=i.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function n(e,t,r){return s(e,t,r)}s.from&&s.alloc&&s.allocUnsafe&&s.allocUnsafeSlow?e.exports=i:(o(i,t),t.Buffer=n),n.prototype=Object.create(s.prototype),o(s,n),n.from=function(e,t,r){if("number"==typeof e)throw TypeError("Argument must not be a number");return s(e,t,r)},n.alloc=function(e,t,r){if("number"!=typeof e)throw TypeError("Argument must be a number");var i=s(e);return void 0!==t?"string"==typeof r?i.fill(t,r):i.fill(t):i.fill(0),i},n.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s(e)},n.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i.SlowBuffer(e)}},12925:(e,t,r)=>{let i=Symbol("SemVer ANY");class s{static get ANY(){return i}constructor(e,t){if(t=o(t),e instanceof s){if(!!t.loose===e.loose)return e;e=e.value}c("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===i?this.value="":this.value=this.operator+this.semver.version,c("comp",this)}parse(e){let t=this.options.loose?n[a.COMPARATORLOOSE]:n[a.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new u(r[2],this.options.loose):this.semver=i}toString(){return this.value}test(e){if(c("Comparator.test",e,this.options.loose),this.semver===i||e===i)return!0;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new p(e.value,t).test(this.value):""===e.operator?""===e.value||new p(this.value,t).test(e.semver):!((t=o(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=s;let o=r(78949),{safeRe:n,t:a}=r(81520),l=r(43978),c=r(1410),u=r(28871),p=r(88889)},88889:(e,t,r)=>{let i=/\s+/g;class s{constructor(e,t){if(t=n(t),e instanceof s){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;return new s(e.raw,t)}if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(i," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!g(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&y(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&m)|(this.options.loose&&E))+":"+e,r=o.get(t);if(r)return r;let i=this.options.loose,s=i?u[p.HYPHENRANGELOOSE]:u[p.HYPHENRANGE];l("hyphen replace",e=e.replace(s,x(this.options.includePrerelease))),l("comparator trim",e=e.replace(u[p.COMPARATORTRIM],h)),l("tilde trim",e=e.replace(u[p.TILDETRIM],f)),l("caret trim",e=e.replace(u[p.CARETTRIM],d));let n=e.split(" ").map(e=>S(e,this.options)).join(" ").split(/\s+/).map(e=>$(e,this.options));i&&(n=n.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(u[p.COMPARATORLOOSE])))),l("range list",n);let c=new Map;for(let e of n.map(e=>new a(e,this.options))){if(g(e))return[e];c.set(e.value,e)}c.size>1&&c.has("")&&c.delete("");let y=[...c.values()];return o.set(t,y),y}intersects(e,t){if(!(e instanceof s))throw TypeError("a Range is required");return this.set.some(r=>v(r,t)&&e.set.some(e=>v(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(C(this.set[t],e,this.options))return!0;return!1}}e.exports=s;let o=new(r(50241)),n=r(78949),a=r(12925),l=r(1410),c=r(28871),{safeRe:u,t:p,comparatorTrimReplace:h,tildeTrimReplace:f,caretTrimReplace:d}=r(81520),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:E}=r(73651),g=e=>"<0.0.0-0"===e.value,y=e=>""===e.value,v=(e,t)=>{let r=!0,i=e.slice(),s=i.pop();for(;r&&i.length;)r=i.every(e=>s.intersects(e,t)),s=i.pop();return r},S=(e,t)=>(l("comp",e,t),l("caret",e=R(e,t)),l("tildes",e=I(e,t)),l("xrange",e=k(e,t)),l("stars",e=O(e,t)),e),b=e=>!e||"x"===e.toLowerCase()||"*"===e,I=(e,t)=>e.trim().split(/\s+/).map(e=>w(e,t)).join(" "),w=(e,t)=>{let r=t.loose?u[p.TILDELOOSE]:u[p.TILDE];return e.replace(r,(t,r,i,s,o)=>{let n;return l("tilde",e,t,r,i,s,o),b(r)?n="":b(i)?n=`>=${r}.0.0 <${+r+1}.0.0-0`:b(s)?n=`>=${r}.${i}.0 <${r}.${+i+1}.0-0`:o?(l("replaceTilde pr",o),n=`>=${r}.${i}.${s}-${o} <${r}.${+i+1}.0-0`):n=`>=${r}.${i}.${s} <${r}.${+i+1}.0-0`,l("tilde return",n),n})},R=(e,t)=>e.trim().split(/\s+/).map(e=>A(e,t)).join(" "),A=(e,t)=>{l("caret",e,t);let r=t.loose?u[p.CARETLOOSE]:u[p.CARET],i=t.includePrerelease?"-0":"";return e.replace(r,(t,r,s,o,n)=>{let a;return l("caret",e,t,r,s,o,n),b(r)?a="":b(s)?a=`>=${r}.0.0${i} <${+r+1}.0.0-0`:b(o)?a="0"===r?`>=${r}.${s}.0${i} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${i} <${+r+1}.0.0-0`:n?(l("replaceCaret pr",n),a="0"===r?"0"===s?`>=${r}.${s}.${o}-${n} <${r}.${s}.${+o+1}-0`:`>=${r}.${s}.${o}-${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${o}-${n} <${+r+1}.0.0-0`):(l("no pr"),a="0"===r?"0"===s?`>=${r}.${s}.${o}${i} <${r}.${s}.${+o+1}-0`:`>=${r}.${s}.${o}${i} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${o} <${+r+1}.0.0-0`),l("caret return",a),a})},k=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map(e=>T(e,t)).join(" ")),T=(e,t)=>{e=e.trim();let r=t.loose?u[p.XRANGELOOSE]:u[p.XRANGE];return e.replace(r,(r,i,s,o,n,a)=>{l("xRange",e,r,i,s,o,n,a);let c=b(s),u=c||b(o),p=u||b(n);return"="===i&&p&&(i=""),a=t.includePrerelease?"-0":"",c?r=">"===i||"<"===i?"<0.0.0-0":"*":i&&p?(u&&(o=0),n=0,">"===i?(i=">=",u?(s=+s+1,o=0):o=+o+1,n=0):"<="===i&&(i="<",u?s=+s+1:o=+o+1),"<"===i&&(a="-0"),r=`${i+s}.${o}.${n}${a}`):u?r=`>=${s}.0.0${a} <${+s+1}.0.0-0`:p&&(r=`>=${s}.${o}.0${a} <${s}.${+o+1}.0-0`),l("xRange return",r),r})},O=(e,t)=>(l("replaceStars",e,t),e.trim().replace(u[p.STAR],"")),$=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(u[t.includePrerelease?p.GTE0PRE:p.GTE0],"")),x=e=>(t,r,i,s,o,n,a,l,c,u,p,h)=>(r=b(i)?"":b(s)?`>=${i}.0.0${e?"-0":""}`:b(o)?`>=${i}.${s}.0${e?"-0":""}`:n?`>=${r}`:`>=${r}${e?"-0":""}`,l=b(c)?"":b(u)?`<${+c+1}.0.0-0`:b(p)?`<${c}.${+u+1}.0-0`:h?`<=${c}.${u}.${p}-${h}`:e?`<${c}.${u}.${+p+1}-0`:`<=${l}`,`${r} ${l}`.trim()),C=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==a.ANY&&e[r].semver.prerelease.length>0){let i=e[r].semver;if(i.major===t.major&&i.minor===t.minor&&i.patch===t.patch)return!0}return!1}return!0}},28871:(e,t,r)=>{let i=r(1410),{MAX_LENGTH:s,MAX_SAFE_INTEGER:o}=r(73651),{safeRe:n,safeSrc:a,t:l}=r(81520),c=r(78949),{compareIdentifiers:u}=r(88178);class p{constructor(e,t){if(t=c(t),e instanceof p){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw TypeError(`version is longer than ${s} characters`);i("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?n[l.LOOSE]:n[l.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>o||this.major<0)throw TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<o)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(i("SemVer.compare",this.version,this.options,e),!(e instanceof p)){if("string"==typeof e&&e===this.version)return 0;e=new p(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof p||(e=new p(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof p||(e=new p(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],s=e.prerelease[t];if(i("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return -1;if(r===s)continue;else return u(r,s)}while(++t)}compareBuild(e){e instanceof p||(e=new p(e,this.options));let t=0;do{let r=this.build[t],s=e.build[t];if(i("build compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return -1;if(r===s)continue;else return u(r,s)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(t){let e=RegExp(`^${this.options.loose?a[l.PRERELEASELOOSE]:a[l.PRERELEASE]}$`),r=`-${t}`.match(e);if(!r||r[1]!==t)throw Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=Number(r)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let i=this.prerelease.length;for(;--i>=0;)"number"==typeof this.prerelease[i]&&(this.prerelease[i]++,i=-2);if(-1===i){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let i=[t,e];!1===r&&(i=[t]),0===u(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=i):this.prerelease=i}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=p},62485:(e,t,r)=>{let i=r(48242);e.exports=(e,t)=>{let r=i(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},43978:(e,t,r)=>{let i=r(58884),s=r(28305),o=r(58368),n=r(76483),a=r(89673),l=r(94106);e.exports=(e,t,r,c)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return i(e,r,c);case"!=":return s(e,r,c);case">":return o(e,r,c);case">=":return n(e,r,c);case"<":return a(e,r,c);case"<=":return l(e,r,c);default:throw TypeError(`Invalid operator: ${t}`)}}},39150:(e,t,r)=>{let i=r(28871),s=r(48242),{safeRe:o,t:n}=r(81520);e.exports=(e,t)=>{if(e instanceof i)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let i;let s=t.includePrerelease?o[n.COERCERTLFULL]:o[n.COERCERTL];for(;(i=s.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&i.index+i[0].length===r.index+r[0].length||(r=i),s.lastIndex=i.index+i[1].length+i[2].length;s.lastIndex=-1}else r=e.match(t.includePrerelease?o[n.COERCEFULL]:o[n.COERCE]);if(null===r)return null;let a=r[2],l=r[3]||"0",c=r[4]||"0",u=t.includePrerelease&&r[5]?`-${r[5]}`:"",p=t.includePrerelease&&r[6]?`+${r[6]}`:"";return s(`${a}.${l}.${c}${u}${p}`,t)}},84472:(e,t,r)=>{let i=r(28871);e.exports=(e,t,r)=>{let s=new i(e,r),o=new i(t,r);return s.compare(o)||s.compareBuild(o)}},98136:(e,t,r)=>{let i=r(51586);e.exports=(e,t)=>i(e,t,!0)},51586:(e,t,r)=>{let i=r(28871);e.exports=(e,t,r)=>new i(e,r).compare(new i(t,r))},10906:(e,t,r)=>{let i=r(48242);e.exports=(e,t)=>{let r=i(e,null,!0),s=i(t,null,!0),o=r.compare(s);if(0===o)return null;let n=o>0,a=n?r:s,l=n?s:r,c=!!a.prerelease.length;if(l.prerelease.length&&!c){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(a))return l.minor&&!l.patch?"minor":"patch"}let u=c?"pre":"";return r.major!==s.major?u+"major":r.minor!==s.minor?u+"minor":r.patch!==s.patch?u+"patch":"prerelease"}},58884:(e,t,r)=>{let i=r(51586);e.exports=(e,t,r)=>0===i(e,t,r)},58368:(e,t,r)=>{let i=r(51586);e.exports=(e,t,r)=>i(e,t,r)>0},76483:(e,t,r)=>{let i=r(51586);e.exports=(e,t,r)=>i(e,t,r)>=0},44580:(e,t,r)=>{let i=r(28871);e.exports=(e,t,r,s,o)=>{"string"==typeof r&&(o=s,s=r,r=void 0);try{return new i(e instanceof i?e.version:e,r).inc(t,s,o).version}catch(e){return null}}},89673:(e,t,r)=>{let i=r(51586);e.exports=(e,t,r)=>0>i(e,t,r)},94106:(e,t,r)=>{let i=r(51586);e.exports=(e,t,r)=>0>=i(e,t,r)},31148:(e,t,r)=>{let i=r(28871);e.exports=(e,t)=>new i(e,t).major},95662:(e,t,r)=>{let i=r(28871);e.exports=(e,t)=>new i(e,t).minor},28305:(e,t,r)=>{let i=r(51586);e.exports=(e,t,r)=>0!==i(e,t,r)},48242:(e,t,r)=>{let i=r(28871);e.exports=(e,t,r=!1)=>{if(e instanceof i)return e;try{return new i(e,t)}catch(e){if(!r)return null;throw e}}},34234:(e,t,r)=>{let i=r(28871);e.exports=(e,t)=>new i(e,t).patch},92606:(e,t,r)=>{let i=r(48242);e.exports=(e,t)=>{let r=i(e,t);return r&&r.prerelease.length?r.prerelease:null}},92490:(e,t,r)=>{let i=r(51586);e.exports=(e,t,r)=>i(t,e,r)},11302:(e,t,r)=>{let i=r(84472);e.exports=(e,t)=>e.sort((e,r)=>i(r,e,t))},24520:(e,t,r)=>{let i=r(88889);e.exports=(e,t,r)=>{try{t=new i(t,r)}catch(e){return!1}return t.test(e)}},83975:(e,t,r)=>{let i=r(84472);e.exports=(e,t)=>e.sort((e,r)=>i(e,r,t))},40628:(e,t,r)=>{let i=r(48242);e.exports=(e,t)=>{let r=i(e,t);return r?r.version:null}},90799:(e,t,r)=>{let i=r(81520),s=r(73651),o=r(28871),n=r(88178),a=r(48242),l=r(40628),c=r(62485),u=r(44580),p=r(10906),h=r(31148),f=r(95662),d=r(34234),m=r(92606),E=r(51586),g=r(92490),y=r(98136),v=r(84472),S=r(83975),b=r(11302),I=r(58368),w=r(89673),R=r(58884),A=r(28305),k=r(76483),T=r(94106),O=r(43978),$=r(39150),x=r(12925),C=r(88889),L=r(24520),N=r(19835),P=r(1446),_=r(75030),j=r(30091),D=r(16269),M=r(45400),U=r(45760),H=r(62884),G=r(53225),F=r(86861),B=r(64550);e.exports={parse:a,valid:l,clean:c,inc:u,diff:p,major:h,minor:f,patch:d,prerelease:m,compare:E,rcompare:g,compareLoose:y,compareBuild:v,sort:S,rsort:b,gt:I,lt:w,eq:R,neq:A,gte:k,lte:T,cmp:O,coerce:$,Comparator:x,Range:C,satisfies:L,toComparators:N,maxSatisfying:P,minSatisfying:_,minVersion:j,validRange:D,outside:M,gtr:U,ltr:H,intersects:G,simplifyRange:F,subset:B,SemVer:o,re:i.re,src:i.src,tokens:i.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:n.compareIdentifiers,rcompareIdentifiers:n.rcompareIdentifiers}},73651:e=>{let t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},1410:e=>{let t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},88178:e=>{let t=/^[0-9]+$/,r=(e,r)=>{let i=t.test(e),s=t.test(r);return i&&s&&(e=+e,r=+r),e===r?0:i&&!s?-1:s&&!i?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},50241:e=>{class t{constructor(){this.max=1e3,this.map=new Map}get(e){let t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},78949:e=>{let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},81520:(e,t,r)=>{let{MAX_SAFE_COMPONENT_LENGTH:i,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:o}=r(73651),n=r(1410),a=(t=e.exports={}).re=[],l=t.safeRe=[],c=t.src=[],u=t.safeSrc=[],p=t.t={},h=0,f="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",o],[f,s]],m=e=>{for(let[t,r]of d)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},E=(e,t,r)=>{let i=m(t),s=h++;n(e,s,t),p[e]=s,c[s]=t,u[s]=i,a[s]=new RegExp(t,r?"g":void 0),l[s]=new RegExp(i,r?"g":void 0)};E("NUMERICIDENTIFIER","0|[1-9]\\d*"),E("NUMERICIDENTIFIERLOOSE","\\d+"),E("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`),E("MAINVERSION",`(${c[p.NUMERICIDENTIFIER]})\\.(${c[p.NUMERICIDENTIFIER]})\\.(${c[p.NUMERICIDENTIFIER]})`),E("MAINVERSIONLOOSE",`(${c[p.NUMERICIDENTIFIERLOOSE]})\\.(${c[p.NUMERICIDENTIFIERLOOSE]})\\.(${c[p.NUMERICIDENTIFIERLOOSE]})`),E("PRERELEASEIDENTIFIER",`(?:${c[p.NUMERICIDENTIFIER]}|${c[p.NONNUMERICIDENTIFIER]})`),E("PRERELEASEIDENTIFIERLOOSE",`(?:${c[p.NUMERICIDENTIFIERLOOSE]}|${c[p.NONNUMERICIDENTIFIER]})`),E("PRERELEASE",`(?:-(${c[p.PRERELEASEIDENTIFIER]}(?:\\.${c[p.PRERELEASEIDENTIFIER]})*))`),E("PRERELEASELOOSE",`(?:-?(${c[p.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[p.PRERELEASEIDENTIFIERLOOSE]})*))`),E("BUILDIDENTIFIER",`${f}+`),E("BUILD",`(?:\\+(${c[p.BUILDIDENTIFIER]}(?:\\.${c[p.BUILDIDENTIFIER]})*))`),E("FULLPLAIN",`v?${c[p.MAINVERSION]}${c[p.PRERELEASE]}?${c[p.BUILD]}?`),E("FULL",`^${c[p.FULLPLAIN]}$`),E("LOOSEPLAIN",`[v=\\s]*${c[p.MAINVERSIONLOOSE]}${c[p.PRERELEASELOOSE]}?${c[p.BUILD]}?`),E("LOOSE",`^${c[p.LOOSEPLAIN]}$`),E("GTLT","((?:<|>)?=?)"),E("XRANGEIDENTIFIERLOOSE",`${c[p.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),E("XRANGEIDENTIFIER",`${c[p.NUMERICIDENTIFIER]}|x|X|\\*`),E("XRANGEPLAIN",`[v=\\s]*(${c[p.XRANGEIDENTIFIER]})(?:\\.(${c[p.XRANGEIDENTIFIER]})(?:\\.(${c[p.XRANGEIDENTIFIER]})(?:${c[p.PRERELEASE]})?${c[p.BUILD]}?)?)?`),E("XRANGEPLAINLOOSE",`[v=\\s]*(${c[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[p.XRANGEIDENTIFIERLOOSE]})(?:${c[p.PRERELEASELOOSE]})?${c[p.BUILD]}?)?)?`),E("XRANGE",`^${c[p.GTLT]}\\s*${c[p.XRANGEPLAIN]}$`),E("XRANGELOOSE",`^${c[p.GTLT]}\\s*${c[p.XRANGEPLAINLOOSE]}$`),E("COERCEPLAIN",`(^|[^\\d])(\\d{1,${i}})(?:\\.(\\d{1,${i}}))?(?:\\.(\\d{1,${i}}))?`),E("COERCE",`${c[p.COERCEPLAIN]}(?:$|[^\\d])`),E("COERCEFULL",c[p.COERCEPLAIN]+`(?:${c[p.PRERELEASE]})?`+`(?:${c[p.BUILD]})?`+"(?:$|[^\\d])"),E("COERCERTL",c[p.COERCE],!0),E("COERCERTLFULL",c[p.COERCEFULL],!0),E("LONETILDE","(?:~>?)"),E("TILDETRIM",`(\\s*)${c[p.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",E("TILDE",`^${c[p.LONETILDE]}${c[p.XRANGEPLAIN]}$`),E("TILDELOOSE",`^${c[p.LONETILDE]}${c[p.XRANGEPLAINLOOSE]}$`),E("LONECARET","(?:\\^)"),E("CARETTRIM",`(\\s*)${c[p.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",E("CARET",`^${c[p.LONECARET]}${c[p.XRANGEPLAIN]}$`),E("CARETLOOSE",`^${c[p.LONECARET]}${c[p.XRANGEPLAINLOOSE]}$`),E("COMPARATORLOOSE",`^${c[p.GTLT]}\\s*(${c[p.LOOSEPLAIN]})$|^$`),E("COMPARATOR",`^${c[p.GTLT]}\\s*(${c[p.FULLPLAIN]})$|^$`),E("COMPARATORTRIM",`(\\s*)${c[p.GTLT]}\\s*(${c[p.LOOSEPLAIN]}|${c[p.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",E("HYPHENRANGE",`^\\s*(${c[p.XRANGEPLAIN]})\\s+-\\s+(${c[p.XRANGEPLAIN]})\\s*$`),E("HYPHENRANGELOOSE",`^\\s*(${c[p.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[p.XRANGEPLAINLOOSE]})\\s*$`),E("STAR","(<|>)?=?\\s*\\*"),E("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),E("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},45760:(e,t,r)=>{let i=r(45400);e.exports=(e,t,r)=>i(e,t,">",r)},53225:(e,t,r)=>{let i=r(88889);e.exports=(e,t,r)=>(e=new i(e,r),t=new i(t,r),e.intersects(t,r))},62884:(e,t,r)=>{let i=r(45400);e.exports=(e,t,r)=>i(e,t,"<",r)},1446:(e,t,r)=>{let i=r(28871),s=r(88889);e.exports=(e,t,r)=>{let o=null,n=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!o||-1===n.compare(e))&&(n=new i(o=e,r))}),o}},75030:(e,t,r)=>{let i=r(28871),s=r(88889);e.exports=(e,t,r)=>{let o=null,n=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!o||1===n.compare(e))&&(n=new i(o=e,r))}),o}},30091:(e,t,r)=>{let i=r(28871),s=r(88889),o=r(58368);e.exports=(e,t)=>{e=new s(e,t);let r=new i("0.0.0");if(e.test(r)||(r=new i("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let s=e.set[t],n=null;s.forEach(e=>{let t=new i(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!n||o(t,n))&&(n=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),n&&(!r||o(r,n))&&(r=n)}return r&&e.test(r)?r:null}},45400:(e,t,r)=>{let i=r(28871),s=r(12925),{ANY:o}=s,n=r(88889),a=r(24520),l=r(58368),c=r(89673),u=r(94106),p=r(76483);e.exports=(e,t,r,h)=>{let f,d,m,E,g;switch(e=new i(e,h),t=new n(t,h),r){case">":f=l,d=u,m=c,E=">",g=">=";break;case"<":f=c,d=p,m=l,E="<",g="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,h))return!1;for(let r=0;r<t.set.length;++r){let i=t.set[r],n=null,a=null;if(i.forEach(e=>{e.semver===o&&(e=new s(">=0.0.0")),n=n||e,a=a||e,f(e.semver,n.semver,h)?n=e:m(e.semver,a.semver,h)&&(a=e)}),n.operator===E||n.operator===g||(!a.operator||a.operator===E)&&d(e,a.semver)||a.operator===g&&m(e,a.semver))return!1}return!0}},86861:(e,t,r)=>{let i=r(24520),s=r(51586);e.exports=(e,t,r)=>{let o=[],n=null,a=null,l=e.sort((e,t)=>s(e,t,r));for(let e of l)i(e,t,r)?(a=e,n||(n=e)):(a&&o.push([n,a]),a=null,n=null);n&&o.push([n,null]);let c=[];for(let[e,t]of o)e===t?c.push(e):t||e!==l[0]?t?e===l[0]?c.push(`<=${t}`):c.push(`${e} - ${t}`):c.push(`>=${e}`):c.push("*");let u=c.join(" || "),p="string"==typeof t.raw?t.raw:String(t);return u.length<p.length?u:t}},64550:(e,t,r)=>{let i=r(88889),s=r(12925),{ANY:o}=s,n=r(24520),a=r(51586),l=[new s(">=0.0.0-0")],c=[new s(">=0.0.0")],u=(e,t,r)=>{let i,s,u,f,d,m,E;if(e===t)return!0;if(1===e.length&&e[0].semver===o){if(1===t.length&&t[0].semver===o)return!0;e=r.includePrerelease?l:c}if(1===t.length&&t[0].semver===o){if(r.includePrerelease)return!0;t=c}let g=new Set;for(let t of e)">"===t.operator||">="===t.operator?i=p(i,t,r):"<"===t.operator||"<="===t.operator?s=h(s,t,r):g.add(t.semver);if(g.size>1||i&&s&&((u=a(i.semver,s.semver,r))>0||0===u&&(">="!==i.operator||"<="!==s.operator)))return null;for(let e of g){if(i&&!n(e,String(i),r)||s&&!n(e,String(s),r))return null;for(let i of t)if(!n(e,String(i),r))return!1;return!0}let y=!!s&&!r.includePrerelease&&!!s.semver.prerelease.length&&s.semver,v=!!i&&!r.includePrerelease&&!!i.semver.prerelease.length&&i.semver;for(let e of(y&&1===y.prerelease.length&&"<"===s.operator&&0===y.prerelease[0]&&(y=!1),t)){if(E=E||">"===e.operator||">="===e.operator,m=m||"<"===e.operator||"<="===e.operator,i){if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if((f=p(i,e,r))===e&&f!==i)return!1}else if(">="===i.operator&&!n(i.semver,String(e),r))return!1}if(s){if(y&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===y.major&&e.semver.minor===y.minor&&e.semver.patch===y.patch&&(y=!1),"<"===e.operator||"<="===e.operator){if((d=h(s,e,r))===e&&d!==s)return!1}else if("<="===s.operator&&!n(s.semver,String(e),r))return!1}if(!e.operator&&(s||i)&&0!==u)return!1}return(!i||!m||!!s||0===u)&&(!s||!E||!!i||0===u)&&!v&&!y},p=(e,t,r)=>{if(!e)return t;let i=a(e.semver,t.semver,r);return i>0?e:i<0?t:">"===t.operator&&">="===e.operator?t:e},h=(e,t,r)=>{if(!e)return t;let i=a(e.semver,t.semver,r);return i<0?e:i>0?t:"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new i(e,r),t=new i(t,r);let s=!1;e:for(let i of e.set){for(let e of t.set){let t=u(i,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},19835:(e,t,r)=>{let i=r(88889);e.exports=(e,t)=>new i(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},16269:(e,t,r)=>{let i=r(88889);e.exports=(e,t)=>{try{return new i(e,t).range||"*"}catch(e){return null}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,5972,9557,2647,1518,117,5564],()=>r(36064));module.exports=i})();