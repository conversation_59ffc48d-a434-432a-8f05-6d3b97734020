"use strict";(()=>{var e={};e.id=6082,e.ids=[6082],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},21765:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>b,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>m,staticGenerationAsyncStorage:()=>h});var o={};t.r(o),t.d(o,{GET:()=>d,POST:()=>l,dynamic:()=>p});var s=t(49303),n=t(88716),a=t(60670),i=t(87070),u=t(75571),c=t(81628);let p="force-dynamic";async function d(e){try{let{searchParams:r}=new URL(e.url),t=r.get("type")||"full",o=null;try{let e=await (0,u.getServerSession)(c.L);o={exists:!!e,user:e?.user?{id:e.user?.id||"N/A",email:e.user.email||"N/A",name:e.user.name||"N/A"}:null,expires:e?.expires||"N/A"}}catch(e){o={error:e instanceof Error?e.message:"Erro desconhecido",exists:!1}}let s=Object.fromEntries(e.headers.entries()),n=e.headers.get("cookie")||"",a={origin:e.nextUrl.origin,pathname:e.nextUrl.pathname,searchParams:Object.fromEntries(e.nextUrl.searchParams.entries()),host:e.nextUrl.host,protocol:e.nextUrl.protocol},p={googleClientId:process.env.AUTH_GOOGLE_CLIENT_ID?"Configurado":"Ausente",googleClientSecret:process.env.AUTH_GOOGLE_CLIENT_SECRET?"Configurado":"Ausente",githubClientId:process.env.AUTH_GITHUB_CLIENT_ID?"Configurado":"Ausente",githubClientSecret:process.env.AUTH_GITHUB_CLIENT_SECRET?"Configurado":"Ausente",nextAuthUrl:process.env.AUTH_NEXTAUTH_URL||"Ausente",nextAuthSecret:process.env.AUTH_NEXTAUTH_SECRET?"Configurado":"Ausente",nodeEnv:"production"},d={google:`${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/google`,github:`${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/github`},l={error:r.get("error"),errorDescription:r.get("error_description"),state:r.get("state"),code:r.get("code")},g=s["user-agent"]||"Desconhecido",f=/bot|crawler|spider/i.test(g),h=n.includes("next-auth.csrf-token"),m=n.includes("next-auth.session-token"),x={timestamp:new Date().toISOString(),debugType:t,session:o,oauth:p,callbacks:d,url:a,errors:l,security:{csrfToken:h?"Presente":"Ausente",sessionToken:m?"Presente":"Ausente",isBot:f,userAgent:g.substring(0,100)},headers:"full"===t?s:{"user-agent":s["user-agent"],referer:s.referer,origin:s.origin,host:s.host},recommendations:[]};if(o?.exists||x.recommendations.push("Usu\xe1rio n\xe3o est\xe1 autenticado"),l.error&&(x.recommendations.push(`Erro OAuth detectado: ${l.error}`),l.errorDescription&&x.recommendations.push(`Descri\xe7\xe3o: ${l.errorDescription}`)),h||x.recommendations.push("Token CSRF ausente - poss\xedvel problema de cookies"),f&&x.recommendations.push("Requisi\xe7\xe3o detectada como bot - OAuth pode n\xe3o funcionar"),a.pathname.includes("/api/auth/callback/")){let e=a.pathname.split("/").pop();x.recommendations.push(`Callback detectado para provedor: ${e}`),l.code&&x.recommendations.push("C\xf3digo de autoriza\xe7\xe3o recebido - fluxo OAuth em andamento")}return i.NextResponse.json(x,{headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(e){return console.error("Erro no debug do fluxo OAuth:",e),i.NextResponse.json({error:"Erro ao executar debug do fluxo OAuth",message:e instanceof Error?e.message:"Erro desconhecido",timestamp:new Date().toISOString()},{status:500})}}async function l(e){try{return await e.json(),i.NextResponse.json({status:"success",message:"Dados de debug recebidos",timestamp:new Date().toISOString()})}catch(e){return console.error("Erro ao processar dados de debug:",e),i.NextResponse.json({error:"Erro ao processar dados de debug",message:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/debug-flow/route",pathname:"/api/auth/debug-flow",filename:"route",bundlePath:"app/api/auth/debug-flow/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-flow\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:m}=g,x="/api/auth/debug-flow/route";function b(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:h})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=s?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(o,n,i):o[n]=e[n]}return o.default=e,t&&t.set(e,o),o}(t(45609));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>t(21765));module.exports=o})();