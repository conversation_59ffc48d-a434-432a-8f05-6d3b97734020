"use strict";(()=>{var e={};e.id=4032,e.ids=[4032],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},62233:(e,s,o)=>{o.r(s),o.d(s,{originalPathname:()=>c,patchFetch:()=>O,requestAsyncStorage:()=>p,routeModule:()=>a,serverHooks:()=>d,staticGenerationAsyncStorage:()=>u});var r={};o.r(r),o.d(r,{GET:()=>I,dynamic:()=>_});var n=o(49303),E=o(88716),t=o(60670),i=o(87070),T=o(52972);let _="force-dynamic";async function I(e){try{let e=T.Vi.FEATURES.SKIP_AUTH_PROVIDERS,s=T.Vi.IS_DEVELOPMENT,o=process.env.AUTH_SKIP_PROVIDERS,r="production",n=e||s&&"false"!==process.env.AUTH_SKIP_PROVIDERS,E={timestamp:new Date().toISOString(),environment:r,conditions:{"ENV.FEATURES.SKIP_AUTH_PROVIDERS":e,"ENV.IS_DEVELOPMENT":s,"process.env.AUTH_SKIP_PROVIDERS":o,"process.env.NODE_ENV":r,shouldUseDevProviders:n},envFeatures:{SKIP_AUTH_PROVIDERS:T.Vi.FEATURES.SKIP_AUTH_PROVIDERS,USE_MOCK_AI:T.Vi.FEATURES.USE_MOCK_AI,ENABLE_REALTIME_COLLABORATION:T.Vi.FEATURES.ENABLE_REALTIME_COLLABORATION,ENABLE_DESKTOP_INTEGRATION:T.Vi.FEATURES.ENABLE_DESKTOP_INTEGRATION,ENABLE_STRIPE_INTEGRATION:T.Vi.FEATURES.ENABLE_STRIPE_INTEGRATION},envFlags:{IS_DEVELOPMENT:T.Vi.IS_DEVELOPMENT,IS_PRODUCTION:T.Vi.IS_PRODUCTION,IS_TEST:T.Vi.IS_TEST,IS_SERVER:T.Vi.IS_SERVER},oauthCredentials:{googleClientId:{exists:!!process.env.AUTH_GOOGLE_CLIENT_ID,length:process.env.AUTH_GOOGLE_CLIENT_ID?.length||0,preview:process.env.AUTH_GOOGLE_CLIENT_ID?`${process.env.AUTH_GOOGLE_CLIENT_ID.substring(0,10)}...`:"undefined"},googleClientSecret:{exists:!!process.env.AUTH_GOOGLE_CLIENT_SECRET,length:process.env.AUTH_GOOGLE_CLIENT_SECRET?.length||0,preview:process.env.AUTH_GOOGLE_CLIENT_SECRET?`${process.env.AUTH_GOOGLE_CLIENT_SECRET.substring(0,10)}...`:"undefined"},githubClientId:{exists:!!process.env.AUTH_GITHUB_CLIENT_ID,length:process.env.AUTH_GITHUB_CLIENT_ID?.length||0,preview:process.env.AUTH_GITHUB_CLIENT_ID?`${process.env.AUTH_GITHUB_CLIENT_ID.substring(0,10)}...`:"undefined"},githubClientSecret:{exists:!!process.env.AUTH_GITHUB_CLIENT_SECRET,length:process.env.AUTH_GITHUB_CLIENT_SECRET?.length||0,preview:process.env.AUTH_GITHUB_CLIENT_SECRET?`${process.env.AUTH_GITHUB_CLIENT_SECRET.substring(0,10)}...`:"undefined"}},recommendation:n?"Usando provedores de desenvolvimento - OAuth social desabilitado":"Deveria usar provedores OAuth sociais (Google/GitHub)",issues:[]};return n&&T.Vi.IS_PRODUCTION&&E.issues.push("Provedores de desenvolvimento ativados em produ\xe7\xe3o"),n||(process.env.AUTH_GOOGLE_CLIENT_ID||E.issues.push("GOOGLE_CLIENT_ID n\xe3o configurado"),process.env.AUTH_GOOGLE_CLIENT_SECRET||E.issues.push("GOOGLE_CLIENT_SECRET n\xe3o configurado"),process.env.AUTH_GITHUB_CLIENT_ID||E.issues.push("GITHUB_CLIENT_ID n\xe3o configurado"),process.env.AUTH_GITHUB_CLIENT_SECRET||E.issues.push("GITHUB_CLIENT_SECRET n\xe3o configurado")),i.NextResponse.json(E,{status:200,headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate"}})}catch(e){return i.NextResponse.json({error:"Erro interno durante debug de provedores",message:e instanceof Error?e.message:"Erro desconhecido",stack:e instanceof Error?e.stack:void 0,timestamp:new Date().toISOString()},{status:500,headers:{"Content-Type":"application/json"}})}}let a=new n.AppRouteRouteModule({definition:{kind:E.x.APP_ROUTE,page:"/api/auth/debug-providers/route",pathname:"/api/auth/debug-providers",filename:"route",bundlePath:"app/api/auth/debug-providers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-providers\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:u,serverHooks:d}=a,c="/api/auth/debug-providers/route";function O(){return(0,t.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:u})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var o=e=>s(s.s=e),r=s.X(0,[8948,5972,7410,2972],()=>o(62233));module.exports=r})();