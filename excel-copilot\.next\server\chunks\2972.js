"use strict";exports.id=2972,exports.ids=[2972],exports.modules={52972:(E,_,e)=>{e.d(_,{Vi:()=>C});var t=e(7410);let i=t.z.string().url("Deve ser uma URL v\xe1lida"),o=t.z.string().min(8,"Deve ter pelo menos 8 caracteres"),n=t.z.string().min(1,"N\xe3o pode estar vazio"),T=t.z.object({NODE_ENV:t.z.enum(["development","production","test"]),APP_NAME:t.z.string().default("Excel Copilot"),APP_VERSION:t.z.string().default("1.0.0"),APP_URL:i,AUTH_NEXTAUTH_SECRET:o,AUTH_NEXTAUTH_URL:i,AUTH_GOOGLE_CLIENT_ID:n.optional(),AUTH_GOOGLE_CLIENT_SECRET:o.optional(),AUTH_GITHUB_CLIENT_ID:n.optional(),AUTH_GITHUB_CLIENT_SECRET:o.optional(),AUTH_SKIP_PROVIDERS:t.z.boolean().default(!1),DB_DATABASE_URL:t.z.string().min(1,"URL do banco \xe9 obrigat\xf3ria"),DB_DIRECT_URL:t.z.string().optional(),DB_PROVIDER:t.z.enum(["postgresql","sqlite"]).default("postgresql"),AI_ENABLED:t.z.boolean().default(!0),AI_USE_MOCK:t.z.boolean().default(!1),AI_VERTEX_PROJECT_ID:t.z.string().optional(),AI_VERTEX_LOCATION:t.z.string().default("us-central1"),AI_VERTEX_MODEL:t.z.string().default("gemini-2.0-flash-001"),STRIPE_ENABLED:t.z.boolean().default(!0),STRIPE_SECRET_KEY:t.z.string().optional(),STRIPE_WEBHOOK_SECRET:t.z.string().optional(),STRIPE_PUBLISHABLE_KEY:t.z.string().optional(),SUPABASE_URL:i.optional(),SUPABASE_ANON_KEY:t.z.string().optional(),SUPABASE_SERVICE_ROLE_KEY:t.z.string().optional(),MCP_VERCEL_TOKEN:t.z.string().optional(),MCP_VERCEL_PROJECT_ID:t.z.string().optional(),MCP_VERCEL_TEAM_ID:t.z.string().optional(),MCP_LINEAR_API_KEY:t.z.string().optional(),MCP_GITHUB_TOKEN:t.z.string().optional(),DEV_DISABLE_VALIDATION:t.z.boolean().default(!1),DEV_FORCE_PRODUCTION:t.z.boolean().default(!1),DEV_LOG_LEVEL:t.z.enum(["debug","info","warn","error"]).default("info"),SECURITY_CSRF_SECRET:t.z.string().optional(),SECURITY_RATE_LIMIT_ENABLED:t.z.boolean().default(!0),SECURITY_CORS_ORIGINS:t.z.string().optional()});function A(E,_){return process.env[E]||_}function I(E,_=!1){return E?["true","1","yes","on"].includes(E.toLowerCase()):_}class s{constructor(){this.initialized=!1,this.config=this.loadConfiguration(),this.validationResult=this.validateConfiguration()}static getInstance(){return s.instance||(s.instance=new s),s.instance}loadConfiguration(){return{NODE_ENV:A("NODE_ENV","development"),APP_NAME:A("APP_NAME","Excel Copilot"),APP_VERSION:A("APP_VERSION","1.0.0"),APP_URL:A("NEXT_PUBLIC_APP_URL")||A("APP_URL","http://localhost:3000"),AUTH_NEXTAUTH_SECRET:A("AUTH_NEXTAUTH_SECRET"),AUTH_NEXTAUTH_URL:A("AUTH_NEXTAUTH_URL"),AUTH_GOOGLE_CLIENT_ID:A("AUTH_GOOGLE_CLIENT_ID"),AUTH_GOOGLE_CLIENT_SECRET:A("AUTH_GOOGLE_CLIENT_SECRET"),AUTH_GITHUB_CLIENT_ID:A("AUTH_GITHUB_CLIENT_ID"),AUTH_GITHUB_CLIENT_SECRET:A("AUTH_GITHUB_CLIENT_SECRET"),AUTH_SKIP_PROVIDERS:I(A("AUTH_SKIP_PROVIDERS")),DB_DATABASE_URL:A("DB_DATABASE_URL"),DB_DIRECT_URL:A("DB_DIRECT_URL"),DB_PROVIDER:A("DB_PROVIDER","postgresql"),AI_ENABLED:this.resolveAIConfiguration(),AI_USE_MOCK:this.resolveAIMockConfiguration(),AI_VERTEX_PROJECT_ID:A("AI_VERTEX_PROJECT_ID"),AI_VERTEX_LOCATION:A("AI_VERTEX_LOCATION","us-central1"),AI_VERTEX_MODEL:A("AI_VERTEX_MODEL","gemini-2.0-flash-001"),STRIPE_ENABLED:!I(A("DISABLE_STRIPE")),STRIPE_SECRET_KEY:A("STRIPE_SECRET_KEY"),STRIPE_WEBHOOK_SECRET:A("STRIPE_WEBHOOK_SECRET"),STRIPE_PUBLISHABLE_KEY:A("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"),SUPABASE_URL:A("SUPABASE_URL")||A("NEXT_PUBLIC_SUPABASE_URL"),SUPABASE_ANON_KEY:A("SUPABASE_ANON_KEY")||A("NEXT_PUBLIC_SUPABASE_ANON_KEY"),SUPABASE_SERVICE_ROLE_KEY:A("SUPABASE_SERVICE_ROLE_KEY"),MCP_VERCEL_TOKEN:A("MCP_VERCEL_TOKEN"),MCP_VERCEL_PROJECT_ID:A("MCP_VERCEL_PROJECT_ID"),MCP_VERCEL_TEAM_ID:A("MCP_VERCEL_TEAM_ID"),MCP_LINEAR_API_KEY:A("MCP_LINEAR_API_KEY"),MCP_GITHUB_TOKEN:A("MCP_GITHUB_TOKEN"),DEV_DISABLE_VALIDATION:!1,DEV_FORCE_PRODUCTION:I(A("DEV_FORCE_PRODUCTION")),DEV_LOG_LEVEL:A("DEV_LOG_LEVEL","info"),SECURITY_CSRF_SECRET:A("SECURITY_CSRF_SECRET"),SECURITY_RATE_LIMIT_ENABLED:!I(A("SECURITY_RATE_LIMIT_ENABLED")),SECURITY_CORS_ORIGINS:A("SECURITY_CORS_ORIGINS")}}resolveAIConfiguration(){if(I(A("NEXT_PUBLIC_DISABLE_VERTEX_AI")))return!1;let E=A("AI_ENABLED");return void 0!==E?I(E,!0):!!A("AI_VERTEX_PROJECT_ID")||"production"===A("NODE_ENV","development")}resolveAIMockConfiguration(){return!!(I(A("FORCE_GOOGLE_MOCKS"))||I(A("AI_USE_MOCK")))||!this.resolveAIConfiguration()||!(A("AI_VERTEX_PROJECT_ID")||A("VERTEX_AI_CREDENTIALS"))||"development"===A("NODE_ENV","development")}validateConfiguration(){let E={valid:!0,errors:[],warnings:[],missing:[],conflicts:[]};try{T.parse(this.config),this.validateByEnvironment(E),this.validateDependencies(E),this.validateConflicts(E),this.validateSecurity(E)}catch(_){_ instanceof t.z.ZodError?E.errors.push(..._.errors.map(E=>`${E.path.join(".")}: ${E.message}`)):E.errors.push(`Erro de valida\xe7\xe3o: ${_}`)}return E.valid=0===E.errors.length,E}validateByEnvironment(E){let _=this.config.NODE_ENV;if("production"===_){for(let _ of["AUTH_NEXTAUTH_SECRET","AUTH_NEXTAUTH_URL","DB_DATABASE_URL"])this.config[_]||E.errors.push(`${_} \xe9 obrigat\xf3ria em produ\xe7\xe3o`);this.config.AUTH_GOOGLE_CLIENT_ID||this.config.AUTH_GITHUB_CLIENT_ID||E.errors.push("Pelo menos um provider OAuth deve estar configurado em produ\xe7\xe3o"),this.config.DEV_DISABLE_VALIDATION&&E.errors.push("CR\xcdTICO: DISABLE_ENV_VALIDATION est\xe1 ativo em produ\xe7\xe3o - RISCO DE SEGURAN\xc7A"),(A("DISABLE_ENV_VALIDATION")||A("DEV_DISABLE_VALIDATION"))&&E.errors.push("CR\xcdTICO: Tentativa de bypass de valida\xe7\xe3o detectada em produ\xe7\xe3o")}"development"!==_||this.config.AUTH_GOOGLE_CLIENT_ID||this.config.AUTH_GITHUB_CLIENT_ID||E.warnings.push("Nenhum provider OAuth configurado - usando modo de desenvolvimento")}validateDependencies(E){this.config.STRIPE_ENABLED&&(this.config.STRIPE_SECRET_KEY||E.errors.push("STRIPE_SECRET_KEY \xe9 obrigat\xf3ria quando Stripe est\xe1 habilitado"),this.config.STRIPE_PUBLISHABLE_KEY||E.errors.push("STRIPE_PUBLISHABLE_KEY \xe9 obrigat\xf3ria quando Stripe est\xe1 habilitado")),!this.config.AI_ENABLED||this.config.AI_USE_MOCK||this.config.AI_VERTEX_PROJECT_ID||E.warnings.push("AI habilitada sem VERTEX_AI_PROJECT_ID - usando modo mock"),this.config.DB_DATABASE_URL?.includes("supabase")&&(this.config.SUPABASE_URL||E.warnings.push("Usando Supabase mas SUPABASE_URL n\xe3o configurada"),this.config.SUPABASE_ANON_KEY||E.warnings.push("Usando Supabase mas SUPABASE_ANON_KEY n\xe3o configurada"))}validateConflicts(E){this.config.AI_ENABLED&&I(A("FORCE_GOOGLE_MOCKS"))&&E.conflicts.push("AI_ENABLED=true mas FORCE_GOOGLE_MOCKS=true - usando mocks"),[A("FORCE_GOOGLE_MOCKS"),A("AI_USE_MOCK"),A("NEXT_PUBLIC_DISABLE_VERTEX_AI")].filter(Boolean).length>1&&E.conflicts.push("M\xfaltiplas flags de configura\xe7\xe3o de IA detectadas - usando hierarquia de preced\xeancia"),"production"===this.config.NODE_ENV&&this.config.AUTH_SKIP_PROVIDERS&&E.conflicts.push("SKIP_AUTH_PROVIDERS=true em produ\xe7\xe3o - pode causar problemas de autentica\xe7\xe3o")}validateSecurity(E){this.config.AUTH_NEXTAUTH_SECRET&&this.config.AUTH_NEXTAUTH_SECRET.length<32&&E.warnings.push("NEXTAUTH_SECRET deveria ter pelo menos 32 caracteres para m\xe1xima seguran\xe7a"),"production"===this.config.NODE_ENV&&(this.config.AUTH_NEXTAUTH_URL?.includes("localhost")&&E.errors.push("NEXTAUTH_URL n\xe3o pode ser localhost em produ\xe7\xe3o"),this.config.APP_URL?.includes("localhost")&&E.errors.push("APP_URL n\xe3o pode ser localhost em produ\xe7\xe3o")),"production"!==this.config.NODE_ENV||this.config.SECURITY_CORS_ORIGINS||E.warnings.push("CORS_ORIGINS n\xe3o configurado em produ\xe7\xe3o - pode causar problemas de seguran\xe7a")}getConfig(){return{...this.config}}getValidationResult(){return{...this.validationResult}}isValid(){return this.validationResult.valid}getAuthConfig(){return{enabled:!this.config.AUTH_SKIP_PROVIDERS,status:this.config.AUTH_SKIP_PROVIDERS?"disabled":"enabled",credentials:{nextAuthSecret:this.config.AUTH_NEXTAUTH_SECRET||"",nextAuthUrl:this.config.AUTH_NEXTAUTH_URL||"",googleClientId:this.config.AUTH_GOOGLE_CLIENT_ID||"",googleClientSecret:this.config.AUTH_GOOGLE_CLIENT_SECRET||"",githubClientId:this.config.AUTH_GITHUB_CLIENT_ID||"",githubClientSecret:this.config.AUTH_GITHUB_CLIENT_SECRET||""}}}getAIConfig(){let E=this.config.AI_ENABLED,_=this.config.AI_USE_MOCK,e="enabled";return E?_&&(e="mock"):e="disabled",{enabled:E,status:e,credentials:{projectId:this.config.AI_VERTEX_PROJECT_ID||"",location:this.config.AI_VERTEX_LOCATION,model:this.config.AI_VERTEX_MODEL}}}getDatabaseConfig(){return{enabled:!!this.config.DB_DATABASE_URL,status:this.config.DB_DATABASE_URL?"enabled":"disabled",credentials:{databaseUrl:this.config.DB_DATABASE_URL||"",directUrl:this.config.DB_DIRECT_URL||"",provider:this.config.DB_PROVIDER}}}getStripeConfig(){return{enabled:this.config.STRIPE_ENABLED,status:this.config.STRIPE_ENABLED?"enabled":"disabled",credentials:{secretKey:this.config.STRIPE_SECRET_KEY||"",webhookSecret:this.config.STRIPE_WEBHOOK_SECRET||"",publishableKey:this.config.STRIPE_PUBLISHABLE_KEY||""}}}getMCPConfig(){return{vercel:{enabled:!!this.config.MCP_VERCEL_TOKEN,status:this.config.MCP_VERCEL_TOKEN?"enabled":"disabled",credentials:{token:this.config.MCP_VERCEL_TOKEN||"",projectId:this.config.MCP_VERCEL_PROJECT_ID||"",teamId:this.config.MCP_VERCEL_TEAM_ID||""}},linear:{enabled:!!this.config.MCP_LINEAR_API_KEY,status:this.config.MCP_LINEAR_API_KEY?"enabled":"disabled",credentials:{apiKey:this.config.MCP_LINEAR_API_KEY||""}},github:{enabled:!!this.config.MCP_GITHUB_TOKEN,status:this.config.MCP_GITHUB_TOKEN?"enabled":"disabled",credentials:{token:this.config.MCP_GITHUB_TOKEN||""}}}}revalidate(){return this.config=this.loadConfiguration(),this.validationResult=this.validateConfiguration(),this.getValidationResult()}generateReport(){let E=this.validationResult,_=this.config,e="\uD83D\uDD27 RELAT\xd3RIO DE CONFIGURA\xc7\xc3O - EXCEL COPILOT\n";e+="=".repeat(60)+"\n\n"+`✅ Status Geral: ${E.valid?"V\xc1LIDA":"INV\xc1LIDA"}
`+`🌍 Ambiente: ${_.NODE_ENV}
`+`📱 Aplica\xe7\xe3o: ${_.APP_NAME} v${_.APP_VERSION}
`+`🔗 URL: ${_.APP_URL}

`+"\uD83D\uDCCB STATUS DOS SERVI\xc7OS:\n"+"-".repeat(30)+"\n";let t=this.getAuthConfig();e+=`🔐 Autentica\xe7\xe3o: ${t.status.toUpperCase()}
`;let i=this.getAIConfig();e+=`🤖 Intelig\xeancia Artificial: ${i.status.toUpperCase()}
`;let o=this.getDatabaseConfig();e+=`🗄️  Banco de Dados: ${o.status.toUpperCase()}
`;let n=this.getStripeConfig();e+=`💳 Stripe: ${n.status.toUpperCase()}
`;let T=this.getMCPConfig();return e+=`🔌 Vercel MCP: ${T.vercel?.status?.toUpperCase()||"DISABLED"}
🔌 Linear MCP: ${T.linear?.status?.toUpperCase()||"DISABLED"}
🔌 GitHub MCP: ${T.github?.status?.toUpperCase()||"DISABLED"}

`,E.errors.length>0&&(e+="❌ ERROS:\n",E.errors.forEach(E=>e+=`  • ${E}
`),e+="\n"),E.warnings.length>0&&(e+="⚠️  AVISOS:\n",E.warnings.forEach(E=>e+=`  • ${E}
`),e+="\n"),E.conflicts.length>0&&(e+="\uD83D\uDD04 CONFLITOS RESOLVIDOS:\n",E.conflicts.forEach(E=>e+=`  • ${E}
`),e+="\n"),e}}let a=s.getInstance(),C={NODE_ENV:a.getConfig().NODE_ENV,IS_DEVELOPMENT:"development"===a.getConfig().NODE_ENV,IS_PRODUCTION:"production"===a.getConfig().NODE_ENV,IS_TEST:"test"===a.getConfig().NODE_ENV,IS_SERVER:!0,APP:{NAME:a.getConfig().APP_NAME,VERSION:a.getConfig().APP_VERSION,URL:a.getConfig().APP_URL},NEXTAUTH_SECRET:a.getConfig().AUTH_NEXTAUTH_SECRET,NEXTAUTH_URL:a.getConfig().AUTH_NEXTAUTH_URL,API_KEYS:{GOOGLE_CLIENT_ID:a.getConfig().AUTH_GOOGLE_CLIENT_ID||"",GOOGLE_CLIENT_SECRET:a.getConfig().AUTH_GOOGLE_CLIENT_SECRET||"",GITHUB_CLIENT_ID:a.getConfig().AUTH_GITHUB_CLIENT_ID||"",GITHUB_CLIENT_SECRET:a.getConfig().AUTH_GITHUB_CLIENT_SECRET||""},DATABASE_URL:a.getConfig().DB_DATABASE_URL,VERTEX_AI:{ENABLED:a.getAIConfig().enabled,PROJECT_ID:a.getConfig().AI_VERTEX_PROJECT_ID||"",LOCATION:a.getConfig().AI_VERTEX_LOCATION,MODEL_NAME:a.getConfig().AI_VERTEX_MODEL,CREDENTIALS_PATH:process.env.VERTEX_AI_CREDENTIALS_PATH||process.env.GOOGLE_APPLICATION_CREDENTIALS},TIMEOUTS:{API_CALL:3e4,HEALTH_CHECK:{DATABASE:5e3,AI_SERVICE:1e4,EXTERNAL_DEPS:15e3}},CACHE:{DEFAULT_TTL:300,EXCEL_CACHE_SIZE:50,EXCEL_CACHE_TTL:1800,AI_CACHE_SIZE:200,AI_CACHE_TTL:86400},LIMITS:{API_RATE_LIMIT:"production"===a.getConfig().NODE_ENV?60:120},FEATURES:{USE_MOCK_AI:"mock"===a.getAIConfig().status,SKIP_AUTH_PROVIDERS:a.getConfig().AUTH_SKIP_PROVIDERS,TELEMETRY_SAMPLE_RATE:.1,ENABLE_REALTIME_COLLABORATION:!0,ENABLE_DESKTOP_INTEGRATION:!0,ENABLE_STRIPE_INTEGRATION:a.getStripeConfig().enabled},VERCEL_API_TOKEN:a.getConfig().MCP_VERCEL_TOKEN,VERCEL_PROJECT_ID:a.getConfig().MCP_VERCEL_PROJECT_ID,VERCEL_TEAM_ID:a.getConfig().MCP_VERCEL_TEAM_ID,LINEAR_API_KEY:a.getConfig().MCP_LINEAR_API_KEY,GITHUB_TOKEN:a.getConfig().MCP_GITHUB_TOKEN,GITHUB_OWNER:process.env.GITHUB_OWNER||process.env.MCP_GITHUB_OWNER,GITHUB_REPO:process.env.GITHUB_REPO||process.env.MCP_GITHUB_REPO||"excel-copilot",SUPABASE_SERVICE_ROLE_KEY:a.getConfig().SUPABASE_SERVICE_ROLE_KEY,SUPABASE_URL:a.getConfig().SUPABASE_URL,validate:()=>a.getValidationResult()}}};