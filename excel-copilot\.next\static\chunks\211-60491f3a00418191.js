"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[211],{14307:function(e,t,r){r.r(t),r.d(t,{Headers:function(){return i},Request:function(){return o},Response:function(){return a},fetch:function(){return n}});var s=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let n=s.fetch;t.default=s.fetch.bind(s);let i=s.Headers,o=s.Request,a=s.Response},56781:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=s(r(14307)),i=s(r(47960));class o{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=n.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,s;let n=null,o=null,a=null,l=e.status,u=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(o="text/csv"===this.headers.Accept?t:this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let s=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),i=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&i&&i.length>1&&(a=parseInt(i[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(n={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,a=null,l=406,u="Not Acceptable"):o=1===o.length?o[0]:null)}else{let t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(o=[],n=null,l=200,u="OK")}catch(r){404===e.status&&""===t?(l=204,u="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(s=null==n?void 0:n.details)||void 0===s?void 0:s.includes("0 rows"))&&(n=null,l=200,u="OK"),n&&this.shouldThrowOnError)throw new i.default(n)}return{error:n,data:o,count:a,status:l,statusText:u}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,s;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(s=null==e?void 0:e.code)&&void 0!==s?s:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=o},18895:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=s(r(69660)),i=s(r(83287)),o=r(31395);class a{constructor(e,{headers:t={},schema:r,fetch:s}={}){this.url=e,this.headers=Object.assign(Object.assign({},o.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=s}from(e){let t=new URL(`${this.url}/${e}`);return new n.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new a(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:s=!1,count:n}={}){let o,a;let l=new URL(`${this.url}/rpc/${e}`);r||s?(o=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(o="POST",a=t);let u=Object.assign({},this.headers);return n&&(u.Prefer=`count=${n}`),new i.default({method:o,url:l,headers:u,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}}t.default=a},47960:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},83287:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=s(r(43432));class i extends n.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let n="";"plain"===s?n="pl":"phrase"===s?n="ph":"websearch"===s&&(n="w");let i=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${i}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=i},69660:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=s(r(83287));class i{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:t=!1,count:r}={}){let s=!1,i=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!s?"":('"'===e&&(s=!s),e)).join("");return this.url.searchParams.set("select",i),r&&(this.headers.Prefer=`count=${r}`),new n.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:s,defaultToNull:i=!0}={}){let o=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),s&&o.push(`count=${s}`),i||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new n.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new n.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=i},43432:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=s(r(56781));class i extends n.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:n=s}={}){let i=n?`${n}.order`:"order",o=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){let n=void 0===s?"offset":`${s}.offset`,i=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(i,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:n=!1,format:i="text"}={}){var o;let a=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,n?"wal":null].filter(Boolean).join("|"),l=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${l}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=i},31395:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let s=r(14373);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${s.version}`}},68292:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let n=s(r(18895));t.PostgrestClient=n.default;let i=s(r(69660));t.PostgrestQueryBuilder=i.default;let o=s(r(83287));t.PostgrestFilterBuilder=o.default;let a=s(r(43432));t.PostgrestTransformBuilder=a.default;let l=s(r(56781));t.PostgrestBuilder=l.default;let u=s(r(47960));t.PostgrestError=u.default,t.default={PostgrestClient:n.default,PostgrestQueryBuilder:i.default,PostgrestFilterBuilder:o.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:l.default,PostgrestError:u.default}},14373:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},26309:function(e,t,r){r.d(t,{eI:function(){return tN}});let s=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,14307)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)};class n extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class i extends n{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class o extends n{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class a extends n{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}(q=J||(J={})).Any="any",q.ApNortheast1="ap-northeast-1",q.ApNortheast2="ap-northeast-2",q.ApSouth1="ap-south-1",q.ApSoutheast1="ap-southeast-1",q.ApSoutheast2="ap-southeast-2",q.CaCentral1="ca-central-1",q.EuCentral1="eu-central-1",q.EuWest1="eu-west-1",q.EuWest2="eu-west-2",q.EuWest3="eu-west-3",q.SaEast1="sa-east-1",q.UsEast1="us-east-1",q.UsWest1="us-west-1",q.UsWest2="us-west-2";class l{constructor(e,{headers:t={},customFetch:r,region:n=J.Any}={}){this.url=e,this.headers=t,this.region=n,this.fetch=s(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,s,n,l,u;return s=this,n=void 0,l=void 0,u=function*(){try{let s;let{headers:n,method:l,body:u}=t,c={},{region:h}=t;h||(h=this.region),h&&"any"!==h&&(c["x-region"]=h),u&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&u instanceof Blob||u instanceof ArrayBuffer?(c["Content-Type"]="application/octet-stream",s=u):"string"==typeof u?(c["Content-Type"]="text/plain",s=u):"undefined"!=typeof FormData&&u instanceof FormData?s=u:(c["Content-Type"]="application/json",s=JSON.stringify(u)));let d=yield this.fetch(`${this.url}/${e}`,{method:l||"POST",headers:Object.assign(Object.assign(Object.assign({},c),this.headers),n),body:s}).catch(e=>{throw new i(e)}),f=d.headers.get("x-relay-error");if(f&&"true"===f)throw new o(d);if(!d.ok)throw new a(d);let p=(null!==(r=d.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return{data:"application/json"===p?yield d.json():"application/octet-stream"===p?yield d.blob():"text/event-stream"===p?d:"multipart/form-data"===p?yield d.formData():yield d.text(),error:null}}catch(e){return{data:null,error:e}}},new(l||(l=Promise))(function(e,t){function r(e){try{o(u.next(e))}catch(e){t(e)}}function i(e){try{o(u.throw(e))}catch(e){t(e)}}function o(t){var s;t.done?e(t.value):((s=t.value)instanceof l?s:new l(function(e){e(s)})).then(r,i)}o((u=u.apply(s,n||[])).next())})}}let{PostgrestClient:u,PostgrestQueryBuilder:c,PostgrestFilterBuilder:h,PostgrestTransformBuilder:d,PostgrestBuilder:f,PostgrestError:p}=r(68292),g={"X-Client-Info":"realtime-js/2.11.2"};(et=W||(W={}))[et.connecting=0]="connecting",et[et.open=1]="open",et[et.closing=2]="closing",et[et.closed=3]="closed",(er=H||(H={})).closed="closed",er.errored="errored",er.joined="joined",er.joining="joining",er.leaving="leaving",(es=V||(V={})).close="phx_close",es.error="phx_error",es.join="phx_join",es.reply="phx_reply",es.leave="phx_leave",es.access_token="access_token",(K||(K={})).websocket="websocket",(en=Z||(Z={})).Connecting="connecting",en.Open="open",en.Closing="closing",en.Closed="closed";class m{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let s=t.getUint8(1),n=t.getUint8(2),i=this.HEADER_LENGTH+2,o=r.decode(e.slice(i,i+s));i+=s;let a=r.decode(e.slice(i,i+n));return i+=n,{ref:null,topic:o,event:a,payload:JSON.parse(r.decode(e.slice(i,e.byteLength)))}}}class y{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(ei=G||(G={})).abstime="abstime",ei.bool="bool",ei.date="date",ei.daterange="daterange",ei.float4="float4",ei.float8="float8",ei.int2="int2",ei.int4="int4",ei.int4range="int4range",ei.int8="int8",ei.int8range="int8range",ei.json="json",ei.jsonb="jsonb",ei.money="money",ei.numeric="numeric",ei.oid="oid",ei.reltime="reltime",ei.text="text",ei.time="time",ei.timestamp="timestamp",ei.timestamptz="timestamptz",ei.timetz="timetz",ei.tsrange="tsrange",ei.tstzrange="tstzrange";let v=(e,t,r={})=>{var s;let n=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((r,s)=>(r[s]=w(s,e,t,n),r),{})},w=(e,t,r,s)=>{let n=t.find(t=>t.name===e),i=null==n?void 0:n.type,o=r[e];return i&&!s.includes(i)?b(i,o):_(o)},b=(e,t)=>{if("_"===e.charAt(0))return T(t,e.slice(1,e.length));switch(e){case G.bool:return k(t);case G.float4:case G.float8:case G.int2:case G.int4:case G.int8:case G.numeric:case G.oid:return S(t);case G.json:case G.jsonb:return E(t);case G.timestamp:return O(t);case G.abstime:case G.date:case G.daterange:case G.int4range:case G.int8range:case G.money:case G.reltime:case G.text:case G.time:case G.timestamptz:case G.timetz:case G.tsrange:case G.tstzrange:default:return _(t)}},_=e=>e,k=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},S=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},E=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},T=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s;let n=e.slice(1,r);try{s=JSON.parse("["+n+"]")}catch(e){s=n?n.split(","):[]}return s.map(e=>b(t,e))}return e},O=e=>"string"==typeof e?e.replace(" ","T"):e,j=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class x{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}(eo=X||(X={})).SYNC="sync",eo.JOIN="join",eo.LEAVE="leave";class R{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=R.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=R.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=R.syncDiff(this.state,e,t,r),s())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){let n=this.cloneDeep(e),i=this.transformState(t),o={},a={};return this.map(n,(e,t)=>{i[e]||(a[e]=t)}),this.map(i,(e,t)=>{let r=n[e];if(r){let s=t.map(e=>e.presence_ref),n=r.map(e=>e.presence_ref),i=t.filter(e=>0>n.indexOf(e.presence_ref)),l=r.filter(e=>0>s.indexOf(e.presence_ref));i.length>0&&(o[e]=i),l.length>0&&(a[e]=l)}else o[e]=t}),this.syncDiff(n,{joins:o,leaves:a},r,s)}static syncDiff(e,t,r,s){let{joins:n,leaves:i}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(n,(t,s)=>{var n;let i=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(s),i.length>0){let r=e[t].map(e=>e.presence_ref),s=i.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...s)}r(t,i,s)}),this.map(i,(t,r)=>{let n=e[t];if(!n)return;let i=r.map(e=>e.presence_ref);n=n.filter(e=>0>i.indexOf(e.presence_ref)),e[t]=n,s(t,n,r),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let s=e[r];return"metas"in s?t[r]=s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(ea=Y||(Y={})).ALL="*",ea.INSERT="INSERT",ea.UPDATE="UPDATE",ea.DELETE="DELETE",(el=Q||(Q={})).BROADCAST="broadcast",el.PRESENCE="presence",el.POSTGRES_CHANGES="postgres_changes",el.SYSTEM="system",(eu=ee||(ee={})).SUBSCRIBED="SUBSCRIBED",eu.TIMED_OUT="TIMED_OUT",eu.CLOSED="CLOSED",eu.CHANNEL_ERROR="CHANNEL_ERROR";class C{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=H.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new x(this,V.join,this.params,this.timeout),this.rejoinTimer=new y(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=H.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=H.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=H.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=H.errored,this.rejoinTimer.scheduleTimeout())}),this._on(V.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new R(this),this.broadcastEndpointURL=j(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:n,presence:i,private:o}}=this.params;this._onError(t=>null==e?void 0:e(ee.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(ee.CLOSED));let a={},l={broadcast:n,presence:i,postgres_changes:null!==(s=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==s?s:[],private:o};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(ee.SUBSCRIBED);return}{let s=this.bindings.postgres_changes,n=null!==(r=null==s?void 0:s.length)&&void 0!==r?r:0,i=[];for(let r=0;r<n;r++){let n=s[r],{filter:{event:o,schema:a,table:l,filter:u}}=n,c=t&&t[r];if(c&&c.event===o&&c.schema===a&&c.table===l&&c.filter===u)i.push(Object.assign(Object.assign({},n),{id:c.id}));else{this.unsubscribe(),null==e||e(ee.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=i,e&&e(ee.SUBSCRIBED);return}}).receive("error",t=>{null==e||e(ee.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(ee.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,n,i;let o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(i=null===(n=null===(s=this.params)||void 0===s?void 0:s.config)||void 0===n?void 0:n.broadcast)||void 0===i?void 0:i.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{let{event:n,payload:i}=e,o={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:n,payload:i,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,o,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await (null===(s=e.body)||void 0===s?void 0:s.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=H.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(V.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{let s=new x(this,V.leave,{},e);s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}async _fetchWithTimeout(e,t,r){let s=new AbortController,n=setTimeout(()=>s.abort(),r),i=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(n),i}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new x(this,e,t,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,n;let i=e.toLocaleLowerCase(),{close:o,error:a,leave:l,join:u}=V;if(r&&[o,a,l,u].indexOf(i)>=0&&r!==this._joinRef())return;let c=this._onMessage(i,t,r);if(t&&!c)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null===(s=this.bindings.postgres_changes)||void 0===s||s.filter(e=>{var t,r,s;return(null===(t=e.filter)||void 0===t?void 0:t.event)==="*"||(null===(s=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===s?void 0:s.toLocaleLowerCase())===i}).map(e=>e.callback(c,r)):null===(n=this.bindings[i])||void 0===n||n.filter(e=>{var r,s,n,o,a,l;if(!["broadcast","presence","postgres_changes"].includes(i))return e.type.toLocaleLowerCase()===i;if("id"in e){let i=e.id,o=null===(r=e.filter)||void 0===r?void 0:r.event;return i&&(null===(s=t.ids)||void 0===s?void 0:s.includes(i))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{let r=null===(a=null===(o=null==e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof c&&"ids"in c){let e=c.data,{schema:t,table:r,commit_timestamp:s,type:n,errors:i}=e;c=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:s,eventType:n,new:{},old:{},errors:i}),this._getPayloadRecords(e))}e.callback(c,r)})}_isClosed(){return this.state===H.closed}_isJoined(){return this.state===H.joined}_isJoining(){return this.state===H.joining}_isLeaving(){return this.state===H.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let s=e.toLocaleLowerCase(),n={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(n):this.bindings[s]=[n],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var s;return!((null===(s=e.type)||void 0===s?void 0:s.toLocaleLowerCase())===r&&C.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(V.close,{},e)}_onError(e){this._on(V.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=H.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=v(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=v(e.columns,e.old_record)),t}}let A=()=>{},P="undefined"!=typeof WebSocket,$=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class I{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=g,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=A,this.conn=null,this.sendBuffer=[],this.serializer=new m,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,14307)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},this.endPoint=`${e}/${K.websocket}`,this.httpEndpoint=j(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let n=null===(s=null==t?void 0:t.params)||void 0===s?void 0:s.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new y(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(P){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new U(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),r.e(7035).then(r.t.bind(r,7035,23)).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case W.connecting:return Z.Connecting;case W.open:return Z.Open;case W.closing:return Z.Closing;default:return Z.Closed}}isConnected(){return this.connectionState()===Z.Open}channel(e,t={config:{}}){let r=new C(`realtime:${e}`,t,this);return this.channels.push(r),r}push(e){let{topic:t,event:r,payload:s,ref:n}=e,i=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${n})`,s),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let e=null;try{e=JSON.parse(atob(t.split(".")[1]))}catch(e){}if(e&&e.exp&&!(Math.floor(Date.now()/1e3)-e.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`);this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(V.access_token,{access_token:t})})}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:s,ref:n}=e;n&&n===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${t} ${r} ${n&&"("+n+")"||""}`,s),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,n)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(V.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",s=new URLSearchParams(t);return`${e}${r}${s}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([$],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class U{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=W.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class L extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function D(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class M extends L{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class N extends L{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let F=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,14307)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},z=()=>{var e,t,s,n;return e=void 0,t=void 0,s=void 0,n=function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,14307))).Response:Response},new(s||(s=Promise))(function(r,i){function o(e){try{l(n.next(e))}catch(e){i(e)}}function a(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})},B=e=>{if(Array.isArray(e))return e.map(e=>B(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=B(r)}),t};var q,J,W,H,V,K,Z,G,X,Y,Q,ee,et,er,es,en,ei,eo,ea,el,eu,ec=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{l(s.next(e))}catch(e){i(e)}}function a(e){try{l(s.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};let eh=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ed=(e,t,r)=>ec(void 0,void 0,void 0,function*(){e instanceof(yield z())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new M(eh(r),e.status||500))}).catch(e=>{t(new N(eh(e),e))}):t(new N(eh(e),e))}),ef=(e,t,r,s)=>{let n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),s&&(n.body=JSON.stringify(s)),Object.assign(Object.assign({},n),r))};function ep(e,t,r,s,n,i){return ec(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,ef(t,s,n,i)).then(e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>ed(e,a,s))})})}function eg(e,t,r,s){return ec(this,void 0,void 0,function*(){return ep(e,"GET",t,r,s)})}function em(e,t,r,s,n){return ec(this,void 0,void 0,function*(){return ep(e,"POST",t,s,n,r)})}function ey(e,t,r,s,n){return ec(this,void 0,void 0,function*(){return ep(e,"DELETE",t,s,n,r)})}var ev=r(9109).lW,ew=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{l(s.next(e))}catch(e){i(e)}}function a(e){try{l(s.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};let eb={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},e_={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ek{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=F(s)}uploadOrUpdate(e,t,r,s){return ew(this,void 0,void 0,function*(){try{let n;let i=Object.assign(Object.assign({},e_),s),o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)}),a=i.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((n=new FormData).append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a)),n.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((n=r).append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a))):(n=r,o["cache-control"]=`max-age=${i.cacheControl}`,o["content-type"]=i.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==s?void 0:s.headers)&&(o=Object.assign(Object.assign({},o),s.headers));let l=this._removeEmptyFolders(t),u=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:n,headers:o},(null==i?void 0:i.duplex)?{duplex:i.duplex}:{})),h=yield c.json();if(c.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(D(e))return{data:null,error:e};throw e}})}upload(e,t,r){return ew(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return ew(this,void 0,void 0,function*(){let n=this._removeEmptyFolders(e),i=this._getFinalPath(n),o=new URL(this.url+`/object/upload/sign/${i}`);o.searchParams.set("token",t);try{let e;let t=Object.assign({upsert:e_.upsert},s),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,i["cache-control"]=`max-age=${t.cacheControl}`,i["content-type"]=t.contentType);let a=yield this.fetch(o.toString(),{method:"PUT",body:e,headers:i}),l=yield a.json();if(a.ok)return{data:{path:n,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return ew(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),s=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(s["x-upsert"]="true");let n=yield em(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),i=new URL(this.url+n.url),o=i.searchParams.get("token");if(!o)throw new L("No token returned by API");return{data:{signedUrl:i.toString(),path:e,token:o},error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}update(e,t,r){return ew(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return ew(this,void 0,void 0,function*(){try{return{data:yield em(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}copy(e,t,r){return ew(this,void 0,void 0,function*(){try{return{data:{path:(yield em(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return ew(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),n=yield em(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:n={signedUrl:encodeURI(`${this.url}${n.signedURL}${i}`)},error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return ew(this,void 0,void 0,function*(){try{let s=yield em(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}download(e,t){return ew(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=s?`?${s}`:"";try{let t=this._getFinalPath(e),s=yield eg(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield s.blob(),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}info(e){return ew(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield eg(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:B(e),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}exists(e){return ew(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,s){return ec(this,void 0,void 0,function*(){return ep(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(D(e)&&e instanceof N){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),s=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&s.push(n);let i=void 0!==(null==t?void 0:t.transform),o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&s.push(o);let a=s.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${i?"render/image":"object"}/public/${r}${a}`)}}}remove(e){return ew(this,void 0,void 0,function*(){try{return{data:yield ey(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}list(e,t,r){return ew(this,void 0,void 0,function*(){try{let s=Object.assign(Object.assign(Object.assign({},eb),t),{prefix:e||""});return{data:yield em(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ev?ev.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let eS={"X-Client-Info":"storage-js/2.7.1"};var eE=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{l(s.next(e))}catch(e){i(e)}}function a(e){try{l(s.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})};class eT{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},eS),t),this.fetch=F(r)}listBuckets(){return eE(this,void 0,void 0,function*(){try{return{data:yield eg(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}getBucket(e){return eE(this,void 0,void 0,function*(){try{return{data:yield eg(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return eE(this,void 0,void 0,function*(){try{return{data:yield em(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return eE(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,s,n){return ec(this,void 0,void 0,function*(){return ep(e,"PUT",t,s,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}emptyBucket(e){return eE(this,void 0,void 0,function*(){try{return{data:yield em(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}deleteBucket(e){return eE(this,void 0,void 0,function*(){try{return{data:yield ey(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}}class eO extends eT{constructor(e,t={},r){super(e,t,r)}from(e){return new ek(this.url,this.headers,e,this.fetch)}}let ej="";"undefined"!=typeof Deno?ej="deno":"undefined"!=typeof document?ej="web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?ej="react-native":ej="node";let ex={headers:{"X-Client-Info":`supabase-js-${ej}/2.49.4`}},eR={schema:"public"},eC={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},eA={};var eP=r(14307);let e$=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=eP.default:t=fetch,(...e)=>t(...e)},eI=()=>"undefined"==typeof Headers?eP.Headers:Headers,eU=(e,t,r)=>{let s=e$(r),n=eI();return(r,i)=>{var o,a,l,u;return o=void 0,a=void 0,l=void 0,u=function*(){var o;let a=null!==(o=yield t())&&void 0!==o?o:e,l=new n(null==i?void 0:i.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),s(r,Object.assign(Object.assign({},i),{headers:l}))},new(l||(l=Promise))(function(e,t){function r(e){try{n(u.next(e))}catch(e){t(e)}}function s(e){try{n(u.throw(e))}catch(e){t(e)}}function n(t){var n;t.done?e(t.value):((n=t.value)instanceof l?n:new l(function(e){e(n)})).then(r,s)}n((u=u.apply(o,a||[])).next())})}},eL="2.69.1",eD={"X-Client-Info":`gotrue-js/${eL}`},eM="X-Supabase-Api-Version",eN={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},eF=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class ez extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function eB(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class eq extends ez{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class eJ extends ez{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class eW extends ez{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class eH extends eW{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class eV extends eW{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class eK extends eW{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class eZ extends eW{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eG extends eW{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eX extends eW{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function eY(e){return eB(e)&&"AuthRetryableFetchError"===e.name}class eQ extends eW{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class e0 extends eW{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let e1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),e2=" 	\n\r=".split(""),e3=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<e2.length;t+=1)e[e2[t].charCodeAt(0)]=-2;for(let t=0;t<e1.length;t+=1)e[e1[t].charCodeAt(0)]=t;return e})();function e4(e,t,r){let s=e3[e];if(s>-1)for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===s)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function e6(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127){r(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,s,r)};for(let t=0;t<e.length;t+=1)e4(e.charCodeAt(t),n,i);return t.join("")}let e5=()=>"undefined"!=typeof window&&"undefined"!=typeof document,e8={tested:!1,writable:!1},e7=()=>{if(!e5())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(e8.tested)return e8.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),e8.tested=!0,e8.writable=!0}catch(e){e8.tested=!0,e8.writable=!1}return e8.writable},e9=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,14307)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},te=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,tt=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},tr=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},ts=async(e,t)=>{await e.removeItem(t)};class tn{constructor(){this.promise=new tn.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function ti(e){let t=e.split(".");if(3!==t.length)throw new e0("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!eF.test(t[e]))throw new e0("JWT not in base64url format");return{header:JSON.parse(e6(t[0])),payload:JSON.parse(e6(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)e4(e.charCodeAt(t),r,s);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function to(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function ta(e){return("0"+e.toString(16)).substr(-2)}async function tl(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function tu(e){return"undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder?btoa(await tl(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e)}async function tc(e,t,r=!1){let s=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,ta).join("")}(),n=s;r&&(n+="/PASSWORD_RECOVERY"),await tt(e,`${t}-code-verifier`,n);let i=await tu(s),o=s===i?"plain":"s256";return[i,o]}tn.promiseConstructor=Promise;let th=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;var td=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)0>t.indexOf(s[n])&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};let tf=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),tp=[502,503,504];async function tg(e){var t;let r,s;if(!te(e))throw new eX(tf(e),0);if(tp.includes(e.status))throw new eX(tf(e),e.status);try{r=await e.json()}catch(e){throw new eJ(tf(e),e)}let n=function(e){let t=e.headers.get(eM);if(!t||!t.match(th))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(n&&n.getTime()>=eN["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?s=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(s=r.error_code),s){if("weak_password"===s)throw new eQ(tf(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===s)throw new eH}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new eQ(tf(r),e.status,r.weak_password.reasons);throw new eq(tf(r),e.status||500,s)}let tm=(e,t,r,s)=>{let n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(s),Object.assign(Object.assign({},n),r))};async function ty(e,t,r,s){var n;let i=Object.assign({},null==s?void 0:s.headers);i[eM]||(i[eM]=eN["2024-01-01"].name),(null==s?void 0:s.jwt)&&(i.Authorization=`Bearer ${s.jwt}`);let o=null!==(n=null==s?void 0:s.query)&&void 0!==n?n:{};(null==s?void 0:s.redirectTo)&&(o.redirect_to=s.redirectTo);let a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await tv(e,t,r+a,{headers:i,noResolveJson:null==s?void 0:s.noResolveJson},{},null==s?void 0:s.body);return(null==s?void 0:s.xform)?null==s?void 0:s.xform(l):{data:Object.assign({},l),error:null}}async function tv(e,t,r,s,n,i){let o;let a=tm(t,s,n,i);try{o=await e(r,Object.assign({},a))}catch(e){throw console.error(e),new eX(tf(e),0)}if(o.ok||await tg(o),null==s?void 0:s.noResolveJson)return o;try{return await o.json()}catch(e){await tg(e)}}function tw(e){var t,r;let s=null;return e.access_token&&e.refresh_token&&e.expires_in&&(s=Object.assign({},e),!e.expires_at)&&(s.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)),{data:{session:s,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tb(e){let t=tw(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function t_(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tk(e){return{data:e,error:null}}function tS(e){let{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i},user:Object.assign({},td(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function tE(e){return e}var tT=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)0>t.indexOf(s[n])&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};class tO{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=e9(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await ty(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await ty(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:t_})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=tT(e,["options"]),s=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(s.new_email=null==r?void 0:r.newEmail,delete s.newEmail),await ty(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:tS,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(eB(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await ty(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:t_})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,s,n,i,o,a;try{let l={nextPage:null,lastPage:0,total:0},u=await ty(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(n=null===(s=null==e?void 0:e.perPage)||void 0===s?void 0:s.toString())&&void 0!==n?n:""},xform:tE});if(u.error)throw u.error;let c=await u.json(),h=null!==(i=u.headers.get("x-total-count"))&&void 0!==i?i:0,d=null!==(a=null===(o=u.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(e){if(eB(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){try{return await ty(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:t_})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){try{return await ty(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:t_})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){try{return await ty(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:t_})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){try{let{data:t,error:r}=await ty(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _deleteFactor(e){try{return{data:await ty(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}}let tj={getItem:e=>e7()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{e7()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{e7()&&globalThis.localStorage.removeItem(e)}};function tx(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let tR={debug:!!(globalThis&&e7()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class tC extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class tA extends tC{}async function tP(e,t,r){tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let s=new globalThis.AbortController;return t>0&&setTimeout(()=>{s.abort(),tR.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},async s=>{if(s){tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await r()}finally{tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}else{if(0===t)throw tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new tA(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(tR.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();let t$={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:eD,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function tI(e,t,r){return await r()}class tU{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=tU.nextInstanceID,tU.nextInstanceID+=1,this.instanceID>0&&e5()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let s=Object.assign(Object.assign({},t$),e);if(this.logDebugMessages=!!s.debug,"function"==typeof s.debug&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new tO({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=e9(s.fetch),this.lock=s.lock||tI,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:e5()&&(null===(t=null==globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=tP:this.lock=tI,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:e7()?this.storage=tj:(this.memoryStorage={},this.storage=tx(this.memoryStorage)):(this.memoryStorage={},this.storage=tx(this.memoryStorage)),e5()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${eL}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),e5()&&this.detectSessionInUrl&&"none"!==r){let{data:s,error:n}=await this._getSessionFromURL(t,r);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),eB(n)&&"AuthImplicitGrantRedirectError"===n.name){let t=null===(e=n.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}let{session:i,redirectType:o}=s;return this._debug("#_initialize()","detected session in URL",i,"redirect type",o),await this._saveSession(i),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(eB(e))return{error:e};return{error:new eJ("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,s;try{let{data:n,error:i}=await ty(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken}},xform:tw});if(i||!n)return{data:{user:null,session:null},error:i};let o=n.session,a=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:a,session:o},error:null}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,s;try{let n;if("email"in e){let{email:r,password:s,options:i}=e,o=null,a=null;"pkce"===this.flowType&&([o,a]=await tc(this.storage,this.storageKey)),n=await ty(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==i?void 0:i.emailRedirectTo,body:{email:r,password:s,data:null!==(t=null==i?void 0:i.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:o,code_challenge_method:a},xform:tw})}else if("phone"in e){let{phone:t,password:i,options:o}=e;n=await ty(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:i,data:null!==(r=null==o?void 0:o.data)&&void 0!==r?r:{},channel:null!==(s=null==o?void 0:o.channel)&&void 0!==s?s:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:tw})}else throw new eK("You must provide either an email or phone number and a password");let{data:i,error:o}=n;if(o||!i)return{data:{user:null,session:null},error:o};let a=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:s,options:n}=e;t=await ty(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:tb})}else if("phone"in e){let{phone:r,password:s,options:n}=e;t=await ty(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:tb})}else throw new eK("You must provide either an email or phone number and a password");let{data:r,error:s}=t;if(s)return{data:{user:null,session:null},error:s};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new eV};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,s,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:null===(n=e.options)||void 0===n?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async _exchangeCodeForSession(e){let t=await tr(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=t?t:"").split("/");try{let{data:t,error:n}=await ty(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:tw});if(await ts(this.storage,`${this.storageKey}-code-verifier`),n)throw n;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new eV};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=s?s:null}),error:n}}catch(e){if(eB(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:s,access_token:n,nonce:i}=e,{data:o,error:a}=await ty(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:n,nonce:i,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:tw});if(a)return{data:{user:null,session:null},error:a};if(!o||!o.session||!o.user)return{data:{user:null,session:null},error:new eV};return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:o,error:a}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,s,n,i;try{if("email"in e){let{email:s,options:n}=e,i=null,o=null;"pkce"===this.flowType&&([i,o]=await tc(this.storage,this.storageKey));let{error:a}=await ty(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},create_user:null===(r=null==n?void 0:n.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:i,code_challenge_method:o},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){let{phone:t,options:r}=e,{data:o,error:a}=await ty(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(s=null==r?void 0:r.data)&&void 0!==s?s:{},create_user:null===(n=null==r?void 0:r.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(i=null==r?void 0:r.channel)&&void 0!==i?i:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new eK("You must provide either an email or phone number.")}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let s,n;"options"in e&&(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo,n=null===(r=e.options)||void 0===r?void 0:r.captchaToken);let{data:i,error:o}=await ty(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:s,xform:tw});if(o)throw o;if(!i)throw Error("An error occurred on token verification.");let a=i.session,l=i.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,s;try{let n=null,i=null;return"pkce"===this.flowType&&([n,i]=await tc(this.storage,this.storageKey)),await ty(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:i}),headers:this.headers,xform:tk})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new eH;let{error:s}=await ty(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:s,options:n}=e,{error:i}=await ty(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){let{phone:r,type:s,options:n}=e,{data:i,error:o}=await ty(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:o}}throw new eK("You must provide either an email or phone number and a type")}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await tr(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))})}return{data:{session:e},error:null}}let{session:s,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{session:null},error:n};return{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await ty(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:t_});return await this._useSession(async e=>{var t,r,s;let{data:n,error:i}=e;if(i)throw i;return(null===(t=n.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await ty(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(s=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0,xform:t_}):{data:{user:null},error:new eH}})}catch(e){if(eB(e))return eB(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await ts(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:s,error:n}=r;if(n)throw n;if(!s.session)throw new eH;let i=s.session,o=null,a=null;"pkce"===this.flowType&&null!=e.email&&([o,a]=await tc(this.storage,this.storageKey));let{data:l,error:u}=await ty(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:a}),jwt:i.access_token,xform:t_});if(u)throw u;return i.user=l.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new eH;let t=Date.now()/1e3,r=t,s=!0,n=null,{payload:i}=ti(e.access_token);if(i.exp&&(s=(r=i.exp)<=t),s){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};n=t}else{let{data:s,error:i}=await this._getUser(e.access_token);if(i)throw i;n={access_token:e.access_token,refresh_token:e.refresh_token,user:s.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(e){if(eB(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:s,error:n}=t;if(n)throw n;e=null!==(r=s.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new eH;let{session:s,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!e5())throw new eZ("No browser detected.");if(e.error||e.error_description||e.error_code)throw new eZ(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new eG("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new eZ("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new eG("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:s,access_token:n,refresh_token:i,expires_in:o,expires_at:a,token_type:l}=e;if(!n||!o||!i||!l)throw new eZ("No session defined in URL");let u=Math.round(Date.now()/1e3),c=parseInt(o),h=u+c;a&&(h=parseInt(a));let d=h-u;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${c}s`);let f=h-c;u-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,h,u):u-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,h,u);let{data:p,error:g}=await this._getUser(n);if(g)throw g;let m={provider_token:r,provider_refresh_token:s,access_token:n,expires_in:c,expires_at:h,refresh_token:i,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(e){if(eB(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await tr(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:s,error:n}=t;if(n)return{error:n};let i=null===(r=s.session)||void 0===r?void 0:r.access_token;if(i){let{error:t}=await this.admin.signOut(i,e);if(t&&!(eB(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await ts(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,s;try{let{data:{session:s},error:n}=t;if(n)throw n;await (null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",e,"session",s)}catch(t){await (null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=await tc(this.storage,this.storageKey,!0));try{return await ty(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:s}=await this._useSession(async t=>{var r,s,n,i,o;let{data:a,error:l}=t;if(l)throw l;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return await ty(this.fetch,"GET",u,{headers:this.headers,jwt:null!==(o=null===(i=a.session)||void 0===i?void 0:i.access_token)&&void 0!==o?o:void 0})});if(s)throw s;return!e5()||(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(eB(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,s;let{data:n,error:i}=t;if(i)throw i;return await ty(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(s=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0})})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,s;let n=Date.now();return await (r=async r=>(r>0&&await to(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await ty(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:tw})),s=(e,t)=>t&&eY(t)&&Date.now()+200*Math.pow(2,e)-n<3e4,new Promise((e,t)=>{(async()=>{for(let n=0;n<1/0;n++)try{let t=await r(n);if(!s(n,null,t)){e(t);return}}catch(e){if(!s(n,e)){t(e);return}}})()}))}catch(e){if(this._debug(t,"error",e),eB(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),e5()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await tr(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let s=(null!==(e=r.expires_at)&&void 0!==e?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${s?"":" not"} expired with margin of 90000s`),s){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),eY(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new eH;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new tn;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new eH;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let s={session:t.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(e){if(this._debug(s,"error",e),eB(e)){let r={session:null,error:e};return eY(e)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(r),r}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,r=!0){let s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let s=[],n=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){s.push(e)}});if(await Promise.all(n),s.length>0){for(let e=0;e<s.length;e+=1)console.error(s[e]);throw s[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await tt(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await ts(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&e5()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let s=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),s<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof tC)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!e5()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let s=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await tc(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});s.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);s.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:s,error:n}=t;return n?{data:null,error:n}:await ty(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,s;let{data:n,error:i}=t;if(i)return{data:null,error:i};let o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:l}=await ty(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(r=null==n?void 0:n.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(s=null==a?void 0:a.totp)||void 0===s?void 0:s.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:s,error:n}=t;if(n)return{data:null,error:n};let{data:i,error:o}=await ty(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:o})})}catch(e){if(eB(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:s,error:n}=t;return n?{data:null,error:n}:await ty(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(e){if(eB(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:s},error:n}=e;if(n)return{data:null,error:n};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:i}=ti(s.access_token),o=null;i.aal&&(o=i.aal);let a=o;return(null!==(r=null===(t=s.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(a="aal2"),{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:i.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:s,error:n}=await ty(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;if(!s.keys||0===s.keys.length)throw new e0("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),!(r=s.keys.find(t=>t.kid===e)))throw new e0("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:s,payload:n,signature:i,raw:{header:o,payload:a}}=ti(r);if(!function(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}(n.exp),!s.kid||"HS256"===s.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:n,header:s,signature:i},error:null}}let l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(s.alg),u=await this.fetchJwk(s.kid,t),c=await crypto.subtle.importKey("jwk",u,l,!0,["verify"]);if(!await crypto.subtle.verify(l,c,i,function(e){let t=[];return!function(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){let t=(s-55296)*1024&65535;s=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(s,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${a}`)))throw new e0("Invalid JWT signature");return{data:{claims:n,header:s,signature:i},error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}}tU.nextInstanceID=0;var tL=tU;class tD extends tL{constructor(e){super(e)}}class tM{constructor(e,t,r){var s,n,i;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let o=e.replace(/\/$/,"");this.realtimeUrl=`${o}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${o}/auth/v1`,this.storageUrl=`${o}/storage/v1`,this.functionsUrl=`${o}/functions/v1`;let a=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,l=function(e,t){let{db:r,auth:s,realtime:n,global:i}=e,{db:o,auth:a,realtime:l,global:u}=t,c={db:Object.assign(Object.assign({},o),r),auth:Object.assign(Object.assign({},a),s),realtime:Object.assign(Object.assign({},l),n),global:Object.assign(Object.assign({},u),i),accessToken:()=>{var e,t,r,s;return e=this,t=void 0,s=function*(){return""},new(r=void 0,r=Promise)(function(n,i){function o(e){try{l(s.next(e))}catch(e){i(e)}}function a(e){try{l(s.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})}};return e.accessToken?c.accessToken=e.accessToken:delete c.accessToken,c}(null!=r?r:{},{db:eR,realtime:eA,auth:Object.assign(Object.assign({},eC),{storageKey:a}),global:ex});this.storageKey=null!==(s=l.auth.storageKey)&&void 0!==s?s:"",this.headers=null!==(n=l.global.headers)&&void 0!==n?n:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(i=l.auth)&&void 0!==i?i:{},this.headers,l.global.fetch),this.fetch=eU(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new u(`${o}/rest/v1`,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new l(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new eO(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,s,n,i;return r=this,s=void 0,n=void 0,i=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null},new(n||(n=Promise))(function(e,t){function o(e){try{l(i.next(e))}catch(e){t(e)}}function a(e){try{l(i.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(o,a)}l((i=i.apply(r,s||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:n,flowType:i,lock:o,debug:a},l,u){let c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new tD({url:this.authUrl,headers:Object.assign(Object.assign({},c),l),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:i,lock:o,debug:a,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new I(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let tN=(e,t,r)=>new tM(e,t,r)},77515:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},18186:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("ArrowDownUp",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"m21 8-4-4-4 4",key:"1c9v7m"}],["path",{d:"M17 4v16",key:"7dpous"}]])},15862:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},70518:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},40933:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},45764:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]])},97589:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},37164:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},36356:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("FileSpreadsheet",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M14 17h2",key:"10kma7"}]])},404:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},56127:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]])},71568:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},74122:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Keyboard",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",ry:"2",key:"15u882"}],["path",{d:"M6 8h.001",key:"1ej0i3"}],["path",{d:"M10 8h.001",key:"1x2st2"}],["path",{d:"M14 8h.001",key:"1vkmyp"}],["path",{d:"M18 8h.001",key:"kfsenl"}],["path",{d:"M8 12h.001",key:"1sjpby"}],["path",{d:"M12 12h.001",key:"al75ts"}],["path",{d:"M16 12h.001",key:"931bgk"}],["path",{d:"M7 16h10",key:"wp8him"}]])},74453:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},47390:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},67524:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},24501:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},39127:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("PiggyBank",[["path",{d:"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2h0V5z",key:"uf6l00"}],["path",{d:"M2 9v1c0 1.1.9 2 2 2h1",key:"nm575m"}],["path",{d:"M16 11h0",key:"k2aug8"}]])},98094:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},3751:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},54817:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},60994:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},29338:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},3722:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]])},55636:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]])},39348:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},11240:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},74740:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]])},56160:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]])},55430:function(e,t,r){r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},76669:function(e,t,r){r.d(t,{$j:function(){return D},Dx:function(){return M},VY:function(){return U},aU:function(){return L},aV:function(){return I},dk:function(){return N},fC:function(){return A},h_:function(){return $},xz:function(){return P}});var s=r(2265),n=r(98324),i=r(1584),o=r(13304),a=r(78149),l=r(71538),u=r(57437),c="AlertDialog",[h,d]=(0,n.b)(c,[o.p8]),f=(0,o.p8)(),p=e=>{let{__scopeAlertDialog:t,...r}=e,s=f(t);return(0,u.jsx)(o.fC,{...s,...r,modal:!0})};p.displayName=c;var g=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,n=f(r);return(0,u.jsx)(o.xz,{...n,...s,ref:t})});g.displayName="AlertDialogTrigger";var m=e=>{let{__scopeAlertDialog:t,...r}=e,s=f(t);return(0,u.jsx)(o.h_,{...s,...r})};m.displayName="AlertDialogPortal";var y=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,n=f(r);return(0,u.jsx)(o.aV,{...n,...s,ref:t})});y.displayName="AlertDialogOverlay";var v="AlertDialogContent",[w,b]=h(v),_=(0,l.sA)("AlertDialogContent"),k=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...l}=e,c=f(r),h=s.useRef(null),d=(0,i.e)(t,h),p=s.useRef(null);return(0,u.jsx)(o.jm,{contentName:v,titleName:S,docsSlug:"alert-dialog",children:(0,u.jsx)(w,{scope:r,cancelRef:p,children:(0,u.jsxs)(o.VY,{role:"alertdialog",...c,...l,ref:d,onOpenAutoFocus:(0,a.M)(l.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=p.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(_,{children:n}),(0,u.jsx)(C,{contentRef:h})]})})})});k.displayName=v;var S="AlertDialogTitle",E=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,n=f(r);return(0,u.jsx)(o.Dx,{...n,...s,ref:t})});E.displayName=S;var T="AlertDialogDescription",O=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,n=f(r);return(0,u.jsx)(o.dk,{...n,...s,ref:t})});O.displayName=T;var j=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,n=f(r);return(0,u.jsx)(o.x8,{...n,...s,ref:t})});j.displayName="AlertDialogAction";var x="AlertDialogCancel",R=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,{cancelRef:n}=b(x,r),a=f(r),l=(0,i.e)(t,n);return(0,u.jsx)(o.x8,{...a,...s,ref:l})});R.displayName=x;var C=e=>{let{contentRef:t}=e,r="`".concat(v,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(v,"` by passing a `").concat(T,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(v,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return s.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},A=p,P=g,$=m,I=y,U=k,L=j,D=R,M=E,N=O},69324:function(e,t,r){r.d(t,{fC:function(){return E},z$:function(){return T}});var s=r(2265),n=r(1584),i=r(98324),o=r(78149),a=r(91715),l=r(47250),u=r(75238),c=r(31383),h=r(25171),d=r(57437),f="Checkbox",[p,g]=(0,i.b)(f),[m,y]=p(f),v=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:i,checked:l,defaultChecked:u,required:c,disabled:p,value:g="on",onCheckedChange:y,form:v,...w}=e,[b,E]=s.useState(null),T=(0,n.e)(t,e=>E(e)),O=s.useRef(!1),j=!b||v||!!b.closest("form"),[x,R]=(0,a.T)({prop:l,defaultProp:null!=u&&u,onChange:y,caller:f}),C=s.useRef(x);return s.useEffect(()=>{let e=null==b?void 0:b.form;if(e){let t=()=>R(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,R]),(0,d.jsxs)(m,{scope:r,state:x,disabled:p,children:[(0,d.jsx)(h.WV.button,{type:"button",role:"checkbox","aria-checked":k(x)?"mixed":x,"aria-required":c,"data-state":S(x),"data-disabled":p?"":void 0,disabled:p,value:g,...w,ref:T,onKeyDown:(0,o.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.M)(e.onClick,e=>{R(e=>!!k(e)||!e),j&&(O.current=e.isPropagationStopped(),O.current||e.stopPropagation())})}),j&&(0,d.jsx)(_,{control:b,bubbles:!O.current,name:i,value:g,checked:x,required:c,disabled:p,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!k(u)&&u})]})});v.displayName=f;var w="CheckboxIndicator",b=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:s,...n}=e,i=y(w,r);return(0,d.jsx)(c.z,{present:s||k(i.state)||!0===i.state,children:(0,d.jsx)(h.WV.span,{"data-state":S(i.state),"data-disabled":i.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=w;var _=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,control:i,checked:o,bubbles:a=!0,defaultChecked:c,...f}=e,p=s.useRef(null),g=(0,n.e)(p,t),m=(0,l.D)(o),y=(0,u.t)(i);s.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==o&&t){let r=new Event("click",{bubbles:a});e.indeterminate=k(o),t.call(e,!k(o)&&o),e.dispatchEvent(r)}},[m,o,a]);let v=s.useRef(!k(o)&&o);return(0,d.jsx)(h.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=c?c:v.current,...f,tabIndex:-1,ref:g,style:{...f.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return"indeterminate"===e}function S(e){return k(e)?"indeterminate":e?"checked":"unchecked"}_.displayName="CheckboxBubbleInput";var E=v,T=b},61485:function(e,t,r){r.d(t,{VY:function(){return W},fC:function(){return B},h_:function(){return J},xz:function(){return q}});var s=r(2265),n=r(78149),i=r(1584),o=r(98324),a=r(53938),l=r(20589),u=r(80467),c=r(53201),h=r(25510),d=r(7715),f=r(31383),p=r(25171),g=r(71538),m=r(91715),y=r(78369),v=r(49418),w=r(57437),b="Popover",[_,k]=(0,o.b)(b,[h.D7]),S=(0,h.D7)(),[E,T]=_(b),O=e=>{let{__scopePopover:t,children:r,open:n,defaultOpen:i,onOpenChange:o,modal:a=!1}=e,l=S(t),u=s.useRef(null),[d,f]=s.useState(!1),[p,g]=(0,m.T)({prop:n,defaultProp:null!=i&&i,onChange:o,caller:b});return(0,w.jsx)(h.fC,{...l,children:(0,w.jsx)(E,{scope:t,contentId:(0,c.M)(),triggerRef:u,open:p,onOpenChange:g,onOpenToggle:s.useCallback(()=>g(e=>!e),[g]),hasCustomAnchor:d,onCustomAnchorAdd:s.useCallback(()=>f(!0),[]),onCustomAnchorRemove:s.useCallback(()=>f(!1),[]),modal:a,children:r})})};O.displayName=b;var j="PopoverAnchor";s.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=T(j,r),o=S(r),{onCustomAnchorAdd:a,onCustomAnchorRemove:l}=i;return s.useEffect(()=>(a(),()=>l()),[a,l]),(0,w.jsx)(h.ee,{...o,...n,ref:t})}).displayName=j;var x="PopoverTrigger",R=s.forwardRef((e,t)=>{let{__scopePopover:r,...s}=e,o=T(x,r),a=S(r),l=(0,i.e)(t,o.triggerRef),u=(0,w.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":z(o.open),...s,ref:l,onClick:(0,n.M)(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?u:(0,w.jsx)(h.ee,{asChild:!0,...a,children:u})});R.displayName=x;var C="PopoverPortal",[A,P]=_(C,{forceMount:void 0}),$=e=>{let{__scopePopover:t,forceMount:r,children:s,container:n}=e,i=T(C,t);return(0,w.jsx)(A,{scope:t,forceMount:r,children:(0,w.jsx)(f.z,{present:r||i.open,children:(0,w.jsx)(d.h,{asChild:!0,container:n,children:s})})})};$.displayName=C;var I="PopoverContent",U=s.forwardRef((e,t)=>{let r=P(I,e.__scopePopover),{forceMount:s=r.forceMount,...n}=e,i=T(I,e.__scopePopover);return(0,w.jsx)(f.z,{present:s||i.open,children:i.modal?(0,w.jsx)(D,{...n,ref:t}):(0,w.jsx)(M,{...n,ref:t})})});U.displayName=I;var L=(0,g.Z8)("PopoverContent.RemoveScroll"),D=s.forwardRef((e,t)=>{let r=T(I,e.__scopePopover),o=s.useRef(null),a=(0,i.e)(t,o),l=s.useRef(!1);return s.useEffect(()=>{let e=o.current;if(e)return(0,y.Ry)(e)},[]),(0,w.jsx)(v.Z,{as:L,allowPinchZoom:!0,children:(0,w.jsx)(N,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),l.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,s=2===t.button||r;l.current=s},{checkForDefaultPrevented:!1}),onFocusOutside:(0,n.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),M=s.forwardRef((e,t)=>{let r=T(I,e.__scopePopover),n=s.useRef(!1),i=s.useRef(!1);return(0,w.jsx)(N,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var s,o;null===(s=e.onCloseAutoFocus)||void 0===s||s.call(e,t),t.defaultPrevented||(n.current||null===(o=r.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),n.current=!1,i.current=!1},onInteractOutside:t=>{var s,o;null===(s=e.onInteractOutside)||void 0===s||s.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(o=r.triggerRef.current)||void 0===o?void 0:o.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),N=s.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:s,onOpenAutoFocus:n,onCloseAutoFocus:i,disableOutsidePointerEvents:o,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,...g}=e,m=T(I,r),y=S(r);return(0,l.EW)(),(0,w.jsx)(u.M,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:n,onUnmountAutoFocus:i,children:(0,w.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:o,onInteractOutside:p,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:f,onDismiss:()=>m.onOpenChange(!1),children:(0,w.jsx)(h.VY,{"data-state":z(m.open),role:"dialog",id:m.contentId,...y,...g,ref:t,style:{...g.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),F="PopoverClose";function z(e){return e?"open":"closed"}s.forwardRef((e,t)=>{let{__scopePopover:r,...s}=e,i=T(F,r);return(0,w.jsx)(p.WV.button,{type:"button",...s,ref:t,onClick:(0,n.M)(e.onClick,()=>i.onOpenChange(!1))})}).displayName=F,s.forwardRef((e,t)=>{let{__scopePopover:r,...s}=e,n=S(r);return(0,w.jsx)(h.Eh,{...n,...s,ref:t})}).displayName="PopoverArrow";var B=O,q=R,J=$,W=U},52431:function(e,t,r){r.d(t,{fC:function(){return b},z$:function(){return _}});var s=r(2265),n=r(98324),i=r(25171),o=r(57437),a="Progress",[l,u]=(0,n.b)(a),[c,h]=l(a),d=s.forwardRef((e,t)=>{var r,s,n,a;let{__scopeProgress:l,value:u=null,max:h,getValueLabel:d=g,...f}=e;(h||0===h)&&!v(h)&&console.error((r="".concat(h),s="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(s,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=v(h)?h:100;null===u||w(u,p)||console.error((n="".concat(u),a="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(a,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=w(u,p)?u:null,_=y(b)?d(b,p):void 0;return(0,o.jsx)(c,{scope:l,value:b,max:p,children:(0,o.jsx)(i.WV.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":y(b)?b:void 0,"aria-valuetext":_,role:"progressbar","data-state":m(b,p),"data-value":null!=b?b:void 0,"data-max":p,...f,ref:t})})});d.displayName=a;var f="ProgressIndicator",p=s.forwardRef((e,t)=>{var r;let{__scopeProgress:s,...n}=e,a=h(f,s);return(0,o.jsx)(i.WV.div,{"data-state":m(a.value,a.max),"data-value":null!==(r=a.value)&&void 0!==r?r:void 0,"data-max":a.max,...n,ref:t})});function g(e,t){return"".concat(Math.round(e/t*100),"%")}function m(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function v(e){return y(e)&&!isNaN(e)&&e>0}function w(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=f;var b=d,_=p},13537:function(e,t,r){r.d(t,{J:function(){return v}});var s,n="basil",i="https://js.stripe.com",o="".concat(i,"/").concat(n,"/stripe.js"),a=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,l=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,u=function(){for(var e=document.querySelectorAll('script[src^="'.concat(i,'"]')),t=0;t<e.length;t++){var r,s=e[t];if(r=s.src,a.test(r)||l.test(r))return s}return null},c=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(o).concat(t);var s=document.head||document.body;if(!s)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return s.appendChild(r),r},h=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.2.0",startTime:t})},d=null,f=null,p=null,g=function(e,t,r){if(null===e)return null;var s,i=t[0].match(/^pk_test/),o=3===(s=e.version)?"v3":s;i&&o!==n&&console.warn("Stripe.js@".concat(o," was loaded on the page, but @stripe/stripe-js@").concat("7.2.0"," expected Stripe.js@").concat(n,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var a=e.apply(void 0,t);return h(a,r),a},m=!1,y=function(){return s||(s=(null!==d?d:(d=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var r,s=u();s?s&&null!==p&&null!==f&&(s.removeEventListener("load",p),s.removeEventListener("error",f),null===(r=s.parentNode)||void 0===r||r.removeChild(s),s=c(null)):s=c(null),p=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},f=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},s.addEventListener("load",p),s.addEventListener("error",f)}catch(e){t(e);return}})).catch(function(e){return d=null,Promise.reject(e)})).catch(function(e){return s=null,Promise.reject(e)}))};Promise.resolve().then(function(){return y()}).catch(function(e){m||console.warn(e)});var v=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];m=!0;var s=Date.now();return y().then(function(e){return g(e,t,s)})}},18272:function(e,t,r){r.d(t,{MG:function(){return b}});var s=r(2265),n=r(54887);function i(e,t,r){let s,n=r.initialDeps??[];function i(){var i,o,a,l;let u,c;r.key&&(null==(i=r.debug)?void 0:i.call(r))&&(u=Date.now());let h=e();if(!(h.length!==n.length||h.some((e,t)=>n[t]!==e)))return s;if(n=h,r.key&&(null==(o=r.debug)?void 0:o.call(r))&&(c=Date.now()),s=t(...h),r.key&&(null==(a=r.debug)?void 0:a.call(r))){let e=Math.round((Date.now()-u)*100)/100,t=Math.round((Date.now()-c)*100)/100,s=t/16,n=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${n(t,5)} /${n(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*s,120))}deg 100% 31%);`,null==r?void 0:r.key)}return null==(l=null==r?void 0:r.onChange)||l.call(r,s),s}return i.updateDeps=e=>{n=e},i}function o(e,t){if(void 0!==e)return e;throw Error(`Unexpected undefined${t?`: ${t}`:""}`)}let a=(e,t)=>1>Math.abs(e-t),l=(e,t,r)=>{let s;return function(...n){e.clearTimeout(s),s=e.setTimeout(()=>t.apply(this,n),r)}},u=e=>e,c=e=>{let t=Math.max(e.startIndex-e.overscan,0),r=Math.min(e.endIndex+e.overscan,e.count-1),s=[];for(let e=t;e<=r;e++)s.push(e);return s},h=(e,t)=>{let r=e.scrollElement;if(!r)return;let s=e.targetWindow;if(!s)return;let n=e=>{let{width:r,height:s}=e;t({width:Math.round(r),height:Math.round(s)})};if(n(r.getBoundingClientRect()),!s.ResizeObserver)return()=>{};let i=new s.ResizeObserver(t=>{let s=()=>{let e=t[0];if(null==e?void 0:e.borderBoxSize){let t=e.borderBoxSize[0];if(t){n({width:t.inlineSize,height:t.blockSize});return}}n(r.getBoundingClientRect())};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(s):s()});return i.observe(r,{box:"border-box"}),()=>{i.unobserve(r)}},d={passive:!0},f="undefined"==typeof window||"onscrollend"in window,p=(e,t)=>{let r=e.scrollElement;if(!r)return;let s=e.targetWindow;if(!s)return;let n=0,i=e.options.useScrollendEvent&&f?()=>void 0:l(s,()=>{t(n,!1)},e.options.isScrollingResetDelay),o=s=>()=>{let{horizontal:o,isRtl:a}=e.options;n=o?r.scrollLeft*(a&&-1||1):r.scrollTop,i(),t(n,s)},a=o(!0),u=o(!1);u(),r.addEventListener("scroll",a,d);let c=e.options.useScrollendEvent&&f;return c&&r.addEventListener("scrollend",u,d),()=>{r.removeEventListener("scroll",a),c&&r.removeEventListener("scrollend",u)}},g=(e,t,r)=>{if(null==t?void 0:t.borderBoxSize){let e=t.borderBoxSize[0];if(e)return Math.round(e[r.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[r.options.horizontal?"width":"height"])},m=(e,{adjustments:t=0,behavior:r},s)=>{var n,i;null==(i=null==(n=s.scrollElement)?void 0:n.scrollTo)||i.call(n,{[s.options.horizontal?"left":"top"]:e+t,behavior:r})};class y{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null,t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver(e=>{e.forEach(e=>{let t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()})}):null);return{disconnect:()=>{var r;null==(r=t())||r.disconnect(),e=null},observe:e=>{var r;return null==(r=t())?void 0:r.observe(e,{box:"border-box"})},unobserve:e=>{var r;return null==(r=t())?void 0:r.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([t,r])=>{void 0===r&&delete e[t]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:u,rangeExtractor:c,onChange:()=>{},measureElement:g,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...e}},this.notify=e=>{var t,r;null==(r=(t=this.options).onChange)||r.call(t,this,e)},this.maybeNotify=i(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;let t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t){this.maybeNotify();return}this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{let r=new Map,s=new Map;for(let n=t-1;n>=0;n--){let t=e[n];if(r.has(t.lane))continue;let i=s.get(t.lane);if(null==i||t.end>i.end?s.set(t.lane,t):t.end<i.end&&r.set(t.lane,!0),r.size===this.options.lanes)break}return s.size===this.options.lanes?Array.from(s.values()).sort((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end)[0]:void 0},this.getMeasurementOptions=i(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,t,r,s,n)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:r,getItemKey:s,enabled:n}),{key:!1}),this.getMeasurements=i(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:t,scrollMargin:r,getItemKey:s,enabled:n},i)=>{if(!n)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(e=>{this.itemSizeCache.set(e.key,e.size)}));let o=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let a=this.measurementsCache.slice(0,o);for(let n=o;n<e;n++){let e=s(n),o=1===this.options.lanes?a[n-1]:this.getFurthestMeasurement(a,n),l=o?o.end+this.options.gap:t+r,u=i.get(e),c="number"==typeof u?u:this.options.estimateSize(n),h=l+c,d=o?o.lane:n%this.options.lanes;a[n]={index:n,start:l,size:c,end:h,key:e,lane:d}}return this.measurementsCache=a,a},{key:!1,debug:()=>this.options.debug}),this.calculateRange=i(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(e,t,r,s)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:r,lanes:s}){let n=e.length-1;if(e.length<=s)return{startIndex:0,endIndex:n};let i=v(0,n,t=>e[t].start,r),o=i;if(1===s)for(;o<n&&e[o].end<r+t;)o++;else if(s>1){let a=Array(s).fill(0);for(;o<n&&a.some(e=>e<r+t);){let t=e[o];a[t.lane]=t.end,o++}let l=Array(s).fill(r+t);for(;i>=0&&l.some(e=>e>=r);){let t=e[i];l[t.lane]=t.start,i--}i=Math.max(0,i-i%s),o=Math.min(n,o+(s-1-o%s))}return{startIndex:i,endIndex:o}}({measurements:e,outerSize:t,scrollOffset:r,lanes:s}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=i(()=>{let e=null,t=null,r=this.calculateRange();return r&&(e=r.startIndex,t=r.endIndex),this.maybeNotify.updateDeps([this.isScrolling,e,t]),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]},(e,t,r,s,n)=>null===s||null===n?[]:e({startIndex:s,endIndex:n,overscan:t,count:r}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{let t=this.options.indexAttribute,r=e.getAttribute(t);return r?parseInt(r,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{let r=this.indexFromElement(e),s=this.measurementsCache[r];if(!s)return;let n=s.key,i=this.elementsCache.get(n);i!==e&&(i&&this.observer.unobserve(i),this.observer.observe(e),this.elementsCache.set(n,e)),e.isConnected&&this.resizeItem(r,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{let r=this.measurementsCache[e];if(!r)return;let s=t-(this.itemSizeCache.get(r.key)??r.size);0!==s&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(r,s,this):r.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=s,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(r.index),this.itemSizeCache=new Map(this.itemSizeCache.set(r.key,t)),this.notify(!1))},this.measureElement=e=>{if(!e){this.elementsCache.forEach((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))});return}this._measureElement(e,void 0)},this.getVirtualItems=i(()=>[this.getVirtualIndexes(),this.getMeasurements()],(e,t)=>{let r=[];for(let s=0,n=e.length;s<n;s++){let n=t[e[s]];r.push(n)}return r},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{let t=this.getMeasurements();if(0!==t.length)return o(t[v(0,t.length-1,e=>o(t[e]).start,e)])},this.getOffsetForAlignment=(e,t,r=0)=>{let s=this.getSize(),n=this.getScrollOffset();"auto"===t&&(t=e>=n+s?"end":"start"),"center"===t?e+=(r-s)/2:"end"===t&&(e-=s);let i=this.options.horizontal?"scrollWidth":"scrollHeight";return Math.max(Math.min((this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[i]:this.scrollElement[i]:0)-s,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));let r=this.measurementsCache[e];if(!r)return;let s=this.getSize(),n=this.getScrollOffset();if("auto"===t){if(r.end>=n+s-this.options.scrollPaddingEnd)t="end";else{if(!(r.start<=n+this.options.scrollPaddingStart))return[n,t];t="start"}}let i="end"===t?r.end+this.options.scrollPaddingEnd:r.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(i,t,r.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:r}={})=>{this.cancelScrollToIndex(),"smooth"===r&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:r})},this.scrollToIndex=(e,{align:t="auto",behavior:r}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===r&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let s=this.getOffsetForIndex(e,t);if(!s)return;let[n,i]=s;this._scrollToOffset(n,{adjustments:void 0,behavior:r}),"smooth"!==r&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){let[t]=o(this.getOffsetForIndex(e,i));a(t,this.getScrollOffset())||this.scrollToIndex(e,{align:i,behavior:r})}else this.scrollToIndex(e,{align:i,behavior:r})}))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;let t;let r=this.getMeasurements();if(0===r.length)t=this.options.paddingStart;else if(1===this.options.lanes)t=(null==(e=r[r.length-1])?void 0:e.end)??0;else{let e=Array(this.options.lanes).fill(null),s=r.length-1;for(;s>=0&&e.some(e=>null===e);){let t=r[s];null===e[t.lane]&&(e[t.lane]=t.end),s--}t=Math.max(...e.filter(e=>null!==e))}return Math.max(t-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:r})=>{this.options.scrollToFn(e,{behavior:r,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}let v=(e,t,r,s)=>{for(;e<=t;){let n=(e+t)/2|0,i=r(n);if(i<s)e=n+1;else{if(!(i>s))return n;t=n-1}}return e>0?e-1:0},w="undefined"!=typeof document?s.useLayoutEffect:s.useEffect;function b(e){return function(e){let t=s.useReducer(()=>({}),{})[1],r={...e,onChange:(r,s)=>{var i;s?(0,n.flushSync)(t):t(),null==(i=e.onChange)||i.call(e,r,s)}},[i]=s.useState(()=>new y(r));return i.setOptions(r),w(()=>i._didMount(),[]),w(()=>i._willUpdate()),i}({observeElementRect:h,observeElementOffset:p,scrollToFn:m,...e})}},38472:function(e,t,r){let s,n,i,o,a;r.d(t,{Z:function(){return tg}});var l,u,c,h,d,f={};function p(e,t){return function(){return e.apply(t,arguments)}}r.r(f),r.d(f,{hasBrowserEnv:function(){return ev},hasStandardBrowserEnv:function(){return eb},hasStandardBrowserWebWorkerEnv:function(){return e_},navigator:function(){return ew},origin:function(){return ek}});var g=r(25566);let{toString:m}=Object.prototype,{getPrototypeOf:y}=Object,{iterator:v,toStringTag:w}=Symbol,b=(s=Object.create(null),e=>{let t=m.call(e);return s[t]||(s[t]=t.slice(8,-1).toLowerCase())}),_=e=>(e=e.toLowerCase(),t=>b(t)===e),k=e=>t=>typeof t===e,{isArray:S}=Array,E=k("undefined"),T=_("ArrayBuffer"),O=k("string"),j=k("function"),x=k("number"),R=e=>null!==e&&"object"==typeof e,C=e=>{if("object"!==b(e))return!1;let t=y(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(w in e)&&!(v in e)},A=_("Date"),P=_("File"),$=_("Blob"),I=_("FileList"),U=_("URLSearchParams"),[L,D,M,N]=["ReadableStream","Request","Response","Headers"].map(_);function F(e,t,{allOwnKeys:r=!1}={}){let s,n;if(null!=e){if("object"!=typeof e&&(e=[e]),S(e))for(s=0,n=e.length;s<n;s++)t.call(null,e[s],s,e);else{let n;let i=r?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;for(s=0;s<o;s++)n=i[s],t.call(null,e[n],n,e)}}}function z(e,t){let r;t=t.toLowerCase();let s=Object.keys(e),n=s.length;for(;n-- >0;)if(t===(r=s[n]).toLowerCase())return r;return null}let B="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,q=e=>!E(e)&&e!==B,J=(n="undefined"!=typeof Uint8Array&&y(Uint8Array),e=>n&&e instanceof n),W=_("HTMLFormElement"),H=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),V=_("RegExp"),K=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),s={};F(r,(r,n)=>{let i;!1!==(i=t(r,n,e))&&(s[n]=i||r)}),Object.defineProperties(e,s)},Z=_("AsyncFunction"),G=(l="function"==typeof setImmediate,u=j(B.postMessage),l?setImmediate:u?(c=`axios@${Math.random()}`,h=[],B.addEventListener("message",({source:e,data:t})=>{e===B&&t===c&&h.length&&h.shift()()},!1),e=>{h.push(e),B.postMessage(c,"*")}):e=>setTimeout(e)),X="undefined"!=typeof queueMicrotask?queueMicrotask.bind(B):void 0!==g&&g.nextTick||G;var Y={isArray:S,isArrayBuffer:T,isBuffer:function(e){return null!==e&&!E(e)&&null!==e.constructor&&!E(e.constructor)&&j(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||j(e.append)&&("formdata"===(t=b(e))||"object"===t&&j(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&T(e.buffer)},isString:O,isNumber:x,isBoolean:e=>!0===e||!1===e,isObject:R,isPlainObject:C,isReadableStream:L,isRequest:D,isResponse:M,isHeaders:N,isUndefined:E,isDate:A,isFile:P,isBlob:$,isRegExp:V,isFunction:j,isStream:e=>R(e)&&j(e.pipe),isURLSearchParams:U,isTypedArray:J,isFileList:I,forEach:F,merge:function e(){let{caseless:t}=q(this)&&this||{},r={},s=(s,n)=>{let i=t&&z(r,n)||n;C(r[i])&&C(s)?r[i]=e(r[i],s):C(s)?r[i]=e({},s):S(s)?r[i]=s.slice():r[i]=s};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&F(arguments[e],s);return r},extend:(e,t,r,{allOwnKeys:s}={})=>(F(t,(t,s)=>{r&&j(t)?e[s]=p(t,r):e[s]=t},{allOwnKeys:s}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,r,s)=>{let n,i,o;let a={};if(t=t||{},null==e)return t;do{for(i=(n=Object.getOwnPropertyNames(e)).length;i-- >0;)o=n[i],(!s||s(o,e,t))&&!a[o]&&(t[o]=e[o],a[o]=!0);e=!1!==r&&y(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:b,kindOfTest:_,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;let s=e.indexOf(t,r);return -1!==s&&s===r},toArray:e=>{if(!e)return null;if(S(e))return e;let t=e.length;if(!x(t))return null;let r=Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{let r;let s=(e&&e[v]).call(e);for(;(r=s.next())&&!r.done;){let s=r.value;t.call(e,s[0],s[1])}},matchAll:(e,t)=>{let r;let s=[];for(;null!==(r=e.exec(t));)s.push(r);return s},isHTMLForm:W,hasOwnProperty:H,hasOwnProp:H,reduceDescriptors:K,freezeMethods:e=>{K(e,(t,r)=>{if(j(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(j(e[r])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(e,t)=>{let r={};return(e=>{e.forEach(e=>{r[e]=!0})})(S(e)?e:String(e).split(t)),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,r){return t.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:z,global:B,isContextDefined:q,isSpecCompliantForm:function(e){return!!(e&&j(e.append)&&"FormData"===e[w]&&e[v])},toJSONObject:e=>{let t=Array(10),r=(e,s)=>{if(R(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[s]=e;let n=S(e)?[]:{};return F(e,(e,t)=>{let i=r(e,s+1);E(i)||(n[t]=i)}),t[s]=void 0,n}}return e};return r(e,0)},isAsyncFn:Z,isThenable:e=>e&&(R(e)||j(e))&&j(e.then)&&j(e.catch),setImmediate:G,asap:X,isIterable:e=>null!=e&&j(e[v])};function Q(e,t,r,s,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),s&&(this.request=s),n&&(this.response=n,this.status=n.status?n.status:null)}Y.inherits(Q,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Y.toJSONObject(this.config),code:this.code,status:this.status}}});let ee=Q.prototype,et={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{et[e]={value:e}}),Object.defineProperties(Q,et),Object.defineProperty(ee,"isAxiosError",{value:!0}),Q.from=(e,t,r,s,n,i)=>{let o=Object.create(ee);return Y.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Q.call(o,e.message,t,r,s,n),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};var er=r(9109).lW;function es(e){return Y.isPlainObject(e)||Y.isArray(e)}function en(e){return Y.endsWith(e,"[]")?e.slice(0,-2):e}function ei(e,t,r){return e?e.concat(t).map(function(e,t){return e=en(e),!r&&t?"["+e+"]":e}).join(r?".":""):t}let eo=Y.toFlatObject(Y,{},null,function(e){return/^is[A-Z]/.test(e)});var ea=function(e,t,r){if(!Y.isObject(e))throw TypeError("target must be an object");t=t||new FormData;let s=(r=Y.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!Y.isUndefined(t[e])})).metaTokens,n=r.visitor||u,i=r.dots,o=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&Y.isSpecCompliantForm(t);if(!Y.isFunction(n))throw TypeError("visitor must be a function");function l(e){if(null===e)return"";if(Y.isDate(e))return e.toISOString();if(!a&&Y.isBlob(e))throw new Q("Blob is not supported. Use a Buffer instead.");return Y.isArrayBuffer(e)||Y.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):er.from(e):e}function u(e,r,n){let a=e;if(e&&!n&&"object"==typeof e){if(Y.endsWith(r,"{}"))r=s?r:r.slice(0,-2),e=JSON.stringify(e);else{var u;if(Y.isArray(e)&&(u=e,Y.isArray(u)&&!u.some(es))||(Y.isFileList(e)||Y.endsWith(r,"[]"))&&(a=Y.toArray(e)))return r=en(r),a.forEach(function(e,s){Y.isUndefined(e)||null===e||t.append(!0===o?ei([r],s,i):null===o?r:r+"[]",l(e))}),!1}}return!!es(e)||(t.append(ei(n,r,i),l(e)),!1)}let c=[],h=Object.assign(eo,{defaultVisitor:u,convertValue:l,isVisitable:es});if(!Y.isObject(e))throw TypeError("data must be an object");return!function e(r,s){if(!Y.isUndefined(r)){if(-1!==c.indexOf(r))throw Error("Circular reference detected in "+s.join("."));c.push(r),Y.forEach(r,function(r,i){!0===(!(Y.isUndefined(r)||null===r)&&n.call(t,r,Y.isString(i)?i.trim():i,s,h))&&e(r,s?s.concat(i):[i])}),c.pop()}}(e),t};function el(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function eu(e,t){this._pairs=[],e&&ea(e,this,t)}let ec=eu.prototype;function eh(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ed(e,t,r){let s;if(!t)return e;let n=r&&r.encode||eh;Y.isFunction(r)&&(r={serialize:r});let i=r&&r.serialize;if(s=i?i(t,r):Y.isURLSearchParams(t)?t.toString():new eu(t,r).toString(n)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}ec.append=function(e,t){this._pairs.push([e,t])},ec.toString=function(e){let t=e?function(t){return e.call(this,t,el)}:el;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class ef{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Y.forEach(this.handlers,function(t){null!==t&&e(t)})}}var ep={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},eg="undefined"!=typeof URLSearchParams?URLSearchParams:eu,em="undefined"!=typeof FormData?FormData:null,ey="undefined"!=typeof Blob?Blob:null;let ev="undefined"!=typeof window&&"undefined"!=typeof document,ew="object"==typeof navigator&&navigator||void 0,eb=ev&&(!ew||0>["ReactNative","NativeScript","NS"].indexOf(ew.product)),e_="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ek=ev&&window.location.href||"http://localhost";var eS={...f,isBrowser:!0,classes:{URLSearchParams:eg,FormData:em,Blob:ey},protocols:["http","https","file","blob","url","data"]},eE=function(e){if(Y.isFormData(e)&&Y.isFunction(e.entries)){let t={};return Y.forEachEntry(e,(e,r)=>{!function e(t,r,s,n){let i=t[n++];if("__proto__"===i)return!0;let o=Number.isFinite(+i),a=n>=t.length;return(i=!i&&Y.isArray(s)?s.length:i,a)?Y.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r:(s[i]&&Y.isObject(s[i])||(s[i]=[]),e(t,r,s[i],n)&&Y.isArray(s[i])&&(s[i]=function(e){let t,r;let s={},n=Object.keys(e),i=n.length;for(t=0;t<i;t++)s[r=n[t]]=e[r];return s}(s[i]))),!o}(Y.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),r,t,0)}),t}return null};let eT={transitional:ep,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let r;let s=t.getContentType()||"",n=s.indexOf("application/json")>-1,i=Y.isObject(e);if(i&&Y.isHTMLForm(e)&&(e=new FormData(e)),Y.isFormData(e))return n?JSON.stringify(eE(e)):e;if(Y.isArrayBuffer(e)||Y.isBuffer(e)||Y.isStream(e)||Y.isFile(e)||Y.isBlob(e)||Y.isReadableStream(e))return e;if(Y.isArrayBufferView(e))return e.buffer;if(Y.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1){var o,a;return(o=e,a=this.formSerializer,ea(o,new eS.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,s){return eS.isNode&&Y.isBuffer(e)?(this.append(t,e.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},a))).toString()}if((r=Y.isFileList(e))||s.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return ea(r?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||n?(t.setContentType("application/json",!1),function(e,t,r){if(Y.isString(e))try{return(0,JSON.parse)(e),Y.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let t=this.transitional||eT.transitional,r=t&&t.forcedJSONParsing,s="json"===this.responseType;if(Y.isResponse(e)||Y.isReadableStream(e))return e;if(e&&Y.isString(e)&&(r&&!this.responseType||s)){let r=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!r&&s){if("SyntaxError"===e.name)throw Q.from(e,Q.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eS.classes.FormData,Blob:eS.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Y.forEach(["delete","get","head","post","put","patch"],e=>{eT.headers[e]={}});let eO=Y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);var ej=e=>{let t,r,s;let n={};return e&&e.split("\n").forEach(function(e){s=e.indexOf(":"),t=e.substring(0,s).trim().toLowerCase(),r=e.substring(s+1).trim(),!t||n[t]&&eO[t]||("set-cookie"===t?n[t]?n[t].push(r):n[t]=[r]:n[t]=n[t]?n[t]+", "+r:r)}),n};let ex=Symbol("internals");function eR(e){return e&&String(e).trim().toLowerCase()}function eC(e){return!1===e||null==e?e:Y.isArray(e)?e.map(eC):String(e)}let eA=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eP(e,t,r,s,n){if(Y.isFunction(s))return s.call(this,t,r);if(n&&(t=r),Y.isString(t)){if(Y.isString(s))return -1!==t.indexOf(s);if(Y.isRegExp(s))return s.test(t)}}class e${constructor(e){e&&this.set(e)}set(e,t,r){let s=this;function n(e,t,r){let n=eR(t);if(!n)throw Error("header name must be a non-empty string");let i=Y.findKey(s,n);i&&void 0!==s[i]&&!0!==r&&(void 0!==r||!1===s[i])||(s[i||t]=eC(e))}let i=(e,t)=>Y.forEach(e,(e,r)=>n(e,r,t));if(Y.isPlainObject(e)||e instanceof this.constructor)i(e,t);else if(Y.isString(e)&&(e=e.trim())&&!eA(e))i(ej(e),t);else if(Y.isObject(e)&&Y.isIterable(e)){let r={},s,n;for(let t of e){if(!Y.isArray(t))throw TypeError("Object iterator must return a key-value pair");r[n=t[0]]=(s=r[n])?Y.isArray(s)?[...s,t[1]]:[s,t[1]]:t[1]}i(r,t)}else null!=e&&n(t,e,r);return this}get(e,t){if(e=eR(e)){let r=Y.findKey(this,e);if(r){let e=this[r];if(!t)return e;if(!0===t)return function(e){let t;let r=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=s.exec(e);)r[t[1]]=t[2];return r}(e);if(Y.isFunction(t))return t.call(this,e,r);if(Y.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=eR(e)){let r=Y.findKey(this,e);return!!(r&&void 0!==this[r]&&(!t||eP(this,this[r],r,t)))}return!1}delete(e,t){let r=this,s=!1;function n(e){if(e=eR(e)){let n=Y.findKey(r,e);n&&(!t||eP(r,r[n],n,t))&&(delete r[n],s=!0)}}return Y.isArray(e)?e.forEach(n):n(e),s}clear(e){let t=Object.keys(this),r=t.length,s=!1;for(;r--;){let n=t[r];(!e||eP(this,this[n],n,e,!0))&&(delete this[n],s=!0)}return s}normalize(e){let t=this,r={};return Y.forEach(this,(s,n)=>{let i=Y.findKey(r,n);if(i){t[i]=eC(s),delete t[n];return}let o=e?n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r):String(n).trim();o!==n&&delete t[n],t[o]=eC(s),r[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return Y.forEach(this,(r,s)=>{null!=r&&!1!==r&&(t[s]=e&&Y.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let r=new this(e);return t.forEach(e=>r.set(e)),r}static accessor(e){let t=(this[ex]=this[ex]={accessors:{}}).accessors,r=this.prototype;function s(e){let s=eR(e);t[s]||(!function(e,t){let r=Y.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+r,{value:function(e,r,n){return this[s].call(this,t,e,r,n)},configurable:!0})})}(r,e),t[s]=!0)}return Y.isArray(e)?e.forEach(s):s(e),this}}function eI(e,t){let r=this||eT,s=t||r,n=e$.from(s.headers),i=s.data;return Y.forEach(e,function(e){i=e.call(r,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function eU(e){return!!(e&&e.__CANCEL__)}function eL(e,t,r){Q.call(this,null==e?"canceled":e,Q.ERR_CANCELED,t,r),this.name="CanceledError"}function eD(e,t,r){let s=r.config.validateStatus;!r.status||!s||s(r.status)?e(r):t(new Q("Request failed with status code "+r.status,[Q.ERR_BAD_REQUEST,Q.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}e$.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Y.reduceDescriptors(e$.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}}),Y.freezeMethods(e$),Y.inherits(eL,Q,{__CANCEL__:!0});var eM=function(e,t){let r;let s=Array(e=e||10),n=Array(e),i=0,o=0;return t=void 0!==t?t:1e3,function(a){let l=Date.now(),u=n[o];r||(r=l),s[i]=a,n[i]=l;let c=o,h=0;for(;c!==i;)h+=s[c++],c%=e;if((i=(i+1)%e)===o&&(o=(o+1)%e),l-r<t)return;let d=u&&l-u;return d?Math.round(1e3*h/d):void 0}},eN=function(e,t){let r,s,n=0,i=1e3/t,o=(t,i=Date.now())=>{n=i,r=null,s&&(clearTimeout(s),s=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),a=t-n;a>=i?o(e,t):(r=e,s||(s=setTimeout(()=>{s=null,o(r)},i-a)))},()=>r&&o(r)]};let eF=(e,t,r=3)=>{let s=0,n=eM(50,250);return eN(r=>{let i=r.loaded,o=r.lengthComputable?r.total:void 0,a=i-s,l=n(a);s=i,e({loaded:i,total:o,progress:o?i/o:void 0,bytes:a,rate:l||void 0,estimated:l&&o&&i<=o?(o-i)/l:void 0,event:r,lengthComputable:null!=o,[t?"download":"upload"]:!0})},r)},ez=(e,t)=>{let r=null!=e;return[s=>t[0]({lengthComputable:r,total:e,loaded:s}),t[1]]},eB=e=>(...t)=>Y.asap(()=>e(...t));var eq=eS.hasStandardBrowserEnv?(i=new URL(eS.origin),o=eS.navigator&&/(msie|trident)/i.test(eS.navigator.userAgent),e=>(e=new URL(e,eS.origin),i.protocol===e.protocol&&i.host===e.host&&(o||i.port===e.port))):()=>!0,eJ=eS.hasStandardBrowserEnv?{write(e,t,r,s,n,i){let o=[e+"="+encodeURIComponent(t)];Y.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),Y.isString(s)&&o.push("path="+s),Y.isString(n)&&o.push("domain="+n),!0===i&&o.push("secure"),document.cookie=o.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function eW(e,t,r){let s=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(s||!1==r)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}let eH=e=>e instanceof e$?{...e}:e;function eV(e,t){t=t||{};let r={};function s(e,t,r,s){return Y.isPlainObject(e)&&Y.isPlainObject(t)?Y.merge.call({caseless:s},e,t):Y.isPlainObject(t)?Y.merge({},t):Y.isArray(t)?t.slice():t}function n(e,t,r,n){return Y.isUndefined(t)?Y.isUndefined(e)?void 0:s(void 0,e,r,n):s(e,t,r,n)}function i(e,t){if(!Y.isUndefined(t))return s(void 0,t)}function o(e,t){return Y.isUndefined(t)?Y.isUndefined(e)?void 0:s(void 0,e):s(void 0,t)}function a(r,n,i){return i in t?s(r,n):i in e?s(void 0,r):void 0}let l={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(e,t,r)=>n(eH(e),eH(t),r,!0)};return Y.forEach(Object.keys(Object.assign({},e,t)),function(s){let i=l[s]||n,o=i(e[s],t[s],s);Y.isUndefined(o)&&i!==a||(r[s]=o)}),r}var eK=e=>{let t;let r=eV({},e),{data:s,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:o,headers:a,auth:l}=r;if(r.headers=a=e$.from(a),r.url=ed(eW(r.baseURL,r.url,r.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),Y.isFormData(s)){if(eS.hasStandardBrowserEnv||eS.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(t=a.getContentType())){let[e,...r]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...r].join("; "))}}if(eS.hasStandardBrowserEnv&&(n&&Y.isFunction(n)&&(n=n(r)),n||!1!==n&&eq(r.url))){let e=i&&o&&eJ.read(o);e&&a.set(i,e)}return r},eZ="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,r){let s,n,i,o,a;let l=eK(e),u=l.data,c=e$.from(l.headers).normalize(),{responseType:h,onUploadProgress:d,onDownloadProgress:f}=l;function p(){o&&o(),a&&a(),l.cancelToken&&l.cancelToken.unsubscribe(s),l.signal&&l.signal.removeEventListener("abort",s)}let g=new XMLHttpRequest;function m(){if(!g)return;let s=e$.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders());eD(function(e){t(e),p()},function(e){r(e),p()},{data:h&&"text"!==h&&"json"!==h?g.response:g.responseText,status:g.status,statusText:g.statusText,headers:s,config:e,request:g}),g=null}g.open(l.method.toUpperCase(),l.url,!0),g.timeout=l.timeout,"onloadend"in g?g.onloadend=m:g.onreadystatechange=function(){g&&4===g.readyState&&(0!==g.status||g.responseURL&&0===g.responseURL.indexOf("file:"))&&setTimeout(m)},g.onabort=function(){g&&(r(new Q("Request aborted",Q.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new Q("Network Error",Q.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let t=l.timeout?"timeout of "+l.timeout+"ms exceeded":"timeout exceeded",s=l.transitional||ep;l.timeoutErrorMessage&&(t=l.timeoutErrorMessage),r(new Q(t,s.clarifyTimeoutError?Q.ETIMEDOUT:Q.ECONNABORTED,e,g)),g=null},void 0===u&&c.setContentType(null),"setRequestHeader"in g&&Y.forEach(c.toJSON(),function(e,t){g.setRequestHeader(t,e)}),Y.isUndefined(l.withCredentials)||(g.withCredentials=!!l.withCredentials),h&&"json"!==h&&(g.responseType=l.responseType),f&&([i,a]=eF(f,!0),g.addEventListener("progress",i)),d&&g.upload&&([n,o]=eF(d),g.upload.addEventListener("progress",n),g.upload.addEventListener("loadend",o)),(l.cancelToken||l.signal)&&(s=t=>{g&&(r(!t||t.type?new eL(null,e,g):t),g.abort(),g=null)},l.cancelToken&&l.cancelToken.subscribe(s),l.signal&&(l.signal.aborted?s():l.signal.addEventListener("abort",s)));let y=function(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(l.url);if(y&&-1===eS.protocols.indexOf(y)){r(new Q("Unsupported protocol "+y+":",Q.ERR_BAD_REQUEST,e));return}g.send(u||null)})},eG=(e,t)=>{let{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,s=new AbortController,n=function(e){if(!r){r=!0,o();let t=e instanceof Error?e:this.reason;s.abort(t instanceof Q?t:new eL(t instanceof Error?t.message:t))}},i=t&&setTimeout(()=>{i=null,n(new Q(`timeout ${t} of ms exceeded`,Q.ETIMEDOUT))},t),o=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(n):e.removeEventListener("abort",n)}),e=null)};e.forEach(e=>e.addEventListener("abort",n));let{signal:a}=s;return a.unsubscribe=()=>Y.asap(o),a}};let eX=function*(e,t){let r,s=e.byteLength;if(!t||s<t){yield e;return}let n=0;for(;n<s;)r=n+t,yield e.slice(n,r),n=r},eY=async function*(e,t){for await(let r of eQ(e))yield*eX(r,t)},eQ=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)break;yield r}}finally{await t.cancel()}},e0=(e,t,r,s)=>{let n;let i=eY(e,t),o=0,a=e=>{!n&&(n=!0,s&&s(e))};return new ReadableStream({async pull(e){try{let{done:t,value:s}=await i.next();if(t){a(),e.close();return}let n=s.byteLength;if(r){let e=o+=n;r(e)}e.enqueue(new Uint8Array(s))}catch(e){throw a(e),e}},cancel:e=>(a(e),i.return())},{highWaterMark:2})},e1="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,e2=e1&&"function"==typeof ReadableStream,e3=e1&&("function"==typeof TextEncoder?(a=new TextEncoder,e=>a.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),e4=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},e6=e2&&e4(()=>{let e=!1,t=new Request(eS.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),e5=e2&&e4(()=>Y.isReadableStream(new Response("").body)),e8={stream:e5&&(e=>e.body)};e1&&(d=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{e8[e]||(e8[e]=Y.isFunction(d[e])?t=>t[e]():(t,r)=>{throw new Q(`Response type '${e}' is not supported`,Q.ERR_NOT_SUPPORT,r)})}));let e7=async e=>{if(null==e)return 0;if(Y.isBlob(e))return e.size;if(Y.isSpecCompliantForm(e)){let t=new Request(eS.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Y.isArrayBufferView(e)||Y.isArrayBuffer(e)?e.byteLength:(Y.isURLSearchParams(e)&&(e+=""),Y.isString(e))?(await e3(e)).byteLength:void 0},e9=async(e,t)=>{let r=Y.toFiniteNumber(e.getContentLength());return null==r?e7(t):r},te={http:null,xhr:eZ,fetch:e1&&(async e=>{let t,r,{url:s,method:n,data:i,signal:o,cancelToken:a,timeout:l,onDownloadProgress:u,onUploadProgress:c,responseType:h,headers:d,withCredentials:f="same-origin",fetchOptions:p}=eK(e);h=h?(h+"").toLowerCase():"text";let g=eG([o,a&&a.toAbortSignal()],l),m=g&&g.unsubscribe&&(()=>{g.unsubscribe()});try{if(c&&e6&&"get"!==n&&"head"!==n&&0!==(r=await e9(d,i))){let e,t=new Request(s,{method:"POST",body:i,duplex:"half"});if(Y.isFormData(i)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){let[e,s]=ez(r,eF(eB(c)));i=e0(t.body,65536,e,s)}}Y.isString(f)||(f=f?"include":"omit");let o="credentials"in Request.prototype;t=new Request(s,{...p,signal:g,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:i,duplex:"half",credentials:o?f:void 0});let a=await fetch(t),l=e5&&("stream"===h||"response"===h);if(e5&&(u||l&&m)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=a[t]});let t=Y.toFiniteNumber(a.headers.get("content-length")),[r,s]=u&&ez(t,eF(eB(u),!0))||[];a=new Response(e0(a.body,65536,r,()=>{s&&s(),m&&m()}),e)}h=h||"text";let y=await e8[Y.findKey(e8,h)||"text"](a,e);return!l&&m&&m(),await new Promise((r,s)=>{eD(r,s,{data:y,headers:e$.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:t})})}catch(r){if(m&&m(),r&&"TypeError"===r.name&&/Load failed|fetch/i.test(r.message))throw Object.assign(new Q("Network Error",Q.ERR_NETWORK,e,t),{cause:r.cause||r});throw Q.from(r,r&&r.code,e,t)}})};Y.forEach(te,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let tt=e=>`- ${e}`,tr=e=>Y.isFunction(e)||null===e||!1===e;var ts=e=>{let t,r;let{length:s}=e=Y.isArray(e)?e:[e],n={};for(let i=0;i<s;i++){let s;if(r=t=e[i],!tr(t)&&void 0===(r=te[(s=String(t)).toLowerCase()]))throw new Q(`Unknown adapter '${s}'`);if(r)break;n[s||"#"+i]=r}if(!r){let e=Object.entries(n).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new Q("There is no suitable adapter to dispatch the request "+(s?e.length>1?"since :\n"+e.map(tt).join("\n"):" "+tt(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function tn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eL(null,e)}function ti(e){return tn(e),e.headers=e$.from(e.headers),e.data=eI.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ts(e.adapter||eT.adapter)(e).then(function(t){return tn(e),t.data=eI.call(e,e.transformResponse,t),t.headers=e$.from(t.headers),t},function(t){return!eU(t)&&(tn(e),t&&t.response&&(t.response.data=eI.call(e,e.transformResponse,t.response),t.response.headers=e$.from(t.response.headers))),Promise.reject(t)})}let to="1.9.0",ta={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ta[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});let tl={};ta.transitional=function(e,t,r){function s(e,t){return"[Axios v"+to+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,n,i)=>{if(!1===e)throw new Q(s(n," has been removed"+(t?" in "+t:"")),Q.ERR_DEPRECATED);return t&&!tl[n]&&(tl[n]=!0,console.warn(s(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,n,i)}},ta.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};var tu={assertOptions:function(e,t,r){if("object"!=typeof e)throw new Q("options must be an object",Q.ERR_BAD_OPTION_VALUE);let s=Object.keys(e),n=s.length;for(;n-- >0;){let i=s[n],o=t[i];if(o){let t=e[i],r=void 0===t||o(t,i,e);if(!0!==r)throw new Q("option "+i+" must be "+r,Q.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new Q("Unknown option "+i,Q.ERR_BAD_OPTION)}},validators:ta};let tc=tu.validators;class th{constructor(e){this.defaults=e||{},this.interceptors={request:new ef,response:new ef}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let r=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?r&&!String(e.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+r):e.stack=r}catch(e){}}throw e}}_request(e,t){let r,s;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:n,paramsSerializer:i,headers:o}=t=eV(this.defaults,t);void 0!==n&&tu.assertOptions(n,{silentJSONParsing:tc.transitional(tc.boolean),forcedJSONParsing:tc.transitional(tc.boolean),clarifyTimeoutError:tc.transitional(tc.boolean)},!1),null!=i&&(Y.isFunction(i)?t.paramsSerializer={serialize:i}:tu.assertOptions(i,{encode:tc.function,serialize:tc.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tu.assertOptions(t,{baseUrl:tc.spelling("baseURL"),withXsrfToken:tc.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=o&&Y.merge(o.common,o[t.method]);o&&Y.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=e$.concat(a,o);let l=[],u=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(u=u&&e.synchronous,l.unshift(e.fulfilled,e.rejected))});let c=[];this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let h=0;if(!u){let e=[ti.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,c),s=e.length,r=Promise.resolve(t);h<s;)r=r.then(e[h++],e[h++]);return r}s=l.length;let d=t;for(h=0;h<s;){let e=l[h++],t=l[h++];try{d=e(d)}catch(e){t.call(this,e);break}}try{r=ti.call(this,d)}catch(e){return Promise.reject(e)}for(h=0,s=c.length;h<s;)r=r.then(c[h++],c[h++]);return r}getUri(e){return ed(eW((e=eV(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Y.forEach(["delete","get","head","options"],function(e){th.prototype[e]=function(t,r){return this.request(eV(r||{},{method:e,url:t,data:(r||{}).data}))}}),Y.forEach(["post","put","patch"],function(e){function t(t){return function(r,s,n){return this.request(eV(n||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:s}))}}th.prototype[e]=t(),th.prototype[e+"Form"]=t(!0)});class td{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let r=this;this.promise.then(e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null}),this.promise.then=e=>{let t;let s=new Promise(e=>{r.subscribe(e),t=e}).then(e);return s.cancel=function(){r.unsubscribe(t)},s},e(function(e,s,n){r.reason||(r.reason=new eL(e,s,n),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new td(function(t){e=t}),cancel:e}}}let tf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(tf).forEach(([e,t])=>{tf[t]=e});let tp=function e(t){let r=new th(t),s=p(th.prototype.request,r);return Y.extend(s,th.prototype,r,{allOwnKeys:!0}),Y.extend(s,r,null,{allOwnKeys:!0}),s.create=function(r){return e(eV(t,r))},s}(eT);tp.Axios=th,tp.CanceledError=eL,tp.CancelToken=td,tp.isCancel=eU,tp.VERSION=to,tp.toFormData=ea,tp.AxiosError=Q,tp.Cancel=tp.CanceledError,tp.all=function(e){return Promise.all(e)},tp.spread=function(e){return function(t){return e.apply(null,t)}},tp.isAxiosError=function(e){return Y.isObject(e)&&!0===e.isAxiosError},tp.mergeConfig=eV,tp.AxiosHeaders=e$,tp.formToJSON=e=>eE(Y.isHTMLForm(e)?new FormData(e):e),tp.getAdapter=ts,tp.HttpStatusCode=tf,tp.default=tp;var tg=tp},91116:function(e,t,r){r.d(t,{x0:function(){return s}});let s=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,t)=>((t&=63)<36?e+=t.toString(36):t<62?e+=(t-26).toString(36).toUpperCase():t>62?e+="-":e+="_",e),"")}}]);