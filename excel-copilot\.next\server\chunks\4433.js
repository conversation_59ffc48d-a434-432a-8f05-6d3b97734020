"use strict";exports.id=4433,exports.ids=[4433],exports.modules={24433:(e,r,a)=>{a.d(r,{OI:()=>o});var t=a(52972),n=a(43895);process.env.AUTH_GOOGLE_CLIENT_ID,process.env.AUTH_GOOGLE_CLIENT_SECRET,process.env.AUTH_GITHUB_CLIENT_ID,process.env.AUTH_GITHUB_CLIENT_SECRET,process.env.AUTH_NEXTAUTH_SECRET,process.env.AUTH_NEXTAUTH_URL,process.env.DB_DATABASE_URL,process.env.AI_VERTEX_PROJECT_ID,process.env.AI_VERTEX_LOCATION;let i={all:["DB_DATABASE_URL","AUTH_NEXTAUTH_SECRET","AUTH_NEXTAUTH_URL"],production:["AUTH_GOOGLE_CLIENT_ID","AUTH_GOOGLE_CLIENT_SECRET","AI_VERTEX_PROJECT_ID"]};function o(e=!1){let r=t.Vi.IS_PRODUCTION,a="1"===process.env.VERCEL,o="true"===process.env.DEV_DISABLE_VALIDATION;if(a&&!r)return n.kg.warn("Valida\xe7\xe3o de ambiente: Ignorando valida\xe7\xf5es para build Vercel (desenvolvimento)"),{valid:!0,missing:[],details:{environment:t.Vi.NODE_ENV,strict:e,checkedVars:0,validationDisabled:!0,reason:"Vercel build-time bypass"}};if(r&&o)n.kg.error("SEGURAN\xc7A: Tentativa de bypass de valida\xe7\xe3o em produ\xe7\xe3o bloqueada - DEV_DISABLE_VALIDATION ignorado");else if(o&&!r)return n.kg.warn("Valida\xe7\xe3o de ambiente: Bypass ativado para desenvolvimento (DEV_DISABLE_VALIDATION=true)"),{valid:!0,missing:[],details:{environment:t.Vi.NODE_ENV,strict:e,checkedVars:0,validationDisabled:!0,reason:"Development bypass"}};let s=[...i.all];(r||e)&&s.push(...i.production);let l=s.filter(e=>!process.env[e]),d={valid:0===l.length,missing:l,details:{environment:t.Vi.NODE_ENV,strict:e,checkedVars:s.length}};return d.valid||(r?n.kg.error(`Vari\xe1veis de ambiente necess\xe1rias n\xe3o configuradas: ${l.join(", ")}`):n.kg.warn(`Ambiente de desenvolvimento com vari\xe1veis ausentes: ${l.join(", ")}`)),d}},43895:(e,r,a)=>{let t;a.d(r,{kg:()=>c});var n=a(99557),i=a.n(n);function o(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],a={};return Object.keys(e).forEach(t=>{r.includes(t)||(a[t]=e[t])}),{normalizedError:e,extractedMetadata:a}}return"object"==typeof e&&null!==e?{normalizedError:o(e),extractedMetadata:e}:{normalizedError:o(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let d={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:i().stdSerializers.err,error:i().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:i().stdSerializers.err,error:i().stdSerializers.err}}};try{let e=d.production;t=i()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),t=i()({level:"info",formatters:{level:e=>({level:e})}})}let c={trace:(e,r)=>{t.trace(r||{},e)},debug:(e,r)=>{t.debug(r||{},e)},info:(e,r)=>{t.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:a}=s(r);t.warn(a,e)}else t.warn(l(r)||{},e)},error:(e,r,a)=>{let{normalizedError:n,extractedMetadata:i}=s(r),o={...a||{},...i,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};t.error(o,e)},fatal:(e,r,a)=>{let{normalizedError:n,extractedMetadata:i}=s(r),o={...a||{},...i,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};t.fatal(o,e)},createChild:e=>{let r=t.child(e);return{trace:(e,a)=>{r.trace(a||{},e)},debug:(e,a)=>{r.debug(a||{},e)},info:(e,a)=>{r.info(a||{},e)},warn:(e,a)=>{if(a instanceof Error||"object"==typeof a&&null!==a){let{extractedMetadata:t}=s(a);r.warn(t,e)}else r.warn(l(a)||{},e)},error:(e,a,t)=>{let{normalizedError:n,extractedMetadata:i}=s(a),o={...t||{},...i,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.error(o,e)},fatal:(e,a,t)=>{let{normalizedError:n,extractedMetadata:i}=s(a),o={...t||{},...i,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.fatal(o,e)}}},child:function(e){return this.createChild(e)}}}};