"use strict";(()=>{var e={};e.id=8854,e.ids=[8854],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},52345:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>F,patchFetch:()=>W,requestAsyncStorage:()=>L,routeModule:()=>M,serverHooks:()=>_,staticGenerationAsyncStorage:()=>T});var o={};t.r(o),t.d(o,{DELETE:()=>q,GET:()=>O,PATCH:()=>z,POST:()=>S,dynamic:()=>C,runtime:()=>R});var a=t(49303),i=t(88716),s=t(60670),n=t(43895);class c{constructor(){this.cache=new Map,this.isConnected=!0,setInterval(()=>this.cleanExpired(),3e5)}static getInstance(){return c.instance||(c.instance=new c),c.instance}cleanExpired(){let e=Date.now();for(let[r,t]of this.cache.entries())t.expiry&&t.expiry<e&&this.cache.delete(r)}async get(e){try{let r=this.cache.get(e);if(!r)return n.kg.debug("❌ Cache miss:",e),null;if(r.expiry&&r.expiry<Date.now())return this.cache.delete(e),n.kg.debug("⏰ Cache expired:",e),null;return n.kg.debug("✅ Cache hit:",e),r.value}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao buscar no cache:",r,{key:e}),null}}async set(e,r,t){try{let o={value:r,expiry:t?Date.now()+1e3*t:void 0};return this.cache.set(e,o),n.kg.debug("\uD83D\uDCBE Cache set:",e,t?`TTL: ${t}s`:"sem TTL"),!0}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao salvar no cache:",r,{key:e}),!1}}async del(e){try{let r=this.cache.has(e);return this.cache.delete(e),n.kg.debug("\uD83D\uDDD1️ Cache deleted:",e,`Existed: ${r}`),r}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao deletar do cache:",r,{key:e}),!1}}async exists(e){try{let r=this.cache.get(e);if(!r)return!1;if(r.expiry&&r.expiry<Date.now())return this.cache.delete(e),!1;return!0}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao verificar exist\xeancia no cache:",r,{key:e}),!1}}async flushPattern(e){try{let r=new RegExp(e.replace(/\*/g,".*")),t=0;for(let e of this.cache.keys())r.test(e)&&(this.cache.delete(e),t++);return n.kg.info("\uD83E\uDDF9 Cache pattern flushed:",e,`Deleted: ${t} keys`),t}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao limpar pattern do cache:",r,{pattern:e}),0}}async getStats(){try{return{connected:this.isConnected,connectionAttempts:0,memoryUsage:`${Math.round(process.memoryUsage().heapUsed/1024/1024)}MB`,keyCount:this.cache.size}}catch(e){return n.kg.error("\uD83D\uDCA5 Erro ao obter stats do cache:",e),{connected:this.isConnected,connectionAttempts:0}}}async disconnect(){this.cache.clear(),n.kg.info("\uD83D\uDC4B Cache em mem\xf3ria limpo")}}let d=c.getInstance(),u={WORKBOOK_METADATA:900,TEMPLATES:86400,POPULAR_COMMANDS:7200},l={WORKBOOK_LIST:e=>`workbooks:list:${e}`,TEMPLATES:()=>"templates:popular",POPULAR_AI_COMMANDS:()=>"ai:commands:popular"};class k{async get(e){try{let r=await d.get(e);if(null===r)return this.metrics.misses++,null;return this.metrics.hits++,JSON.parse(r)}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao buscar do cache:",r,{key:e}),null}}async set(e,r,t){try{let o=JSON.stringify(r),a=await d.set(e,o,t);return a?this.metrics.sets++:this.metrics.errors++,a}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao salvar no cache:",r,{key:e}),!1}}async delete(e){try{let r=await d.del(e);return r&&this.metrics.deletes++,r}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao deletar do cache:",r,{key:e}),!1}}async exists(e){try{return await d.exists(e)}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao verificar exist\xeancia no cache:",r,{key:e}),!1}}async getOrSet(e,r,t){let o=await this.get(e);if(null!==o)return o;try{let o=await r();return await this.set(e,o,t),o}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao executar fun\xe7\xe3o para cache:",r,{key:e}),null}}async invalidatePattern(e){try{let r=await d.flushPattern(e);return this.metrics.deletes+=r,n.kg.info("\uD83E\uDDF9 Cache invalidado por padr\xe3o:",e,`${r} chaves`),r}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao invalidar padr\xe3o do cache:",r,{pattern:e}),0}}async invalidateUserCache(e){let r=[`dashboard:metrics:${e}`,`workbooks:list:${e}`,`activity:recent:${e}`,`session:${e}`,`permissions:${e}:*`];await Promise.all(r.map(e=>this.invalidatePattern(e))),n.kg.info("\uD83D\uDC64 Cache do usu\xe1rio invalidado:",e)}async invalidateWorkbookCache(e){let r=[`workbook:meta:${e}`,`permissions:*:${e}`];await Promise.all(r.map(e=>this.invalidatePattern(e))),n.kg.info("\uD83D\uDCCA Cache do workbook invalidado:",e)}async warmupPopularTemplates(){try{n.kg.info("\uD83D\uDD25 Iniciando pr\xe9-aquecimento de templates populares..."),await this.set(l.TEMPLATES(),[{id:"financial",name:"Controle Financeiro",usage:1250},{id:"dashboard",name:"Dashboard",usage:980},{id:"calculator",name:"Calculadora",usage:750}],u.TEMPLATES),n.kg.info("✅ Templates populares pr\xe9-aquecidos")}catch(e){n.kg.error("\uD83D\uDCA5 Erro no pr\xe9-aquecimento de templates:",e)}}async warmupPopularAICommands(){try{n.kg.info("\uD83D\uDD25 Iniciando pr\xe9-aquecimento de comandos de IA populares..."),await this.set(l.POPULAR_AI_COMMANDS(),["Criar planilha de controle financeiro com categorias","Adicionar gr\xe1fico de barras para vendas mensais","Calcular soma total da coluna","Criar tabela din\xe2mica com dados","Formatar c\xe9lulas como moeda"],u.POPULAR_COMMANDS),n.kg.info("✅ Comandos de IA populares pr\xe9-aquecidos")}catch(e){n.kg.error("\uD83D\uDCA5 Erro no pr\xe9-aquecimento de comandos de IA:",e)}}async warmupCache(){n.kg.info("\uD83D\uDE80 Iniciando pr\xe9-aquecimento completo do cache..."),await Promise.all([this.warmupPopularTemplates(),this.warmupPopularAICommands()]),n.kg.info("\uD83C\uDF89 Pr\xe9-aquecimento do cache conclu\xeddo")}getMetrics(){let e=this.metrics.hits+this.metrics.misses,r=e>0?this.metrics.hits/e*100:0;return{...this.metrics,hitRate:Math.round(100*r)/100}}resetMetrics(){this.metrics={hits:0,misses:0,sets:0,deletes:0,errors:0}}async getDetailedStats(){return{redis:await d.getStats(),cache:this.getMetrics(),timestamp:new Date().toISOString()}}constructor(){this.metrics={hits:0,misses:0,sets:0,deletes:0,errors:0}}}let m=new k;var h=t(21270),p=t(99747),g=t(39762),w=t(62091),b=t(36044),D=t(63841),f=t(21954);async function y(e){try{let r=(0,f.ZR)(e.userId,e.endpoint,e.count??1,e.workbookId,e.billable??!0);await D.prisma.apiUsage.create({data:r})}catch(r){n.kg.error("Erro ao registrar uso da API",{userId:e.userId,endpoint:e.endpoint,error:r})}}var v=t(4579);let I={addJob:()=>Promise.resolve({id:"mock-job"}),getJobStatus:()=>Promise.resolve({status:"completed"})},x={sendNotification:()=>Promise.resolve(),scheduleNotification:()=>Promise.resolve()},A={MAX_WORKBOOKS:{free:5,pro_monthly:1/0,pro_annual:1/0},MAX_CELLS:{free:1e3,pro_monthly:5e4,pro_annual:1/0}},E={async getUserWorkbooks(e,r){try{let t=r?.limit||20,o=r?.page||0,a=r?.sortBy||"updatedAt",i=r?.sortOrder||"desc",s=r?.dateRange||"all",n={};if("all"!==s){let e;let r=new Date;switch(s){case"today":e=new Date(r.getFullYear(),r.getMonth(),r.getDate());break;case"week":e=new Date(r.getTime()-6048e5);break;case"month":e=new Date(r.getFullYear(),r.getMonth(),1);break;case"year":e=new Date(r.getFullYear(),0,1);break;default:e=new Date(0)}n={updatedAt:{gte:e}}}let c={};c="sheets"===a?{sheets:{_count:i}}:{[a]:i};let[d,u]=await Promise.all([(0,v.jw)(()=>D.prisma.workbook.findMany({where:{userId:e,...n,...r?.isPublic!==void 0&&{isPublic:r.isPublic},...r?.search&&{OR:[{name:{contains:r.search,mode:"insensitive"}},{description:{contains:r.search,mode:"insensitive"}}]}},include:{sheets:{select:{id:!0,name:!0,updatedAt:!0}}},orderBy:c,take:t,skip:o*t}),["user-workbooks",e,r],{ttl:60}),(0,v.jw)(()=>D.prisma.workbook.count({where:{userId:e,...n,...r?.isPublic!==void 0&&{isPublic:r.isPublic},...r?.search&&{OR:[{name:{contains:r.search,mode:"insensitive"}},{description:{contains:r.search,mode:"insensitive"}}]}}}),["user-workbooks-count",e,r],{ttl:60})]),l=d;(r?.minSheets!==void 0||r?.maxSheets!==void 0)&&(l=d.filter(e=>{let t=e.sheets.length,o=r?.minSheets??0,a=r?.maxSheets??1/0;return t>=o&&t<=a}));let k=l,m=r?.minSheets!==void 0||r?.maxSheets!==void 0?l.length:u,h=Math.ceil(m/t);return{workbooks:k,pagination:{page:o,limit:t,totalItems:m,totalPages:h,hasMore:o<h-1}}}catch(r){throw n.kg.error("Erro ao buscar workbooks do usu\xe1rio",{userId:e,error:r}),Error("Falha ao buscar workbooks")}},async getPublicWorkbooks(e){try{return await (0,v.jw)(()=>D.prisma.workbook.findMany({where:{isPublic:!0,...e?.search&&{OR:[{name:{contains:e.search,mode:"insensitive"}},{description:{contains:e.search,mode:"insensitive"}}]}},include:{user:{select:{id:!0,name:!0,image:!0}},sheets:{select:{id:!0,name:!0}}},orderBy:{updatedAt:"desc"},take:e?.limit||20,skip:e?.page?e.page*(e.limit||20):0}),["public-workbooks",e],{ttl:300})}catch(e){throw n.kg.error("Erro ao buscar workbooks p\xfablicos",{error:e}),Error("Falha ao buscar workbooks p\xfablicos")}},async getWorkbookById(e,r){try{let t=await D.prisma.workbook.findUnique({where:{id:e},include:{sheets:!0,user:{select:{id:!0,name:!0,image:!0}}}});if(!t)throw Error("Workbook n\xe3o encontrado");if(r&&t.userId!==r&&!t.isPublic)throw await D.prisma.securityLog.create({data:{userId:r,eventType:"unauthorized_workbook_access",details:JSON.stringify({workbookId:e,ownerId:t.userId}),timestamp:new Date}}),Error("Usu\xe1rio n\xe3o autorizado a acessar este workbook");return t}catch(t){throw n.kg.error("Erro ao buscar workbook por ID",{workbookId:e,userId:r,error:t}),t}},async createWorkbook(e,r){try{let t=await D.prisma.$transaction(async t=>{let o=await t.subscription.findFirst({where:{userId:r,OR:[{status:"active"},{status:"trialing"}]},select:{plan:!0,apiCallsLimit:!0}}),a=await t.workbook.count({where:{userId:r}}),i={FREE:"free"},s=o?.plan||i.FREE,c=A.MAX_WORKBOOKS[s]||A.MAX_WORKBOOKS[i.FREE];if(a>=c)throw Error(`Limite de planilhas excedido. Voc\xea tem ${a} de ${c} planilhas permitidas para seu plano.`);if(e.initialData){let r=function(e){if(!e)return 0;let r=0;return e.rows&&Array.isArray(e.rows)?e.rows.forEach(e=>{e&&e.cells&&Array.isArray(e.cells)&&(r+=e.cells.filter(e=>e&&null!==e.value&&void 0!==e.value).length)}):e.cells&&"object"==typeof e.cells&&(r=Object.keys(e.cells).length),r}(e.initialData),t=A.MAX_CELLS[s]||A.MAX_CELLS[i.FREE];if(r>t)throw Error(`Os dados iniciais cont\xeam ${r} c\xe9lulas, excedendo o limite de ${t} do seu plano.`)}let d=await t.workbook.create({data:{name:e.name,description:e.description||"",isPublic:e.isPublic??!1,userId:r,sheets:{create:{name:"Planilha 1",data:e.initialData?JSON.stringify(e.initialData):null}}},include:{sheets:!0}});if(e.aiCommand){n.kg.info("Workbook criado com comando de IA - iniciando processamento ass\xedncrono",{workbookId:d.id,userId:r,aiCommand:e.aiCommand.substring(0,100)+"..."});try{await I.initialize();let t=await I.addAIProcessingJob({workbookId:d.id,userId:r,command:e.aiCommand,context:{headers:[],rowCount:0,colCount:0,existingData:e.initialData},priority:"normal"});await x.sendAIProcessingStarted(r,d.id,e.aiCommand),n.kg.info("✅ Job de IA adicionado \xe0 fila",{workbookId:d.id,jobId:t.id,userId:r})}catch(e){n.kg.error("\uD83D\uDCA5 Erro ao adicionar job de IA \xe0 fila:",e,{workbookId:d.id,userId:r})}}return d});return n.kg.info("Novo workbook criado",{workbookId:t.id,userId:r}),t}catch(t){throw n.kg.error("Erro ao criar workbook",{userId:r,data:e,error:t}),t instanceof Error?t:Error("Falha ao criar workbook")}},async updateWorkbook(e,r){try{let t=await D.prisma.$transaction(async t=>{if(!await t.workbook.findFirst({where:{id:e.id,userId:r}}))throw await t.securityLog.create({data:{userId:r,eventType:"unauthorized_workbook_update",details:JSON.stringify({workbookId:e.id}),timestamp:new Date}}),Error("Workbook n\xe3o encontrado ou n\xe3o pertence ao usu\xe1rio");let o={};void 0!==e.name&&(o.name=e.name),void 0!==e.description&&(o.description=e.description),void 0!==e.isPublic&&(o.isPublic=e.isPublic);let a=await t.workbook.update({where:{id:e.id},data:o,include:{sheets:!0}});return await t.userActionLog.create({data:{userId:r,action:"workbook_updated",details:JSON.stringify({workbookId:e.id,fields:Object.keys(o)}),timestamp:new Date}}),a});return n.kg.info("Workbook atualizado",{workbookId:t.id,userId:r}),t}catch(t){throw n.kg.error("Erro ao atualizar workbook",{workbookId:e.id,userId:r,error:t}),t}},async deleteWorkbook(e,r){try{return await D.prisma.$transaction(async t=>{if(!await t.workbook.findFirst({where:{id:e,userId:r}}))throw await t.securityLog.create({data:{userId:r,eventType:"unauthorized_workbook_deletion",details:JSON.stringify({workbookId:e}),timestamp:new Date}}),Error("Workbook n\xe3o encontrado ou n\xe3o pertence ao usu\xe1rio");await t.workbook.delete({where:{id:e}}),await t.userActionLog.create({data:{userId:r,action:"workbook_deleted",details:JSON.stringify({workbookId:e}),timestamp:new Date}})}),n.kg.info("Workbook exclu\xeddo",{workbookId:e,userId:r}),!0}catch(t){throw n.kg.error("Erro ao excluir workbook",{workbookId:e,userId:r,error:t}),t}}};var P=t(82840);let C="force-dynamic",R="nodejs",O=(0,p.x)([w.VF,g.Ev,h.l],async(e,r)=>{try{let t;n.kg.info("\uD83D\uDE80 API /workbooks: Iniciando processamento",{userId:r.userId});let o=e.nextUrl.searchParams,a={isPublic:o.has("isPublic")?"true"===o.get("isPublic"):void 0,search:o.get("search")||void 0,sortBy:o.get("sortBy")||void 0,sortOrder:o.get("sortOrder")||void 0,dateRange:o.get("dateRange")||void 0,minSheets:o.has("minSheets")?parseInt(o.get("minSheets"),10):void 0,maxSheets:o.has("maxSheets")?parseInt(o.get("maxSheets"),10):void 0,limit:o.has("limit")?parseInt(o.get("limit"),10):void 0,page:o.has("page")?parseInt(o.get("page"),10):void 0};n.kg.info("\uD83D\uDCCB API /workbooks: Filtros extra\xeddos",a);let i=b.S9.safeParse(a);if(!i.success)return n.kg.warn("❌ API /workbooks: Filtros inv\xe1lidos",i.error),(0,g.ng)(P.R.badRequest("Par\xe2metros de filtro inv\xe1lidos",i.error.format()),r);n.kg.info("✅ API /workbooks: Filtros validados",i.data);let s=l.WORKBOOK_LIST(r.userId);i.data.search||i.data.isPublic||"updatedAt"!==i.data.sortBy||"desc"!==i.data.sortOrder||"all"!==i.data.dateRange||i.data.minSheets||i.data.maxSheets?(n.kg.info("\uD83D\uDD0D API /workbooks: Buscando workbooks (query complexa)..."),t=await E.getUserWorkbooks(r.userId,i.data)):t=await m.getOrSet(s,()=>E.getUserWorkbooks(r.userId,i.data),u.WORKBOOK_METADATA),n.kg.info("\uD83D\uDCCA API /workbooks: Workbooks encontrados",{count:t?.workbooks?.length||0,data:t}),await y({userId:r.userId,endpoint:"workbooks/list",count:1}),n.kg.info("\uD83C\uDFAF API /workbooks: Criando resposta de sucesso");let c=P.R.success(t);return n.kg.info("\uD83D\uDCE4 API /workbooks: Resposta criada",{status:c.status,hasBody:!!c.body}),(0,g.ng)(c,r)}catch(e){if(n.kg.error("❌ API /workbooks: Erro no processamento",e),e instanceof Error)return(0,g.ng)(P.R.error(e.message),r);return(0,g.ng)(P.R.error("Erro ao buscar workbooks"),r)}}),S=(0,p.x)([w.VF,g.Ev,h.l],async(e,r)=>{try{let t=await e.json(),o=b.t9.safeParse(t);if(!o.success)return(0,g.ng)(P.R.badRequest("Dados inv\xe1lidos para criar workbook",o.error.format()),r);let a=await E.createWorkbook(o.data,r.userId);return await m.invalidateUserCache(r.userId),(0,v.e5)(`user-workbooks:${r.userId}`),await y({userId:r.userId,endpoint:"workbooks/create",count:1,workbookId:a.id}),(0,g.ng)(P.R.success(a,void 0,201),r)}catch(e){if(e instanceof Error)return(0,g.ng)(P.R.error(e.message),r);return(0,g.ng)(P.R.error("Erro ao criar workbook"),r)}}),z=(0,p.x)([w.VF,g.Ev,h.l],async(e,r)=>{try{let t=await e.json(),o=b.OR.safeParse(t);if(!o.success)return(0,g.ng)(P.R.badRequest("Dados inv\xe1lidos para atualizar workbook",o.error.format()),r);let a=await E.updateWorkbook(o.data,r.userId);return(0,v.e5)(`user-workbooks:${r.userId}`),await y({userId:r.userId,endpoint:"workbooks/update",count:1,workbookId:o.data.id}),(0,g.ng)(P.R.success(a),r)}catch(e){if(e instanceof Error)return(0,g.ng)(P.R.error(e.message),r);return(0,g.ng)(P.R.error("Erro ao atualizar workbook"),r)}}),q=(0,p.x)([w.VF,g.Ev,h.l],async(e,r)=>{try{let t=e.nextUrl.searchParams.get("id");if(!t)return(0,g.ng)(P.R.badRequest("ID do workbook \xe9 obrigat\xf3rio"),r);if(!b.ou.safeParse({id:t}).success)return(0,g.ng)(P.R.badRequest("ID de workbook inv\xe1lido"),r);return await E.deleteWorkbook(t,r.userId),(0,v.e5)(`user-workbooks:${r.userId}`),await y({userId:r.userId,endpoint:"workbooks/delete",count:1,workbookId:t}),(0,g.ng)(P.R.success({success:!0,message:"Workbook exclu\xeddo com sucesso"}),r)}catch(e){if(e instanceof Error)return(0,g.ng)(P.R.error(e.message),r);return(0,g.ng)(P.R.error("Erro ao excluir workbook"),r)}}),M=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/workbooks/route",pathname:"/api/workbooks",filename:"route",bundlePath:"app/api/workbooks/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:L,staticGenerationAsyncStorage:T,serverHooks:_}=M,F="/api/workbooks/route";function W(){return(0,s.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:T})}},62091:(e,r,t)=>{t.d(r,{VF:()=>c});var o=t(43895),a=t(82840);let i=new Map;function s(){let e=Date.now();for(let[r,t]of i.entries())e>t.resetAt&&i.delete(r)}function n(e=100,r=6e4,t=e=>e.ip||"unknown"){return async(n,c)=>{let d=t(n),u=Date.now();.01>Math.random()&&s();let l=i.get(d);if((!l||u>l.resetAt)&&(l={count:0,resetAt:u+r}),l.count+=1,i.set(d,l),c.headers.set("X-RateLimit-Limit",e.toString()),c.headers.set("X-RateLimit-Remaining",Math.max(0,e-l.count).toString()),c.headers.set("X-RateLimit-Reset",Math.ceil(l.resetAt/1e3).toString()),l.count>e){o.kg.warn(`Rate limit excedido para ${d}`,{path:n.nextUrl.pathname,method:n.method,key:d,count:l.count,limit:e});let r=Math.ceil((l.resetAt-u)/1e3);return a.R.tooManyRequests("Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",r)}}}"undefined"!=typeof setInterval&&setInterval(s,3e5);let c=n(100,6e4);n(20,6e4),n(30,6e4)},36044:(e,r,t)=>{t.d(r,{I9:()=>c,OR:()=>s,S9:()=>n,ou:()=>a,t9:()=>i});var o=t(7410);let a=o.z.object({id:o.z.string().cuid({message:"ID de workbook inv\xe1lido"})}),i=o.z.object({name:o.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),isPublic:o.z.boolean().default(!1),initialData:o.z.any().optional(),aiCommand:o.z.string().max(1e3,{message:"Comando de IA deve ter no m\xe1ximo 1000 caracteres"}).optional()}),s=o.z.object({id:o.z.string().cuid({message:"ID de workbook inv\xe1lido"}),name:o.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}).optional(),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),isPublic:o.z.boolean().optional()});o.z.object({name:o.z.string().min(1,{message:"Nome da folha \xe9 obrigat\xf3rio"}).max(50,{message:"Nome da folha deve ter no m\xe1ximo 50 caracteres"}),workbookId:o.z.string().cuid({message:"ID de workbook inv\xe1lido"}),data:o.z.any().optional()});let n=o.z.object({isPublic:o.z.boolean().optional(),search:o.z.string().optional(),sortBy:o.z.enum(["name","createdAt","updatedAt","sheets"]).default("updatedAt"),sortOrder:o.z.enum(["asc","desc"]).default("desc"),dateRange:o.z.enum(["all","today","week","month","year"]).default("all"),minSheets:o.z.number().int().nonnegative().optional(),maxSheets:o.z.number().int().nonnegative().optional(),limit:o.z.number().int().positive().default(10),page:o.z.number().int().nonnegative().default(0)}),c=o.z.object({row:o.z.number().int().nonnegative(),col:o.z.number().int().nonnegative(),value:o.z.any(),formula:o.z.string().optional(),style:o.z.any().optional()})},21954:(e,r,t)=>{function o(e,r,t,o,a){return{userId:e,endpoint:r,count:t,workbookId:o||null,billable:a}}function a(e,r,t,o){return{name:e,userId:r,description:t||null,sheets:{create:o}}}function i(e,r,t,o){return{userId:e,message:r,response:t,workbookId:o||null}}t.d(r,{PL:()=>i,ZR:()=>o,fp:()=>a})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628,5590],()=>t(52345));module.exports=o})();