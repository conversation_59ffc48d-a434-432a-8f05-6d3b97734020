"use strict";exports.id=180,exports.ids=[180],exports.modules={94019:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},10321:(e,t,r)=>{r.d(t,{Dx:()=>Q,aU:()=>et,dk:()=>ee,fC:()=>J,l_:()=>G,x8:()=>er,zt:()=>B});var n=r(17577),o=r(60962),a=r(82561),i=r(48051),s=r(70545),l=r(93095),d=r(825),u=r(83078),c=r(9815),p=r(45226),f=r(55049),v=r(52067),w=r(65819),m=r(6009),y=r(10326),x="ToastProvider",[E,T,h]=(0,s.B)("Toast"),[g,b]=(0,l.b)("Toast",[h]),[C,P]=g(x),R=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:s}=e,[l,d]=n.useState(null),[u,c]=n.useState(0),p=n.useRef(!1),f=n.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${x}\`. Expected non-empty \`string\`.`),(0,y.jsx)(E.Provider,{scope:t,children:(0,y.jsx)(C,{scope:t,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:d,onToastAdd:n.useCallback(()=>c(e=>e+1),[]),onToastRemove:n.useCallback(()=>c(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:f,children:s})})};R.displayName=x;var j="ToastViewport",L=["F8"],D="toast.viewportPause",M="toast.viewportResume",N=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=L,label:a="Notifications ({hotkey})",...s}=e,l=P(j,r),u=T(r),c=n.useRef(null),f=n.useRef(null),v=n.useRef(null),w=n.useRef(null),m=(0,i.e)(t,w,l.onViewportChange),x=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),h=l.toastCount>0;n.useEffect(()=>{let e=e=>{0!==o.length&&o.every(t=>e[t]||e.code===t)&&w.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=c.current,t=w.current;if(h&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(M);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},a=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",a),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[h,l.isClosePausedRef]);let g=n.useCallback(({tabbingDirection:e})=>{let t=u().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[u]);return n.useEffect(()=>{let e=w.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n){f.current?.focus();return}let o=g({tabbingDirection:n?"backwards":"forwards"}),a=o.findIndex(e=>e===r);Z(o.slice(a+1))?t.preventDefault():n?f.current?.focus():v.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[u,g]),(0,y.jsxs)(d.I0,{ref:c,role:"region","aria-label":a.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:h?void 0:"none"},children:[h&&(0,y.jsx)(S,{ref:f,onFocusFromOutsideViewport:()=>{Z(g({tabbingDirection:"forwards"}))}}),(0,y.jsx)(E.Slot,{scope:r,children:(0,y.jsx)(p.WV.ol,{tabIndex:-1,...s,ref:m})}),h&&(0,y.jsx)(S,{ref:v,onFocusFromOutsideViewport:()=>{Z(g({tabbingDirection:"backwards"}))}})]})});N.displayName=j;var F="ToastFocusProxy",S=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,a=P(F,r);return(0,y.jsx)(m.TX,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;a.viewport?.contains(t)||n()}})});S.displayName=F;var k="Toast",I=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:i,...s}=e,[l,d]=(0,v.T)({prop:n,defaultProp:o??!0,onChange:i,caller:k});return(0,y.jsx)(c.z,{present:r||l,children:(0,y.jsx)(K,{open:l,...s,ref:t,onClose:()=>d(!1),onPause:(0,f.W)(e.onPause),onResume:(0,f.W)(e.onResume),onSwipeStart:(0,a.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,a.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),d(!1)})})})});I.displayName=k;var[A,W]=g(k,{onClose(){}}),K=n.forwardRef((e,t)=>{let{__scopeToast:r,type:s="foreground",duration:l,open:u,onClose:c,onEscapeKeyDown:v,onPause:w,onResume:m,onSwipeStart:x,onSwipeMove:T,onSwipeCancel:h,onSwipeEnd:g,...b}=e,C=P(k,r),[R,j]=n.useState(null),L=(0,i.e)(t,e=>j(e)),N=n.useRef(null),F=n.useRef(null),S=l||C.duration,I=n.useRef(0),W=n.useRef(S),K=n.useRef(0),{onToastAdd:X,onToastRemove:_}=C,O=(0,f.W)(()=>{R?.contains(document.activeElement)&&C.viewport?.focus(),c()}),$=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(K.current),I.current=new Date().getTime(),K.current=window.setTimeout(O,e))},[O]);n.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{$(W.current),m?.()},r=()=>{let e=new Date().getTime()-I.current;W.current=W.current-e,window.clearTimeout(K.current),w?.()};return e.addEventListener(D,r),e.addEventListener(M,t),()=>{e.removeEventListener(D,r),e.removeEventListener(M,t)}}},[C.viewport,S,w,m,$]),n.useEffect(()=>{u&&!C.isClosePausedRef.current&&$(S)},[u,S,C.isClosePausedRef,$]),n.useEffect(()=>(X(),()=>_()),[X,_]);let H=n.useMemo(()=>R?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(R):null,[R]);return C.viewport?(0,y.jsxs)(y.Fragment,{children:[H&&(0,y.jsx)(V,{__scopeToast:r,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:H}),(0,y.jsx)(A,{scope:r,onClose:O,children:o.createPortal((0,y.jsx)(E.ItemSlot,{scope:r,children:(0,y.jsx)(d.fC,{asChild:!0,onEscapeKeyDown:(0,a.M)(v,()=>{C.isFocusedToastEscapeKeyDownRef.current||O(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,y.jsx)(p.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":u?"open":"closed","data-swipe-direction":C.swipeDirection,...b,ref:L,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.M)(e.onKeyDown,e=>{"Escape"!==e.key||(v?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,O()))}),onPointerDown:(0,a.M)(e.onPointerDown,e=>{0===e.button&&(N.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.M)(e.onPointerMove,e=>{if(!N.current)return;let t=e.clientX-N.current.x,r=e.clientY-N.current.y,n=!!F.current,o=["left","right"].includes(C.swipeDirection),a=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,i=o?a(0,t):0,s=o?0:a(0,r),l="touch"===e.pointerType?10:2,d={x:i,y:s},u={originalEvent:e,delta:d};n?(F.current=d,q("toast.swipeMove",T,u,{discrete:!1})):Y(d,C.swipeDirection,l)?(F.current=d,q("toast.swipeStart",x,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(N.current=null)}),onPointerUp:(0,a.M)(e.onPointerUp,e=>{let t=F.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),F.current=null,N.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Y(t,C.swipeDirection,C.swipeThreshold)?q("toast.swipeEnd",g,n,{discrete:!0}):q("toast.swipeCancel",h,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),V=e=>{let{__scopeToast:t,children:r,...o}=e,a=P(k,t),[i,s]=n.useState(!1),[l,d]=n.useState(!1);return function(e=()=>{}){let t=(0,f.W)(e);(0,w.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>s(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>d(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,y.jsx)(u.h,{asChild:!0,children:(0,y.jsx)(m.TX,{...o,children:i&&(0,y.jsxs)(y.Fragment,{children:[a.label," ",r]})})})},X=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(p.WV.div,{...n,ref:t})});X.displayName="ToastTitle";var _=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(p.WV.div,{...n,ref:t})});_.displayName="ToastDescription";var O="ToastAction",$=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,y.jsx)(z,{altText:r,asChild:!0,children:(0,y.jsx)(U,{...n,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${O}\`. Expected non-empty \`string\`.`),null)});$.displayName=O;var H="ToastClose",U=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=W(H,r);return(0,y.jsx)(z,{asChild:!0,children:(0,y.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,a.M)(e.onClick,o.onClose)})})});U.displayName=H;var z=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,y.jsx)(p.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function q(e,t,r,{discrete:n}){let o=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,p.jH)(o,a):o.dispatchEvent(a)}var Y=(e,t,r=0)=>{let n=Math.abs(e.x),o=Math.abs(e.y),a=n>o;return"left"===t||"right"===t?a&&n>r:!a&&o>r};function Z(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var B=R,G=N,J=I,Q=X,ee=_,et=$,er=U},6009:(e,t,r)=>{r.d(t,{C2:()=>i,TX:()=>s,fC:()=>l});var n=r(17577),o=r(45226),a=r(10326),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),s=n.forwardRef((e,t)=>(0,a.jsx)(o.WV.span,{...e,ref:t,style:{...i,...e.style}}));s.displayName="VisuallyHidden";var l=s}};