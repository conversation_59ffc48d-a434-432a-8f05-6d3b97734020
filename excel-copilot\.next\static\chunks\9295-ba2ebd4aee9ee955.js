"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9295],{42421:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},14392:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},74109:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},24258:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},62361:function(e,t,n){n.d(t,{u:function(){return r}});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},38364:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(2265),l=n(25171),o=n(57437),a=r.forwardRef((e,t)=>(0,o.jsx)(l.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},17549:function(e,t,n){n.d(t,{$G:function(){return ez},B4:function(){return eE},JO:function(){return eP},VY:function(){return eW},Z0:function(){return eO},ZA:function(){return e_},__:function(){return eA},ck:function(){return eH},eT:function(){return eB},fC:function(){return eV},h_:function(){return eN},l_:function(){return eL},u_:function(){return eK},wU:function(){return eF},xz:function(){return eI}});var r=n(2265),l=n(54887),o=n(62361),a=n(78149),i=n(38620),u=n(1584),s=n(98324),d=n(87513),c=n(53938),f=n(20589),p=n(80467),v=n(53201),h=n(25510),m=n(7715),w=n(25171),g=n(71538),x=n(75137),y=n(91715),b=n(1336),C=n(47250),S=n(31725),M=n(78369),j=n(49418),T=n(57437),k=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],D="Select",[V,I,E]=(0,i.B)(D),[P,N]=(0,s.b)(D,[E,h.D7]),W=(0,h.D7)(),[L,_]=P(D),[A,H]=P(D),B=e=>{let{__scopeSelect:t,children:n,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:u,onValueChange:s,dir:c,name:f,autoComplete:p,disabled:m,required:w,form:g}=e,x=W(t),[b,C]=r.useState(null),[S,M]=r.useState(null),[j,k]=r.useState(!1),R=(0,d.gm)(c),[I,E]=(0,y.T)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:D}),[P,N]=(0,y.T)({prop:i,defaultProp:u,onChange:s,caller:D}),_=r.useRef(null),H=!b||g||!!b.closest("form"),[B,F]=r.useState(new Set),K=Array.from(B).map(e=>e.props.value).join(";");return(0,T.jsx)(h.fC,{...x,children:(0,T.jsxs)(L,{required:w,scope:t,trigger:b,onTriggerChange:C,valueNode:S,onValueNodeChange:M,valueNodeHasChildren:j,onValueNodeHasChildrenChange:k,contentId:(0,v.M)(),value:P,onValueChange:N,open:I,onOpenChange:E,dir:R,triggerPointerDownPosRef:_,disabled:m,children:[(0,T.jsx)(V.Provider,{scope:t,children:(0,T.jsx)(A,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{F(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),H?(0,T.jsxs)(eT,{"aria-hidden":!0,required:w,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>N(e.target.value),disabled:m,form:g,children:[void 0===P?(0,T.jsx)("option",{value:""}):null,Array.from(B)]},K):null]})})};B.displayName=D;var F="SelectTrigger",K=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:l=!1,...o}=e,i=W(n),s=_(F,n),d=s.disabled||l,c=(0,u.e)(t,s.onTriggerChange),f=I(n),p=r.useRef("touch"),[v,m,g]=eR(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eD(t,e,n);void 0!==r&&s.onValueChange(r.value)}),x=e=>{d||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(h.ee,{asChild:!0,...i,children:(0,T.jsx)(w.WV.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ek(s.value)?"":void 0,...o,ref:c,onClick:(0,a.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&x(e)}),onPointerDown:(0,a.M)(o.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.M)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(x(),e.preventDefault())})})})});K.displayName=F;var z="SelectValue",O=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,children:o,placeholder:a="",...i}=e,s=_(z,n),{onValueNodeHasChildrenChange:d}=s,c=void 0!==o,f=(0,u.e)(t,s.onValueNodeChange);return(0,b.b)(()=>{d(c)},[d,c]),(0,T.jsx)(w.WV.span,{...i,ref:f,style:{pointerEvents:"none"},children:ek(s.value)?(0,T.jsx)(T.Fragment,{children:a}):o})});O.displayName=z;var Z=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...l}=e;return(0,T.jsx)(w.WV.span,{"aria-hidden":!0,...l,ref:t,children:r||"▼"})});Z.displayName="SelectIcon";var U=e=>(0,T.jsx)(m.h,{asChild:!0,...e});U.displayName="SelectPortal";var q="SelectContent",Y=r.forwardRef((e,t)=>{let n=_(q,e.__scopeSelect),[o,a]=r.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),n.open)?(0,T.jsx)($,{...e,ref:t}):o?l.createPortal((0,T.jsx)(X,{scope:e.__scopeSelect,children:(0,T.jsx)(V.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),o):null});Y.displayName=q;var[X,G]=P(q),J=(0,g.Z8)("SelectContent.RemoveScroll"),$=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:C,...S}=e,k=_(q,n),[R,D]=r.useState(null),[V,E]=r.useState(null),P=(0,u.e)(t,e=>D(e)),[N,W]=r.useState(null),[L,A]=r.useState(null),H=I(n),[B,F]=r.useState(!1),K=r.useRef(!1);r.useEffect(()=>{if(R)return(0,M.Ry)(R)},[R]),(0,f.EW)();let z=r.useCallback(e=>{let[t,...n]=H().map(e=>e.ref.current),[r]=n.slice(-1),l=document.activeElement;for(let n of e)if(n===l||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&V&&(V.scrollTop=0),n===r&&V&&(V.scrollTop=V.scrollHeight),null==n||n.focus(),document.activeElement!==l))return},[H,V]),O=r.useCallback(()=>z([N,R]),[z,N,R]);r.useEffect(()=>{B&&O()},[B,O]);let{onOpenChange:Z,triggerPointerDownPosRef:U}=k;r.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var n,r,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(n=U.current)||void 0===n?void 0:n.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(r=U.current)||void 0===r?void 0:r.y)&&void 0!==o?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||Z(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,Z,U]),r.useEffect(()=>{let e=()=>Z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[Z]);let[Y,G]=eR(e=>{let t=H().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eD(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),$=r.useCallback((e,t,n)=>{let r=!K.current&&!n;(void 0!==k.value&&k.value===t||r)&&(W(e),r&&(K.current=!0))},[k.value]),et=r.useCallback(()=>null==R?void 0:R.focus(),[R]),en=r.useCallback((e,t,n)=>{let r=!K.current&&!n;(void 0!==k.value&&k.value===t||r)&&A(e)},[k.value]),er="popper"===l?ee:Q,el=er===ee?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:C}:{};return(0,T.jsx)(X,{scope:n,content:R,viewport:V,onViewportChange:E,itemRefCallback:$,selectedItem:N,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:O,selectedItemText:L,position:l,isPositioned:B,searchRef:Y,children:(0,T.jsx)(j.Z,{as:J,allowPinchZoom:!0,children:(0,T.jsx)(p.M,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(o,e=>{var t;null===(t=k.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,T.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,T.jsx)(er,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...S,...el,onPlaced:()=>F(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,a.M)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:l,...a}=e,i=_(q,n),s=G(q,n),[d,c]=r.useState(null),[f,p]=r.useState(null),v=(0,u.e)(t,e=>p(e)),h=I(n),m=r.useRef(!1),g=r.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:C,focusSelectedItem:S}=s,M=r.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&f&&x&&y&&C){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),r=C.getBoundingClientRect();if("rtl"!==i.dir){let l=r.left-t.left,a=n.left-l,i=e.left-a,u=e.width+i,s=Math.max(u,t.width),c=window.innerWidth-10,f=(0,o.u)(a,[10,Math.max(10,c-s)]);d.style.minWidth=u+"px",d.style.left=f+"px"}else{let l=t.right-r.right,a=window.innerWidth-n.right-l,i=window.innerWidth-e.right-a,u=e.width+i,s=Math.max(u,t.width),c=window.innerWidth-10,f=(0,o.u)(a,[10,Math.max(10,c-s)]);d.style.minWidth=u+"px",d.style.right=f+"px"}let a=h(),u=window.innerHeight-20,s=x.scrollHeight,c=window.getComputedStyle(f),p=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=p+v+s+parseInt(c.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),S=window.getComputedStyle(x),M=parseInt(S.paddingTop,10),j=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,k=y.offsetHeight/2,R=p+v+(y.offsetTop+k);if(R<=T){let e=a.length>0&&y===a[a.length-1].ref.current;d.style.bottom="0px";let t=f.clientHeight-x.offsetTop-x.offsetHeight;d.style.height=R+Math.max(u-T,k+(e?j:0)+t+w)+"px"}else{let e=a.length>0&&y===a[0].ref.current;d.style.top="0px";let t=Math.max(T,p+x.offsetTop+(e?M:0)+k);d.style.height=t+(g-R)+"px",x.scrollTop=R-T+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=u+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,f,x,y,C,i.dir,l]);(0,b.b)(()=>M(),[M]);let[j,k]=r.useState();(0,b.b)(()=>{f&&k(window.getComputedStyle(f).zIndex)},[f]);let R=r.useCallback(e=>{e&&!0===g.current&&(M(),null==S||S(),g.current=!1)},[M,S]);return(0,T.jsx)(et,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,T.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,T.jsx)(w.WV.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:l=10,...o}=e,a=W(n);return(0,T.jsx)(h.VY,{...a,...o,ref:t,align:r,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=P(q,{}),er="SelectViewport",el=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:l,...o}=e,i=G(er,n),s=en(er,n),d=(0,u.e)(t,i.onViewportChange),c=r.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,T.jsx)(V.Slot,{scope:n,children:(0,T.jsx)(w.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,l=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(l<r){let o=l+e,a=Math.min(r,o),i=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=er;var eo="SelectGroup",[ea,ei]=P(eo),eu=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=(0,v.M)();return(0,T.jsx)(ea,{scope:n,id:l,children:(0,T.jsx)(w.WV.div,{role:"group","aria-labelledby":l,...r,ref:t})})});eu.displayName=eo;var es="SelectLabel",ed=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=ei(es,n);return(0,T.jsx)(w.WV.div,{id:l.id,...r,ref:t})});ed.displayName=es;var ec="SelectItem",[ef,ep]=P(ec),ev=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:l,disabled:o=!1,textValue:i,...s}=e,d=_(ec,n),c=G(ec,n),f=d.value===l,[p,h]=r.useState(null!=i?i:""),[m,g]=r.useState(!1),x=(0,u.e)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,l,o)}),y=(0,v.M)(),b=r.useRef("touch"),C=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(ef,{scope:n,value:l,disabled:o,textId:y,isSelected:f,onItemTextChange:r.useCallback(e=>{h(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,T.jsx)(V.ItemSlot,{scope:n,value:l,disabled:o,textValue:p,children:(0,T.jsx)(w.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:x,onFocus:(0,a.M)(s.onFocus,()=>g(!0)),onBlur:(0,a.M)(s.onBlur,()=>g(!1)),onClick:(0,a.M)(s.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,a.M)(s.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,a.M)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(s.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,a.M)(s.onKeyDown,e=>{var t;(null===(t=c.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(R.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ec;var eh="SelectItemText",em=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:o,style:a,...i}=e,s=_(eh,n),d=G(eh,n),c=ep(eh,n),f=H(eh,n),[p,v]=r.useState(null),h=(0,u.e)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,c.value,c.disabled)}),m=null==p?void 0:p.textContent,g=r.useMemo(()=>(0,T.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=f;return(0,b.b)(()=>(x(g),()=>y(g)),[x,y,g]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(w.WV.span,{id:c.textId,...i,ref:h}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(i.children,s.valueNode):null]})});em.displayName=eh;var ew="SelectItemIndicator",eg=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ep(ew,n).isSelected?(0,T.jsx)(w.WV.span,{"aria-hidden":!0,...r,ref:t}):null});eg.displayName=ew;var ex="SelectScrollUpButton",ey=r.forwardRef((e,t)=>{let n=G(ex,e.__scopeSelect),l=en(ex,e.__scopeSelect),[o,a]=r.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eb="SelectScrollDownButton",eC=r.forwardRef((e,t)=>{let n=G(eb,e.__scopeSelect),l=en(eb,e.__scopeSelect),[o,a]=r.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eC.displayName=eb;var eS=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:l,...o}=e,i=G("SelectScrollButton",n),u=r.useRef(null),s=I(n),d=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,b.b)(()=>{var e;let t=s().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[s]),(0,T.jsx)(w.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.M)(o.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(l,50))}),onPointerMove:(0,a.M)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===u.current&&(u.current=window.setInterval(l,50))}),onPointerLeave:(0,a.M)(o.onPointerLeave,()=>{d()})})}),eM=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,T.jsx)(w.WV.div,{"aria-hidden":!0,...r,ref:t})});eM.displayName="SelectSeparator";var ej="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=W(n),o=_(ej,n),a=G(ej,n);return o.open&&"popper"===a.position?(0,T.jsx)(h.Eh,{...l,...r,ref:t}):null}).displayName=ej;var eT=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:l,...o}=e,a=r.useRef(null),i=(0,u.e)(t,a),s=(0,C.D)(l);return r.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==l&&t){let n=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(n)}},[s,l]),(0,T.jsx)(w.WV.select,{...o,style:{...S.C2,...o.style},ref:i,defaultValue:l})});function ek(e){return""===e||void 0===e}function eR(e){let t=(0,x.W)(e),n=r.useRef(""),l=r.useRef(0),o=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(l.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(l.current),[]),[n,o,a]}function eD(e,t,n){var r;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===l.length&&(o=o.filter(e=>e!==n));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==n?a:void 0}eT.displayName="SelectBubbleInput";var eV=B,eI=K,eE=O,eP=Z,eN=U,eW=Y,eL=el,e_=eu,eA=ed,eH=ev,eB=em,eF=eg,eK=ey,ez=eC,eO=eM},62447:function(e,t,n){n.d(t,{VY:function(){return I},aV:function(){return D},fC:function(){return R},xz:function(){return V}});var r=n(2265),l=n(78149),o=n(98324),a=n(53398),i=n(31383),u=n(25171),s=n(87513),d=n(91715),c=n(53201),f=n(57437),p="Tabs",[v,h]=(0,o.b)(p,[a.Pc]),m=(0,a.Pc)(),[w,g]=v(p),x=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:l,defaultValue:o,orientation:a="horizontal",dir:i,activationMode:v="automatic",...h}=e,m=(0,s.gm)(i),[g,x]=(0,d.T)({prop:r,onChange:l,defaultProp:null!=o?o:"",caller:p});return(0,f.jsx)(w,{scope:n,baseId:(0,c.M)(),value:g,onValueChange:x,orientation:a,dir:m,activationMode:v,children:(0,f.jsx)(u.WV.div,{dir:m,"data-orientation":a,...h,ref:t})})});x.displayName=p;var y="TabsList",b=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...l}=e,o=g(y,n),i=m(n);return(0,f.jsx)(a.fC,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:r,children:(0,f.jsx)(u.WV.div,{role:"tablist","aria-orientation":o.orientation,...l,ref:t})})});b.displayName=y;var C="TabsTrigger",S=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,s=g(C,n),d=m(n),c=T(s.baseId,r),p=k(s.baseId,r),v=r===s.value;return(0,f.jsx)(a.ck,{asChild:!0,...d,focusable:!o,active:v,children:(0,f.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":p,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...i,ref:t,onMouseDown:(0,l.M)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,l.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,l.M)(e.onFocus,()=>{let e="manual"!==s.activationMode;v||o||!e||s.onValueChange(r)})})})});S.displayName=C;var M="TabsContent",j=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:l,forceMount:o,children:a,...s}=e,d=g(M,n),c=T(d.baseId,l),p=k(d.baseId,l),v=l===d.value,h=r.useRef(v);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(i.z,{present:o||v,children:n=>{let{present:r}=n;return(0,f.jsx)(u.WV.div,{"data-state":v?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:p,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&a})}})});function T(e,t){return"".concat(e,"-trigger-").concat(t)}function k(e,t){return"".concat(e,"-content-").concat(t)}j.displayName=M;var R=x,D=b,V=S,I=j},47250:function(e,t,n){n.d(t,{D:function(){return l}});var r=n(2265);function l(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},31725:function(e,t,n){n.d(t,{C2:function(){return a},TX:function(){return i},fC:function(){return u}});var r=n(2265),l=n(25171),o=n(57437),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=r.forwardRef((e,t)=>(0,o.jsx)(l.WV.span,{...e,ref:t,style:{...a,...e.style}}));i.displayName="VisuallyHidden";var u=i}}]);