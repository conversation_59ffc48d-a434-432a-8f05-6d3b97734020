(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{5352:function(e,a,s){Promise.resolve().then(s.bind(s,48567))},48567:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return ey}});var t=s(57437),r=s(33142),l=s(47201),i=s(36356),o=s(77424),n=s(70518),d=s(87592),c=s(74109),m=s(33907),h=s(54817),u=s(74697),x=s(40933),p=s(24241),f=s(66648),j=s(16463),g=s(30998),N=s(2265),v=s(27776),b=s(92513),w=s(58184),y=s(6884),k=s(11240),C=s(39127),A=s(38711),Z=s(34567),E=s(89733),S=s(48185),R=s(49354);function B(e){let{className:a,onCreateWorkbook:s,onImportFile:r,recentWorkbooks:l=[]}=e,o=(0,j.useRouter)(),[n,d]=(0,N.useState)(!1),[c,h]=(0,N.useState)(!1),u=async()=>{if(s){s();return}try{d(!0),o.push("/dashboard?create=true")}catch(e){v.toast.error("Erro ao criar planilha")}finally{d(!1)}},x=async()=>{if(r){r();return}try{h(!0);let e=document.createElement("input");e.type="file",e.accept=".xlsx,.xls,.csv",e.onchange=async e=>{var a;let s=null===(a=e.target.files)||void 0===a?void 0:a[0];s&&v.toast.success("Arquivo ".concat(s.name," selecionado para importa\xe7\xe3o"))},e.click()}catch(e){v.toast.error("Erro ao importar arquivo")}finally{h(!1)}},p=async()=>{if(0===l.length){v.toast.error("Nenhuma planilha recente encontrada");return}let e=l[0];if(!e){v.toast.error("Nenhuma planilha recente encontrada");return}try{v.toast.success('Duplicando planilha "'.concat(e.name,'"'))}catch(e){v.toast.error("Erro ao duplicar planilha")}},f=[{id:"create",title:"Nova Planilha",description:"Criar planilha em branco",icon:(0,t.jsx)(b.Z,{className:"h-5 w-5"}),action:u,disabled:n},{id:"ai-create",title:"Criar com IA",description:"Usar assistente inteligente",icon:(0,t.jsx)(m.Z,{className:"h-5 w-5"}),action:()=>{o.push("/dashboard?create=true&ai=true")},badge:"IA"},{id:"import",title:"Importar Arquivo",description:"Excel, CSV ou outros formatos",icon:(0,t.jsx)(w.Z,{className:"h-5 w-5"}),action:x,variant:"outline",disabled:c},{id:"duplicate",title:"Duplicar Recente",description:"Copiar \xfaltima planilha editada",icon:(0,t.jsx)(y.Z,{className:"h-5 w-5"}),action:p,variant:"outline",disabled:0===l.length}],g=[{id:"templates",title:"Templates",description:"Modelos prontos",icon:(0,t.jsx)(i.Z,{className:"h-4 w-4"}),action:()=>{o.push("/dashboard?templates=true")},variant:"secondary"},{id:"collaborate",title:"Colaborar",description:"Planilhas compartilhadas",icon:(0,t.jsx)(k.Z,{className:"h-4 w-4"}),action:()=>{o.push("/dashboard?tab=shared")},variant:"secondary"}];return(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",a),children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-lg",children:"A\xe7\xf5es R\xe1pidas"}),(0,t.jsx)(S.SZ,{children:"Comece rapidamente com suas planilhas"})]}),(0,t.jsxs)(S.aY,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:f.map(e=>(0,t.jsxs)(E.Button,{variant:e.variant||"default",className:(0,R.cn)("h-auto p-4 flex flex-col items-start space-y-2 relative",e.disabled&&"opacity-50 cursor-not-allowed"),onClick:e.action,disabled:e.disabled,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.icon,(0,t.jsx)("span",{className:"font-medium",children:e.title})]}),e.badge&&(0,t.jsx)("span",{className:"text-xs bg-primary/20 text-primary px-2 py-1 rounded-full",children:e.badge})]}),(0,t.jsx)("p",{className:"text-xs text-left opacity-80",children:e.description})]},e.id))}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:g.map(e=>(0,t.jsxs)(E.Button,{variant:e.variant||"secondary",size:"sm",className:"flex items-center space-x-2",onClick:e.action,disabled:e.disabled,children:[e.icon,(0,t.jsx)("span",{children:e.title})]},e.id))})]})]})}function z(e){let{className:a}=e,s=(0,j.useRouter)(),r=[{id:"financial",name:"Controle Financeiro",icon:(0,t.jsx)(C.Z,{className:"h-4 w-4"}),color:"text-green-600"},{id:"dashboard",name:"Dashboard",icon:(0,t.jsx)(A.Z,{className:"h-4 w-4"}),color:"text-blue-600"},{id:"calculator",name:"Calculadora",icon:(0,t.jsx)(Z.Z,{className:"h-4 w-4"}),color:"text-purple-600"}],l=e=>{s.push("/dashboard?template=".concat(e)),v.toast.success("Carregando template...")};return(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",a),children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-base",children:"Templates Populares"}),(0,t.jsx)(S.SZ,{children:"Comece com modelos prontos"})]}),(0,t.jsxs)(S.aY,{children:[(0,t.jsx)("div",{className:"space-y-2",children:r.map(e=>(0,t.jsxs)(E.Button,{variant:"ghost",className:"w-full justify-start h-auto p-3",onClick:()=>l(e.id),children:[(0,t.jsx)("div",{className:(0,R.cn)("mr-3",e.color),children:e.icon}),(0,t.jsx)("span",{className:"text-sm",children:e.name})]},e.id))}),(0,t.jsx)(E.Button,{variant:"outline",size:"sm",className:"w-full mt-3",onClick:()=>s.push("/dashboard?templates=true"),children:"Ver Todos os Templates"})]})]})}function T(e){let{recentWorkbooks:a=[],className:s}=e,r=(0,j.useRouter)(),l=e=>{r.push("/workbook/".concat(e))};return 0===a.length?null:(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",s),children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-base",children:"Acesso R\xe1pido"}),(0,t.jsx)(S.SZ,{children:"Suas planilhas mais recentes"})]}),(0,t.jsxs)(S.aY,{children:[(0,t.jsx)("div",{className:"space-y-2",children:a.slice(0,3).map(e=>(0,t.jsxs)(E.Button,{variant:"ghost",className:"w-full justify-start h-auto p-3",onClick:()=>l(e.id),children:[(0,t.jsx)(i.Z,{className:"h-4 w-4 mr-3 text-primary"}),(0,t.jsxs)("div",{className:"flex-1 text-left",children:[(0,t.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Editado ",new Date(e.updatedAt).toLocaleDateString("pt-BR")]})]})]},e.id))}),a.length>3&&(0,t.jsxs)(E.Button,{variant:"outline",size:"sm",className:"w-full mt-3",onClick:()=>r.push("/dashboard?tab=recent"),children:["Ver Todas (",a.length,")"]})]})]})}var P=s(39451),O=s(37733),D=s(23787),F=s(87138),_=s(79055),W=s(31590),V=s(2183);let I={workbook_created:{icon:i.Z,color:"text-green-600",bgColor:"bg-green-50",label:"Cria\xe7\xe3o"},workbook_edited:{icon:P.Z,color:"text-blue-600",bgColor:"bg-blue-50",label:"Edi\xe7\xe3o"},ai_command:{icon:m.Z,color:"text-purple-600",bgColor:"bg-purple-50",label:"IA"},collaboration:{icon:k.Z,color:"text-orange-600",bgColor:"bg-orange-50",label:"Colabora\xe7\xe3o"}};function M(e){var a,s;let{activity:i}=e,o=I[i.type],n=o.icon,d=(0,r.Q)(new Date(i.timestamp),{addSuffix:!0,locale:l.F}),c=null===(a=i.metadata)||void 0===a?void 0:a.workbookId,m=null===(s=i.metadata)||void 0===s?void 0:s.hasAI;return(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors group",children:[(0,t.jsx)("div",{className:(0,R.cn)("flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",o.bgColor),children:(0,t.jsx)(n,{className:(0,R.cn)("h-4 w-4",o.color)})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:i.title}),(0,t.jsx)(_.C,{variant:"outline",className:"text-xs",children:o.label}),m&&(0,t.jsx)(_.C,{variant:"secondary",className:"text-xs bg-purple-100 text-purple-700",children:"IA"})]}),(0,t.jsxs)(W.h_,{children:[(0,t.jsx)(W.$F,{asChild:!0,children:(0,t.jsx)(E.Button,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsx)(O.Z,{className:"h-3 w-3"})})}),(0,t.jsxs)(W.AW,{align:"end",children:[c&&(0,t.jsx)(W.Xi,{asChild:!0,children:(0,t.jsxs)(F.default,{href:"/workbook/".concat(c),children:[(0,t.jsx)(D.Z,{className:"h-3 w-3 mr-2"}),"Abrir Planilha"]})}),(0,t.jsx)(W.Xi,{children:"Ver Detalhes"})]})]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1 truncate",children:i.description}),i.metadata&&(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(x.Z,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:d})]}),i.metadata.sharedBy&&(0,t.jsxs)("span",{children:["por ",i.metadata.sharedBy]}),i.metadata.sharedWith&&(0,t.jsxs)("span",{children:["com ",i.metadata.sharedWith]})]})]})]})}function Q(){return(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3",children:[(0,t.jsx)(V.O,{className:"w-8 h-8 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(V.O,{className:"h-4 w-32"}),(0,t.jsx)(V.O,{className:"h-4 w-12"})]}),(0,t.jsx)(V.O,{className:"h-3 w-48"}),(0,t.jsx)(V.O,{className:"h-3 w-24"})]})]})}function L(e){let{activities:a,isLoading:s,className:r,maxItems:l=10}=e,i=a.slice(0,l);return(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",r),children:[(0,t.jsx)(S.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.ll,{className:"text-lg",children:"Atividade Recente"}),(0,t.jsx)(S.SZ,{children:"Suas \xfaltimas a\xe7\xf5es no Excel Copilot"})]}),!s&&a.length>l&&(0,t.jsxs)(E.Button,{variant:"outline",size:"sm",children:["Ver Todas (",a.length,")"]})]})}),(0,t.jsx)(S.aY,{className:"p-0",children:s?(0,t.jsx)("div",{className:"space-y-1",children:Array.from({length:5}).map((e,a)=>(0,t.jsx)(Q,{},a))}):i.length>0?(0,t.jsx)("div",{className:"space-y-1",children:i.map(e=>(0,t.jsx)(M,{activity:e},e.id))}):(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,t.jsx)(x.Z,{className:"h-8 w-8 text-muted-foreground mb-2"}),(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Nenhuma atividade recente"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Suas a\xe7\xf5es aparecer\xe3o aqui conforme voc\xea usa o Excel Copilot"})]})})]})}var U=s(77515),Y=s(24258),q=s(97529),X=s(10883),J=s(84666);function $(e){let{title:a,description:s,icon:r,action:l,className:i}=e;return(0,t.jsxs)("div",{className:(0,R.cn)("flex flex-col items-center justify-center p-8 text-center","bg-gray-50 dark:bg-gray-900 border border-dashed rounded-lg","min-h-[220px]",i),children:[r&&(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:r}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-200",children:a}),s&&(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400 max-w-md",children:s}),l&&(0,t.jsx)("div",{className:"mt-5",children:l})]})}var H=s(188);let G=N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("table",{ref:a,className:(0,R.cn)("w-full caption-bottom text-sm",s),...r})});G.displayName="Table";let K=N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("thead",{ref:a,className:(0,R.cn)("[&_tr]:border-b",s),...r})});K.displayName="TableHeader";let ee=N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tbody",{ref:a,className:(0,R.cn)("[&_tr:last-child]:border-0",s),...r})});ee.displayName="TableBody",N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tfoot",{ref:a,className:(0,R.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let ea=N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tr",{ref:a,className:(0,R.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});ea.displayName="TableRow";let es=N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("th",{ref:a,className:(0,R.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})});es.displayName="TableHead";let et=N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("td",{ref:a,className:(0,R.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})});et.displayName="TableCell";let er=N.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("caption",{ref:a,className:(0,R.cn)("mt-4 text-sm text-muted-foreground",s),...r})});er.displayName="TableCaption";var el=s(18473);function ei(e){var a,s,o;let{searchQuery:n="",filters:d={},onFiltersChange:c}=e,m=(0,j.useRouter)(),{data:h}=(0,g.useSession)(),[u,f]=(0,N.useState)([]),[w,k]=(0,N.useState)(!0),[C,A]=(0,N.useState)(null),{fetchWithCSRF:Z}=(0,J.useFetchWithCSRF)(),[S,R]=(0,N.useState)(!1),[B,z]=(0,N.useState)({current:0,total:1,limit:10,hasMore:!1}),T=e=>(0,r.Q)(e,{addSuffix:!0,locale:l.F}),P=(0,N.useCallback)(async function(){var e,a,s,t,r,l;let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{let l;if(k(!0),!(null==h?void 0:h.user)){R(!0),f([]),k(!1);return}let o=0,c=!1;for(;o<3&&!c;)try{o++;let e=new URLSearchParams({...n&&{search:n},...d.sortBy&&{sortBy:d.sortBy},...d.sortOrder&&{sortOrder:d.sortOrder},...d.dateRange&&"all"!==d.dateRange&&{dateRange:d.dateRange},...d.minSheets&&{minSheets:d.minSheets.toString()},...d.maxSheets&&{maxSheets:d.maxSheets.toString()},page:i.toString(),limit:B.limit.toString()}),a=await Z("/api/workbooks?".concat(e.toString()),{headers:{"Content-Type":"application/json"}});if(a.ok)c=!0,l=await a.json();else{let e="";try{let s=await a.json();e=s.details||s.error||""}catch(e){}if(401===a.status)throw Error("N\xe3o autorizado: ".concat(e));if(o<3)await new Promise(e=>setTimeout(e,1e3*o));else throw Error("API retornou c\xf3digo ".concat(a.status,": ").concat(e))}}catch(e){if(e instanceof Error&&(null===(t=e.message)||void 0===t?void 0:t.includes("N\xe3o autorizado"))){v.toast.error("Voc\xea precisa estar autenticado para acessar suas planilhas"),R(!0),f([]),k(!1);return}if(o<3)await new Promise(e=>setTimeout(e,1e3*o));else throw e}if(!c)throw Error("M\xe1ximo de tentativas atingido ao carregar planilhas");el.logger.debug("WorkbooksTable: Processando workbooks",{responseDataKeys:Object.keys(l||{}),hasData:!!(null==l?void 0:l.data),hasWorkbooks:!!(null==l?void 0:null===(e=l.data)||void 0===e?void 0:e.workbooks)||!!(null==l?void 0:l.workbooks)});let m=(null==l?void 0:null===(a=l.data)||void 0===a?void 0:a.workbooks)||(null==l?void 0:l.workbooks)||[];if(el.logger.debug("WorkbooksTable: Array de workbooks extra\xeddo",{count:m.length,isArray:Array.isArray(m)}),(null==l?void 0:null===(s=l.data)||void 0===s?void 0:s.pagination)||(null==l?void 0:l.pagination)){let e=(null==l?void 0:null===(r=l.data)||void 0===r?void 0:r.pagination)||(null==l?void 0:l.pagination);z({current:e.page,total:e.totalPages,limit:e.limit,hasMore:e.hasMore})}let u=m.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));f(u),R(!1)}catch(e){el.logger.error("WorkbooksTable: Erro ao carregar planilhas",e,{userId:null==h?void 0:null===(l=h.user)||void 0===l?void 0:l.id,searchQuery:n,attempts:"max_attempts_reached"}),e instanceof Error?e.message.includes("401")||e.message.includes("unauthorized")?v.toast.error("Sess\xe3o expirada. Fa\xe7a login novamente."):e.message.includes("network")||e.message.includes("fetch")?v.toast.error("Erro de conex\xe3o. Verificando conectividade..."):e.message.includes("timeout")?v.toast.error("Tempo limite excedido. Tente novamente."):v.toast.error("Erro ao carregar planilhas: ".concat(e.message)):v.toast.error("Erro desconhecido ao carregar planilhas. Tente novamente mais tarde."),R(!0),f([])}finally{el.logger.debug("WorkbooksTable: Finalizando carregamento",{workbooksCount:u.length,hasError:S}),k(!1)}},[Z,null==h?void 0:null===(a=h.user)||void 0===a?void 0:a.id,n,d,B.limit]),O=()=>{R(!1),k(!0),P()};(0,N.useEffect)(()=>{var e;(null==h?void 0:null===(e=h.user)||void 0===e?void 0:e.id)?P():k(!1)},[null==h?void 0:null===(s=h.user)||void 0===s?void 0:s.id]),(0,N.useEffect)(()=>{var e;(null==h?void 0:null===(e=h.user)||void 0===e?void 0:e.id)&&(void 0!==n||Object.keys(d).length>0)&&(z(e=>({...e,current:0})),P(0))},[n,d,P,null==h?void 0:null===(o=h.user)||void 0===o?void 0:o.id]);let D=e=>{m.push("/workbook/".concat(e))},F=async e=>{try{let a=await Z("/api/workbooks/".concat(e,"/duplicate"),{method:"POST",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Erro ao duplicar planilha");let s=await a.json(),t={...s.workbook,createdAt:new Date(s.workbook.createdAt),updatedAt:new Date(s.workbook.updatedAt),sheets:Array.isArray(s.workbook.sheets)?s.workbook.sheets:s.workbook.sheetsCount||0};f([...u,t]),v.toast.success("Planilha duplicada com sucesso!")}catch(s){var a;el.logger.error("WorkbooksTable: Erro ao duplicar workbook",s,{workbookId:e,userId:null==h?void 0:null===(a=h.user)||void 0===a?void 0:a.id}),v.toast.error("N\xe3o foi poss\xedvel duplicar a planilha")}},V=async e=>{A(e);try{if(!(await Z("/api/workbooks/".concat(e),{method:"DELETE",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao excluir planilha");f(u.filter(a=>a.id!==e)),v.toast.success("Planilha exclu\xedda com sucesso!")}catch(s){var a;el.logger.error("WorkbooksTable: Erro ao excluir workbook",s,{workbookId:e,userId:null==h?void 0:null===(a=h.user)||void 0===a?void 0:a.id}),v.toast.error("N\xe3o foi poss\xedvel excluir a planilha")}finally{A(null)}};return w?(0,t.jsx)("div",{className:"w-full py-10 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"})}):S?(0,t.jsx)("div",{className:"flex flex-col items-center justify-center h-64 text-center",children:(0,t.jsx)($,{icon:(0,t.jsx)(U.Z,{className:"h-12 w-12 text-destructive"}),title:"Erro ao carregar planilhas",description:"N\xe3o foi poss\xedvel carregar suas planilhas. Isso pode ser um problema tempor\xe1rio.",action:(0,t.jsx)(E.Button,{onClick:()=>O(),children:"Tentar Novamente"})})}):0===u.length?(0,t.jsx)("div",{className:"flex flex-col items-center justify-center h-64 text-center",children:(0,t.jsx)($,{icon:(0,t.jsx)(i.Z,{className:"h-12 w-12"}),title:"Nenhuma planilha encontrada",description:n?'N\xe3o encontramos planilhas com "'.concat(n,'". Tente outro termo de busca.'):"Voc\xea ainda n\xe3o criou nenhuma planilha. Comece criando sua primeira planilha agora.",action:n?void 0:(0,t.jsxs)(H.MD,{onClick:()=>m.push("/dashboard?create=true"),children:[(0,t.jsx)(b.Z,{className:"h-4 w-4 mr-2"}),"Criar Nova Planilha"]})})}):(0,t.jsxs)("div",{className:"w-full space-y-4",children:[(0,t.jsxs)(G,{children:[(0,t.jsx)(er,{children:"Lista de suas planilhas Excel"}),(0,t.jsx)(K,{children:(0,t.jsxs)(ea,{children:[(0,t.jsxs)(es,{className:"w-[40%]",children:["Nome","name"===d.sortBy&&(0,t.jsx)("span",{className:"ml-1 text-xs",children:"asc"===d.sortOrder?"↑":"↓"})]}),(0,t.jsxs)(es,{children:["Folhas","sheets"===d.sortBy&&(0,t.jsx)("span",{className:"ml-1 text-xs",children:"asc"===d.sortOrder?"↑":"↓"})]}),(0,t.jsxs)(es,{children:["Criado","createdAt"===d.sortBy&&(0,t.jsx)("span",{className:"ml-1 text-xs",children:"asc"===d.sortOrder?"↑":"↓"})]}),(0,t.jsxs)(es,{children:["Modificado","updatedAt"===d.sortBy&&(0,t.jsx)("span",{className:"ml-1 text-xs",children:"asc"===d.sortOrder?"↑":"↓"})]}),(0,t.jsx)(es,{className:"text-right",children:"A\xe7\xf5es"})]})}),(0,t.jsx)(ee,{children:u.map(e=>(0,t.jsxs)(ea,{className:"cursor-pointer hover:bg-muted/50",children:[(0,t.jsxs)(et,{className:"font-medium flex items-center gap-2",onClick:()=>D(e.id),children:[(0,t.jsx)(i.Z,{className:"h-4 w-4 text-primary"}),e.name]}),(0,t.jsx)(et,{onClick:()=>D(e.id),children:(0,t.jsxs)(_.C,{variant:"outline",className:"text-xs",children:["number"==typeof e.sheets?e.sheets:e.sheets.length," ",("number"==typeof e.sheets?e.sheets:e.sheets.length)>1?"folhas":"folha"]})}),(0,t.jsx)(et,{onClick:()=>D(e.id),children:(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,t.jsx)(p.Z,{className:"h-3 w-3 mr-1"}),T(e.createdAt)]})}),(0,t.jsx)(et,{onClick:()=>D(e.id),children:(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,t.jsx)(x.Z,{className:"h-3 w-3 mr-1"}),T(e.updatedAt)]})}),(0,t.jsx)(et,{className:"text-right",children:(0,t.jsxs)(W.h_,{children:[(0,t.jsx)(W.$F,{asChild:!0,children:(0,t.jsx)(H.Kk,{variant:"ghost",size:"icon",actionId:e.id,onAction:()=>{},children:(0,t.jsx)(Y.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(W.AW,{align:"end",children:[(0,t.jsx)(W.Ju,{children:"Op\xe7\xf5es"}),(0,t.jsx)(W.VD,{}),(0,t.jsxs)(W.Xi,{onClick:()=>D(e.id),children:[(0,t.jsx)(q.Z,{className:"h-4 w-4 mr-2"}),"Editar"]}),(0,t.jsxs)(W.Xi,{onClick:()=>F(e.id),children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Duplicar"]}),(0,t.jsx)(W.VD,{}),(0,t.jsx)(W.Xi,{className:"text-destructive focus:text-destructive",onClick:()=>V(e.id),disabled:C===e.id,children:C===e.id?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-4 w-4 mr-2 rounded-full border-2 border-destructive/20 border-t-destructive animate-spin"}),"Excluindo..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(X.Z,{className:"h-4 w-4 mr-2"}),"Excluir"]})})]})]})})]},e.id))})]}),B.total>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["P\xe1gina ",B.current+1," de ",B.total]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(E.Button,{variant:"outline",size:"sm",onClick:()=>{if(B.current>0){let e=B.current-1;z(a=>({...a,current:e})),P(e)}},disabled:0===B.current,children:"Anterior"}),(0,t.jsx)(E.Button,{variant:"outline",size:"sm",onClick:()=>{if(B.hasMore){let e=B.current+1;z(a=>({...a,current:e})),P(e)}},disabled:!B.hasMore,children:"Pr\xf3xima"})]})]})]})}var eo=s(404),en=s(42421),ed=s(30139),ec=s(28680),em=s(20969),eh=s(77209),eu=s(70402),ex=s(2128),ep=s(21413);function ef(e){let{filters:a,onFiltersChange:s,onReset:r}=e,[l,i]=(0,N.useState)(!1),o=(e,t)=>{s({...a,[e]:t})},n=Object.entries(a).filter(e=>{let[a,s]=e;return("sortBy"!==a||"updatedAt"!==s)&&("sortOrder"!==a||"desc"!==s)&&("dateRange"!==a||"all"!==s)&&null!=s&&""!==s}).length;return(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(ep.J2,{open:l,onOpenChange:i,children:[(0,t.jsx)(ep.xo,{asChild:!0,children:(0,t.jsxs)(E.Button,{variant:"outline",size:"sm",className:"relative",children:[(0,t.jsx)(eo.Z,{className:"h-4 w-4 mr-2"}),"Filtros",n>0&&(0,t.jsx)(_.C,{variant:"secondary",className:"ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs",children:n}),(0,t.jsx)(en.Z,{className:"h-3 w-3 ml-1"})]})}),(0,t.jsx)(ep.yk,{className:"w-80",align:"start",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Filtros Avan\xe7ados"}),n>0&&(0,t.jsxs)(E.Button,{variant:"ghost",size:"sm",onClick:()=>{r(),i(!1)},children:[(0,t.jsx)(u.Z,{className:"h-3 w-3 mr-1"}),"Limpar"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eu._,{className:"text-sm font-medium",children:"Ordenar por"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(ex.Ph,{value:a.sortBy||"updatedAt",onValueChange:e=>o("sortBy",e),children:[(0,t.jsx)(ex.i4,{className:"flex-1",children:(0,t.jsx)(ex.ki,{})}),(0,t.jsxs)(ex.Bw,{children:[(0,t.jsx)(ex.Ql,{value:"name",children:"Nome"}),(0,t.jsx)(ex.Ql,{value:"createdAt",children:"Data de Cria\xe7\xe3o"}),(0,t.jsx)(ex.Ql,{value:"updatedAt",children:"\xdaltima Modifica\xe7\xe3o"}),(0,t.jsx)(ex.Ql,{value:"sheets",children:"N\xfamero de Folhas"})]})]}),(0,t.jsx)(E.Button,{variant:"outline",size:"sm",onClick:()=>o("sortOrder","asc"===a.sortOrder?"desc":"asc"),children:"asc"===a.sortOrder?(0,t.jsx)(ed.Z,{className:"h-4 w-4"}):(0,t.jsx)(ec.Z,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(eu._,{className:"text-sm font-medium flex items-center gap-1",children:[(0,t.jsx)(p.Z,{className:"h-3 w-3"}),"Per\xedodo"]}),(0,t.jsxs)(ex.Ph,{value:a.dateRange||"all",onValueChange:e=>o("dateRange",e),children:[(0,t.jsx)(ex.i4,{children:(0,t.jsx)(ex.ki,{})}),(0,t.jsxs)(ex.Bw,{children:[(0,t.jsx)(ex.Ql,{value:"all",children:"Todos os per\xedodos"}),(0,t.jsx)(ex.Ql,{value:"today",children:"Hoje"}),(0,t.jsx)(ex.Ql,{value:"week",children:"\xdaltima semana"}),(0,t.jsx)(ex.Ql,{value:"month",children:"\xdaltimo m\xeas"}),(0,t.jsx)(ex.Ql,{value:"year",children:"\xdaltimo ano"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(eu._,{className:"text-sm font-medium flex items-center gap-1",children:[(0,t.jsx)(em.Z,{className:"h-3 w-3"}),"N\xfamero de Folhas"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(eu._,{className:"text-xs text-muted-foreground",children:"M\xednimo"}),(0,t.jsx)(eh.I,{type:"number",placeholder:"0",min:"0",value:a.minSheets||"",onChange:e=>o("minSheets",e.target.value?parseInt(e.target.value):void 0)})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(eu._,{className:"text-xs text-muted-foreground",children:"M\xe1ximo"}),(0,t.jsx)(eh.I,{type:"number",placeholder:"∞",min:"0",value:a.maxSheets||"",onChange:e=>o("maxSheets",e.target.value?parseInt(e.target.value):void 0)})]})]})]}),(0,t.jsx)("div",{className:"flex gap-2 pt-2",children:(0,t.jsx)(E.Button,{size:"sm",className:"flex-1",onClick:()=>i(!1),children:"Aplicar Filtros"})})]})})]}),n>0&&(0,t.jsxs)("div",{className:"flex gap-1 flex-wrap",children:[a.sortBy&&"updatedAt"!==a.sortBy&&(0,t.jsxs)(_.C,{variant:"secondary",className:"text-xs",children:["Ordenar: ","name"===a.sortBy?"Nome":"createdAt"===a.sortBy?"Cria\xe7\xe3o":"sheets"===a.sortBy?"Folhas":"Modifica\xe7\xe3o",(0,t.jsx)(E.Button,{variant:"ghost",size:"sm",className:"h-3 w-3 p-0 ml-1",onClick:()=>o("sortBy","updatedAt"),children:(0,t.jsx)(u.Z,{className:"h-2 w-2"})})]}),a.dateRange&&"all"!==a.dateRange&&(0,t.jsxs)(_.C,{variant:"secondary",className:"text-xs",children:["today"===a.dateRange?"Hoje":"week"===a.dateRange?"Semana":"month"===a.dateRange?"M\xeas":"Ano",(0,t.jsx)(E.Button,{variant:"ghost",size:"sm",className:"h-3 w-3 p-0 ml-1",onClick:()=>o("dateRange","all"),children:(0,t.jsx)(u.Z,{className:"h-2 w-2"})})]}),(a.minSheets||a.maxSheets)&&(0,t.jsxs)(_.C,{variant:"secondary",className:"text-xs",children:["Folhas: ",a.minSheets||0,"-",a.maxSheets||"∞",(0,t.jsx)(E.Button,{variant:"ghost",size:"sm",className:"h-3 w-3 p-0 ml-1",onClick:()=>{o("minSheets",void 0),o("maxSheets",void 0)},children:(0,t.jsx)(u.Z,{className:"h-2 w-2"})})]})]})]})}var ej=s(86864);function eg(e){let{workbookId:a,onComplete:s,allowDuplicate:r=!0,allowDelete:l=!0,buttonVariant:i="ghost",buttonSize:o="icon",onlyEdit:n=!1}=e,d=(0,j.useRouter)(),{fetchWithCSRF:c}=(0,J.useFetchWithCSRF)(),[m,h]=(0,N.useState)(!1),u=e=>{e&&e.stopPropagation(),d.push("/workbook/".concat(a)),s&&s()},x=async e=>{e&&e.stopPropagation();try{if(!(await c("/api/workbooks/".concat(a,"/duplicate"),{method:"POST",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao duplicar planilha");v.toast.success("Planilha duplicada com sucesso!"),s&&s()}catch(e){console.error("Erro ao duplicar workbook:",e),v.toast.error("N\xe3o foi poss\xedvel duplicar a planilha")}},p=async e=>{e&&e.stopPropagation();try{if(h(!0),!(await c("/api/workbooks/".concat(a),{method:"DELETE",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao excluir planilha");v.toast.success("Planilha exclu\xedda com sucesso!"),s&&s()}catch(e){console.error("Erro ao excluir workbook:",e),v.toast.error("N\xe3o foi poss\xedvel excluir a planilha")}finally{h(!1)}};return n?(0,t.jsxs)(E.Button,{variant:i,size:o,onClick:u,"aria-label":"Editar planilha",children:[(0,t.jsx)(q.Z,{className:"h-4 w-4 mr-2"}),"Editar"]}):(0,t.jsxs)(W.h_,{children:[(0,t.jsx)(W.$F,{asChild:!0,onClick:e=>e.stopPropagation(),children:(0,t.jsx)(E.Button,{variant:i,size:o,"aria-label":"Op\xe7\xf5es da planilha",children:(0,t.jsx)(Y.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(W.AW,{align:"end",children:[(0,t.jsx)(W.Ju,{children:"Op\xe7\xf5es"}),(0,t.jsx)(W.VD,{}),(0,t.jsxs)(W.Xi,{onClick:e=>u(e),children:[(0,t.jsx)(q.Z,{className:"h-4 w-4 mr-2"}),"Editar"]}),r&&(0,t.jsxs)(W.Xi,{onClick:e=>x(e),children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Duplicar"]}),l&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(W.VD,{}),(0,t.jsxs)(W.Xi,{className:"text-destructive focus:text-destructive",onClick:e=>p(e),disabled:m,children:[(0,t.jsx)(X.Z,{className:"h-4 w-4 mr-2"}),m?"Excluindo...":"Excluir"]})]})]})]})}var eN=s(82518),ev=s(4919);let eb={headers:["A","B","C","D","E"],rows:[["","","","",""],["","","","",""],["","","","",""],["","","","",""],["","","","",""]],charts:[],name:"Nova Planilha"},ew=[{title:"Planilha de Finan\xe7as",description:"Crie uma planilha para controle de gastos mensais com categorias",icon:(0,t.jsx)(i.Z,{className:"h-6 w-6 text-green-500"}),command:"Criar planilha de controle financeiro pessoal com categorias de gastos e receitas e balan\xe7o mensal"},{title:"Controle de Tarefas",description:"Organize suas tarefas com datas, status e prioridades",icon:(0,t.jsx)(i.Z,{className:"h-6 w-6 text-blue-500"}),command:"Criar planilha de gerenciamento de tarefas com datas, status, respons\xe1veis e prioridades"},{title:"Dashboard de Vendas",description:"Visualize dados de vendas com gr\xe1ficos e tabelas din\xe2micas",icon:(0,t.jsx)(o.Z,{className:"h-6 w-6 text-indigo-500"}),command:"Criar dashboard de vendas com tabela de dados, gr\xe1fico de barras para vendas mensais e gr\xe1fico de pizza para produtos"}];function ey(){var e,a;let s=(0,j.useRouter)(),{data:o,status:b}=(0,g.useSession)(),[w,y]=(0,N.useState)(""),[k,C]=(0,N.useState)(!1),[A,Z]=(0,N.useState)(""),[R,P]=(0,N.useState)(""),[O,D]=(0,N.useState)(""),[F,_]=(0,N.useState)(!1),W=(0,j.useSearchParams)(),{csrfToken:V}=(0,J.useCSRF)(),[I,M]=(0,N.useState)([]),[Q,U]=(0,N.useState)([]),Y={recentActivity:[]},[q,X]=(0,N.useState)(!1),[H,er]=(0,N.useState)(null),[el,eo]=(0,N.useState)({}),en=(0,N.useRef)({}),[ed,ec]=(0,N.useState)({current:0,total:1,limit:10,hasMore:!1}),[em,ex]=(0,N.useState)(""),[ep,ey]=(0,N.useState)({sortBy:"updatedAt",sortOrder:"desc",dateRange:"all"});(0,N.useEffect)(()=>{if(W){let e=W.get("command");e&&(D(e),C(!0))}},[W]),(0,N.useEffect)(()=>{let e=new Date().getHours();e<12?y("Bom dia"):e<18?y("Boa tarde"):y("Boa noite")},[]),(0,N.useEffect)(()=>{"unauthenticated"===b&&s.push("/auth/signin?callbackUrl="+encodeURIComponent("/dashboard"))},[b,s]),(0,N.useEffect)(()=>()=>{Object.values(en.current).forEach(e=>{e.abort()})},[]);let ek=()=>{C(!0)},eC=async()=>{try{if(_(!0),!A&&!O){v.toast.error("Por favor, forne\xe7a um nome ou um comando para a planilha"),_(!1);return}let e=await fetch("/api/workbooks",{method:"POST",headers:{"Content-Type":"application/json",...V?{"x-csrf-token":V}:{}},body:JSON.stringify({name:A||"Nova Planilha",description:R,aiCommand:O||null,initialData:eb})});if(!e.ok)throw Error("Erro ao criar planilha");let a=await e.json();v.toast.success("Planilha criada com sucesso"),O?s.push("/workbook/".concat(a.workbook.id,"?command=").concat(encodeURIComponent(O))):s.push("/workbook/".concat(a.workbook.id))}catch(e){v.toast.error("N\xe3o foi poss\xedvel criar a planilha"),_(!1)}},eA=e=>{D(e),C(!0)},eZ=(0,N.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{if(!(null==o?void 0:o.user))return;en.current.recentWorkbooks&&en.current.recentWorkbooks.abort();let a=new AbortController;if(en.current.recentWorkbooks=a,!V)throw Error("Token CSRF n\xe3o dispon\xedvel");let s=await fetch("/api/workbooks/recent?page=".concat(e,"&limit=").concat(ed.limit),{headers:{"Content-Type":"application/json",...V?{"x-csrf-token":V}:{}},signal:a.signal});if(delete en.current.recentWorkbooks,!s.ok){let e=await s.json();eo(a=>({...a,recentWorkbooks:e.details||e.error||"Erro ao carregar planilhas recentes"}));return}let t=await s.json();t.pagination&&ec({current:t.pagination.page,total:t.pagination.totalPages,limit:t.pagination.limit,hasMore:t.pagination.hasMore});let r=t.workbooks.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),lastAccessedAt:new Date(e.lastAccessedAt||e.updatedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));M(r),eo(e=>({...e,recentWorkbooks:""}))}catch(a){if(a instanceof DOMException&&"AbortError"===a.name)return;console.error("Erro ao carregar planilhas recentes:",a);let e=a instanceof Error?"Falha ao carregar planilhas: ".concat(a.message):"Erro ao carregar planilhas recentes";v.toast.error(e),eo(a=>({...a,recentWorkbooks:e}))}},[o,V,ed.limit]);(0,N.useEffect)(()=>{(null==o?void 0:o.user)&&eZ(0)},[o,eZ]);let eE=()=>{ed.hasMore&&eZ(ed.current+1)},eS=()=>{ed.current>0&&eZ(ed.current-1)},eR=(0,N.useCallback)(async()=>{try{if(!(null==o?void 0:o.user))return;X(!0),en.current.sharedWorkbooks&&en.current.sharedWorkbooks.abort();let e=new AbortController;en.current.sharedWorkbooks=e;let a=await fetch("/api/workbooks/shared",{headers:{"Content-Type":"application/json",...V?{"x-csrf-token":V}:{}},signal:e.signal});if(delete en.current.sharedWorkbooks,!a.ok){let e=await a.json();eo(a=>({...a,sharedWorkbooks:e.details||e.error||"Erro ao carregar planilhas compartilhadas"}));return}let s=(await a.json()).workbooks.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),sharedAt:new Date(e.sharedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));U(s),eo(e=>({...e,sharedWorkbooks:""}))}catch(a){if(a instanceof DOMException&&"AbortError"===a.name)return;console.error("Erro ao carregar planilhas compartilhadas:",a);let e=a instanceof Error?"Falha ao carregar planilhas compartilhadas: ".concat(a.message):"Erro ao carregar planilhas compartilhadas";v.toast.error(e),eo(a=>({...a,sharedWorkbooks:e}))}finally{X(!1)}},[o,V]);(0,N.useEffect)(()=>{(null==o?void 0:o.user)&&eR()},[o,eR]);let eB=e=>(0,r.Q)(e,{addSuffix:!0,locale:l.F}),ez=e=>{s.push("/workbook/".concat(e))};return"loading"===b?(0,t.jsx)("div",{className:"h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)(c.Z,{className:"h-10 w-10 animate-spin text-primary"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):"unauthenticated"===b?null:k?(0,t.jsxs)("main",{className:"container mx-auto py-6 px-4 max-w-3xl",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Criar Nova Planilha"}),(0,t.jsx)(E.Button,{variant:"ghost",onClick:()=>C(!1),children:"Voltar"})]}),(0,t.jsxs)(S.Zb,{className:"mb-6",children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-xl",children:"Informa\xe7\xf5es B\xe1sicas"}),(0,t.jsx)(S.SZ,{children:"Defina os detalhes da sua planilha"})]}),(0,t.jsxs)(S.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eu._,{htmlFor:"name",children:"Nome da Planilha"}),(0,t.jsx)(eh.Z,{id:"name",placeholder:"Ex: Controle Financeiro 2023",value:A,onChange:e=>Z(e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eu._,{htmlFor:"description",children:"Descri\xe7\xe3o (opcional)"}),(0,t.jsx)(ev.Z,{id:"description",placeholder:"Descreva o prop\xf3sito desta planilha...",rows:2,value:R,onChange:e=>P(e.target.value)})]})]})]}),(0,t.jsxs)(S.Zb,{className:"mb-6",children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsxs)(S.ll,{className:"text-xl flex items-center gap-2",children:[(0,t.jsx)(m.Z,{className:"h-5 w-5 text-primary"}),"Assistente IA"]}),(0,t.jsx)(S.SZ,{children:"Descreva o que voc\xea deseja criar e deixe a IA fazer o trabalho"})]}),(0,t.jsx)(S.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eu._,{htmlFor:"prompt",children:"Comando para IA (opcional)"}),(0,t.jsx)(ev.Z,{id:"prompt",placeholder:"Ex: Crie uma planilha de controle financeiro com categorias de gastos e gr\xe1ficos...",rows:3,value:O,onChange:e=>D(e.target.value)}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Descreva em linguagem natural o que voc\xea quer criar. Quanto mais detalhes, melhor ser\xe1 o resultado."})]})}),(0,t.jsxs)(S.eW,{className:"flex justify-between items-center",children:[(0,t.jsx)(E.Button,{variant:"outline",onClick:()=>D(""),disabled:!O,children:"Limpar"}),(0,t.jsx)(E.Button,{onClick:()=>eC(),disabled:F,className:"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",children:F?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Criando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Criar Nova Planilha"]})})]})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Dica: voc\xea tamb\xe9m pode usar templates pr\xe9-definidos"}),(0,t.jsx)(eN.WorkbookTemplatesServer,{})]})]}):(0,t.jsxs)("main",{className:"container mx-auto py-6 px-4 max-w-7xl",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-6 mb-8 shadow-sm",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gradient bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400",children:[w,", ",(null==o?void 0:null===(a=o.user)||void 0===a?void 0:null===(e=a.name)||void 0===e?void 0:e.split(" ")[0])||"Bem-vindo","!"]}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2 max-w-xl",children:"Pronto para turbinar suas planilhas? Use o Excel Copilot com IA para criar, editar e analisar dados de forma inteligente."})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(E.Button,{size:"lg",onClick:()=>ek(),className:"mt-4 md:mt-0 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-all duration-300 hover:shadow-lg",children:[(0,t.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),"Criar Nova Planilha"]})})]})}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-medium mb-4",children:"Criar Rapidamente"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:ew.map((e,a)=>(0,t.jsxs)(S.Zb,{className:"cursor-pointer hover:shadow-md transition-all border-2 hover:border-primary/20",onClick:()=>eA(e.command),children:[(0,t.jsx)(S.Ol,{className:"pb-2",children:(0,t.jsxs)(S.ll,{className:"text-lg flex items-center gap-2",children:[e.icon,e.title]})}),(0,t.jsx)(S.aY,{children:(0,t.jsx)(S.SZ,{children:e.description})})]},a))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)(L,{activities:(null==Y?void 0:Y.recentActivity)||[],isLoading:!1,maxItems:8})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(B,{onCreateWorkbook:()=>C(!0),recentWorkbooks:I}),(0,t.jsx)(z,{}),(0,t.jsx)(T,{recentWorkbooks:I})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"mb-6 space-y-4",children:(0,t.jsxs)("div",{className:"flex gap-4 items-start",children:[(0,t.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,t.jsx)(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,t.jsx)(eh.Z,{placeholder:"Buscar planilhas...",value:em,onChange:e=>ex(e.target.value),className:"pl-10 pr-10"}),em&&(0,t.jsx)(E.Button,{variant:"ghost",size:"sm",onClick:()=>ex(""),className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",children:(0,t.jsx)(u.Z,{className:"h-3 w-3"})})]}),(0,t.jsx)(ef,{filters:ep,onFiltersChange:ey,onReset:()=>ey({sortBy:"updatedAt",sortOrder:"desc",dateRange:"all"})})]})}),(0,t.jsxs)(ej.mQ,{defaultValue:"all",className:"w-full",children:[(0,t.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,t.jsxs)(ej.dr,{children:[(0,t.jsx)(ej.SP,{value:"all",children:"Todas as Planilhas"}),(0,t.jsx)(ej.SP,{value:"recent",children:"Recentes"}),(0,t.jsx)(ej.SP,{value:"shared",children:"Compartilhadas"})]})}),(0,t.jsx)(ej.nU,{value:"all",className:"mt-0",children:(0,t.jsx)(ei,{searchQuery:em,filters:ep,onFiltersChange:ey})}),(0,t.jsxs)(ej.nU,{value:"recent",className:"mt-0",children:[el.recentWorkbooks?(0,t.jsx)("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Erro ao carregar planilhas recentes"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:(0,t.jsx)("p",{children:el.recentWorkbooks})}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(E.Button,{size:"sm",variant:"outline",onClick:()=>eZ(ed.current),className:"text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40",children:"Tentar novamente"})})]})]})}):null,I.length>0?(0,t.jsxs)("div",{className:"border rounded-md",children:[(0,t.jsxs)(G,{children:[(0,t.jsx)(K,{children:(0,t.jsxs)(ea,{children:[(0,t.jsx)(es,{children:"Nome"}),(0,t.jsx)(es,{children:"\xdaltimo acesso"}),(0,t.jsx)(es,{className:"text-right",children:"A\xe7\xf5es"})]})}),(0,t.jsx)(ee,{children:I.map(e=>(0,t.jsxs)(ea,{className:"cursor-pointer hover:bg-muted/40",onClick:()=>ez(e.id),children:[(0,t.jsxs)(et,{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(i.Z,{className:"h-4 w-4 text-blue-500"}),e.name]}),(0,t.jsx)(et,{className:"text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(x.Z,{className:"h-3 w-3"}),eB(e.lastAccessedAt||e.updatedAt)]})}),(0,t.jsx)(et,{className:"text-right",children:(0,t.jsx)(eg,{workbookId:e.id,onComplete:eZ,buttonVariant:"ghost",buttonSize:"icon"})})]},e.id))})]}),(0,t.jsx)(()=>(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["P\xe1gina ",ed.current+1," de ",Math.max(1,ed.total)]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(E.Button,{variant:"outline",size:"sm",onClick:eS,disabled:ed.current<=0,children:[(0,t.jsx)(n.Z,{className:"h-4 w-4 mr-1"})," Anterior"]}),(0,t.jsxs)(E.Button,{variant:"outline",size:"sm",onClick:eE,disabled:!ed.hasMore,children:["Pr\xf3xima ",(0,t.jsx)(d.Z,{className:"h-4 w-4 ml-1"})]})]})]}),{})]}):(0,t.jsx)($,{icon:(0,t.jsx)(i.Z,{className:"h-10 w-10"}),title:"Nenhuma planilha recente",description:"As planilhas que voc\xea acessou recentemente aparecer\xe3o aqui."})]}),(0,t.jsxs)(ej.nU,{value:"shared",className:"mt-0",children:[el.sharedWorkbooks?(0,t.jsx)("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Erro ao carregar planilhas compartilhadas"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:(0,t.jsx)("p",{children:el.sharedWorkbooks})}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(E.Button,{size:"sm",variant:"outline",onClick:eR,className:"text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40",children:"Tentar novamente"})})]})]})}):null,q?(0,t.jsx)("div",{className:"w-full py-10 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"})}):Q.length>0?(0,t.jsx)("div",{className:"border rounded-md",children:(0,t.jsxs)(G,{children:[(0,t.jsx)(K,{children:(0,t.jsxs)(ea,{children:[(0,t.jsx)(es,{children:"Nome"}),(0,t.jsx)(es,{children:"Compartilhado por"}),(0,t.jsx)(es,{children:"Data de compartilhamento"}),(0,t.jsx)(es,{className:"text-right",children:"A\xe7\xf5es"})]})}),(0,t.jsx)(ee,{children:Q.map(e=>{var a;return(0,t.jsxs)(ea,{className:"cursor-pointer hover:bg-muted/40",onClick:()=>ez(e.id),children:[(0,t.jsxs)(et,{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(i.Z,{className:"h-4 w-4 text-blue-500"}),e.name]}),(0,t.jsx)(et,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.sharedBy.image?(0,t.jsx)(f.default,{src:e.sharedBy.image,alt:e.sharedBy.name||"Usu\xe1rio",width:24,height:24,className:"h-6 w-6 rounded-full"}):(0,t.jsx)("div",{className:"h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs",children:(null===(a=e.sharedBy.name)||void 0===a?void 0:a.charAt(0))||"?"}),(0,t.jsx)("span",{children:e.sharedBy.name||e.sharedBy.email})]})}),(0,t.jsx)(et,{className:"text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.Z,{className:"h-3 w-3"}),eB(e.sharedAt)]})}),(0,t.jsx)(et,{className:"text-right",children:(0,t.jsx)(eg,{workbookId:e.id,onComplete:eR,onlyEdit:!0,buttonVariant:"ghost",buttonSize:"sm",allowDelete:!1,allowDuplicate:!1})})]},e.id)})})]})}):(0,t.jsx)($,{icon:(0,t.jsx)(i.Z,{className:"h-10 w-10"}),title:"Nenhuma planilha compartilhada",description:"As planilhas compartilhadas com voc\xea aparecer\xe3o aqui."})]})]})]})]})}},31590:function(e,a,s){"use strict";s.d(a,{$F:function(){return m},AW:function(){return u},Ju:function(){return p},Qk:function(){return h},VD:function(){return f},Xi:function(){return x},h_:function(){return c}});var t=s(57437),r=s(81622),l=s(87592),i=s(22468),o=s(28165),n=s(2265),d=s(49354);let c=r.fC,m=r.xz,h=r.ZA;r.Uv,r.Tr,r.Ee,n.forwardRef((e,a)=>{let{className:s,inset:i,children:o,...n}=e;return(0,t.jsxs)(r.fF,{ref:a,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",i&&"pl-8",s),...n,children:[o,(0,t.jsx)(l.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=r.fF.displayName,n.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.tu,{ref:a,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...l})}).displayName=r.tu.displayName;let u=n.forwardRef((e,a)=>{let{className:s,sideOffset:l=4,...i}=e;return(0,t.jsx)(r.Uv,{children:(0,t.jsx)(r.VY,{ref:a,sideOffset:l,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})})});u.displayName=r.VY.displayName;let x=n.forwardRef((e,a)=>{let{className:s,inset:l,...i}=e;return(0,t.jsx)(r.ck,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",s),...i})});x.displayName=r.ck.displayName,n.forwardRef((e,a)=>{let{className:s,children:l,checked:o,...n}=e;return(0,t.jsxs)(r.oC,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:null!=o&&o,...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(r.wU,{children:(0,t.jsx)(i.Z,{className:"h-4 w-4"})})}),l]})}).displayName=r.oC.displayName,n.forwardRef((e,a)=>{let{className:s,children:l,...i}=e;return(0,t.jsxs)(r.Rk,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(r.wU,{children:(0,t.jsx)(o.Z,{className:"h-2 w-2 fill-current"})})}),l]})}).displayName=r.Rk.displayName;let p=n.forwardRef((e,a)=>{let{className:s,inset:l,...i}=e;return(0,t.jsx)(r.__,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",s),...i})});p.displayName=r.__.displayName;let f=n.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...l})});f.displayName=r.Z0.displayName},70402:function(e,a,s){"use strict";s.d(a,{_:function(){return d}});var t=s(57437),r=s(38364),l=s(13027),i=s(2265),o=s(49354);let n=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.f,{ref:a,className:(0,o.cn)(n(),s),...l})});d.displayName=r.f.displayName},21413:function(e,a,s){"use strict";s.d(a,{J2:function(){return o},xo:function(){return n},yk:function(){return d}});var t=s(57437),r=s(61485),l=s(2265),i=s(49354);let o=r.fC,n=r.xz,d=l.forwardRef((e,a)=>{let{className:s,align:l="center",sideOffset:o=4,...n}=e;return(0,t.jsx)(r.h_,{children:(0,t.jsx)(r.VY,{ref:a,align:l,sideOffset:o,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n})})});d.displayName=r.VY.displayName},2128:function(e,a,s){"use strict";s.d(a,{Bw:function(){return p},Ph:function(){return c},Ql:function(){return f},i4:function(){return h},ki:function(){return m}});var t=s(57437),r=s(17549),l=s(42421),i=s(14392),o=s(22468),n=s(2265),d=s(49354);let c=r.fC;r.ZA;let m=r.B4,h=n.forwardRef((e,a)=>{let{className:s,children:i,...o}=e;return(0,t.jsxs)(r.xz,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...o,children:[i,(0,t.jsx)(r.JO,{asChild:!0,children:(0,t.jsx)(l.Z,{className:"h-4 w-4 opacity-50"})})]})});h.displayName=r.xz.displayName;let u=n.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.u_,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...l,children:(0,t.jsx)(i.Z,{className:"h-4 w-4"})})});u.displayName=r.u_.displayName;let x=n.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)(r.$G,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...i,children:(0,t.jsx)(l.Z,{className:"h-4 w-4"})})});x.displayName=r.$G.displayName;let p=n.forwardRef((e,a)=>{let{className:s,children:l,position:i="popper",...o}=e;return(0,t.jsx)(r.h_,{children:(0,t.jsxs)(r.VY,{ref:a,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...o,children:[(0,t.jsx)(u,{}),(0,t.jsx)(r.l_,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,t.jsx)(x,{})]})})});p.displayName=r.VY.displayName,n.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.__,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...l})}).displayName=r.__.displayName;let f=n.forwardRef((e,a)=>{let{className:s,children:l,...i}=e;return(0,t.jsxs)(r.ck,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(r.wU,{children:(0,t.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,t.jsx)(r.eT,{children:l})]})});f.displayName=r.ck.displayName,n.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...l})}).displayName=r.Z0.displayName},86864:function(e,a,s){"use strict";s.d(a,{SP:function(){return d},dr:function(){return n},mQ:function(){return o},nU:function(){return c}});var t=s(57437),r=s(62447),l=s(2265),i=s(49354);let o=r.fC,n=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.aV,{ref:a,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...l})});n.displayName=r.aV.displayName;let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.xz,{ref:a,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...l})});d.displayName=r.xz.displayName;let c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.VY,{ref:a,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...l})});c.displayName=r.VY.displayName},4919:function(e,a,s){"use strict";var t=s(57437),r=s(2265),l=s(6432);let i=r.forwardRef((e,a)=>{let{className:s,wrapperClassName:r,variant:i="default",fieldSize:o="md",textareaSize:n,...d}=e,c=(0,t.jsx)("textarea",{className:(0,l.RM)(i,n||o,!0,s),ref:a,...d});return(0,l.aF)(c,r)});i.displayName="Textarea",a.Z=i}},function(e){e.O(0,[7142,8638,7776,5660,3526,4462,231,4974,9295,193,1e3,8194,4800,2518,2971,7023,1744],function(){return e(e.s=5352)}),_N_E=e.O()}]);