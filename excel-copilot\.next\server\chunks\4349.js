"use strict";exports.id=4349,exports.ids=[4349],exports.modules={64349:(e,o,r)=>{r.d(o,{prisma:()=>i});var n=r(94007);let a={info:(e,...o)=>{},error:(e,...o)=>{console.error(`[DB ERROR] ${e}`,...o)},warn:(e,...o)=>{console.warn(`[DB WARNING] ${e}`,...o)}},s={totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null},t=[],i=global.prisma||new n.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});async function c(){try{await i.$disconnect(),a.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){a.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{s.totalQueries++,e.duration&&(t.push(e.duration),t.length>100&&t.shift(),s.averageQueryTime=t.reduce((e,o)=>e+o,0)/t.length),e.duration&&e.duration>500&&a.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{s.failedQueries++,s.connectionFailures++,s.lastConnectionFailure=new Date().toISOString(),a.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})}};