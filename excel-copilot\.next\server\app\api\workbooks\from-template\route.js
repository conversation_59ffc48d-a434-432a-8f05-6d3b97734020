"use strict";(()=>{var e={};e.id=4699,e.ids=[4699],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},55109:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>z,requestAsyncStorage:()=>x,routeModule:()=>g,serverHooks:()=>b,staticGenerationAsyncStorage:()=>f});var o={};r.r(o),r.d(o,{GET:()=>p,POST:()=>d});var a=r(49303),s=r(88716),i=r(60670),n=r(75571),c=r(81628),u=r(63841),l=r(24522),m=r(540);async function d(e){try{let t;let r=await (0,n.getServerSession)(c.L);if(!r?.user)return m.R.error("N\xe3o autorizado",401);let o=await e.json(),{templateId:a,workbookName:s,workbookDescription:i}=l.re.parse(o),d=await u.prisma.template.findUnique({where:{id:a},include:{categories:!0}});if(!d)return m.R.error("Template n\xe3o encontrado",404);if(!d.isActive)return m.R.error("Template n\xe3o est\xe1 ativo",400);if(!d.isPublic&&d.createdBy!==r.user.id)return m.R.error("Acesso negado ao template",403);try{t=JSON.parse(d.data)}catch(e){return m.R.error("Dados do template inv\xe1lidos",400)}if(!t.sheets||!Array.isArray(t.sheets))return m.R.error("Estrutura do template inv\xe1lida",400);let p=s||d.title,g=i||d.description,x=await u.prisma.$transaction(async e=>{let o=await e.workbook.create({data:{name:p,description:g,userId:r.user.id,isPublic:!1,sheets:{create:t.sheets.map((e,t)=>({name:e.name||`Sheet ${t+1}`,data:JSON.stringify(e.data||{headers:[],rows:[]})}))}},include:{sheets:!0}});return await e.templateUsage.create({data:{templateId:d.id,userId:r.user.id,workbookId:o.id}}),await e.template.update({where:{id:d.id},data:{usageCount:{increment:1},popularity:{increment:1}}}),o});return m.R.success({workbook:x,message:"Workbook criado com sucesso a partir do template"},201)}catch(e){return console.error("Erro ao criar workbook a partir do template:",e),m.R.error(e instanceof Error?e.message:"Erro interno do servidor",500)}}async function p(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("limit")||"12"),o=t.get("categoryId"),a={isActive:!0,isPublic:!0};o&&(a.categories={some:{id:o}});let s=await u.prisma.template.findMany({where:a,orderBy:[{isFeatured:"desc"},{popularity:"desc"},{usageCount:"desc"}],take:r,select:{id:!0,name:!0,title:!0,description:!0,icon:!0,isFeatured:!0,isNew:!0,popularity:!0,usageCount:!0,categories:{select:{id:!0,name:!0,slug:!0,color:!0}},_count:{select:{reviews:!0,usage:!0}},createdAt:!0}}),i=await Promise.all(s.map(async e=>{let t=await u.prisma.templateReview.aggregate({where:{templateId:e.id},_avg:{rating:!0}});return{...e,averageRating:t._avg.rating||0,reviewCount:e._count.reviews,sheets:Math.floor(3*Math.random())+1}}));return m.R.success({templates:i})}catch(e){return console.error("Erro ao buscar templates para workbooks:",e),m.R.error(e instanceof Error?e.message:"Erro interno do servidor",500)}}let g=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/workbooks/from-template/route",pathname:"/api/workbooks/from-template",filename:"route",bundlePath:"app/api/workbooks/from-template/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\from-template\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:x,staticGenerationAsyncStorage:f,serverHooks:b}=g,v="/api/workbooks/from-template/route";function z(){return(0,i.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:f})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var a=r(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var n=a?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(o,s,n):o[s]=e[s]}return o.default=e,r&&r.set(e,o),o}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},540:(e,t,r)=>{r.d(t,{R:()=>a});var o=r(87070);class a{static success(e,t=200){return o.NextResponse.json({success:!0,data:e},{status:t})}static error(e,t=400,r){return o.NextResponse.json({success:!1,error:{message:e,...r&&{details:r}}},{status:t})}static validation(e){return o.NextResponse.json({success:!1,error:{message:"Dados inv\xe1lidos",validation:e}},{status:422})}static unauthorized(e="N\xe3o autorizado"){return o.NextResponse.json({success:!1,error:{message:e}},{status:401})}static notFound(e="Recurso n\xe3o encontrado"){return o.NextResponse.json({success:!1,error:{message:e}},{status:404})}static conflict(e="Conflito de dados"){return o.NextResponse.json({success:!1,error:{message:e}},{status:409})}static internal(e="Erro interno do servidor"){return o.NextResponse.json({success:!1,error:{message:e}},{status:500})}}},24522:(e,t,r)=>{r.d(t,{J5:()=>i,L8:()=>s,XZ:()=>a,re:()=>n});var o=r(7410);o.z.object({id:o.z.string().cuid({message:"ID de template inv\xe1lido"})});let a=o.z.object({name:o.z.string().min(1,{message:"Nome do template \xe9 obrigat\xf3rio"}).max(100,{message:"Nome do template deve ter no m\xe1ximo 100 caracteres"}),title:o.z.string().min(1,{message:"T\xedtulo do template \xe9 obrigat\xf3rio"}).max(150,{message:"T\xedtulo do template deve ter no m\xe1ximo 150 caracteres"}),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),icon:o.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),isPublic:o.z.boolean().default(!0),isFeatured:o.z.boolean().default(!1),isNew:o.z.boolean().default(!1),data:o.z.any(),categoryIds:o.z.array(o.z.string().cuid()).optional()});o.z.object({name:o.z.string().min(1,{message:"Nome do template \xe9 obrigat\xf3rio"}).max(100,{message:"Nome do template deve ter no m\xe1ximo 100 caracteres"}).optional(),title:o.z.string().min(1,{message:"T\xedtulo do template \xe9 obrigat\xf3rio"}).max(150,{message:"T\xedtulo do template deve ter no m\xe1ximo 150 caracteres"}).optional(),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),icon:o.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),isPublic:o.z.boolean().optional(),isFeatured:o.z.boolean().optional(),isNew:o.z.boolean().optional(),data:o.z.any().optional(),categoryIds:o.z.array(o.z.string().cuid()).optional()});let s=o.z.object({categoryId:o.z.string().cuid().optional(),isPublic:o.z.boolean().optional(),isFeatured:o.z.boolean().optional(),isNew:o.z.boolean().optional(),search:o.z.string().max(100).optional(),sortBy:o.z.enum(["popularity","recent","name","usage"]).default("popularity"),sortOrder:o.z.enum(["asc","desc"]).default("desc"),limit:o.z.number().int().min(1).max(100).default(20),offset:o.z.number().int().min(0).default(0)}),i=o.z.object({name:o.z.string().min(1,{message:"Nome da categoria \xe9 obrigat\xf3rio"}).max(50,{message:"Nome da categoria deve ter no m\xe1ximo 50 caracteres"}),slug:o.z.string().min(1,{message:"Slug da categoria \xe9 obrigat\xf3rio"}).max(50,{message:"Slug da categoria deve ter no m\xe1ximo 50 caracteres"}).regex(/^[a-z0-9-]+$/,{message:"Slug deve conter apenas letras min\xfasculas, n\xfameros e h\xedfens"}),description:o.z.string().max(200,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 200 caracteres"}).optional(),icon:o.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),color:o.z.string().regex(/^#[0-9A-Fa-f]{6}$/,{message:"Cor deve estar no formato hexadecimal (#RRGGBB)"}).optional(),sortOrder:o.z.number().int().min(0).default(0)});o.z.object({templateId:o.z.string().cuid({message:"ID de template inv\xe1lido"}),rating:o.z.number().int().min(1,{message:"Avalia\xe7\xe3o deve ser entre 1 e 5"}).max(5,{message:"Avalia\xe7\xe3o deve ser entre 1 e 5"}),comment:o.z.string().max(1e3,{message:"Coment\xe1rio deve ter no m\xe1ximo 1000 caracteres"}).optional()});let n=o.z.object({templateId:o.z.string().cuid({message:"ID de template inv\xe1lido"}),workbookName:o.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}).optional(),workbookDescription:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional()});o.z.object({sheets:o.z.array(o.z.object({name:o.z.string().min(1,{message:"Nome da sheet \xe9 obrigat\xf3rio"}),data:o.z.object({headers:o.z.array(o.z.string()),rows:o.z.array(o.z.array(o.z.any()))})}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>r(55109));module.exports=o})();