"use strict";exports.id=5590,exports.ids=[5590],exports.modules={21270:(e,t,s)=>{s.d(t,{l:()=>u,q:()=>c});var r=s(45609),a=s(43895),n=s(81628),o=s(63841),i=s(82840);async function u(e,t,s){var u;let c=Date.now(),d=function(e){let t=e.headers,s=t instanceof Headers?t.get("x-forwarded-for"):t?.["x-forwarded-for"],r=t instanceof Headers?t.get("x-real-ip"):t?.["x-real-ip"],a=e.connection?.remoteAddress||e.socket?.remoteAddress,n=Array.isArray(s)?s[0]:s,o=Array.isArray(r)?r[0]:r,i=n?.split(",")[0]||o||a||"unknown";return/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(i)||/^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(i)?i:"unknown"}(e),l=(u=e.headers.get("user-agent")||void 0)&&u.substring(0,200).replace(/[<>"'&]/g,"").replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g,"[IP]").replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,"[EMAIL]")||"Unknown";try{let t=await (0,r.getServerSession)(n.L);if(!t?.user)return a.kg.warn("\uD83D\uDEAB Acesso n\xe3o autorizado a rota protegida",{path:e.nextUrl.pathname,method:e.method,ip:d,userAgent:l,timestamp:new Date().toISOString(),duration:Date.now()-c}),i.R.unauthorized("Usu\xe1rio n\xe3o autenticado");s.session=t,s.user=t.user;let u=t.user.id;if(u){s.userId=u;try{let t=await o.prisma.user.findUnique({where:{id:u}});if(t&&!0===t.isBanned){a.kg.warn("Tentativa de acesso por usu\xe1rio banido",{userId:u,path:e.nextUrl.pathname,banReason:t.banReason,banDate:t.banDate});try{await o.prisma.session.deleteMany({where:{userId:u}})}catch(e){a.kg.error("Erro ao revogar sess\xe3o de usu\xe1rio banido",e)}return i.R.forbidden(`Conta suspensa: ${t.banReason||"Viola\xe7\xe3o de termos de uso"}`)}let s={lastIpAddress:d,lastLoginAt:new Date,loginCount:{increment:1}};null!==t&&(s.userAgent=l),await o.prisma.user.update({where:{id:u},data:s}),a.kg.debug("\uD83D\uDCCA Dados de auditoria atualizados",{userId:u,ip:d,userAgent:l.substring(0,50)+"...",path:e.nextUrl.pathname})}catch(e){a.kg.error("Erro ao verificar dados do usu\xe1rio",e)}}a.kg.debug("✅ Usu\xe1rio autenticado com sucesso",{userId:s.userId,path:e.nextUrl.pathname,duration:Date.now()-c})}catch(t){return a.kg.error("❌ Erro cr\xedtico ao verificar autentica\xe7\xe3o",{error:t instanceof Error?t.message:"Erro desconhecido",stack:t instanceof Error?t.stack:void 0,path:e.nextUrl.pathname,method:e.method,ip:d,duration:Date.now()-c}),i.R.error("Erro ao verificar autentica\xe7\xe3o","AUTH_ERROR",500)}}async function c(e,t,s){try{let t=await (0,r.getServerSession)(n.L);if(t?.user){s.session=t,s.user=t.user;let r=t.user.id;if(r){s.userId=r;try{let t=await o.prisma.user.findUnique({where:{id:r}});t&&!0===t.isBanned?(s.isBanned=!0,a.kg.warn("Usu\xe1rio banido acessando rota opcional",{userId:r,path:e.nextUrl.pathname})):s.isBanned=!1}catch(e){a.kg.error("Erro ao verificar banimento do usu\xe1rio",e),s.isBanned=!1}}s.isAuthenticated=!0}else s.isAuthenticated=!1,s.isBanned=!1}catch(e){a.kg.error("Erro ao verificar autentica\xe7\xe3o opcional",e),s.isAuthenticated=!1,s.isBanned=!1}}},99747:(e,t,s)=>{s.d(t,{x:()=>n});var r=s(87070),a=s(43895);function n(e,t){let s=function(...e){return async(t,s=new r.NextResponse,n={})=>{try{for(let a of e){let e=await a(t,s,n);if(e instanceof r.NextResponse)return e}return{res:s,context:n}}catch(e){return a.kg.error("Erro na cadeia de middleware",e),r.NextResponse.json({code:"MIDDLEWARE_ERROR",message:"Erro interno no servidor",timestamp:new Date().toISOString()},{status:500})}}}(...e);return async e=>{let n=new r.NextResponse,o={},i=await s(e,n,o);if(i instanceof r.NextResponse)return i;try{let s=i&&"object"==typeof i&&"context"in i?{...o,...i.context}:o;return await t(e,s)}catch(e){return a.kg.error("Erro no handler de API",e),r.NextResponse.json({code:"HANDLER_ERROR",message:e instanceof Error?e.message:"Erro interno no servidor",timestamp:new Date().toISOString()},{status:500})}}}},39762:(e,t,s)=>{s.d(t,{Ev:()=>n,ON:()=>i,ng:()=>o});var r=s(43895);let a={totalRequests:0,successfulRequests:0,failedRequests:0,responseTimeTotal:0,requestsByPath:new Map,requestsByMethod:new Map,requestsByStatusCode:new Map};async function n(e,t,s){let n=Date.now(),o=e.nextUrl.pathname,i=e.method;a.totalRequests+=1,a.requestsByPath.set(o,(a.requestsByPath.get(o)||0)+1),a.requestsByMethod.set(i,(a.requestsByMethod.get(i)||0)+1),s.requestStartTime=n,s.recordMetrics=e=>{let t=Date.now()-n;e<400?a.successfulRequests+=1:a.failedRequests+=1;let u=e.toString();return a.requestsByStatusCode.set(u,(a.requestsByStatusCode.get(u)||0)+1),a.responseTimeTotal+=t,r.kg.debug(`${i} ${o} - ${e} - ${t}ms`,{path:o,method:i,statusCode:e,duration:t,userId:s.userId}),{path:o,method:i,statusCode:e,duration:t,userId:s.userId}}}function o(e,t){return t.recordMetrics&&"function"==typeof t.recordMetrics&&t.recordMetrics(e.status),e}function i(){return{totalRequests:a.totalRequests,successfulRequests:a.successfulRequests,failedRequests:a.failedRequests,averageResponseTime:a.totalRequests>0?a.responseTimeTotal/a.totalRequests:0,requestsByPath:Object.fromEntries(a.requestsByPath),requestsByMethod:Object.fromEntries(a.requestsByMethod),requestsByStatusCode:Object.fromEntries(a.requestsByStatusCode),timestamp:new Date().toISOString()}}},4579:(e,t,s)=>{s.d(t,{UZ:()=>d,e5:()=>c,jw:()=>u});var r=s(52972),a=s(43895);let n=r.Vi.CACHE?.DEFAULT_TTL||60;class o{constructor(){this.cache=new Map,this.checkInterval=null,this.stats={hits:0,misses:0,sets:0,evictions:0},this.checkInterval=setInterval(()=>this.cleanup(),3e4)}set(e,t,s=n){this.cache.size>=1e3&&this.evictOldest(),this.cache.set(e,{value:t,expiry:Date.now()+1e3*s}),this.stats.sets++}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiry?(this.cache.delete(e),this.stats.misses++,null):(this.stats.hits++,t.value):(this.stats.misses++,null)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}invalidateByPrefix(e){let t=0;for(let s of this.cache.keys())s.startsWith(e)&&(this.cache.delete(s),t++);return t}cleanup(){let e=Date.now();for(let[t,s]of this.cache.entries())e>s.expiry&&this.cache.delete(t)}evictOldest(){let e=null,t=Date.now();for(let[s,r]of this.cache.entries())r.expiry<t&&(e=s,t=r.expiry);e&&(this.cache.delete(e),this.stats.evictions++)}shutdown(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null,a.kg.debug("Cache de consultas desligado")),this.clear()}getStats(){return{...this.stats,size:this.cache.size,maxSize:1e3,hitRatio:this.stats.hits/(this.stats.hits+this.stats.misses)||0}}}let i=new o;async function u(e,t,s={}){if(!r.Vi.IS_PRODUCTION)return e();let a=t.map(e=>{if(null==e)return"null";if("object"==typeof e)try{return JSON.stringify(e)}catch{}return String(e)}).join(":"),n=i.get(a);if(null!==n)return n;let o=await e();return i.set(a,o,s.ttl),o}function c(e){return i.invalidateByPrefix(e)}function d(){return i.getStats()}"undefined"!=typeof process&&process.on("beforeExit",()=>{i.shutdown(),a.kg.debug("Cache de queries desligado")})},82840:(e,t,s)=>{s.d(t,{R:()=>n});var r=s(87070),a=s(43895);let n={success(e,t,s=200){let a={data:e,...t&&{meta:t}};return r.NextResponse.json(a,{status:s})},error(e,t="INTERNAL_ERROR",s=500,n){let o={code:t,message:e,timestamp:new Date().toISOString(),...void 0!==n&&{details:n}};return a.kg.error(`API Error [${t}]: ${e}`,{details:n}),r.NextResponse.json(o,{status:s})},unauthorized(e="N\xe3o autorizado",t){return this.error(e,"UNAUTHORIZED",401,t)},badRequest(e,t){return this.error(e,"BAD_REQUEST",400,t)},notFound(e="Recurso n\xe3o encontrado",t){return this.error(e,"NOT_FOUND",404,t)},forbidden(e="Acesso negado",t){return this.error(e,"FORBIDDEN",403,t)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",t){let s={};return t&&(s["Retry-After"]=t.toString()),r.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:s})}}}};