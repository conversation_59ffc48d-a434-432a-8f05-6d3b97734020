"use strict";(()=>{var e={};e.id=2474,e.ids=[2474],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},84555:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>D,requestAsyncStorage:()=>O,routeModule:()=>q,serverHooks:()=>P,staticGenerationAsyncStorage:()=>j});var o={};t.r(o),t.d(o,{DELETE:()=>F,GET:()=>R,POST:()=>k,dynamic:()=>w,runtime:()=>E});var a=t(49303),i=t(88716),n=t(60670),s=t(87070),u=t(75571),c=t(81628),d=t(43895),p=t(6113),l=t(63841),f=t(52972);let g=new Map;async function m(e,r){try{let t=Math.floor(1e5+9e5*Math.random()).toString(),o=Date.now()+3e5;return g.set(e,{code:t,expires:o,attempts:0}),d.kg.info("\uD83D\uDCE7 C\xf3digo 2FA gerado",{userId:e,email:r.replace(/(.{2}).*(@.*)/,"$1***$2"),code:"development"===f.Vi.NODE_ENV?t:"******",expiresIn:"5 minutos"}),!0}catch(r){return d.kg.error("Erro ao enviar c\xf3digo 2FA por email",{error:r,userId:e}),!1}}async function v(e,r){try{let t=g.get(e);if(!t)return d.kg.warn("Tentativa de verifica\xe7\xe3o 2FA sem c\xf3digo armazenado",{userId:e}),!1;if(Date.now()>t.expires)return g.delete(e),d.kg.warn("C\xf3digo 2FA expirado",{userId:e}),!1;if(t.attempts++,t.attempts>3)return g.delete(e),d.kg.warn("Muitas tentativas de verifica\xe7\xe3o 2FA",{userId:e,attempts:t.attempts}),!1;if(t.code===r)return g.delete(e),d.kg.info("✅ C\xf3digo 2FA verificado com sucesso",{userId:e}),!0;return d.kg.warn("C\xf3digo 2FA inv\xe1lido",{userId:e,attempts:t.attempts}),!1}catch(r){return d.kg.error("Erro ao verificar c\xf3digo 2FA",{error:r,userId:e}),!1}}async function h(e){try{let r=(0,p.randomBytes)(20).toString("hex"),t=function(e=10){let r=[];for(let t=0;t<e;t++){let e=(0,p.randomBytes)(4).toString("hex").toUpperCase();r.push(e)}return r}();return t.map(e=>(0,p.createHash)("sha256").update(e).digest("hex")),d.kg.info("✅ 2FA habilitado para usu\xe1rio",{userId:e}),{secret:r,backupCodes:t}}catch(r){return d.kg.error("Erro ao habilitar 2FA",{error:r,userId:e}),null}}async function y(e){try{return g.delete(e),d.kg.info("✅ 2FA desabilitado para usu\xe1rio",{userId:e}),!0}catch(r){return d.kg.error("Erro ao desabilitar 2FA",{error:r,userId:e}),!1}}async function x(e){try{return!1}catch(r){return d.kg.error("Erro ao verificar status 2FA",{error:r,userId:e}),!1}}async function A(e){try{return await l.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0}}),!1}catch(r){return d.kg.error("Erro ao verificar necessidade de 2FA",{error:r,userId:e}),!1}}var b=t(82840);let w="force-dynamic",E="nodejs";async function R(e){try{let e=await (0,u.getServerSession)(c.L);if(!e?.user?.id)return s.NextResponse.json({error:"N\xe3o autenticado"},{status:401});let r=e.user.id,t=await x(r),o=await A(r);return b.R.success({isEnabled:t,isRequired:o,userId:r})}catch(e){return d.kg.error("Erro ao verificar status 2FA",{error:e}),b.R.error("Erro interno do servidor")}}async function k(e){try{let r=await (0,u.getServerSession)(c.L);if(!r?.user?.id)return s.NextResponse.json({error:"N\xe3o autenticado"},{status:401});let t=r.user.id,{action:o,code:a}=await e.json();switch(o){case"enable":let i=await h(t);if(!i)return b.R.error("Erro ao habilitar 2FA");return d.kg.info("✅ 2FA habilitado via API",{userId:t}),b.R.success({message:"2FA habilitado com sucesso",secret:i.secret,backupCodes:i.backupCodes});case"send-code":if(!r.user.email)return b.R.error("Email n\xe3o encontrado na sess\xe3o");if(!await m(t,r.user.email))return b.R.error("Erro ao enviar c\xf3digo 2FA");return b.R.success({message:"C\xf3digo 2FA enviado por email"});case"verify-code":if(!a)return b.R.error("C\xf3digo \xe9 obrigat\xf3rio");if(!await v(t,a))return b.R.error("C\xf3digo inv\xe1lido ou expirado");return b.R.success({message:"C\xf3digo verificado com sucesso",verified:!0});default:return b.R.error("A\xe7\xe3o n\xe3o suportada")}}catch(e){return d.kg.error("Erro na API 2FA",{error:e}),b.R.error("Erro interno do servidor")}}async function F(e){try{let e=await (0,u.getServerSession)(c.L);if(!e?.user?.id)return s.NextResponse.json({error:"N\xe3o autenticado"},{status:401});let r=e.user.id;if(await A(r))return b.R.error("Usu\xe1rios administrativos n\xe3o podem desabilitar 2FA");if(!await y(r))return b.R.error("Erro ao desabilitar 2FA");return d.kg.info("✅ 2FA desabilitado via API",{userId:r}),b.R.success({message:"2FA desabilitado com sucesso"})}catch(e){return d.kg.error("Erro ao desabilitar 2FA",{error:e}),b.R.error("Erro interno do servidor")}}let q=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/two-factor/route",pathname:"/api/auth/two-factor",filename:"route",bundlePath:"app/api/auth/two-factor/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\two-factor\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:O,staticGenerationAsyncStorage:j,serverHooks:P}=q,_="/api/auth/two-factor/route";function D(){return(0,n.patchFetch)({serverHooks:P,staticGenerationAsyncStorage:j})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=a?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(o,i,s):o[i]=e[i]}return o.default=e,t&&t.set(e,o),o}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})},82840:(e,r,t)=>{t.d(r,{R:()=>i});var o=t(87070),a=t(43895);let i={success(e,r,t=200){let a={data:e,...r&&{meta:r}};return o.NextResponse.json(a,{status:t})},error(e,r="INTERNAL_ERROR",t=500,i){let n={code:r,message:e,timestamp:new Date().toISOString(),...void 0!==i&&{details:i}};return a.kg.error(`API Error [${r}]: ${e}`,{details:i}),o.NextResponse.json(n,{status:t})},unauthorized(e="N\xe3o autorizado",r){return this.error(e,"UNAUTHORIZED",401,r)},badRequest(e,r){return this.error(e,"BAD_REQUEST",400,r)},notFound(e="Recurso n\xe3o encontrado",r){return this.error(e,"NOT_FOUND",404,r)},forbidden(e="Acesso negado",r){return this.error(e,"FORBIDDEN",403,r)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",r){let t={};return r&&(t["Retry-After"]=r.toString()),o.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:t})}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>t(84555));module.exports=o})();