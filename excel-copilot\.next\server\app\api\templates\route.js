"use strict";(()=>{var e={};e.id=6412,e.ids=[6412],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},56681:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>b,patchFetch:()=>y,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>p});var o=r(49303),s=r(88716),i=r(60670),n=r(75571),c=r(81628),u=r(63841),l=r(24522),d=r(540);async function m(e){try{let{searchParams:t}=new URL(e.url),r={categoryId:t.get("categoryId")||void 0,isPublic:t.get("isPublic")?"true"===t.get("isPublic"):void 0,isFeatured:t.get("isFeatured")?"true"===t.get("isFeatured"):void 0,isNew:t.get("isNew")?"true"===t.get("isNew"):void 0,search:t.get("search")||void 0,sortBy:t.get("sortBy")||"popularity",sortOrder:t.get("sortOrder")||"desc",limit:t.get("limit")?parseInt(t.get("limit")):20,offset:t.get("offset")?parseInt(t.get("offset")):0},a=l.L8.parse(r),o={isActive:!0};a.categoryId&&(o.categories={some:{id:a.categoryId}}),void 0!==a.isPublic&&(o.isPublic=a.isPublic),void 0!==a.isFeatured&&(o.isFeatured=a.isFeatured),void 0!==a.isNew&&(o.isNew=a.isNew),a.search&&(o.OR=[{name:{contains:a.search,mode:"insensitive"}},{title:{contains:a.search,mode:"insensitive"}},{description:{contains:a.search,mode:"insensitive"}}]);let s={};switch(a.sortBy){case"popularity":s.popularity=a.sortOrder;break;case"recent":s.createdAt=a.sortOrder;break;case"name":s.name=a.sortOrder;break;case"usage":s.usageCount=a.sortOrder;break;default:s.popularity="desc"}let[i,n]=await Promise.all([u.prisma.template.findMany({where:o,orderBy:s,take:a.limit,skip:a.offset,include:{categories:{select:{id:!0,name:!0,slug:!0,color:!0}},creator:{select:{id:!0,name:!0,image:!0}},_count:{select:{reviews:!0,usage:!0}}}}),u.prisma.template.count({where:o})]),c=await Promise.all(i.map(async e=>{let t=await u.prisma.templateReview.aggregate({where:{templateId:e.id},_avg:{rating:!0}});return{id:e.id,name:e.name,title:e.title,description:e.description,icon:e.icon,isPublic:e.isPublic,isFeatured:e.isFeatured,isNew:e.isNew,popularity:e.popularity,usageCount:e.usageCount,categories:e.categories,creator:e.creator,reviewCount:e._count.reviews,usageTotal:e._count.usage,averageRating:t._avg.rating||0,createdAt:e.createdAt,updatedAt:e.updatedAt}}));return d.R.success({templates:c,pagination:{total:n,limit:a.limit,offset:a.offset,hasMore:a.offset+a.limit<n}})}catch(e){return console.error("Erro ao buscar templates:",e),d.R.error(e instanceof Error?e.message:"Erro interno do servidor",500)}}async function p(e){try{let t=await (0,n.getServerSession)(c.L);if(!t?.user)return d.R.error("N\xe3o autorizado",401);let r=await e.json(),a=l.XZ.parse(r),o=await u.prisma.template.create({data:{name:a.name,title:a.title,description:a.description||null,icon:a.icon||null,isPublic:a.isPublic,isFeatured:a.isFeatured,isNew:a.isNew,data:JSON.stringify(a.data),createdBy:t.user.id,...a.categoryIds&&a.categoryIds.length>0&&{categories:{connect:a.categoryIds.map(e=>({id:e}))}}},include:{categories:!0,creator:{select:{id:!0,name:!0,image:!0}}}});return d.R.success(o,201)}catch(e){return console.error("Erro ao criar template:",e),d.R.error(e instanceof Error?e.message:"Erro interno do servidor",500)}}let g=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/templates/route",pathname:"/api/templates",filename:"route",bundlePath:"app/api/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\templates\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:f,staticGenerationAsyncStorage:x,serverHooks:v}=g,b="/api/templates/route";function y(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var n=o?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(a,s,n):a[s]=e[s]}return a.default=e,r&&r.set(e,a),a}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},540:(e,t,r)=>{r.d(t,{R:()=>o});var a=r(87070);class o{static success(e,t=200){return a.NextResponse.json({success:!0,data:e},{status:t})}static error(e,t=400,r){return a.NextResponse.json({success:!1,error:{message:e,...r&&{details:r}}},{status:t})}static validation(e){return a.NextResponse.json({success:!1,error:{message:"Dados inv\xe1lidos",validation:e}},{status:422})}static unauthorized(e="N\xe3o autorizado"){return a.NextResponse.json({success:!1,error:{message:e}},{status:401})}static notFound(e="Recurso n\xe3o encontrado"){return a.NextResponse.json({success:!1,error:{message:e}},{status:404})}static conflict(e="Conflito de dados"){return a.NextResponse.json({success:!1,error:{message:e}},{status:409})}static internal(e="Erro interno do servidor"){return a.NextResponse.json({success:!1,error:{message:e}},{status:500})}}},24522:(e,t,r)=>{r.d(t,{J5:()=>i,L8:()=>s,XZ:()=>o,re:()=>n});var a=r(7410);a.z.object({id:a.z.string().cuid({message:"ID de template inv\xe1lido"})});let o=a.z.object({name:a.z.string().min(1,{message:"Nome do template \xe9 obrigat\xf3rio"}).max(100,{message:"Nome do template deve ter no m\xe1ximo 100 caracteres"}),title:a.z.string().min(1,{message:"T\xedtulo do template \xe9 obrigat\xf3rio"}).max(150,{message:"T\xedtulo do template deve ter no m\xe1ximo 150 caracteres"}),description:a.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),icon:a.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),isPublic:a.z.boolean().default(!0),isFeatured:a.z.boolean().default(!1),isNew:a.z.boolean().default(!1),data:a.z.any(),categoryIds:a.z.array(a.z.string().cuid()).optional()});a.z.object({name:a.z.string().min(1,{message:"Nome do template \xe9 obrigat\xf3rio"}).max(100,{message:"Nome do template deve ter no m\xe1ximo 100 caracteres"}).optional(),title:a.z.string().min(1,{message:"T\xedtulo do template \xe9 obrigat\xf3rio"}).max(150,{message:"T\xedtulo do template deve ter no m\xe1ximo 150 caracteres"}).optional(),description:a.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),icon:a.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),isPublic:a.z.boolean().optional(),isFeatured:a.z.boolean().optional(),isNew:a.z.boolean().optional(),data:a.z.any().optional(),categoryIds:a.z.array(a.z.string().cuid()).optional()});let s=a.z.object({categoryId:a.z.string().cuid().optional(),isPublic:a.z.boolean().optional(),isFeatured:a.z.boolean().optional(),isNew:a.z.boolean().optional(),search:a.z.string().max(100).optional(),sortBy:a.z.enum(["popularity","recent","name","usage"]).default("popularity"),sortOrder:a.z.enum(["asc","desc"]).default("desc"),limit:a.z.number().int().min(1).max(100).default(20),offset:a.z.number().int().min(0).default(0)}),i=a.z.object({name:a.z.string().min(1,{message:"Nome da categoria \xe9 obrigat\xf3rio"}).max(50,{message:"Nome da categoria deve ter no m\xe1ximo 50 caracteres"}),slug:a.z.string().min(1,{message:"Slug da categoria \xe9 obrigat\xf3rio"}).max(50,{message:"Slug da categoria deve ter no m\xe1ximo 50 caracteres"}).regex(/^[a-z0-9-]+$/,{message:"Slug deve conter apenas letras min\xfasculas, n\xfameros e h\xedfens"}),description:a.z.string().max(200,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 200 caracteres"}).optional(),icon:a.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),color:a.z.string().regex(/^#[0-9A-Fa-f]{6}$/,{message:"Cor deve estar no formato hexadecimal (#RRGGBB)"}).optional(),sortOrder:a.z.number().int().min(0).default(0)});a.z.object({templateId:a.z.string().cuid({message:"ID de template inv\xe1lido"}),rating:a.z.number().int().min(1,{message:"Avalia\xe7\xe3o deve ser entre 1 e 5"}).max(5,{message:"Avalia\xe7\xe3o deve ser entre 1 e 5"}),comment:a.z.string().max(1e3,{message:"Coment\xe1rio deve ter no m\xe1ximo 1000 caracteres"}).optional()});let n=a.z.object({templateId:a.z.string().cuid({message:"ID de template inv\xe1lido"}),workbookName:a.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}).optional(),workbookDescription:a.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional()});a.z.object({sheets:a.z.array(a.z.object({name:a.z.string().min(1,{message:"Nome da sheet \xe9 obrigat\xf3rio"}),data:a.z.object({headers:a.z.array(a.z.string()),rows:a.z.array(a.z.array(a.z.any()))})}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>r(56681));module.exports=a})();