(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{11907:function(e,t,r){Promise.resolve().then(r.bind(r,79512)),Promise.resolve().then(r.t.bind(r,231,23)),Promise.resolve().then(r.t.bind(r,33827,23)),Promise.resolve().then(r.t.bind(r,53054,23)),Promise.resolve().then(r.bind(r,57039)),Promise.resolve().then(r.bind(r,1474)),Promise.resolve().then(r.bind(r,35519)),Promise.resolve().then(r.bind(r,31616)),Promise.resolve().then(r.bind(r,75515))},57039:function(e,t,r){"use strict";r.d(t,{Providers:function(){return q}});var i,n,o,a,s=r(57437),l=r(47808),c=r(27079),d=r(51164),u=r(30998),m=r(79512),f=r(2265),h=r(16865),p=r(9821),g=(r(67217),r(48646),r(18473));async function v(){return Date.now(),{service:"database",status:"healthy",responseTime:0,error:void 0,details:{type:"mysql",provider:"planetscale",mode:"client-side-mock"}}}async function x(){let e;let t=Date.now(),r="failing";if(p.Vi.FEATURES.USE_MOCK_AI||!p.Vi.VERTEX_AI.ENABLED)return{service:"ai-service",status:"healthy",responseTime:0,error:void 0,details:{mode:p.Vi.FEATURES.USE_MOCK_AI?"mock":"vertex-ai-disabled",provider:"vertex-ai"}};try{let t=p.Vi.VERTEX_AI.PROJECT_ID,i=p.Vi.VERTEX_AI.LOCATION;if(!t||!i)throw Error("Configura\xe7\xf5es do Vertex AI incompletas");if(p.Vi.IS_DEVELOPMENT){let e=p.Vi.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS,t=Math.min(100,e/10);await new Promise(e=>setTimeout(e,t)),r="healthy"}else try{r="healthy"}catch(t){r="degraded",e="Erro ao verificar arquivo de credenciais: ".concat(t instanceof Error?t.message:"Erro desconhecido")}}catch(t){e=t instanceof Error?t.message:"Erro desconhecido na API de IA",g.logger.error("Falha na verifica\xe7\xe3o de sa\xfade da API de IA:",t)}let i=Date.now()-t,n=.3*p.Vi.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS;return"healthy"===r&&i>n&&(r="degraded",g.logger.warn("API de IA com resposta lenta: ".concat(i,"ms (threshold: ").concat(n,"ms)"))),{service:"ai-service",status:r,responseTime:i,error:e,details:{provider:"vertex-ai",model:p.Vi.VERTEX_AI.MODEL_NAME||"gemini-1.5-pro",projectId:p.Vi.VERTEX_AI.PROJECT_ID,location:p.Vi.VERTEX_AI.LOCATION}}}async function b(){let e=Date.now(),t="healthy",r="Rate limiters funcionando normalmente",i={chat:null,excel:null,api:null};try{if(i.chat=null,i.excel=null,i.api=null,!i.chat||!i.excel||!i.api)return{service:"rate-limiters",status:"unhealthy",message:"Um ou mais rate limiters n\xe3o est\xe3o inicializados",responseTime:Date.now()-e,error:"Rate limiters n\xe3o inicializados corretamente"};let n=i.chat.getDiagnostics(),o=i.excel.getDiagnostics(),a=i.api.getDiagnostics();return(n.totalEntries>.9*n.maxEntries||o.totalEntries>.9*o.maxEntries||a.totalEntries>.9*a.maxEntries)&&(t="degraded",r="Um ou mais rate limiters est\xe3o pr\xf3ximos da capacidade m\xe1xima",g.logger.warn("Rate limiters com alta utiliza\xe7\xe3o de mem\xf3ria",{chat:"".concat(n.totalEntries,"/").concat(n.maxEntries," (").concat(Math.round(n.totalEntries/n.maxEntries*100),"%)"),excel:"".concat(o.totalEntries,"/").concat(o.maxEntries," (").concat(Math.round(o.totalEntries/o.maxEntries*100),"%)"),api:"".concat(a.totalEntries,"/").concat(a.maxEntries," (").concat(Math.round(a.totalEntries/a.maxEntries*100),"%)")})),{service:"rate-limiters",status:t,message:r,responseTime:Date.now()-e,error:void 0,data:{chat:n,excel:o,api:a}}}catch(r){let t=r instanceof Error?r.message:String(r);return{service:"rate-limiters",status:"unhealthy",message:"Erro ao verificar rate limiters",responseTime:Date.now()-e,error:t}}finally{g.logger.debug("Concluindo health check de rate limiters")}}async function y(){return Date.now(),{service:"mcp-integrations",status:"healthy",responseTime:0,error:void 0,details:{mode:"client-side-mock",integrations:["vercel","linear","github","supabase"]}}}async function w(){let e=Date.now(),t=null,r=null;try{if(p.Vi.IS_PRODUCTION){let e=(t=new AbortController).signal,i=p.Vi.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS,n=setTimeout(()=>null==t?void 0:t.abort(),i);try{r=await fetch("https://api.external-service.com/health",{signal:e}),clearTimeout(n)}catch(e){if(e instanceof Error&&"AbortError"!==e.name)throw e}}return{service:"external-dependencies",status:"healthy",message:"Depend\xeancias externas funcionando normalmente",responseTime:Date.now()-e,error:void 0}}catch(r){let t=r instanceof Error?r.message:String(r);return{service:"external-dependencies",status:"degraded",message:"Erro ao verificar depend\xeancias externas",responseTime:Date.now()-e,error:t}}finally{if(t)try{t.abort()}catch(e){g.logger.error("Erro ao abortar conex\xe3o HTTP em external dependency check:",e)}if(r&&!r.bodyUsed)try{var i;null===(i=r.body)||void 0===i||i.cancel()}catch(e){g.logger.error("Erro ao cancelar body de resposta em external dependency check:",e)}}}r(25566);let E={info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]},debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]}};(i=o||(o={}))[i.CRITICAL=0]="CRITICAL",i[i.HIGH=1]="HIGH",i[i.MEDIUM=2]="MEDIUM",i[i.LOW=3]="LOW",i[i.LAZY=4]="LAZY";class j{static getInstance(){return j.instance||(j.instance=new j),j.instance}registerService(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2;return this.services[e]&&E.warn("Servi\xe7o '".concat(e,"' j\xe1 estava registrado e ser\xe1 substitu\xeddo.")),this.services[e]={name:e,dependencies:r,initialized:!1,instance:t,priority:i},this}getService(e){let t=this.services[e];return t?t.instance:null}isInitialized(){return this.initialized}setInitialized(e){if(this.initialized=e,e){this.initializationEndTime=performance.now();let e=this.initializationEndTime-this.initializationStartTime;E.info("Inicializa\xe7\xe3o completa do ServiceManager em ".concat(e.toFixed(2),"ms"))}else this.initializationStartTime=performance.now()}isServiceInitialized(e){var t;return!!(null===(t=this.services[e])||void 0===t?void 0:t.initialized)}setServiceInitialized(e,t){if(this.services[e]){let r=performance.now();if(t&&!this.services[e].initialized){if(this.services[e].initEndTime=r,this.services[e].initStartTime){let t=r-this.services[e].initStartTime;this.services[e].initDuration=t,t>5&&E.debug("Servi\xe7o '".concat(e,"' inicializado em ").concat(t.toFixed(2),"ms"))}}else!t&&this.services[e].initialized&&(this.services[e].initStartTime=r,this.services[e].initEndTime=void 0,this.services[e].initDuration=void 0);this.services[e].initialized=t}else E.warn("Tentativa de marcar servi\xe7o n\xe3o registrado '".concat(e,"' como inicializado"))}markServiceInitializationStart(e){this.services[e]&&(this.services[e].initStartTime=performance.now())}getServicesByPriority(e){return Object.keys(this.services).filter(t=>{let r=this.services[t];return r&&r.priority===e})}getTotalInitializationTime(){return this.initialized&&this.initializationEndTime&&this.initializationStartTime?this.initializationEndTime-this.initializationStartTime:0}getAllDependencies(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set,r=this.services[e];if(!r)return[];if(t.has(e))return E.warn("Depend\xeancia circular detectada para o servi\xe7o '".concat(e,"'")),[];t.add(e);let i=[...r.dependencies];for(let e of r.dependencies)for(let r of this.getAllDependencies(e,new Set(t)))i.includes(r)||i.push(r);return i}getInitializationOrder(e){let t=new Set,r=[],i=n=>{if(t.has(n))return;t.add(n);let o=this.services[n];if(o){if(void 0!==e&&o.priority!==e)return;for(let e of o.dependencies)i(e);(void 0===e||o.priority===e)&&r.push(n)}};return Object.keys(this.services).forEach(i),r}getShutdownOrder(){return this.getInitializationOrder().reverse()}async shutdown(){E.info("Iniciando finaliza\xe7\xe3o ordenada dos servi\xe7os...");let e=this.getShutdownOrder(),t=[];for(let r of e)try{let e=this.getService(r);if(!e)continue;E.debug("Finalizando servi\xe7o: ".concat(r));let t=e=>!!(e&&"function"==typeof e.shutdown),i=e=>!!(e&&"function"==typeof e.dispose),n=e=>!!(e&&"function"==typeof e.close),o=e=>!!(e&&"function"==typeof e.terminate),a=e=>!!(e&&"function"==typeof e.destroy);t(e)?await e.shutdown():i(e)?await e.dispose():n(e)?await e.close():o(e)?await e.terminate():a(e)?await e.destroy():e instanceof Map||e instanceof Set?e.clear():e&&"object"==typeof e&&"clearInterval"in globalThis&&!!(e&&"object"==typeof e&&"_timer"in e)&&e._timer&&clearInterval(e._timer),this.setServiceInitialized(r,!1)}catch(i){let e=i instanceof Error?i.message:String(i);E.error("Erro ao finalizar servi\xe7o '".concat(r,"': ").concat(e),i),t.push({service:r,error:i})}this.initialized=!1,t.length>0?E.warn("Finaliza\xe7\xe3o de servi\xe7os conclu\xedda com ".concat(t.length," erros")):E.info("Todos os servi\xe7os finalizados com sucesso")}reset(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(e){E.warn("Tentativa de reset do ServiceManager em produ\xe7\xe3o bloqueada.");return}this.services={},this.initialized=!1}listServices(){return Object.values(this.services)}getInitializationMetrics(){let e=this.listServices(),t=this.getTotalInitializationTime(),r=e.filter(e=>e.initialized).length;return{totalServices:e.length,initializedServices:r,uninitializedServices:e.length-r,totalInitTime:t,serviceMetrics:e.map(e=>({name:e.name,initialized:e.initialized,initTime:e.initDuration||0,priority:e.priority}))}}constructor(){this.services={},this.initialized=!1,this.initializationStartTime=0,this.initializationEndTime=0}}var N=r(56143);r(25566);class k{static getInstance(){return k.instance||(k.instance=new k),k.instance}get initialized(){return this._initialized}get initializationResult(){return this._initializationResult}set initializationResult(e){this._initializationResult=e,this._initialized=(null==e?void 0:e.success)||!1}get initializationPromise(){return this._initializationPromise}set initializationPromise(e){this._initializationPromise=e}get isShuttingDown(){return this._isShuttingDown}set isShuttingDown(e){this._isShuttingDown=e}get shutdownPromise(){return this._shutdownPromise}set shutdownPromise(e){this._shutdownPromise=e}constructor(){this._initialized=!1,this._initializationResult=null,this._initializationPromise=null,this._shutdownPromise=null,this._isShuttingDown=!1}}async function I(e,t){let i=performance.now();for(let i of t){if(e.isServiceInitialized(i)){g.logger.debug("Servi\xe7o '".concat(i,"' j\xe1 inicializado, pulando."));continue}try{let{initLogger:t}=await Promise.resolve().then(r.bind(r,18473));t.info("Inicializando servi\xe7o: ".concat(i));let n=e.getService(i);n&&"function"==typeof n.initialize&&await n.initialize(),e.setServiceInitialized(i,!0)}catch(t){let e=t instanceof Error?t.message:String(t);throw(0,N.H)("Falha ao inicializar servi\xe7o '".concat(i,"': ").concat(e),t),t}}let n=performance.now()-i;g.logger.debug("Inicializa\xe7\xe3o de servi\xe7os [".concat(t.join(", "),"] conclu\xedda em ").concat(n.toFixed(2),"ms"))}let z=k.getInstance();(n=a||(a={})).NETWORK="network",n.WEBSOCKET="websocket",n.BRIDGE="bridge",n.UI="ui",n.GENERAL="general";let S={silenceNonCritical:!0,retryNetwork:!0};class C extends Error{toJSON(){return{name:this.name,message:this.message,code:this.code,level:this.level,stack:this.stack,context:this.context,originalError:this.originalError?{message:this.originalError.message,stack:this.originalError.stack}:void 0,userId:this.userId,sessionId:this.sessionId}}report(){if(!this.shouldReport)return;let e=this.toJSON();"warn"===this.level?g.logger.warn("Aviso da aplica\xe7\xe3o",e):"error"===this.level?g.logger.error("Erro da aplica\xe7\xe3o",this):"fatal"===this.level&&g.logger.fatal("Erro fatal da aplica\xe7\xe3o",this)}constructor(e,t={}){super(e),this.name=this.constructor.name,this.level=t.level||"error",this.code=t.code||"UNKNOWN_ERROR",this.context=t.context||{},this.shouldReport=!1!==t.shouldReport,this.originalError=t.originalError,this.userId=t.userId,this.sessionId=t.sessionId,Object.setPrototypeOf(this,new.target.prototype)}}class T{static async handle(e,t){T.normalizeError(e,t).report()}static normalizeError(e,t){if(e instanceof C)return t?new C(e.message,{level:e.level,code:e.code,originalError:e.originalError,userId:e.userId,sessionId:e.sessionId,shouldReport:e.shouldReport,context:{...e.context,...t}}):e;if(e instanceof Error)return g.logger.debug("Normalizando erro nativo",{errorName:e.name}),new C(e.message,{originalError:e,context:t||void 0,code:"Error"===e.name?"UNKNOWN_ERROR":e.name});if("string"==typeof e)return new C(e,{context:t||void 0});try{let r=JSON.stringify(e);return new C("Erro n\xe3o estruturado: ".concat(r),{context:t||void 0})}catch(e){return new C("Erro desconhecido n\xe3o serializ\xe1vel",{context:t||void 0})}}static async handleApiError(e,t,r){let i=this.getUserIdFromRequest(t),n=this.getSessionIdFromRequest(t),o=T.normalizeError(e,{url:t.url,method:t.method,userId:i,sessionId:n}),a=new C(o.message,{level:o.level,code:o.code,context:o.context,originalError:o.originalError,shouldReport:o.shouldReport,userId:i||void 0,sessionId:n||void 0});return a.report(),g.logger.debug("API Error",{errorId:r,code:a.code,message:a.message}),{message:this.getSafeErrorMessage(a),code:a.code,id:r}}static getSafeErrorMessage(e){return"fatal"===e.level||"INTERNAL_SERVER_ERROR"===e.code?"Ocorreu um erro inesperado no servidor.":e.message}static getUserIdFromRequest(e){try{if(!e.headers.get("authorization"))return;return"user-id-extracted"}catch(e){g.logger.warn("Erro ao extrair ID do usu\xe1rio",{error:e});return}}static getSessionIdFromRequest(e){try{return e.headers.get("x-session-id")||void 0}catch(e){g.logger.warn("Erro ao extrair ID da sess\xe3o",{error:e});return}}}function R(e){let{children:t}=e,[r,i]=(0,f.useState)(!1),[n,o]=(0,f.useState)(!0),[a,l]=(0,f.useState)(null),[c,d]=(0,f.useState)("not-started"),[u,m]=(0,f.useState)(0),h=(0,f.useRef)(0),p=(0,f.useRef)(null);return((0,f.useEffect)(()=>{let e;let t=()=>{if(z){if(z.initialized)return i(!0),d("complete"),m(100),o(!1),!0;if(z.initializationResult){let e=z.initializationResult.details||{};if(e&&"object"==typeof e&&"criticalServicesReady"in e&&e.criticalServicesReady){let t="initializedServices"in e&&e.initializedServices||[];"healthChecksComplete"in e&&e.healthChecksComplete?(d("health-checks"),m(95)):t.includes("ai")?(d("low-priority"),m(80)):t.includes("telemetry")?(d("medium-priority"),m(60)):t.includes("rateLimiters")?(d("high-priority"),m(40)):(d("critical-services"),m(20))}}}return!1},r=()=>{"requestIdleCallback"in window?e=window.requestIdleCallback(()=>{t()||r()}):setTimeout(()=>{t()||r()},200)};return r(),()=>{"cancelIdleCallback"in window&&e&&window.cancelIdleCallback(e)}},[]),(0,f.useEffect)(()=>(window.addEventListener("error",e=>{(function(e){var t;if(null===(t=e.message)||void 0===t?void 0:t.includes("ErrorHandler"))return;let r=function(e){if(e instanceof ErrorEvent){var t,r,i,n,o,a,s,l,c,d;if((null===(t=e.filename)||void 0===t?void 0:t.includes(".js"))||(null===(r=e.filename)||void 0===r?void 0:r.includes(".ts")))return"general";if((null===(i=e.message)||void 0===i?void 0:i.includes("network"))||(null===(n=e.message)||void 0===n?void 0:n.includes("fetch"))||(null===(o=e.message)||void 0===o?void 0:o.includes("load")))return"network";if((null===(a=e.message)||void 0===a?void 0:a.includes("WebSocket"))||(null===(s=e.message)||void 0===s?void 0:s.includes("socket")))return"websocket";if((null===(l=e.message)||void 0===l?void 0:l.includes("bridge"))||(null===(c=e.message)||void 0===c?void 0:c.includes("electron"))||(null===(d=e.message)||void 0===d?void 0:d.includes("desktop")))return"bridge"}if(e instanceof Error){let t=e.message.toLowerCase();if(t.includes("network")||t.includes("fetch"))return"network";if(t.includes("websocket")||t.includes("socket"))return"websocket";if(t.includes("bridge")||t.includes("electron"))return"bridge";if(t.includes("render")||t.includes("component")||t.includes("prop"))return"ui"}return"general"}(e);if(S.silenceNonCritical&&"general"!==r){g.logger.warn("Erro n\xe3o cr\xedtico [".concat(r,"]: ").concat(e.message),{filename:e.filename,line:e.lineno,column:e.colno});return}let i={level:"general"===r?"error":"warn",code:"UNCAUGHT_".concat(r.toUpperCase()),context:{source:e.filename||"unknown",line:e.lineno,column:e.colno}},n=new C(e.message||"Erro n\xe3o capturado",i);T.handle(n),"network"===r&&S.retryNetwork})(e),e.target&&"tagName"in e.target&&e.preventDefault()}),window.addEventListener("unhandledrejection",e=>{(function(e){let t=e.reason;if(t instanceof Error){g.logger.error("Rejei\xe7\xe3o de Promise n\xe3o tratada:",t);let e={level:"error",code:"UNHANDLED_REJECTION",originalError:t,context:{stack:t.stack}},r=new C(t.message,e);T.handle(r)}else{let e=new C("string"==typeof t?t:"object"==typeof t&&null!==t?JSON.stringify(t):"Rejei\xe7\xe3o de Promise n\xe3o tratada",{level:"error",code:"UNHANDLED_REJECTION",context:{reason:t}});T.handle(e)}})(e),e.preventDefault()}),h.current=performance.now(),d("not-started"),n&&(p.current=window.setInterval(()=>{m(e=>Math.min(e+.5,"critical-services"===c?25:"high-priority"===c?50:"medium-priority"===c?75:"low-priority"===c?90:"health-checks"===c?95:"complete"===c?100:10))},150)),(async()=>{try{d("critical-services"),i(!0),d("complete"),m(100);return}catch(e){l(e instanceof Error?e.message:"Erro desconhecido na inicializa\xe7\xe3o"),d("error"),console.error("Erro na inicializa\xe7\xe3o:",e)}finally{o(!1),null!==p.current&&(clearInterval(p.current),p.current=null),"error"!==c&&m(100)}})(),()=>{null!==p.current&&clearInterval(p.current)}),[]),n)?(0,s.jsx)("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-6 max-w-md p-8",children:[(0,s.jsxs)("div",{className:"relative h-10 w-10",children:[(0,s.jsx)("div",{className:"absolute h-10 w-10 animate-ping rounded-full bg-primary opacity-75"}),(0,s.jsx)("div",{className:"relative flex h-10 w-10 items-center justify-center rounded-full bg-primary",children:(0,s.jsxs)("svg",{className:"h-5 w-5 text-white",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3"}),(0,s.jsx)("path",{d:"M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3"}),(0,s.jsx)("path",{d:"M4 12H2"}),(0,s.jsx)("path",{d:"M10 12H8"}),(0,s.jsx)("path",{d:"M16 12h-2"}),(0,s.jsx)("path",{d:"M22 12h-2"})]})})]}),(0,s.jsxs)("div",{className:"space-y-4 text-center w-full",children:[(0,s.jsx)("h3",{className:"text-xl font-medium",children:"Inicializando Excel Copilot"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:(e=>{switch(e){case"not-started":return"Preparando ambiente...";case"critical-services":return"Inicializando servi\xe7os essenciais...";case"high-priority":return"Preparando interface de usu\xe1rio...";case"medium-priority":return"Carregando funcionalidades avan\xe7adas...";case"low-priority":return"Inicializando recursos de IA...";case"health-checks":return"Verificando conex\xf5es externas...";case"complete":return"Inicializa\xe7\xe3o conclu\xedda!";case"error":return"Erro de inicializa\xe7\xe3o";default:return"Carregando..."}})(c)}),(0,s.jsx)("div",{className:"w-full bg-secondary rounded-full h-2.5 overflow-hidden",children:(0,s.jsx)("div",{className:"bg-primary h-2.5 rounded-full transition-all duration-300 ease-out",style:{width:"".concat(u,"%")}})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground pt-1",children:[(0,s.jsx)("span",{className:"not-started"!==c?"text-primary font-medium":"",children:"Essenciais"}),(0,s.jsx)("span",{className:"high-priority"===c||"medium-priority"===c||"low-priority"===c||"health-checks"===c||"complete"===c?"text-primary font-medium":"",children:"Interface"}),(0,s.jsx)("span",{className:"medium-priority"===c||"low-priority"===c||"health-checks"===c||"complete"===c?"text-primary font-medium":"",children:"Avan\xe7ados"}),(0,s.jsx)("span",{className:"low-priority"===c||"health-checks"===c||"complete"===c?"text-primary font-medium":"",children:"IA"}),(0,s.jsx)("span",{className:"health-checks"===c||"complete"===c?"text-primary font-medium":"",children:"Verifica\xe7\xe3o"})]})]})]})}):a?(0,s.jsx)("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4 max-w-md p-6",children:[(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-destructive flex items-center justify-center",children:(0,s.jsxs)("svg",{className:"h-5 w-5 text-white",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M18 6 6 18"}),(0,s.jsx)("path",{d:"m6 6 12 12"})]})}),(0,s.jsxs)("div",{className:"space-y-2 text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-medium",children:"Erro de inicializa\xe7\xe3o"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:a}),(0,s.jsx)("button",{className:"inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary",onClick:()=>window.location.reload(),children:"Tentar novamente"})]})]})}):r?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{children:"Estado de inicializa\xe7\xe3o inconsistente. Atualize a p\xe1gina."}),(0,s.jsx)("button",{className:"mt-4 inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90",onClick:()=>window.location.reload(),children:"Atualizar"})]})})}function _(e){let{children:t,fallback:r=null}=e,[i,n]=(0,f.useState)(!1);return((0,f.useEffect)(()=>{n(!0)},[]),i)?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)(s.Fragment,{children:r})}let A=(0,f.memo)(e=>{let{providers:t,children:r,fallback:i}=e,n=(0,f.useMemo)(()=>t.filter(e=>{let{condition:t=!0}=e;return t}),[t]),o=(0,f.useMemo)(()=>n.reduceRight((e,t)=>{let{provider:r,props:i={},key:n}=t;return n?f.cloneElement((0,s.jsx)(r,{...i,children:e}),{key:n}):(0,s.jsx)(r,{...i,children:e})},r),[n,r]);return 0===n.length?(0,s.jsx)(s.Fragment,{children:i||r}):(0,s.jsx)(s.Fragment,{children:o})});A.displayName="ProviderComposer",(0,f.memo)(e=>{let{provider:t,props:r={},children:i,condition:n=!0,fallback:o}=e;return n?(0,s.jsx)(t,{...r,children:i}):(0,s.jsx)(s.Fragment,{children:o||i})}).displayName="SingleProvider";var P=r(89605),L=r(76552),O=r(25566);let D=(0,L.ec)();function M(e){let{children:t}=e,r=(0,f.useMemo)(()=>new l.S({defaultOptions:{queries:{refetchOnWindowFocus:!1,staleTime:3e5,retry:1,cacheTime:6e5}}}),[]),i=(0,f.useMemo)(()=>{let e=O.env.NEXT_PUBLIC_APP_URL?O.env.NEXT_PUBLIC_APP_URL:window.location.origin;return D.createClient({links:[(0,P.N8)({url:"".concat(e,"/api/trpc"),headers:()=>({"x-trpc-source":"react"})})],transformer:h.ZP})},[]);return(0,s.jsx)(D.Provider,{client:i,queryClient:r,children:(0,s.jsx)(c.aH,{client:r,children:t})})}let F=(0,f.lazy)(()=>r.e(5340).then(r.bind(r,45340)).then(e=>({default:e.TourProvider}))),U=(0,f.lazy)(()=>r.e(4666).then(r.bind(r,84666)).then(e=>({default:e.CSRFProvider}))),V=(0,f.lazy)(()=>Promise.all([r.e(2),r.e(2775)]).then(r.bind(r,92775)).then(e=>({default:e.ToastProvider}))),H=(0,f.lazy)(()=>Promise.all([r.e(2),r.e(7100)]).then(r.bind(r,47100)).then(e=>({default:e.Toaster}))),X=(0,f.lazy)(()=>r.e(7776).then(r.bind(r,27776)).then(e=>({default:e.Toaster})));function q(e){let{children:t,locale:r="pt-BR"}=e,[i,n]=(0,f.useState)(!1);(0,f.useEffect)(()=>{n(!0)},[]);let[o]=(0,f.useState)(()=>new l.S({defaultOptions:{queries:{staleTime:6e4,cacheTime:3e5,refetchOnWindowFocus:!1,retry:1,queryFn:e=>{let{queryKey:t}=e;return h.ZP.stringify(t)}}}})),a=[{provider:u.SessionProvider,key:"session"},{provider:m.ThemeProvider,props:{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0},key:"theme"},{provider:e=>{let{children:t}=e;return(0,s.jsx)(c.aH,{client:o,children:t})},key:"query"},{provider:M,key:"trpc"}].map(e=>{var t,r;let{provider:i,props:n,key:o,condition:a}=e;return{provider:i,props:n||{},key:(t={key:o||void 0,condition:null==a||a}).key,condition:null===(r=null==t?void 0:t.condition)||void 0===r||r}}),p=(0,s.jsx)("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:(0,s.jsx)("div",{className:"text-center",children:"Carregando..."})});return(0,s.jsx)(A,{providers:a,children:(0,s.jsx)(_,{fallback:p,children:(0,s.jsxs)(R,{children:[(0,s.jsx)(f.Suspense,{fallback:null,children:(0,s.jsx)(U,{children:(0,s.jsx)(f.Suspense,{fallback:null,children:(0,s.jsxs)(V,{children:[(0,s.jsx)(f.Suspense,{fallback:null,children:i&&(0,s.jsx)(F,{children:t})}),(0,s.jsx)(d.c,{})]})})})}),(0,s.jsx)(f.Suspense,{fallback:null,children:(0,s.jsx)(H,{})}),(0,s.jsx)(f.Suspense,{fallback:null,children:(0,s.jsx)(X,{position:"top-right",toastOptions:{style:{fontSize:"0.875rem"},duration:4e3}})})]})})})}},35519:function(e,t,r){"use strict";r.d(t,{default:function(){return o}});var i=r(57437),n=r(2265);function o(e){let{children:t,className:r,interClassName:o}=e;return(0,n.useEffect)(()=>{if(["/images/excel-copilot-icon.svg","/favicon.ico"].forEach(e=>{new Image().src=e}),"connection"in navigator&&!1===navigator.connection.saveData)try{["/dashboard"].forEach(e=>{let t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)})}catch(e){}if(window.Worker)try{window.__resolveExternalResource=function(e){if(window.__resourceCache||(window.__resourceCache={}),window.__resourceCache[e])return window.__resourceCache[e];if(["raw.githack.com","fonts.gstatic.com"].some(t=>e.includes(t))){let t=e.split("/").pop(),r="/assets/"+t;return window.__resourceCache[e]=r,r}return window.__resourceCache[e]=e,e}}catch(e){}},[]),(0,i.jsxs)(i.Fragment,{children:[t,(0,i.jsx)("div",{id:"portal-root","aria-hidden":"true"})]})}},1474:function(e,t,r){"use strict";r.d(t,{ClientScripts:function(){return n}});var i=r(2265);function n(){return(0,i.useEffect)(()=>{!function(){function e(t){return new Proxy(()=>{},{get:()=>e(t),set:()=>!0,apply:()=>e(t),construct:()=>e(t)})}window.GoogleGenerativeAI=e("GoogleGenerativeAI"),window.VertexAI=e("VertexAI"),window.GenerativeModel=e("GenerativeModel"),window.GoogleAuth=e("GoogleAuth");let t=console.error;console.error=function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];let n=String(r.join(" ")),o=["Neither apiKey nor config.authenticator provided","_setAuthenticator","GoogleGenerativeAI","VertexAI","google-auth-library","Failed to fetch RSC payload","Rejei\xe7\xe3o de Promise n\xe3o tratada","Erro da aplica\xe7\xe3o","Unhandled Promise Rejection","Application Error","ChunkLoadError","Loading chunk","Loading CSS chunk","404 (Not Found)","_rsc=","RSC payload","fetch error","Network Error"];for(let e=0;e<o.length;e++){let t=o[e];if(t&&-1!==n.toLowerCase().indexOf(t.toLowerCase()))return}if("[object Object]"!==n&&"Object"!==n&&-1===n.indexOf("[object Object]"))return t.apply(this,r)},window.addEventListener("unhandledrejection",function(e){let t=String(e.reason),r=["Neither apiKey nor config.authenticator provided","_setAuthenticator","GoogleGenerativeAI","VertexAI","google-auth-library","Failed to fetch RSC payload","Rejei\xe7\xe3o de Promise n\xe3o tratada","Erro da aplica\xe7\xe3o","Unhandled Promise Rejection","Application Error","ChunkLoadError","Loading chunk","Loading CSS chunk","404 (Not Found)","_rsc=","RSC payload","fetch error","Network Error"];for(let i=0;i<r.length;i++){let n=r[i];if(n&&-1!==t.toLowerCase().indexOf(n.toLowerCase()))return e.preventDefault(),!1}if("[object Object]"===t||"Object"===t||-1!==t.indexOf("[object Object]"))return e.preventDefault(),!1})}()},[]),null}},31616:function(e,t,r){"use strict";r.r(t),r.d(t,{NavBar:function(){return g}});var i=r(57437),n=r(11005),o=r(87140),a=r(56935),s=r(74622),l=r(58948),c=r(87138),d=r(16463),u=r(2265),m=r(15589),f=r(49354);let h=[{href:"/",label:"In\xedcio",icon:n.Z,ariaLabel:"P\xe1gina inicial"},{href:"/dashboard",label:"Painel",icon:o.Z,ariaLabel:"Painel de controle"},{href:"/pricing",label:"Pre\xe7os",icon:a.Z,ariaLabel:"Planos e pre\xe7os"},{href:"/help",label:"Ajuda",icon:s.Z,ariaLabel:"Central de ajuda"},{href:"/api-docs",label:"API",icon:l.Z,ariaLabel:"Documenta\xe7\xe3o da API"}],p=e=>{let{href:t,children:r,className:n}=e;return(0,i.jsx)(c.default,{href:t,className:(0,f.cn)("flex items-center text-lg font-semibold text-foreground hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary",n),children:r})};function g(){let e=(0,d.usePathname)(),[t,r]=(0,u.useState)(!1),[n,o]=(0,u.useState)(!1);!function(e){let[t,r]=(0,u.useState)(()=>window.matchMedia(e).matches);(0,u.useEffect)(()=>{let t=window.matchMedia(e);r(t.matches);let i=e=>{r(e.matches)};return t.addEventListener?t.addEventListener("change",i):t.addListener(i),()=>{t.removeEventListener?t.removeEventListener("change",i):t.removeListener(i)}},[e])}("(prefers-reduced-motion: reduce)");let a=(0,u.useRef)(null),s=(0,u.useRef)(null),l=(0,u.useRef)(null),g=(0,u.useRef)(null),v=(0,u.useCallback)(()=>{o(window.scrollY>5)},[]);(0,u.useEffect)(()=>{let e;v();let t=()=>{clearTimeout(e),e=setTimeout(v,10)};return window.addEventListener("scroll",t,{passive:!0}),()=>{clearTimeout(e),window.removeEventListener("scroll",t)}},[v]),(0,u.useEffect)(()=>{r(!1)},[e]),(0,u.useEffect)(()=>{if(t){setTimeout(()=>{var e;null===(e=l.current)||void 0===e||e.focus()},100);let e=e=>{var t,i,n;if("Escape"===e.key){r(!1),null===(t=s.current)||void 0===t||t.focus();return}"Tab"===e.key&&(e.shiftKey?document.activeElement===l.current&&(e.preventDefault(),null===(i=g.current)||void 0===i||i.focus()):document.activeElement===g.current&&(e.preventDefault(),null===(n=l.current)||void 0===n||n.focus()))};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}if(document.activeElement===l.current||document.activeElement===g.current){var e;null===(e=s.current)||void 0===e||e.focus()}},[t]);let x=()=>{r(!t)},b=m.OK.transition.medium;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:outline-ring",children:"Skip to main content"}),(0,i.jsxs)("div",{className:"flex items-center w-full",children:[(0,i.jsx)("div",{className:"mr-4 flex items-center md:hidden",children:(0,i.jsxs)("button",{ref:s,type:"button",onClick:()=>x(),className:"inline-flex items-center justify-center rounded-md p-2 text-foreground hover:bg-muted focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","aria-controls":"mobile-menu","aria-expanded":t,children:[(0,i.jsx)("span",{className:"sr-only",children:t?"Close main menu":"Open main menu"}),(0,i.jsx)("svg",{className:(0,f.cn)("h-6 w-6",t?"hidden":"block"),fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})}),(0,i.jsx)("svg",{className:(0,f.cn)("h-6 w-6",t?"block":"hidden"),fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})})]})}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(p,{href:"/",className:(0,f.cn)("flex items-center text-lg font-semibold text-foreground","hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary",b),children:"Excel Copilot"}),(0,i.jsxs)("div",{className:"hidden md:flex md:items-center md:gap-6 ml-10",children:[(0,i.jsx)(p,{href:"/",className:(0,f.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/"===e?"text-primary font-semibold":"text-foreground/80",b),"aria-current":"/"===e?"page":void 0,children:"In\xedcio"}),(0,i.jsx)(p,{href:"/dashboard",className:(0,f.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/dashboard"===e?"text-primary font-semibold":"text-foreground/80",b),"aria-current":"/dashboard"===e?"page":void 0,children:"Painel"}),(0,i.jsx)(p,{href:"/pricing",className:(0,f.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/pricing"===e?"text-primary font-semibold":"text-foreground/80",b),"aria-current":"/pricing"===e?"page":void 0,children:"Pre\xe7os"}),(0,i.jsx)(p,{href:"/help",className:(0,f.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/help"===e?"text-primary font-semibold":"text-foreground/80",b),"aria-current":"/help"===e?"page":void 0,children:"Ajuda"}),(0,i.jsx)(p,{href:"/api-docs",className:(0,f.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/api-docs"===e?"text-primary font-semibold":"text-foreground/80",b),"aria-current":"/api-docs"===e?"page":void 0,children:"API"})]})]}),(0,i.jsx)("div",{className:"flex-grow min-w-[120px] md:min-w-[200px] lg:min-w-[300px]"}),(0,i.jsx)("div",{className:"flex items-center gap-3 md:gap-5"})]}),t&&(0,i.jsx)("div",{ref:a,id:"mobile-menu",className:"md:hidden fixed inset-0 top-16 z-40 bg-background/80 backdrop-blur-sm","aria-label":"Mobile Navigation",children:(0,i.jsxs)("div",{className:"p-4 bg-background border-b shadow-md flex flex-col space-y-4",children:[h.map((t,r)=>(0,i.jsxs)(c.default,{href:t.href,className:(0,f.cn)("flex items-center py-2 text-base font-medium rounded-md",e===t.href?"text-primary font-semibold":"text-foreground/70 hover:text-primary",b),"aria-current":e===t.href?"page":void 0,ref:0===r?l:void 0,children:[(0,i.jsx)(t.icon,{className:"mr-3 h-5 w-5","aria-hidden":"true"}),t.label]},t.href)),(0,i.jsx)("div",{className:"pt-4 border-t border-border",children:(0,i.jsx)("button",{ref:g,onClick:()=>x(),className:"w-full flex items-center justify-center px-4 py-2 text-base font-medium text-foreground bg-muted rounded-md hover:bg-muted/80",children:"Fechar Menu"})})]})})]})}},57226:function(e,t,r){"use strict";r.d(t,{F$:function(){return l},Q5:function(){return c},qE:function(){return s}});var i=r(57437),n=r(81464),o=r(2265),a=r(49354);let s=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,i.jsx)(n.fC,{ref:t,className:(0,a.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...o})});s.displayName=n.fC.displayName;let l=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,i.jsx)(n.Ee,{ref:t,className:(0,a.cn)("aspect-square h-full w-full",r),...o})});l.displayName=n.Ee.displayName;let c=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,i.jsx)(n.NY,{ref:t,className:(0,a.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",r),...o})});c.displayName=n.NY.displayName},89733:function(e,t,r){"use strict";r.d(t,{Button:function(){return u},d:function(){return d}});var i=r(57437),n=r(71538),o=r(13027),a=r(847),s=r(2265),l=r(18043),c=r(49354);let d=(0,o.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),u=s.forwardRef((e,t)=>{let{className:r,variant:o,size:s,rounded:u,cssFeedback:m,asChild:f=!1,animated:h=!1,icon:p,iconPosition:g="left",children:v,...x}=e,b=f?n.g7:"button",y=(0,i.jsxs)("span",{className:"inline-flex items-center justify-center",children:[p&&"left"===g&&(0,i.jsx)("span",{className:"mr-2",children:p}),v,p&&"right"===g&&(0,i.jsx)("span",{className:"ml-2",children:p})]});if(h){let e={whileTap:{scale:.97},whileHover:["link","ghost"].includes(o)?void 0:{y:-2},transition:{duration:.67*l.zn,ease:l.d}},n=(0,c.cn)(d({variant:o,size:s,rounded:u,cssFeedback:"none",className:r})),m={...x,className:n,...e};return(0,i.jsx)(a.E.button,{ref:t,...m,children:y})}return(0,i.jsx)(b,{className:(0,c.cn)(d({variant:o,size:s,rounded:u,cssFeedback:m,className:r})),ref:t,...x,children:y})});u.displayName="Button"},31590:function(e,t,r){"use strict";r.d(t,{$F:function(){return u},AW:function(){return f},Ju:function(){return p},Qk:function(){return m},VD:function(){return g},Xi:function(){return h},h_:function(){return d}});var i=r(57437),n=r(81622),o=r(87592),a=r(22468),s=r(28165),l=r(2265),c=r(49354);let d=n.fC,u=n.xz,m=n.ZA;n.Uv,n.Tr,n.Ee,l.forwardRef((e,t)=>{let{className:r,inset:a,children:s,...l}=e;return(0,i.jsxs)(n.fF,{ref:t,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",r),...l,children:[s,(0,i.jsx)(o.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=n.fF.displayName,l.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,i.jsx)(n.tu,{ref:t,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})}).displayName=n.tu.displayName;let f=l.forwardRef((e,t)=>{let{className:r,sideOffset:o=4,...a}=e;return(0,i.jsx)(n.Uv,{children:(0,i.jsx)(n.VY,{ref:t,sideOffset:o,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...a})})});f.displayName=n.VY.displayName;let h=l.forwardRef((e,t)=>{let{className:r,inset:o,...a}=e;return(0,i.jsx)(n.ck,{ref:t,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",o&&"pl-8",r),...a})});h.displayName=n.ck.displayName,l.forwardRef((e,t)=>{let{className:r,children:o,checked:s,...l}=e;return(0,i.jsxs)(n.oC,{ref:t,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:null!=s&&s,...l,children:[(0,i.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,i.jsx)(n.wU,{children:(0,i.jsx)(a.Z,{className:"h-4 w-4"})})}),o]})}).displayName=n.oC.displayName,l.forwardRef((e,t)=>{let{className:r,children:o,...a}=e;return(0,i.jsxs)(n.Rk,{ref:t,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...a,children:[(0,i.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,i.jsx)(n.wU,{children:(0,i.jsx)(s.Z,{className:"h-2 w-2 fill-current"})})}),o]})}).displayName=n.Rk.displayName;let p=l.forwardRef((e,t)=>{let{className:r,inset:o,...a}=e;return(0,i.jsx)(n.__,{ref:t,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",r),...a})});p.displayName=n.__.displayName;let g=l.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,i.jsx)(n.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",r),...o})});g.displayName=n.Z0.displayName},75515:function(e,t,r){"use strict";r.r(t),r.d(t,{UserNav:function(){return m}});var i=r(57437),n=r(38296),o=r(92699),a=r(16463),s=r(30998),l=r(79512),c=r(57226),d=r(89733),u=r(31590);function m(){var e;let{data:t}=(0,s.useSession)(),r=(0,a.useRouter)(),{theme:m,setTheme:f}=(0,l.F)(),h=null==t?void 0:t.user,p=async()=>{await (0,s.signOut)({redirect:!1}),r.push("/auth/signin")},g=()=>{f("dark"===m?"light":"dark")};return(0,i.jsxs)(u.h_,{children:[(0,i.jsx)(u.$F,{asChild:!0,children:(0,i.jsx)(d.Button,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,i.jsxs)(c.qE,{className:"h-8 w-8",children:[(0,i.jsx)(c.F$,{src:(null==h?void 0:h.image)||"",alt:(null==h?void 0:h.name)||"Usu\xe1rio"}),(0,i.jsx)(c.Q5,{children:(e=null==h?void 0:h.name)?e.split(" ").map(e=>e[0]).join("").toUpperCase().substring(0,2):"?"})]})})}),(0,i.jsxs)(u.AW,{className:"w-56",align:"end",forceMount:!0,children:[(0,i.jsx)(u.Ju,{className:"font-normal",children:(0,i.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,i.jsx)("p",{className:"text-sm font-medium leading-none",children:null==h?void 0:h.name}),(0,i.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:null==h?void 0:h.email})]})}),(0,i.jsx)(u.VD,{}),(0,i.jsxs)(u.Qk,{children:[(0,i.jsx)(u.Xi,{onClick:()=>r.push("/dashboard"),children:"Painel"}),(0,i.jsx)(u.Xi,{onClick:()=>r.push("/profile"),children:"Perfil"}),(0,i.jsx)(u.Xi,{onClick:()=>r.push("/settings"),children:"Configura\xe7\xf5es"}),(0,i.jsx)(u.Xi,{onClick:()=>r.push("/dashboard/account"),children:"Conta"})]}),(0,i.jsx)(u.VD,{}),(0,i.jsxs)(u.Xi,{onClick:()=>g(),children:["dark"===m?(0,i.jsx)(n.Z,{className:"mr-2 h-4 w-4"}):(0,i.jsx)(o.Z,{className:"mr-2 h-4 w-4"}),(0,i.jsx)("span",{children:"dark"===m?"Tema claro":"Tema escuro"})]}),(0,i.jsx)(u.VD,{}),(0,i.jsx)(u.Xi,{onClick:()=>p(),children:"Sair"})]})]})}},15589:function(e,t,r){"use strict";r.d(t,{OK:function(){return n},z6:function(){return i}});let i={radius:{sm:"rounded",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl",full:"rounded-full"},width:{none:"border-0",thin:"border",medium:"border-2",thick:"border-4"},card:"border rounded-lg",input:"border rounded-md",button:"rounded-md"},n={transition:{fast:"transition-all duration-150 ease-in-out",medium:"transition-all duration-300 ease-in-out",slow:"transition-all duration-500 ease-in-out"},hover:{scale:"hover:scale-105",brightness:"hover:brightness-110",opacity:"hover:opacity-90"},spin:"animate-spin",pulse:"animate-pulse",loading:"animate-pulse",shake:"animate-[shake_0.82s_cubic-bezier(.36,.07,.19,.97)_both]"},o="bg-primary text-primary-foreground",a="hover:bg-primary/90",s="bg-primary/10 text-primary",l="bg-secondary text-secondary-foreground",c="hover:bg-secondary/90",d="bg-destructive text-destructive-foreground",u="hover:bg-destructive/90",m="bg-destructive/10 text-destructive";"".concat(i.button," font-medium ").concat(n.transition.fast),"".concat(o," ").concat(a),"".concat(l," ").concat(c),"".concat(d," ").concat(u),"".concat(i.input," ").concat("px-4 py-2"," w-full ").concat("focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"),"".concat(m," ").concat(i.radius.md," ").concat("p-2"),"".concat("bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"," ").concat(i.radius.md," ").concat("p-2"),"".concat("bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"," ").concat(i.radius.md," ").concat("p-2"),"".concat(s," ").concat(i.radius.md," ").concat("p-2"),"".concat("p-2"," ").concat(i.radius.md," ").concat(n.transition.fast," hover:bg-accent hover:text-accent-foreground"),"".concat("p-2"," ").concat(i.radius.md," bg-accent text-accent-foreground")},56143:function(e,t,r){"use strict";r.d(t,{H:function(){return o},KC:function(){return n},KE:function(){return a}});var i=r(18473);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{let t=JSON.stringify(e);return Error(t)}catch(e){return Error("Unknown error")}}}function o(e,t,r){let o=n(t);i.logger.error(e,o)}function a(e,t,r){let n=null==t?void 0:"object"==typeof t?t:{value:t},o=r?{...r,...n}:n;i.logger.warn(e,o)}},53054:function(){}},function(e){e.O(0,[3982,9141,7142,8638,5660,3526,4462,231,4974,193,7004,8194,2971,7023,1744],function(){return e(e.s=11907)}),_N_E=e.O()}]);