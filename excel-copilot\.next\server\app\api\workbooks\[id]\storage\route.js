"use strict";(()=>{var e={};e.id=3511,e.ids=[3511],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},85477:e=>{e.exports=require("punycode")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},24404:e=>{e.exports=require("tls")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},80514:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>k,requestAsyncStorage:()=>w,routeModule:()=>h,serverHooks:()=>E,staticGenerationAsyncStorage:()=>x});var o={};t.r(o),t.d(o,{DELETE:()=>m,GET:()=>p,POST:()=>f});var s=t(49303),a=t(88716),i=t(60670),n=t(87070),u=t(75571),l=t(95456),c=t(33035),d=t(63841);async function p(e,{params:r}){try{let t=await (0,u.getServerSession)(l.Lz);if(!t?.user)return n.NextResponse.json({error:"N\xe3o autorizado"},{status:401});let o=r.id,{searchParams:s}=new URL(e.url),a="true"===s.get("stats");if(!await d.prisma.workbook.findFirst({where:{id:o,OR:[{userId:t.user.id},{shares:{some:{sharedWithUserId:t.user.id}}}]}}))return n.NextResponse.json({error:"Workbook n\xe3o encontrado"},{status:404});let i=await c.Hw.listUserFiles(t.user.id||t.user.email||"unknown","excel-files",`users/${t.user.id||t.user.email||"unknown"}/workbooks/${o}`),p=i.map(e=>({path:e.path,name:e.name,size:e.size,type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",uploadedAt:e.lastModified})),f={success:!0,files:p,workbookId:o};if(a){let e=i.reduce((e,r)=>e+r.size,0);f.stats={totalFiles:i.length,totalSize:e,usedQuota:e,maxQuota:104857600,quotaPercentage:Math.min(e/104857600*100,100),lastUpload:i.length>0?i[0]?.lastModified:null}}return n.NextResponse.json(f)}catch(e){return console.error("Erro ao listar arquivos:",e),n.NextResponse.json({error:"Erro interno do servidor"},{status:500})}}async function f(e,{params:r}){try{let t=await (0,u.getServerSession)(l.Lz);if(!t?.user)return n.NextResponse.json({error:"N\xe3o autorizado"},{status:401});let o=r.id;if(!await d.prisma.workbook.findFirst({where:{id:o,OR:[{userId:t.user.id},{shares:{some:{sharedWithUserId:t.user.id,permissionLevel:{in:["EDIT","ADMIN"]}}}}]}}))return n.NextResponse.json({error:"Workbook n\xe3o encontrado ou sem permiss\xe3o"},{status:404});let s=(await e.formData()).get("file");if(!s)return n.NextResponse.json({error:"Nenhum arquivo enviado"},{status:400});if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","text/csv"].includes(s.type))return n.NextResponse.json({error:"Tipo de arquivo n\xe3o suportado"},{status:400});if(s.size>52428800)return n.NextResponse.json({error:"Arquivo muito grande (m\xe1ximo 50MB)"},{status:400});let a=await c.Hw.uploadExcelFile(s,t.user.id||t.user.email||"unknown",o,{fileName:s.name,upsert:!0});return await d.prisma.workbook.update({where:{id:o},data:{updatedAt:new Date}}),n.NextResponse.json({success:!0,file:{path:a.path,size:a.size,name:s.name,type:s.type,uploadedAt:new Date().toISOString()},workbookId:o})}catch(e){return console.error("Erro no upload:",e),n.NextResponse.json({error:"Erro no upload do arquivo",details:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}async function m(e,{params:r}){try{let t=await (0,u.getServerSession)(l.Lz);if(!t?.user)return n.NextResponse.json({error:"N\xe3o autorizado"},{status:401});let o=r.id,{searchParams:s}=new URL(e.url),a=s.get("path");if(!a)return n.NextResponse.json({error:"Caminho do arquivo n\xe3o fornecido"},{status:400});if(!await d.prisma.workbook.findFirst({where:{id:o,userId:t.user.id}}))return n.NextResponse.json({error:"Workbook n\xe3o encontrado ou sem permiss\xe3o"},{status:404});let i=t.user.id||t.user.email||"unknown";if(!a.includes(`users/${i}/workbooks/${o}`))return n.NextResponse.json({error:"Sem permiss\xe3o para deletar este arquivo"},{status:403});return await c.Hw.deleteFile(a,"excel-files"),n.NextResponse.json({success:!0,message:"Arquivo deletado com sucesso",workbookId:o})}catch(e){return console.error("Erro ao deletar arquivo:",e),n.NextResponse.json({error:"Erro ao deletar arquivo",details:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/workbooks/[id]/storage/route",pathname:"/api/workbooks/[id]/storage",filename:"route",bundlePath:"app/api/workbooks/[id]/storage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\storage\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:w,staticGenerationAsyncStorage:x,serverHooks:E}=h,g="/api/workbooks/[id]/storage/route";function k(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:x})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var n=s?Object.getOwnPropertyDescriptor(e,a):null;n&&(n.get||n.set)?Object.defineProperty(o,a,n):o[a]=e[a]}return o.default=e,t&&t.set(e,o),o}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})},95456:(e,r,t)=>{t.d(r,{Lz:()=>s.L,rc:()=>i});var o=t(75571),s=t(81628);async function a(){return await (0,o.getServerSession)(s.L)}async function i(){let e=await a();return e?.user?.id||null}},33035:(e,r,t)=>{t.d(r,{xk:()=>a,Hw:()=>n});var o=t(31518);process.env.SUPABASE_URL||console.warn("SUPABASE_URL n\xe3o est\xe1 configurada - usando NEXT_PUBLIC_SUPABASE_URL"),process.env.SUPABASE_SERVICE_ROLE_KEY||console.warn("SUPABASE_SERVICE_ROLE_KEY n\xe3o est\xe1 configurada - cliente admin n\xe3o estar\xe1 dispon\xedvel"),(0,o.eI)("https://eliuoignzzxnjkcmmtml.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk",{auth:{autoRefreshToken:!1,persistSession:!1,detectSessionInUrl:!1},db:{schema:"public"}});let s=process.env.SUPABASE_SERVICE_ROLE_KEY?(0,o.eI)(process.env.SUPABASE_URL||"https://eliuoignzzxnjkcmmtml.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"}}):null,a={EXCEL_FILES:"excel-files",EXPORTS:"exports",TEMPLATES:"templates",BACKUPS:"backups"};class i{async uploadExcelFile(e,r,t,o={}){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");let a=o.bucket||this.defaultBucket,i=o.folder||`users/${r}/workbooks/${t}`,n=o.fileName||`workbook_${Date.now()}.xlsx`,u=`${i}/${n}`;try{let r=await this.validateBucketPermissions(a,o.isPublic);if(!r.isValid)throw Error(`Bucket validation failed: ${r.error}`);await this.ensureBucketExists(a,o.isPublic);let{data:t,error:i}=await s.storage.from(a).upload(u,e,{upsert:o.upsert||!1,contentType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});if(i)throw i;let n=e instanceof File?e.size:Buffer.byteLength(e),l={path:t.path,fullPath:t.fullPath,size:n};if(o.isPublic){let{data:e}=s.storage.from(a).getPublicUrl(t.path);l.publicUrl=e.publicUrl}return l}catch(e){throw Error(`Erro no upload: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async downloadExcelFile(e,r=this.defaultBucket){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:t,error:o}=await s.storage.from(r).download(e);if(o)throw o;return t}catch(e){throw Error(`Erro no download: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async getSignedUrl(e,r=3600,t=this.defaultBucket){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:o,error:a}=await s.storage.from(t).createSignedUrl(e,r);if(a)throw a;return o.signedUrl}catch(e){throw Error(`Erro ao gerar URL: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async deleteFile(e,r=this.defaultBucket){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{error:t}=await s.storage.from(r).remove([e]);if(t)throw t}catch(e){throw Error(`Erro ao deletar: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async listUserFiles(e,r=this.defaultBucket,t){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let o=t||`users/${e}`,{data:a,error:i}=await s.storage.from(r).list(o,{limit:100,sortBy:{column:"updated_at",order:"desc"}});if(i)throw i;return a.map(e=>({name:e.name,size:e.metadata?.size||0,lastModified:e.updated_at||e.created_at,path:`${o}/${e.name}`}))}catch(e){throw Error(`Erro ao listar arquivos: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async createBackup(e,r,t){let o=JSON.stringify({workbook:e,timestamp:new Date().toISOString(),userId:r,workbookId:t}),s=Buffer.from(o,"utf-8"),i=`backup_${t}_${Date.now()}.json`;return this.uploadExcelFile(s,r,t,{bucket:a.BACKUPS,fileName:i,folder:`backups/${r}/${t}`})}async validateBucketPermissions(e,r=!1){if(!s)return{isValid:!1,error:"Supabase admin client n\xe3o est\xe1 configurado"};try{let{data:r,error:t}=await s.storage.listBuckets();if(t)return{isValid:!1,error:`Sem permiss\xe3o para listar buckets: ${t.message}`};if(r?.some(r=>r.name===e))try{let r=`test_permissions_${Date.now()}.txt`,{error:t}=await s.storage.from(e).upload(r,"test",{upsert:!0});if(t)return{isValid:!1,error:`Sem permiss\xe3o de escrita no bucket ${e}: ${t.message}`};return await s.storage.from(e).remove([r]),{isValid:!0}}catch(e){return{isValid:!1,error:`Erro ao testar permiss\xf5es do bucket: ${e}`}}else{let e=`test_permissions_${Date.now()}`,{error:r}=await s.storage.createBucket(e,{public:!1});if(r)return{isValid:!1,error:`Sem permiss\xe3o para criar buckets: ${r.message}`};return await s.storage.deleteBucket(e),{isValid:!0}}}catch(e){return{isValid:!1,error:`Erro na valida\xe7\xe3o de permiss\xf5es: ${e}`}}}async ensureBucketExists(e,r=!1){if(!s)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:t}=await s.storage.listBuckets();if(!t?.some(r=>r.name===e)){let{error:t}=await s.storage.createBucket(e,{public:r,allowedMimeTypes:["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/json","text/csv"],fileSizeLimit:52428800});if(t)throw t}}catch(r){console.warn(`Aviso: N\xe3o foi poss\xedvel verificar/criar bucket ${e}:`,r)}}async getStorageStats(e){let r={totalFiles:0,totalSize:0,bucketStats:{}};try{for(let t of Object.values(a)){let o=await this.listUserFiles(e,t),s=o.reduce((e,r)=>e+r.size,0);r.bucketStats[t]={files:o.length,size:s},r.totalFiles+=o.length,r.totalSize+=s}}catch(e){console.warn("Erro ao obter estat\xedsticas de storage:",e)}return r}constructor(){this.defaultBucket=a.EXCEL_FILES}}let n=new i}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609,1518,2972,1628],()=>t(80514));module.exports=o})();