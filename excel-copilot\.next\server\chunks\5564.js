"use strict";exports.id=5564,exports.ids=[5564],exports.modules={23811:(e,r,o)=>{o.d(r,{H:()=>i});var a=o(92647),t=o(43895);class s{constructor(){this.redis=null,this.REDIS_PREFIX="collaboration:",this.EXPIRY_TIME=86400,this.CLEANUP_INTERVAL=36e5,this.memoryFallback=new Map,process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?(this.redis=new a.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}),t.kg.info("Redis configurado para persist\xeancia de colabora\xe7\xe3o")):t.kg.warn("Redis n\xe3o configurado, usando fallback em mem\xf3ria"),setInterval(()=>{this.cleanupExpiredStates()},this.CLEANUP_INTERVAL)}async saveCollaborationState(e,r){let o={workbookId:e,collaborators:r,lastActivity:Date.now(),activeConnections:r.length};try{if(this.redis){let r=`${this.REDIS_PREFIX}${e}`;await this.redis.setex(r,this.EXPIRY_TIME,JSON.stringify(o))}else this.memoryFallback.set(e,o);t.kg.debug("Estado de colabora\xe7\xe3o salvo",{workbookId:e,collaboratorsCount:r.length})}catch(r){t.kg.error("Erro ao salvar estado de colabora\xe7\xe3o",{workbookId:e,error:r instanceof Error?r.message:"Erro desconhecido"}),this.memoryFallback.set(e,o)}}async loadCollaborationState(e){try{if(this.redis){let r=`${this.REDIS_PREFIX}${e}`,o=await this.redis.get(r);if(o&&"string"==typeof o){let r=JSON.parse(o);return t.kg.debug("Estado de colabora\xe7\xe3o carregado do Redis",{workbookId:e,collaboratorsCount:r.collaborators.length}),r}}else{let r=this.memoryFallback.get(e);if(r)return t.kg.debug("Estado de colabora\xe7\xe3o carregado da mem\xf3ria",{workbookId:e,collaboratorsCount:r.collaborators.length}),r}return null}catch(r){return t.kg.error("Erro ao carregar estado de colabora\xe7\xe3o",{workbookId:e,error:r instanceof Error?r.message:"Erro desconhecido"}),this.memoryFallback.get(e)||null}}async removeCollaborationState(e){try{if(this.redis){let r=`${this.REDIS_PREFIX}${e}`;await this.redis.del(r)}this.memoryFallback.delete(e),t.kg.debug("Estado de colabora\xe7\xe3o removido",{workbookId:e})}catch(r){t.kg.error("Erro ao remover estado de colabora\xe7\xe3o",{workbookId:e,error:r instanceof Error?r.message:"Erro desconhecido"})}}async listActiveWorkbooks(){try{let e=[];if(this.redis){let r=`${this.REDIS_PREFIX}*`;for(let o of(await this.redis.keys(r))){let r=o.replace(this.REDIS_PREFIX,"");e.push(r)}}for(let r of Array.from(this.memoryFallback.keys()))e.includes(r)||e.push(r);return e}catch(e){return t.kg.error("Erro ao listar planilhas ativas",{error:e instanceof Error?e.message:"Erro desconhecido"}),Array.from(this.memoryFallback.keys())}}async getCollaborationStats(){try{let e=await this.listActiveWorkbooks(),r=0;for(let o of e){let e=await this.loadCollaborationState(o);e&&(r+=e.collaborators.length)}return{totalWorkbooks:e.length,totalCollaborators:r,averageCollaboratorsPerWorkbook:e.length>0?Math.round(r/e.length*100)/100:0}}catch(e){return t.kg.error("Erro ao obter estat\xedsticas de colabora\xe7\xe3o",{error:e instanceof Error?e.message:"Erro desconhecido"}),{totalWorkbooks:0,totalCollaborators:0,averageCollaboratorsPerWorkbook:0}}}async updateActivity(e){try{let r=await this.loadCollaborationState(e);r&&(r.lastActivity=Date.now(),await this.saveCollaborationState(e,r.collaborators))}catch(r){t.kg.error("Erro ao atualizar atividade",{workbookId:e,error:r instanceof Error?r.message:"Erro desconhecido"})}}async cleanupExpiredStates(){try{let e=Date.now()-1e3*this.EXPIRY_TIME,r=0;for(let[o,a]of Array.from(this.memoryFallback.entries()))a.lastActivity<e&&(this.memoryFallback.delete(o),r++);if(this.redis)for(let o of(await this.listActiveWorkbooks())){let a=await this.loadCollaborationState(o);a&&a.lastActivity<e&&(await this.removeCollaborationState(o),r++)}r>0&&t.kg.info("Limpeza de estados de colabora\xe7\xe3o realizada",{estadosRemovidos:r})}catch(e){t.kg.error("Erro na limpeza de estados expirados",{error:e instanceof Error?e.message:"Erro desconhecido"})}}async healthCheck(){let e={redis:!1,memory:!0};try{this.redis&&(await this.redis.ping(),e.redis=!0)}catch(e){t.kg.warn("Redis n\xe3o dispon\xedvel para colabora\xe7\xe3o",{error:e instanceof Error?e.message:"Erro desconhecido"})}return e}}let i=new s},43895:(e,r,o)=>{let a;o.d(r,{kg:()=>d});var t=o(99557),s=o.n(t);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function l(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],o={};return Object.keys(e).forEach(a=>{r.includes(a)||(o[a]=e[a])}),{normalizedError:e,extractedMetadata:o}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function n(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let c={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err}}};try{let e=c.production;a=s()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=s()({level:"info",formatters:{level:e=>({level:e})}})}let d={trace:(e,r)=>{a.trace(r||{},e)},debug:(e,r)=>{a.debug(r||{},e)},info:(e,r)=>{a.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:o}=l(r);a.warn(o,e)}else a.warn(n(r)||{},e)},error:(e,r,o)=>{let{normalizedError:t,extractedMetadata:s}=l(r),i={...o||{},...s,...t&&{error:{message:t.message,stack:t.stack,name:t.name}}};a.error(i,e)},fatal:(e,r,o)=>{let{normalizedError:t,extractedMetadata:s}=l(r),i={...o||{},...s,...t&&{error:{message:t.message,stack:t.stack,name:t.name}}};a.fatal(i,e)},createChild:e=>{let r=a.child(e);return{trace:(e,o)=>{r.trace(o||{},e)},debug:(e,o)=>{r.debug(o||{},e)},info:(e,o)=>{r.info(o||{},e)},warn:(e,o)=>{if(o instanceof Error||"object"==typeof o&&null!==o){let{extractedMetadata:a}=l(o);r.warn(a,e)}else r.warn(n(o)||{},e)},error:(e,o,a)=>{let{normalizedError:t,extractedMetadata:s}=l(o),i={...a||{},...s,...t&&{error:{message:t.message,stack:t.stack,name:t.name}}};r.error(i,e)},fatal:(e,o,a)=>{let{normalizedError:t,extractedMetadata:s}=l(o),i={...a||{},...s,...t&&{error:{message:t.message,stack:t.stack,name:t.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,r,o)=>{o.d(r,{P:()=>n,prisma:()=>l});var a=o(53524);let t={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},s={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],l=global.prisma||new a.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function n(){return{...s,activeConnections:Math.min(Math.floor(5*Math.random())+1,s.maxPoolSize),poolSize:s.poolSize}}async function c(){try{await l.$disconnect(),t.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){t.error("Erro ao desconectar do banco de dados",e)}}l.$on("query",e=>{s.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),s.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&t.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),l.$on("error",e=>{s.failedQueries++,s.connectionFailures++,s.lastConnectionFailure=new Date().toISOString(),t.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})}};