"use strict";(()=>{var e={};e.id=5684,e.ids=[5684],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},22197:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>h,patchFetch:()=>E,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>p,staticGenerationAsyncStorage:()=>g});var s={};o.r(s),o.d(s,{GET:()=>l,dynamic:()=>u});var a=o(49303),r=o(88716),n=o(60670),i=o(87070);let u="force-dynamic";async function l(){try{let e=process.env.AUTH_GOOGLE_CLIENT_ID,t=process.env.AUTH_GOOGLE_CLIENT_SECRET,o=process.env.AUTH_NEXTAUTH_URL,s=process.env.AUTH_NEXTAUTH_SECRET,a=`${o}/api/auth/callback/google`,r=`${o}/api/auth/signin/google`,n={environment:"production",timestamp:new Date().toISOString(),variables:{GOOGLE_CLIENT_ID:e?`${e.substring(0,10)}...`:"MISSING",GOOGLE_CLIENT_SECRET:t?"CONFIGURED":"MISSING",NEXTAUTH_URL:o||"MISSING",NEXTAUTH_SECRET:s?"CONFIGURED":"MISSING"},urls:{callback:a,signin:r,base:o},validation:{hasGoogleClientId:!!e,hasGoogleClientSecret:!!t,hasNextAuthUrl:!!o,hasNextAuthSecret:!!s,allConfigured:!!(e&&t&&o&&s)}};if(!n.validation.allConfigured)return i.NextResponse.json({status:"error",message:"Configura\xe7\xe3o OAuth incompleta",config:n,recommendations:["Verifique se todas as vari\xe1veis de ambiente est\xe3o configuradas no Vercel","GOOGLE_CLIENT_ID deve ser obtido do Google Cloud Console","GOOGLE_CLIENT_SECRET deve ser obtido do Google Cloud Console","NEXTAUTH_URL deve ser a URL base da aplica\xe7\xe3o","NEXTAUTH_SECRET deve ser uma string aleat\xf3ria segura"]},{status:500});let u=new URL("https://accounts.google.com/oauth/authorize");return u.searchParams.set("client_id",e),u.searchParams.set("redirect_uri",a),u.searchParams.set("response_type","code"),u.searchParams.set("scope","openid email profile"),u.searchParams.set("state","test-state"),i.NextResponse.json({status:"success",message:"Configura\xe7\xe3o OAuth v\xe1lida",config:n,testUrls:{googleAuth:u.toString(),nextAuthSignin:r,nextAuthCallback:a},instructions:["1. Verifique se as URLs de callback est\xe3o configuradas no Google Cloud Console","2. Teste o login acessando a URL de signin","3. Verifique os logs do Vercel para erros detalhados"]})}catch(e){return i.NextResponse.json({status:"error",message:"Erro ao verificar configura\xe7\xe3o OAuth",error:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/auth/test-google/route",pathname:"/api/auth/test-google",filename:"route",bundlePath:"app/api/auth/test-google/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-google\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:p}=c,h="/api/auth/test-google/route";function E(){return(0,n.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>o(22197));module.exports=s})();