"use strict";(()=>{var e={};e.id=7740,e.ids=[7740],e.modules={53524:e=>{e.exports=require("@prisma/client")},57641:e=>{e.exports=require("exceljs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},46135:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>R,patchFetch:()=>y,requestAsyncStorage:()=>x,routeModule:()=>h,serverHooks:()=>v,staticGenerationAsyncStorage:()=>E});var a={};t.r(a),t.d(a,{POST:()=>g,dynamic:()=>f});var o=t(49303),s=t(88716),i=t(60670),n=t(57641),l=t.n(n),u=t(87070),c=t(45609),d=t(43895);function p(e,r,t){return e&&"string"==typeof e?e&&[/=.*EXEC\(/i,/=.*CMD\(/i,/=.*SHELL\(/i,/=.*RUN\(/i,/=.*CALL\(/i,/=.*SYSTEM\(/i,/=.*DDE\(/i,/=.*DDEEXEC\(/i,/=.*DDEAUTO\(/i,/=.*DDEPOKE\(/i,/=.*XLM\./i,/=.*MSExcel\./i,/=.*Application\./i,/=.*VBA\./i,/=.*MACRO\(/i,/=.*HYPERLINK\(".*javascript:/i,/=.*HYPERLINK\(".*data:/i,/=.*HYPERLINK\(".*file:/i,/=.*HYPERLINK\(".*ftp:/i,/=.*IMPORTFROMWEB\(/i,/=.*WEBSERVICE\(/i,/=.*FILTERXML\(/i,/=.*DOCUMENT\(/i,/=.*FILES\(/i,/=.*DIRECTORY\(/i,/=.*POWERQUERY\(/i,/=.*ODBC\(/i,/=.*OLEDB\(/i,/=.*WINEXEC\(/i,/=.*CREATEOBJECT\(/i,/=.*GETOBJECT\(/i].some(r=>r.test(e))?{sanitizedValue:"[F\xd3RMULA BLOQUEADA POR SEGURAN\xc7A]",wasBlocked:!0,reason:"F\xf3rmula cont\xe9m comandos potencialmente perigosos"}:e&&[/Sub\s+\w+\(/i,/Function\s+\w+\(/i,/Private\s+Sub/i,/Public\s+Sub/i,/Auto_Open/i,/Auto_Close/i,/Workbook_Open/i,/<script/i,/javascript:/i,/vbscript:/i,/activexobject/i,/cmd\.exe/i,/powershell/i,/wscript/i,/cscript/i].some(r=>r.test(e))?{sanitizedValue:"[MACRO BLOQUEADA POR SEGURAN\xc7A]",wasBlocked:!0,reason:"Macro ou script malicioso detectado"}:e&&[/javascript:/i,/data:/i,/file:/i,/ftp:/i,/bit\.ly/i,/tinyurl/i,/t\.co/i,/goo\.gl/i,/https?:\/\/127\./i,/https?:\/\/localhost/i,/https?:\/\/192\.168\./i,/https?:\/\/10\./i,/https?:\/\/172\.(1[6-9]|2[0-9]|3[01])\./i].some(r=>r.test(e))?{sanitizedValue:"[URL BLOQUEADA POR SEGURAN\xc7A]",wasBlocked:!0,reason:"URL potencialmente maliciosa detectada"}:{sanitizedValue:function(e){if(!e||"string"!=typeof e)return e;let r=e.replace(/<[^>]*>/g,"");r=(r=(r=(r=r.replace(/javascript:/gi,"blocked:")).replace(/data:/gi,"blocked:")).replace(/vbscript:/gi,"blocked:")).replace(/file:/gi,"blocked:");let t=RegExp(`[\\u0000-\\u001F\\u007F]`,"g");return(r=(r=(r=r.replace(t,"")).replace(/\\x[0-9a-fA-F]{2}/g,"")).replace(/\\u[0-9a-fA-F]{4}/g,"")).length>1e4&&(r=r.substring(0,1e4)+"[TRUNCADO]"),r}(e),wasBlocked:!1}:{sanitizedValue:e,wasBlocked:!1}}var m=t(63841);let f="force-dynamic";async function g(e){try{let r=await (0,c.getServerSession)();if(!r?.user)return u.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let t=(await e.formData()).get("file");if(!t)return u.NextResponse.json({error:"Nenhum arquivo enviado."},{status:400});if(t.size>5242880)return u.NextResponse.json({error:"Arquivo muito grande. O tamanho m\xe1ximo permitido \xe9 5MB."},{status:400});let a="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===t.type||"application/vnd.ms-excel"===t.type||t.name.endsWith(".xlsx")||t.name.endsWith(".xls"),o="text/csv"===t.type||t.name.endsWith(".csv");if(!a&&!o)return u.NextResponse.json({error:"Formato de arquivo inv\xe1lido. Por favor, envie um arquivo Excel (.xlsx, .xls) ou CSV (.csv)."},{status:400});t.name.replace(/[^\w\s.-]/gi,"")!==t.name&&d.kg.warn(`Nome de arquivo potencialmente inseguro detectado: ${t.name}`);let s=await t.arrayBuffer(),i=Buffer.from(s),n=new(l()).Workbook;try{if(o){let e=i.toString("utf-8"),r=n.addWorksheet("Sheet1"),t=e.split("\n").map(e=>e.split(","));t.length>0&&r.addRows(t)}else await n.xlsx.load(s)}catch(e){return d.kg.error("Erro ao processar arquivo:",e),u.NextResponse.json({error:"Erro ao processar o arquivo. Formato inv\xe1lido ou arquivo corrompido."},{status:400})}let f={sheets:[]};n.eachSheet((e,r)=>{let t={id:r,name:e.name,rows:[]},a=[];e.eachRow({includeEmpty:!1},(e,r)=>{let o={cells:[]};e.eachCell({includeEmpty:!0},(e,t)=>{1===r&&a.push(e.value?.toString()||`Coluna ${t}`),o.cells.push({columnName:a[t-1]||`Coluna ${t}`,value:e.value,formula:e.formula||null,type:e.type})}),t.rows.push(o)}),f.sheets.push(t)});let g={sheets:[]},h=0,x=[];for(let e of f.sheets){let{sanitizedData:r,securityReport:t}=function(e){let r=JSON.parse(JSON.stringify(e)),t={hasDangerousFormulas:!1,formulasRejected:0,details:[]};try{Array.isArray(r)?r.forEach((e,r)=>{"object"==typeof e&&null!==e&&Object.keys(e).forEach(a=>{let o=e[a];if("string"==typeof o){let s=p(o,r,a);e[a]=s.sanitizedValue,s.wasBlocked&&(t.hasDangerousFormulas=!0,t.formulasRejected++,t.details.push({rowIndex:r,columnName:a,reason:s.reason||"Conte\xfado malicioso detectado"}))}})}):"object"==typeof r&&null!==r&&Object.keys(r).forEach(e=>{let a=r[e];if("string"==typeof a){let o=p(a,0,e);r[e]=o.sanitizedValue,o.wasBlocked&&(t.hasDangerousFormulas=!0,t.formulasRejected++,t.details.push({rowIndex:0,columnName:e,reason:o.reason||"Conte\xfado malicioso detectado"}))}}),t.hasDangerousFormulas&&d.kg.warn("F\xf3rmulas maliciosas detectadas e bloqueadas no Excel",{formulasRejected:t.formulasRejected,details:t.details})}catch(e){return d.kg.error("Erro durante sanitiza\xe7\xe3o de dados Excel",e),{sanitizedData:{},securityReport:{hasDangerousFormulas:!0,formulasRejected:1,details:[{rowIndex:-1,columnName:"SYSTEM_ERROR",reason:"Erro durante sanitiza\xe7\xe3o - dados bloqueados por seguran\xe7a"}]}}}return{sanitizedData:r,securityReport:t}}(e);g.sheets.push(r),t.hasDangerousFormulas&&(h+=t.formulasRejected,x.push({sheetName:r.name,...t}))}h>0&&d.kg.warn(`[SECURITY_ALERT] Detectadas ${h} f\xf3rmulas potencialmente maliciosas em '${t.name}'`,{userId:r.user?.id,fileName:t.name,securityReports:x});let E=await m.prisma.workbook.create({data:{name:t.name,userId:r.user?.id||"",sheets:{create:g.sheets.map(e=>({name:e.name,data:JSON.stringify(e)}))}},include:{sheets:!0}}),v={success:!0,workbook:{id:E.id,name:E.name,sheetCount:E.sheets.length}};return h>0&&(v.warnings={security:{formulasRemoved:h,message:`${h} f\xf3rmula(s) potencialmente perigosa(s) foram removidas por motivos de seguran\xe7a.`}}),u.NextResponse.json(v)}catch(e){return d.kg.error("[EXCEL_PROCESSING_ERROR]",e),u.NextResponse.json({error:"Erro ao processar o arquivo. Por favor, tente novamente.",details:void 0},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/excel/route",pathname:"/api/excel",filename:"route",bundlePath:"app/api/excel/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:x,staticGenerationAsyncStorage:E,serverHooks:v}=h,R="/api/excel/route";function y(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:E})}},43895:(e,r,t)=>{let a;t.d(r,{kg:()=>c});var o=t(99557),s=t.n(o);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function n(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(a=>{r.includes(a)||(t[a]=e[a])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err}}};try{let e=u.production;a=s()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=s()({level:"info",formatters:{level:e=>({level:e})}})}let c={trace:(e,r)=>{a.trace(r||{},e)},debug:(e,r)=>{a.debug(r||{},e)},info:(e,r)=>{a.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=n(r);a.warn(t,e)}else a.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:o,extractedMetadata:s}=n(r),i={...t||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};a.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:o,extractedMetadata:s}=n(r),i={...t||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};a.fatal(i,e)},createChild:e=>{let r=a.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:a}=n(t);r.warn(a,e)}else r.warn(l(t)||{},e)},error:(e,t,a)=>{let{normalizedError:o,extractedMetadata:s}=n(t),i={...a||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.error(i,e)},fatal:(e,t,a)=>{let{normalizedError:o,extractedMetadata:s}=n(t),i={...a||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>n});var a=t(53524);let o={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},s={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],n=global.prisma||new a.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...s,activeConnections:Math.min(Math.floor(5*Math.random())+1,s.maxPoolSize),poolSize:s.poolSize}}async function u(){try{await n.$disconnect(),o.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){o.error("Erro ao desconectar do banco de dados",e)}}n.$on("query",e=>{s.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),s.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&o.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),n.$on("error",e=>{s.failedQueries++,s.connectionFailures++,s.lastConnectionFailure=new Date().toISOString(),o.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{u()})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557,330,5609],()=>t(46135));module.exports=a})();