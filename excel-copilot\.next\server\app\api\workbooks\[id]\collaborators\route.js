"use strict";(()=>{var e={};e.id=4124,e.ids=[4124],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},70214:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>w,requestAsyncStorage:()=>b,routeModule:()=>k,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var o={};r.r(o),r.d(o,{GET:()=>h});var s=r(49303),a=r(88716),i=r(60670),n=r(87070),l=r(45609),c=r(23811),d=r(43895);class u{constructor(){this.activeCollaborators=new Map,this.userSockets=new Map,this.socketUsers=new Map,this.workbookSockets=new Map}static getInstance(){return u.instance||(u.instance=new u),u.instance}async addCollaborator(e,t){let r=this.activeCollaborators.get(e)||[],o=r.findIndex(e=>e.id===t.id);o>=0?r[o]=t:r.push(t),this.activeCollaborators.set(e,r),this.userSockets.set(t.id,t.socket),this.socketUsers.set(t.socket,t.id);let s=this.workbookSockets.get(e)||new Set;s.add(t.socket),this.workbookSockets.set(e,s);try{await c.H.saveCollaborationState(e,r)}catch(r){d.kg.error("Erro ao persistir estado de colabora\xe7\xe3o",{workbookId:e,collaboratorId:t.id,error:r instanceof Error?r.message:"Erro desconhecido"})}}async removeCollaborator(e){let t=this.socketUsers.get(e);if(!t)return[];let r=[];return this.activeCollaborators.forEach(async(o,s)=>{let a=o.findIndex(e=>e.id===t);if(a>=0){o.splice(a,1),this.activeCollaborators.set(s,o),r.push(s);let i=this.workbookSockets.get(s);i&&(i.delete(e),0===i.size?this.workbookSockets.delete(s):this.workbookSockets.set(s,i));try{0===o.length?await c.H.removeCollaborationState(s):await c.H.saveCollaborationState(s,o)}catch(e){d.kg.error("Erro ao persistir remo\xe7\xe3o de colaborador",{workbookId:s,userId:t,error:e instanceof Error?e.message:"Erro desconhecido"})}}}),this.userSockets.delete(t),this.socketUsers.delete(e),r}getCollaborators(e){return this.activeCollaborators.get(e)||[]}updateCollaboratorPosition(e,t){let r=this.socketUsers.get(e);if(!r)return null;let o=[];return this.activeCollaborators.forEach((e,s)=>{let a=e.find(e=>e.id===r);a&&(a.position=t,a.lastActive=new Date,a.status="active",o.push(s))}),{userId:r,workbookIds:o}}updateCollaboratorStatus(e,t){let r=[];return this.activeCollaborators.forEach((o,s)=>{let a=o.find(t=>t.id===e);a&&(a.status=t,a.lastActive=new Date,r.push(s))}),r}getWorkbookSockets(e){let t=this.workbookSockets.get(e);return t?Array.from(t):[]}getUserIdFromSocket(e){return this.socketUsers.get(e)}getSocketFromUserId(e){return this.userSockets.get(e)}async loadPersistedState(e){try{let t=await c.H.loadCollaborationState(e);if(t&&t.collaborators.length>0){let r=t.collaborators.filter(e=>Date.now()-e.lastActive.getTime()<3e5);if(r.length>0)return this.activeCollaborators.set(e,r),r.forEach(t=>{this.userSockets.set(t.id,t.socket),this.socketUsers.set(t.socket,t.id);let r=this.workbookSockets.get(e)||new Set;r.add(t.socket),this.workbookSockets.set(e,r)}),d.kg.info("Estado de colabora\xe7\xe3o carregado",{workbookId:e,colaboradoresCarregados:r.length}),!0}return!1}catch(t){return d.kg.error("Erro ao carregar estado persistente",{workbookId:e,error:t instanceof Error?t.message:"Erro desconhecido"}),!1}}async getStats(){let e=this.activeCollaborators.size,t=Array.from(this.activeCollaborators.values()).reduce((e,t)=>e+t.length,0),r=await c.H.getCollaborationStats();return{totalWorkbooks:Math.max(e,r.totalWorkbooks),totalCollaborators:Math.max(t,r.totalCollaborators),memoryWorkbooks:e,persistentWorkbooks:r.totalWorkbooks}}}var p=r(63841);async function h(e,{params:t}){try{let e=await (0,l.getServerSession)();if(!e?.user)return n.NextResponse.json({error:"Voc\xea precisa estar autenticado"},{status:401});let r=t.id;if(!r)return n.NextResponse.json({error:"ID da planilha \xe9 obrigat\xf3rio"},{status:400});let o=e.user.id,s=await p.prisma.workbook.findUnique({where:{id:r},include:{user:{select:{id:!0,name:!0,email:!0,image:!0}}}});if(!s)return n.NextResponse.json({error:"Planilha n\xe3o encontrada"},{status:404});if(s.userId!==o&&!s.isPublic&&!await p.prisma.workbookShare.findFirst({where:{workbookId:r,sharedWithUserId:o}}))return n.NextResponse.json({error:"Acesso negado"},{status:403});let a=await p.prisma.workbookShare.findMany({where:{workbookId:r},include:{sharedWithUser:{select:{id:!0,name:!0,email:!0,image:!0}}}}),i=[];i.push({id:s.user.id,name:s.user.name||"Usu\xe1rio",email:s.user.email||"",avatar:s.user.image||void 0,role:"owner",permission:"edit",lastActive:new Date}),a.length>0&&i.push(...a.map(e=>({id:e.sharedWithUser.id,name:e.sharedWithUser.name||"Usu\xe1rio",email:e.sharedWithUser.email||"",avatar:e.sharedWithUser.image||void 0,role:"collaborator",permission:e.permissionLevel,lastActive:e.updatedAt||new Date})));let c=u.getInstance().getCollaborators(r),d=i.map(e=>{let t=c.find(t=>t.id===e.id);return{...e,status:t?t.status:"offline",position:t?t.position:void 0,lastActive:t?t.lastActive:e.lastActive}});return c.forEach(e=>{i.some(t=>t.id===e.id)||d.push({id:e.id,name:e.name||"Convidado",email:e.email||"",avatar:e.avatar,role:"guest",permission:"view",status:e.status,position:e.position||{row:0,col:0},lastActive:e.lastActive})}),n.NextResponse.json({collaborators:d})}catch(e){return console.error("Erro ao obter colaboradores:",e),n.NextResponse.json({error:"Erro interno do servidor"},{status:500})}}let k=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/workbooks/[id]/collaborators/route",pathname:"/api/workbooks/[id]/collaborators",filename:"route",bundlePath:"app/api/workbooks/[id]/collaborators/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\collaborators\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:b,staticGenerationAsyncStorage:m,serverHooks:g}=k,v="/api/workbooks/[id]/collaborators/route";function w(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,9557,330,5609,2647,5564],()=>r(70214));module.exports=o})();