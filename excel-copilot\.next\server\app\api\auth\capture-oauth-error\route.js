"use strict";(()=>{var e={};e.id=5713,e.ids=[5713],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19914:(e,r,o)=>{o.r(r),o.d(r,{originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>h});var t={};o.r(t),o.d(t,{GET:()=>c,POST:()=>d,dynamic:()=>i});var s=o(49303),a=o(88716),n=o(60670),u=o(87070);let i="force-dynamic";async function c(e){try{let{searchParams:r}=new URL(e.url),o=Object.fromEntries(r.entries()),t={timestamp:new Date().toISOString(),url:e.url,method:e.method,headers:Object.fromEntries(e.headers.entries()),userAgent:e.headers.get("user-agent"),referer:e.headers.get("referer"),origin:e.headers.get("origin")},s={hasError:!!o.error,errorType:o.error||null,errorDescription:o.error_description||null,state:o.state||null,code:o.code||null,scope:o.scope||null},a="unknown";if(t.referer?.includes("google"))a="google";else if(t.referer?.includes("github"))a="github";else if(o.state)try{let e=decodeURIComponent(o.state);e.includes("google")&&(a="google"),e.includes("github")&&(a="github")}catch{}let n=[];"access_denied"===s.errorType&&n.push("Usu\xe1rio rejeitou as permiss\xf5es ou cancelou o login"),"redirect_uri_mismatch"===s.errorType&&(n.push("URL de callback n\xe3o est\xe1 configurada corretamente no provedor OAuth"),n.push(`Verifique se a URL est\xe1 exatamente: ${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/${a}`)),"invalid_client"===s.errorType&&(n.push("Client ID ou Client Secret incorretos"),n.push("Verifique as vari\xe1veis de ambiente na Vercel")),"invalid_request"===s.errorType&&(n.push("Par\xe2metros da requisi\xe7\xe3o OAuth inv\xe1lidos"),n.push("Poss\xedvel problema na configura\xe7\xe3o do NextAuth.js")),s.hasError||s.code||(n.push("Nenhum erro ou c\xf3digo de autoriza\xe7\xe3o detectado"),n.push("Poss\xedvel problema no fluxo de redirecionamento"));let i={status:s.hasError?"error":"info",message:s.hasError?`Erro OAuth detectado: ${s.errorType}`:"Captura de dados OAuth",provider:a,oauth:s,request:t,allParams:o,commonIssues:n,nextSteps:["Verifique as configura\xe7\xf5es OAuth no provedor","Confirme as URLs de callback","Verifique as vari\xe1veis de ambiente na Vercel","Teste novamente o login"]};if(s.hasError&&s.errorType){let r=new URL("/auth/signin",e.url);return r.searchParams.set("error",s.errorType),s.errorDescription&&r.searchParams.set("error_description",s.errorDescription),r.searchParams.set("provider",a),r.searchParams.set("debug","true"),u.NextResponse.redirect(r)}return u.NextResponse.json(i)}catch(e){return console.error("\uD83D\uDEA8 [OAUTH-CAPTURE] Erro ao capturar dados OAuth:",e),u.NextResponse.json({status:"error",message:"Erro ao capturar dados OAuth",error:e instanceof Error?e.message:"Erro desconhecido",timestamp:new Date().toISOString()},{status:500})}}async function d(e){try{return await e.json(),u.NextResponse.json({status:"success",message:"Erro OAuth capturado com sucesso",timestamp:new Date().toISOString()})}catch(e){return console.error("\uD83D\uDEA8 [OAUTH-CAPTURE] Erro ao processar dados do frontend:",e),u.NextResponse.json({status:"error",message:"Erro ao processar dados do frontend",error:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/capture-oauth-error/route",pathname:"/api/auth/capture-oauth-error",filename:"route",bundlePath:"app/api/auth/capture-oauth-error/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\capture-oauth-error\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:l,staticGenerationAsyncStorage:h,serverHooks:m}=p,g="/api/auth/capture-oauth-error/route";function f(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:h})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[8948,5972],()=>o(19914));module.exports=t})();