"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1e3],{77515:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},28680:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("ArrowDownWideNarrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]])},30139:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]])},38711:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},58215:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("BarChart4",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M13 17V9",key:"1fwyjl"}],["path",{d:"M18 17V5",key:"sfb6ij"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},77424:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},34567:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},24241:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},70518:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},40933:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6884:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},23787:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},36356:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("FileSpreadsheet",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M14 17h2",key:"10kma7"}]])},22023:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},404:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},20969:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]])},37733:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},39451:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]])},97529:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},39127:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("PiggyBank",[["path",{d:"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2h0V5z",key:"uf6l00"}],["path",{d:"M2 9v1c0 1.1.9 2 2 2h1",key:"nm575m"}],["path",{d:"M16 11h0",key:"k2aug8"}]])},92513:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},54817:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},33907:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},92222:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},10883:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},58184:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},11240:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},74697:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},66648:function(e,t,n){n.d(t,{default:function(){return r.a}});var a=n(61669),r=n.n(a)},87138:function(e,t,n){n.d(t,{default:function(){return r.a}});var a=n(231),r=n.n(a)},38173:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let a=n(99920),r=n(41452),i=n(57437),o=r._(n(2265)),u=a._(n(54887)),d=a._(n(28321)),l=n(80497),s=n(7103),c=n(93938);n(72301);let f=n(60291),h=a._(n(21241)),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1};function p(e,t,n,a,r,i,o){let u=null==e?void 0:e.src;e&&e["data-loaded-src"]!==u&&(e["data-loaded-src"]=u,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&r(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let a=!1,r=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>a,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{a=!0,t.preventDefault()},stopPropagation:()=>{r=!0,t.stopPropagation()}})}(null==a?void 0:a.current)&&a.current(e)}}))}function y(e){let[t,n]=o.version.split(".",2),a=parseInt(t,10),r=parseInt(n,10);return a>18||18===a&&r>=3?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let g=(0,o.forwardRef)((e,t)=>{let{src:n,srcSet:a,sizes:r,height:u,width:d,decoding:l,className:s,style:c,fetchPriority:f,placeholder:h,loading:m,unoptimized:g,fill:v,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:M,setShowAltText:x,sizesInput:k,onLoad:j,onError:P,...C}=e;return(0,i.jsx)("img",{...C,...y(f),loading:m,width:d,height:u,decoding:l,"data-nimg":v?"fill":"1",className:s,style:c,sizes:r,srcSet:a,src:n,ref:(0,o.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(P&&(e.src=e.src),e.complete&&p(e,h,b,w,M,g,k))},[n,h,b,w,M,P,g,k,t]),onLoad:e=>{p(e.currentTarget,h,b,w,M,g,k)},onError:e=>{x(!0),"empty"!==h&&M(!0),P&&P(e)}})});function v(e){let{isAppRouter:t,imgAttributes:n}=e,a={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...y(n.fetchPriority)};return t&&u.default.preload?(u.default.preload(n.src,a),null):(0,i.jsx)(d.default,{children:(0,i.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...a},"__nimg-"+n.src+n.srcSet+n.sizes)})}let b=(0,o.forwardRef)((e,t)=>{let n=(0,o.useContext)(f.RouterContext),a=(0,o.useContext)(c.ImageConfigContext),r=(0,o.useMemo)(()=>{let e=m||a||s.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),n=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:n}},[a]),{onLoad:u,onLoadingComplete:d}=e,p=(0,o.useRef)(u);(0,o.useEffect)(()=>{p.current=u},[u]);let y=(0,o.useRef)(d);(0,o.useEffect)(()=>{y.current=d},[d]);let[b,w]=(0,o.useState)(!1),[M,x]=(0,o.useState)(!1),{props:k,meta:j}=(0,l.getImgProps)(e,{defaultLoader:h.default,imgConf:r,blurComplete:b,showAltText:M});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(g,{...k,unoptimized:j.unoptimized,placeholder:j.placeholder,fill:j.fill,onLoadRef:p,onLoadingCompleteRef:y,setBlurComplete:w,setShowAltText:x,sizesInput:e.sizes,ref:t}),j.priority?(0,i.jsx)(v,{isAppRouter:!n,imgAttributes:k}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82901:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return a}});let a=n(99920)._(n(2265)).default.createContext({})},40687:function(e,t){function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:a=!1}=void 0===e?{}:e;return t||n&&a}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},80497:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return u}}),n(72301);let a=n(51564),r=n(7103);function i(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function u(e,t){var n;let u,d,l,{src:s,sizes:c,unoptimized:f=!1,priority:h=!1,loading:m,className:p,quality:y,width:g,height:v,fill:b=!1,style:w,overrideSrc:M,onLoad:x,onLoadingComplete:k,placeholder:j="empty",blurDataURL:P,fetchPriority:C,layout:S,objectFit:_,objectPosition:D,lazyBoundary:W,lazyRoot:z,...Z}=e,{imgConf:O,showAltText:A,blurComplete:E,defaultLoader:R}=t,T=O||r.imageConfigDefault;if("allSizes"in T)u=T;else{let e=[...T.deviceSizes,...T.imageSizes].sort((e,t)=>e-t),t=T.deviceSizes.sort((e,t)=>e-t);u={...T,allSizes:e,deviceSizes:t}}if(void 0===R)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let F=Z.loader||R;delete Z.loader,delete Z.srcSet;let q="__next_img_default"in F;if(q){if("custom"===u.loader)throw Error('Image with src "'+s+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=F;F=t=>{let{config:n,...a}=t;return e(a)}}if(S){"fill"===S&&(b=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!c&&(c=t)}let I="",Y=o(g),N=o(v);if("object"==typeof(n=s)&&(i(n)||void 0!==n.src)){let e=i(s)?s.default:s;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(d=e.blurWidth,l=e.blurHeight,P=P||e.blurDataURL,I=e.src,!b){if(Y||N){if(Y&&!N){let t=Y/e.width;N=Math.round(e.height*t)}else if(!Y&&N){let t=N/e.height;Y=Math.round(e.width*t)}}else Y=e.width,N=e.height}}let H=!h&&("lazy"===m||void 0===m);(!(s="string"==typeof s?s:I)||s.startsWith("data:")||s.startsWith("blob:"))&&(f=!0,H=!1),u.unoptimized&&(f=!0),q&&s.endsWith(".svg")&&!u.dangerouslyAllowSVG&&(f=!0),h&&(C="high");let X=o(y),V=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:_,objectPosition:D}:{},A?{}:{color:"transparent"},w),L=E||"empty"===j?null:"blur"===j?'url("data:image/svg+xml;charset=utf-8,'+(0,a.getImageBlurSvg)({widthInt:Y,heightInt:N,blurWidth:d,blurHeight:l,blurDataURL:P||"",objectFit:V.objectFit})+'")':'url("'+j+'")',U=L?{backgroundSize:V.objectFit||"cover",backgroundPosition:V.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:L}:{},B=function(e){let{config:t,src:n,unoptimized:a,width:r,quality:i,sizes:o,loader:u}=e;if(a)return{src:n,srcSet:void 0,sizes:void 0};let{widths:d,kind:l}=function(e,t,n){let{deviceSizes:a,allSizes:r}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let a;a=e.exec(n);a)t.push(parseInt(a[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=a[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:a,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,o),s=d.length-1;return{sizes:o||"w"!==l?o:"100vw",srcSet:d.map((e,a)=>u({config:t,src:n,quality:i,width:e})+" "+("w"===l?e:a+1)+l).join(", "),src:u({config:t,src:n,quality:i,width:d[s]})}}({config:u,src:s,unoptimized:f,width:Y,quality:X,sizes:c,loader:F});return{props:{...Z,loading:H?"lazy":m,fetchPriority:C,width:Y,height:N,decoding:"async",className:p,style:{...V,...U},sizes:B.sizes,srcSet:B.srcSet,src:M||B.src},meta:{unoptimized:f,priority:h,placeholder:j,fill:b}}}},28321:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return p},defaultHead:function(){return c}});let a=n(99920),r=n(41452),i=n(57437),o=r._(n(2265)),u=a._(n(65960)),d=n(82901),l=n(36590),s=n(40687);function c(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(72301);let h=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:n}=t;return e.reduce(f,[]).reverse().concat(c(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,a={};return r=>{let i=!0,o=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){o=!0;let t=r.key.slice(r.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(r.type){case"title":case"base":t.has(r.type)?i=!1:t.add(r.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(r.props.hasOwnProperty(t)){if("charSet"===t)n.has(t)?i=!1:n.add(t);else{let e=r.props[t],n=a[t]||new Set;("name"!==t||!o)&&n.has(e)?i=!1:(n.add(e),a[t]=n)}}}}return i}}()).reverse().map((e,t)=>{let a=e.key||t;if(!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:a})})}let p=function(e){let{children:t}=e,n=(0,o.useContext)(d.AmpStateContext),a=(0,o.useContext)(l.HeadManagerContext);return(0,i.jsx)(u.default,{reduceComponentsToState:m,headManager:a,inAmpMode:(0,s.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51564:function(e,t){function n(e){let{widthInt:t,heightInt:n,blurWidth:a,blurHeight:r,blurDataURL:i,objectFit:o}=e,u=a?40*a:t,d=r?40*r:n,l=u&&d?"viewBox='0 0 "+u+" "+d+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+l+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(l?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},93938:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let a=n(99920)._(n(2265)),r=n(7103),i=a.default.createContext(r.imageConfigDefault)},7103:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return a}});let n=["default","imgix","cloudinary","akamai","custom"],a={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},61669:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return d},getImageProps:function(){return u}});let a=n(99920),r=n(80497),i=n(38173),o=a._(n(21241));function u(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let d=i.Image},21241:function(e,t){function n(e){let{config:t,src:n,width:a,quality:r}=e;return t.path+"?url="+encodeURIComponent(n)+"&w="+a+"&q="+(r||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),n.__next_img_default=!0;let a=n},65960:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let a=n(2265),r="undefined"==typeof window,i=r?()=>{}:a.useLayoutEffect,o=r?()=>{}:a.useEffect;function u(e){let{headManager:t,reduceComponentsToState:n}=e;function u(){if(t&&t.mountedInstances){let r=a.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(r,e))}}if(r){var d;null==t||null==(d=t.mountedInstances)||d.add(e.children),u()}return i(()=>{var n;return null==t||null==(n=t.mountedInstances)||n.add(e.children),()=>{var n;null==t||null==(n=t.mountedInstances)||n.delete(e.children)}}),i(()=>(t&&(t._pendingUpdate=u),()=>{t&&(t._pendingUpdate=u)})),o(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},61485:function(e,t,n){n.d(t,{VY:function(){return V},fC:function(){return N},h_:function(){return X},xz:function(){return H}});var a=n(2265),r=n(78149),i=n(1584),o=n(98324),u=n(53938),d=n(20589),l=n(80467),s=n(53201),c=n(25510),f=n(7715),h=n(31383),m=n(25171),p=n(71538),y=n(91715),g=n(78369),v=n(49418),b=n(57437),w="Popover",[M,x]=(0,o.b)(w,[c.D7]),k=(0,c.D7)(),[j,P]=M(w),C=e=>{let{__scopePopover:t,children:n,open:r,defaultOpen:i,onOpenChange:o,modal:u=!1}=e,d=k(t),l=a.useRef(null),[f,h]=a.useState(!1),[m,p]=(0,y.T)({prop:r,defaultProp:null!=i&&i,onChange:o,caller:w});return(0,b.jsx)(c.fC,{...d,children:(0,b.jsx)(j,{scope:t,contentId:(0,s.M)(),triggerRef:l,open:m,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:f,onCustomAnchorAdd:a.useCallback(()=>h(!0),[]),onCustomAnchorRemove:a.useCallback(()=>h(!1),[]),modal:u,children:n})})};C.displayName=w;var S="PopoverAnchor";a.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=P(S,n),o=k(n),{onCustomAnchorAdd:u,onCustomAnchorRemove:d}=i;return a.useEffect(()=>(u(),()=>d()),[u,d]),(0,b.jsx)(c.ee,{...o,...r,ref:t})}).displayName=S;var _="PopoverTrigger",D=a.forwardRef((e,t)=>{let{__scopePopover:n,...a}=e,o=P(_,n),u=k(n),d=(0,i.e)(t,o.triggerRef),l=(0,b.jsx)(m.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Y(o.open),...a,ref:d,onClick:(0,r.M)(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?l:(0,b.jsx)(c.ee,{asChild:!0,...u,children:l})});D.displayName=_;var W="PopoverPortal",[z,Z]=M(W,{forceMount:void 0}),O=e=>{let{__scopePopover:t,forceMount:n,children:a,container:r}=e,i=P(W,t);return(0,b.jsx)(z,{scope:t,forceMount:n,children:(0,b.jsx)(h.z,{present:n||i.open,children:(0,b.jsx)(f.h,{asChild:!0,container:r,children:a})})})};O.displayName=W;var A="PopoverContent",E=a.forwardRef((e,t)=>{let n=Z(A,e.__scopePopover),{forceMount:a=n.forceMount,...r}=e,i=P(A,e.__scopePopover);return(0,b.jsx)(h.z,{present:a||i.open,children:i.modal?(0,b.jsx)(T,{...r,ref:t}):(0,b.jsx)(F,{...r,ref:t})})});E.displayName=A;var R=(0,p.Z8)("PopoverContent.RemoveScroll"),T=a.forwardRef((e,t)=>{let n=P(A,e.__scopePopover),o=a.useRef(null),u=(0,i.e)(t,o),d=a.useRef(!1);return a.useEffect(()=>{let e=o.current;if(e)return(0,g.Ry)(e)},[]),(0,b.jsx)(v.Z,{as:R,allowPinchZoom:!0,children:(0,b.jsx)(q,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),d.current||null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,r.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,a=2===t.button||n;d.current=a},{checkForDefaultPrevented:!1}),onFocusOutside:(0,r.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),F=a.forwardRef((e,t)=>{let n=P(A,e.__scopePopover),r=a.useRef(!1),i=a.useRef(!1);return(0,b.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,o;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current||null===(o=n.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var a,o;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let u=t.target;(null===(o=n.triggerRef.current)||void 0===o?void 0:o.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),q=a.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:a,onOpenAutoFocus:r,onCloseAutoFocus:i,disableOutsidePointerEvents:o,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:m,...p}=e,y=P(A,n),g=k(n);return(0,d.EW)(),(0,b.jsx)(l.M,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:r,onUnmountAutoFocus:i,children:(0,b.jsx)(u.XB,{asChild:!0,disableOutsidePointerEvents:o,onInteractOutside:m,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:h,onDismiss:()=>y.onOpenChange(!1),children:(0,b.jsx)(c.VY,{"data-state":Y(y.open),role:"dialog",id:y.contentId,...g,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),I="PopoverClose";function Y(e){return e?"open":"closed"}a.forwardRef((e,t)=>{let{__scopePopover:n,...a}=e,i=P(I,n);return(0,b.jsx)(m.WV.button,{type:"button",...a,ref:t,onClick:(0,r.M)(e.onClick,()=>i.onOpenChange(!1))})}).displayName=I,a.forwardRef((e,t)=>{let{__scopePopover:n,...a}=e,r=k(n);return(0,b.jsx)(c.Eh,{...r,...a,ref:t})}).displayName="PopoverArrow";var N=C,H=D,X=O,V=E},33142:function(e,t,n){n.d(t,{Q:function(){return v}});let a=Symbol.for("constructDateFrom");function r(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&a in e?e[a](t):e instanceof Date?new e.constructor(t):new Date(t)}let i={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};var o=n(7423);let u={date:(0,o.l)({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:(0,o.l)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,o.l)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},d={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};var l=n(91980);let s={ordinalNumber:(e,t)=>{let n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:(0,l.Y)({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:(0,l.Y)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,l.Y)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:(0,l.Y)({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:(0,l.Y)({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};var c=n(36572);let f={code:"en-US",formatDistance:(e,t,n)=>{let a;let r=i[e];return(a="string"==typeof r?r:1===t?r.one:r.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+a:a+" ago":a},formatLong:u,formatRelative:(e,t,n,a)=>d[e],localize:s,match:{ordinalNumber:(0,n(12602).y)({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,c.t)({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:(0,c.t)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,c.t)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,c.t)({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,c.t)({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},h={};function m(e,t){return r(t||e,e)}function p(e){let t=m(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function y(e){for(var t=arguments.length,n=Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];let i=r.bind(null,e||n.find(e=>"object"==typeof e));return n.map(i)}function g(e,t){let n=+m(e)-+m(t);return n<0?-1:n>0?1:n}function v(e,t){return function(e,t,n){var a,r,i,o,u,d;let l;let s=null!==(r=null!==(a=null==n?void 0:n.locale)&&void 0!==a?a:h.locale)&&void 0!==r?r:f,c=g(e,t);if(isNaN(c))throw RangeError("Invalid time value");let v=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:c}),[b,w]=y(null==n?void 0:n.in,...c>0?[t,e]:[e,t]),M=(i=w,o=b,(d=null==void 0?void 0:(void 0).roundingMethod,e=>{let t=(d?Math[d]:Math.trunc)(e);return 0===t?0:t})((+m(i)-+m(o))/1e3)),x=Math.round((M-(p(w)-p(b))/1e3)/60);if(x<2){if(null==n?void 0:n.includeSeconds){if(M<5)return s.formatDistance("lessThanXSeconds",5,v);if(M<10)return s.formatDistance("lessThanXSeconds",10,v);if(M<20)return s.formatDistance("lessThanXSeconds",20,v);if(M<40)return s.formatDistance("halfAMinute",0,v);else if(M<60)return s.formatDistance("lessThanXMinutes",1,v);else return s.formatDistance("xMinutes",1,v)}return 0===x?s.formatDistance("lessThanXMinutes",1,v):s.formatDistance("xMinutes",x,v)}if(x<45)return s.formatDistance("xMinutes",x,v);if(x<90)return s.formatDistance("aboutXHours",1,v);if(x<1440)return s.formatDistance("aboutXHours",Math.round(x/60),v);if(x<2520)return s.formatDistance("xDays",1,v);if(x<43200)return s.formatDistance("xDays",Math.round(x/1440),v);if(x<86400)return l=Math.round(x/43200),s.formatDistance("aboutXMonths",l,v);if((l=function(e,t,n){let[a,r,i]=y(void 0,e,e,t),o=g(r,i),u=Math.abs(function(e,t,n){let[a,r]=y(void 0,e,t);return 12*(a.getFullYear()-r.getFullYear())+(a.getMonth()-r.getMonth())}(r,i));if(u<1)return 0;1===r.getMonth()&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-o*u);let d=g(r,i)===-o;(function(e,t){let n=m(e,void 0);return+function(e,t){let n=m(e,null==t?void 0:t.in);return n.setHours(23,59,59,999),n}(n,void 0)==+function(e,t){let n=m(e,null==t?void 0:t.in),a=n.getMonth();return n.setFullYear(n.getFullYear(),a+1,0),n.setHours(23,59,59,999),n}(n,void 0)})(a)&&1===u&&1===g(a,i)&&(d=!1);let l=o*(u-+d);return 0===l?0:l}(w,b))<12)return s.formatDistance("xMonths",Math.round(x/43200),v);{let e=l%12,t=Math.trunc(l/12);return e<3?s.formatDistance("aboutXYears",t,v):e<9?s.formatDistance("overXYears",t,v):s.formatDistance("almostXYears",t+1,v)}}(e,r(e,Date.now()),t)}},7423:function(e,t,n){n.d(t,{l:function(){return a}});function a(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}},91980:function(e,t,n){n.d(t,{Y:function(){return a}});function a(e){return(t,n)=>{let a;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):t;a=e.formattingValues[r]||e.formattingValues[t]}else{let t=e.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;a=e.values[r]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}},36572:function(e,t,n){function a(e){return function(t){let n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.width,i=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;let u=o[0],d=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(d)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(d,e=>e.test(u)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(d,e=>e.test(u));return n=e.valueCallback?e.valueCallback(l):l,{value:n=a.valueCallback?a.valueCallback(n):n,rest:t.slice(u.length)}}}n.d(t,{t:function(){return a}})},12602:function(e,t,n){n.d(t,{y:function(){return a}});function a(e){return function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.match(e.matchPattern);if(!a)return null;let r=a[0],i=t.match(e.parsePattern);if(!i)return null;let o=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:t.slice(r.length)}}}},47201:function(e,t,n){n.d(t,{F:function(){return s}});let a={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 m\xeas",other:"cerca de {{count}} meses"},xMonths:{one:"1 m\xeas",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}};var r=n(7423);let i={date:(0,r.l)({formats:{full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:(0,r.l)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,r.l)({formats:{full:"{{date}} '\xe0s' {{time}}",long:"{{date}} '\xe0s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:e=>{let t=e.getDay();return"'"+(0===t||6===t?"\xfaltimo":"\xfaltima")+"' eeee '\xe0s' p"},yesterday:"'ontem \xe0s' p",today:"'hoje \xe0s' p",tomorrow:"'amanh\xe3 \xe0s' p",nextWeek:"eeee '\xe0s' p",other:"P"};var u=n(91980);let d={ordinalNumber:(e,t)=>{let n=Number(e);return(null==t?void 0:t.unit)==="week"?n+"\xaa":n+"\xba"},era:(0,u.Y)({values:{narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},defaultWidth:"wide"}),quarter:(0,u.Y)({values:{narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xba trimestre","2\xba trimestre","3\xba trimestre","4\xba trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,u.Y)({values:{narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","mar\xe7o","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},defaultWidth:"wide"}),day:(0,u.Y)({values:{narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","ter\xe7a","quarta","quinta","sexta","s\xe1bado"],wide:["domingo","segunda-feira","ter\xe7a-feira","quarta-feira","quinta-feira","sexta-feira","s\xe1bado"]},defaultWidth:"wide"}),dayPeriod:(0,u.Y)({values:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},defaultFormattingWidth:"wide"})};var l=n(36572);let s={code:"pt-BR",formatDistance:(e,t,n)=>{let r;let i=a[e];return(r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",String(t)),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"em "+r:"h\xe1 "+r:r},formatLong:i,formatRelative:(e,t,n,a)=>{let r=o[e];return"function"==typeof r?r(t):r},localize:d,match:{ordinalNumber:(0,n(12602).y)({matchPattern:/^(\d+)[ºªo]?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,l.t)({matchPatterns:{narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},defaultParseWidth:"any"}),quarter:(0,l.t)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,l.t)({matchPatterns:{narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},defaultParseWidth:"any"}),day:(0,l.t)({matchPatterns:{narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},defaultMatchWidth:"wide",parsePatterns:{short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},defaultParseWidth:"any"}),dayPeriod:(0,l.t)({matchPatterns:{narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}}}}]);