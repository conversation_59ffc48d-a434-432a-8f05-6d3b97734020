"use strict";exports.id=95,exports.ids=[95,4349],exports.modules={33261:(e,a,r)=>{r.d(a,{Cd:()=>c,X:()=>d,bZ:()=>l});var t=r(10326),s=r(79360),o=r(17577),n=r(51223);let i=(0,s.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-500/50 text-yellow-600 dark:border-yellow-500/30 dark:text-yellow-500 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-500"}},defaultVariants:{variant:"default"}}),l=o.forwardRef(({className:e,variant:a,...r},s)=>t.jsx("div",{ref:s,role:"alert",className:(0,n.cn)(i({variant:a}),e),...r}));l.displayName="Alert";let c=o.forwardRef(({className:e,...a},r)=>t.jsx("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...a}));c.displayName="AlertTitle";let d=o.forwardRef(({className:e,...a},r)=>t.jsx("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...a}));d.displayName="AlertDescription"},24118:(e,a,r)=>{r.d(a,{$N:()=>x,Be:()=>f,Vq:()=>l,cN:()=>h,cZ:()=>m,fK:()=>p,hg:()=>c});var t=r(10326),s=r(98958),o=r(94019),n=r(17577),i=r(51223);let l=s.fC,c=s.xz,d=s.h_;s.x8;let u=n.forwardRef(({className:e,...a},r)=>t.jsx(s.aV,{ref:r,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));u.displayName=s.aV.displayName;let m=n.forwardRef(({className:e,children:a,...r},n)=>(0,t.jsxs)(d,{children:[t.jsx(u,{}),(0,t.jsxs)(s.VY,{ref:n,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[a,(0,t.jsxs)(s.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(o.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=s.VY.displayName;let p=({className:e,...a})=>t.jsx("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});p.displayName="DialogHeader";let h=({className:e,...a})=>t.jsx("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});h.displayName="DialogFooter";let x=n.forwardRef(({className:e,...a},r)=>t.jsx(s.Dx,{ref:r,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));x.displayName=s.Dx.displayName;let f=n.forwardRef(({className:e,...a},r)=>t.jsx(s.dk,{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));f.displayName=s.dk.displayName},33269:(e,a,r)=>{r.d(a,{E:()=>i});var t=r(10326),s=r(87273),o=r(17577),n=r(51223);let i=o.forwardRef(({className:e,value:a,...r},o)=>t.jsx(s.fC,{ref:o,className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:t.jsx(s.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})}));i.displayName=s.fC.displayName},3236:(e,a,r)=>{r.d(a,{x:()=>i});var t=r(10326),s=r(16999),o=r(17577),n=r(51223);let i=o.forwardRef(({className:e,children:a,...r},o)=>(0,t.jsxs)(s.fC,{ref:o,className:(0,n.cn)("relative overflow-hidden",e),...r,children:[t.jsx(s.l_,{className:"h-full w-full rounded-[inherit]",children:a}),t.jsx(l,{}),t.jsx(s.Ns,{})]}));i.displayName=s.fC.displayName;let l=o.forwardRef(({className:e,orientation:a="vertical",...r},o)=>t.jsx(s.gb,{ref:o,orientation:a,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 border-t border-t-transparent p-[1px]",e),...r,children:t.jsx(s.q4,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=s.gb.displayName},82631:(e,a,r)=>{r.d(a,{_v:()=>d,aJ:()=>c,pn:()=>i,u:()=>l});var t=r(10326),s=r(39313),o=r(17577),n=r(51223);let i=s.zt,l=s.fC,c=s.xz,d=o.forwardRef(({className:e,sideOffset:a=4,...r},o)=>t.jsx(s.VY,{ref:o,sideOffset:a,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));d.displayName=s.VY.displayName},95:(e,a,r)=>{r.d(a,{SpreadsheetEditor:()=>a6});var t,s=r(10326),o=r(25596),n=r(99102),i=r(24230),l=r(95396),c=r(1572),d=r(94019),u=r(23133),m=r(99316),p=r(75290),h=r(31215),x=r(11890),f=r(39183),g=r(75126),y=r(227),v=r(40617),w=r(87888),b=r(35047),N=r(17577),A=r(85999),E=r(88307),j=r(70717),C=r(69436),S=r(91664),k=r(51027),O=r(82631),R=r(50337),T=r(51223),F=r(72257),I=r(41137),D=r(59368),_=r(4198),L=r(73078),$=r(38443),M=r(3236);let z=[{id:"soma",command:"/soma",description:"Soma valores em um intervalo de c\xe9lulas",example:"/soma A1:A10",category:"calculation",icon:s.jsx(F.Z,{className:"h-4 w-4 text-blue-500"})},{id:"media",command:"/media",description:"Calcula a m\xe9dia de um intervalo de c\xe9lulas",example:"/media B1:B10",category:"calculation",icon:s.jsx(F.Z,{className:"h-4 w-4 text-blue-500"})},{id:"maximo",command:"/maximo",description:"Encontra o valor m\xe1ximo em um intervalo",example:"/maximo C1:C20",category:"calculation",icon:s.jsx(F.Z,{className:"h-4 w-4 text-blue-500"})},{id:"grafico",command:"/grafico",description:"Cria um gr\xe1fico com os dados selecionados",example:'/grafico tipo="barras" dados=A1:B10',category:"visualization",icon:s.jsx(l.Z,{className:"h-4 w-4 text-green-500"})},{id:"pizza",command:"/grafico-pizza",description:"Cria um gr\xe1fico de pizza",example:'/grafico-pizza dados=C1:D10 titulo="Vendas por Regi\xe3o"',category:"visualization",icon:s.jsx(l.Z,{className:"h-4 w-4 text-green-500"})},{id:"formatar",command:"/formatar",description:"Formata c\xe9lulas selecionadas",example:'/formatar A1:C10 negrito cor="azul"',category:"formatting",icon:s.jsx(c.Z,{className:"h-4 w-4 text-purple-500"})},{id:"condicional",command:"/formato-condicional",description:"Aplica formata\xe7\xe3o condicional",example:'/formato-condicional A1:A10 maior=100 cor="verde"',category:"formatting",icon:s.jsx(c.Z,{className:"h-4 w-4 text-purple-500"})},{id:"filtrar",command:"/filtrar",description:"Filtra dados com base em crit\xe9rios",example:'/filtrar coluna="Vendas" valor>1000',category:"filter",icon:s.jsx(I.Z,{className:"h-4 w-4 text-amber-500"})},{id:"ordenar",command:"/ordenar",description:"Ordena dados de uma coluna",example:'/ordenar coluna="Data" crescente=true',category:"filter",icon:s.jsx(D.Z,{className:"h-4 w-4 text-amber-500"})},{id:"tabela",command:"/tabela",description:"Converte intervalo em tabela formatada",example:'/tabela A1:D10 nome="MinhaTabela"',category:"data",icon:s.jsx(_.Z,{className:"h-4 w-4 text-red-500"})},{id:"inserir",command:"/inserir",description:"Insere novas linhas ou colunas",example:"/inserir linhas=5 posicao=A10",category:"data",icon:s.jsx(n.Z,{className:"h-4 w-4 text-red-500"})}];function P({onSelect:e,onClose:a}){let[r,t]=(0,N.useState)(""),[o,n]=(0,N.useState)(z),[i,u]=(0,N.useState)(0),[m,p]=(0,N.useState)("all"),h=(0,N.useRef)(null),x=(0,N.useRef)(null),f=r=>{"string"==typeof r&&(e(r),a())},g=[{id:"all",label:"Todos",icon:s.jsx(E.Z,{className:"h-4 w-4"})},{id:"calculation",label:"C\xe1lculos",icon:s.jsx(F.Z,{className:"h-4 w-4"})},{id:"visualization",label:"Gr\xe1ficos",icon:s.jsx(l.Z,{className:"h-4 w-4"})},{id:"formatting",label:"Formata\xe7\xe3o",icon:s.jsx(c.Z,{className:"h-4 w-4"})},{id:"filter",label:"Filtros",icon:s.jsx(I.Z,{className:"h-4 w-4"})},{id:"data",label:"Dados",icon:s.jsx(_.Z,{className:"h-4 w-4"})}];return(0,s.jsxs)("div",{ref:x,className:"absolute bottom-full left-0 w-full max-w-md bg-background border border-input rounded-md shadow-md z-50 mb-2 overflow-hidden",role:"dialog","aria-label":"Paleta de comandos",children:[(0,s.jsxs)("div",{className:"flex items-center p-2 border-b",children:[s.jsx(L.Z,{className:"h-4 w-4 text-muted-foreground mr-2"}),s.jsx("input",{ref:h,type:"text",value:r,onChange:e=>t(e.target.value),onKeyDown:e=>{o&&0!==o.length&&("ArrowDown"===e.key?(e.preventDefault(),u(e=>(e+1)%o.length)):"ArrowUp"===e.key?(e.preventDefault(),u(e=>(e-1+o.length)%o.length)):"Enter"===e.key?(e.preventDefault(),i>=0&&i<o.length&&o[i]&&"string"==typeof o[i].command&&f(o[i].command)):"Escape"===e.key&&(e.preventDefault(),a()))},placeholder:"Pesquisar comandos...",className:"flex-1 bg-transparent outline-none text-sm","aria-label":"Pesquisar comandos"}),s.jsx("button",{onClick:()=>a(),className:"h-6 w-6 flex items-center justify-center rounded-sm hover:bg-muted","aria-label":"Fechar paleta de comandos",children:s.jsx(d.Z,{className:"h-4 w-4 text-muted-foreground"})})]}),s.jsx("div",{className:"flex items-center gap-1 p-2 overflow-x-auto border-b scrollbar-hide",children:g.map(e=>(0,s.jsxs)($.C,{variant:m===e.id?"default":"outline",className:"cursor-pointer px-2 py-1 flex items-center gap-1",onClick:()=>p(e.id),children:[e.icon,s.jsx("span",{children:e.label})]},e.id))}),s.jsx(M.x,{className:"max-h-[300px]",children:s.jsx("div",{className:"py-1",role:"listbox",children:o.length>0?o.map((e,a)=>s.jsx("div",{className:`px-3 py-2 text-sm cursor-pointer transition-colors ${a===i?"bg-muted":"hover:bg-muted/50"}`,onClick:()=>f(e.command),onMouseEnter:()=>u(a),role:"option","aria-selected":a===i,tabIndex:-1,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"font-medium",children:e.command}),s.jsx("kbd",{className:"text-xs bg-muted px-1.5 py-0.5 rounded text-muted-foreground",children:"Enter ↵"})]}),s.jsx("p",{className:"text-xs text-muted-foreground",children:e.description}),s.jsx("p",{className:"text-xs italic mt-0.5 text-muted-foreground",children:e.example})]})]})},e.id)):s.jsx("div",{className:"px-3 py-4 text-sm text-center text-muted-foreground",children:"Nenhum comando encontrado"})})})]})}function B({onSendMessage:e,isLoading:a=!1,placeholder:r="Digite um comando...",disabled:t=!1,showExamples:o=!0,autoFocus:n=!0,className:l="",onChange:d}){let[u,m]=(0,N.useState)(""),[h,x]=(0,N.useState)(!1),[f,g]=(0,N.useState)(!1),[y,v]=(0,N.useState)(!1),w=(0,N.useRef)(null),[b,F]=(0,N.useState)(()=>[]),[I,D]=(0,N.useState)(-1),_=async r=>{r&&r.preventDefault();let s=u.trim();if(s&&!a&&!t){F(e=>{if(!Array.isArray(e))return[s];let a=e.filter(e=>e!==s);return[s,...a].slice(0,10)}),m(""),d&&"function"==typeof d&&d(""),h&&x(!1);try{await e(s)}catch(e){console.error("Erro ao enviar mensagem:",e),A.toast.error("Erro ao enviar comando",{description:"N\xe3o foi poss\xedvel processar seu comando. Tente novamente."})}w.current&&w.current.focus(),D(-1)}},L=e=>{m(e),v(!1),w.current&&w.current.focus()},$=[{text:"Somar valores da coluna B",icon:s.jsx(i.Z,{className:"h-3 w-3"}),category:"calc"},{text:"Criar gr\xe1fico de vendas por regi\xe3o",icon:s.jsx(c.Z,{className:"h-3 w-3"}),category:"visual"},{text:"Filtrar valores maiores que 100",icon:s.jsx(E.Z,{className:"h-3 w-3"}),category:"filter"},{text:"Formatar c\xe9lulas como moeda",icon:s.jsx(c.Z,{className:"h-3 w-3"}),category:"format"},{text:"Ordenar coluna A em ordem alfab\xe9tica",icon:s.jsx(i.Z,{className:"h-3 w-3"}),category:"order"}];return(0,s.jsxs)("div",{className:`relative w-full ${l}`,children:[h&&s.jsx(P,{onSelect:e=>{m(e),x(!1),w.current&&w.current.focus()},onClose:()=>x(!1)}),(0,s.jsxs)("form",{onSubmit:_,className:"flex items-center gap-2 w-full",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[s.jsx("input",{ref:w,className:(0,T.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","pr-10",R.z6.radius.md,f?"border-primary":void 0),placeholder:a?"Processando comando...":r,value:u,onChange:e=>{m(e.target.value),d&&d(e.target.value)},onKeyDown:e=>{if("Enter"===e.key&&!e.shiftKey){e.preventDefault(),_();return}if("ArrowUp"===e.key&&!h){if((""===u||0===e.currentTarget.selectionStart)&&Array.isArray(b)&&b.length>0){e.preventDefault();let a=I<b.length-1?I+1:b.length-1;a>=0&&a<b.length&&(D(a),b[a]&&m(b[a]))}return}if("ArrowDown"===e.key&&!h&&Array.isArray(b)){if(e.preventDefault(),I>0){let e=I-1;D(e),e>=0&&e<b.length&&b[e]&&m(b[e])}else 0===I&&(D(-1),m(""));return}if("/"===e.key&&""===u){e.preventDefault(),x(!0);return}if("Escape"===e.key&&h){e.preventDefault(),x(!1);return}},disabled:a||t,autoFocus:n,"aria-label":"Digite seu comando para a planilha"}),a&&s.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-primary",children:s.jsx(p.Z,{className:"h-4 w-4 animate-spin"})})]}),s.jsx(O.pn,{children:(0,s.jsxs)(O.u,{children:[s.jsx(O.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsxs)(k.J2,{open:y,onOpenChange:v,children:[s.jsx(k.xo,{asChild:!0,children:s.jsx(S.Button,{type:"button",size:"icon",variant:"outline",disabled:0===b.length,className:"shrink-0",children:s.jsx(j.Z,{className:"h-4 w-4"})})}),(0,s.jsxs)(k.yk,{className:"w-72 p-0",align:"end",children:[s.jsx("div",{className:"text-sm font-medium p-3 border-b",children:"Comandos recentes"}),s.jsx("div",{className:"max-h-[200px] overflow-y-auto",children:b.map((e,a)=>s.jsx("div",{onClick:()=>L(e),className:"p-2 hover:bg-muted cursor-pointer text-sm truncate px-3",children:e},a))})]})]}),s.jsx(S.Button,{type:"submit",size:"icon",variant:u.trim()?"default":"secondary",disabled:!u.trim()||a||t,"aria-label":"Enviar comando",className:"transition-all duration-300 shrink-0",children:a?s.jsx(p.Z,{className:"h-4 w-4 animate-spin"}):s.jsx(C.Z,{className:"h-4 w-4"})})]})}),s.jsx(O._v,{children:s.jsx("p",{children:"Enviar comando (Enter)"})})]})})]}),o&&!u&&!h&&0===b.length&&s.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:$.map((e,a)=>(0,s.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-7 text-xs",onClick:()=>m(e.text),children:[e.icon,s.jsx("span",{className:"ml-1",children:e.text})]},a))})]})}var V=r(59593),Z=r(687),U=r(29752);function q({commandId:e,command:a,onDismiss:r,onFeedbackSubmit:t}){let[o,n]=(0,N.useState)(null),[i,l]=(0,N.useState)(""),[c,u]=(0,N.useState)(!1),[m,p]=(0,N.useState)(!1),h=async e=>{n(e),u(!0),e&&!c&&await x(e,"")},x=async(s,o)=>{try{p(!0),await t({commandId:e,command:a,successful:s,feedbackText:o}),A.toast.success("Feedback enviado",{description:"Obrigado por ajudar a melhorar nosso sistema!"}),r()}catch(e){console.error("Erro ao enviar feedback:",e),A.toast.error("N\xe3o foi poss\xedvel enviar o feedback")}finally{p(!1)}},f=async()=>{null!==o&&await x(o,i)};return s.jsx(U.Zb,{className:"p-3 mb-3 border border-gray-200 dark:border-gray-800",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm font-medium text-slate-600 dark:text-slate-300",children:"O comando funcionou como esperado?"}),s.jsx(S.Button,{variant:"ghost",size:"sm",onClick:r,className:"h-6 w-6 p-0 rounded-full",children:s.jsx(d.Z,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(S.Button,{variant:!0===o?"default":"outline",size:"sm",onClick:()=>h(!0),className:!0===o?"bg-green-600 hover:bg-green-700":"",disabled:m,children:[s.jsx(V.Z,{className:"h-4 w-4 mr-1"}),"Sim"]}),(0,s.jsxs)(S.Button,{variant:!1===o?"default":"outline",size:"sm",onClick:()=>h(!1),className:!1===o?"bg-red-600 hover:bg-red-700":"",disabled:m,children:[s.jsx(Z.Z,{className:"h-4 w-4 mr-1"}),"N\xe3o"]})]}),c&&(0,s.jsxs)("div",{className:"mt-2 space-y-2",children:[s.jsx("textarea",{className:(0,T.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","resize-vertical"),value:i,onChange:e=>l(e.target.value),placeholder:"Descreva o que voc\xea gostaria que o comando fizesse...",id:"feedback-text"}),s.jsx("div",{className:"flex justify-end",children:(0,s.jsxs)(S.Button,{variant:"default",size:"sm",onClick:f,disabled:m,className:"flex items-center",children:[m?"Enviando...":"Enviar",s.jsx(C.Z,{className:"h-3 w-3 ml-1"})]})})]})]})})}var H=r(91470),G=r(94893);function X({command:e,interpretation:a,isLoading:r,onExecute:t,onCancel:o}){let[n,i]=(0,N.useState)(!1);return n?s.jsx(U.Zb,{className:"p-4 mb-3 border border-blue-200 dark:border-blue-900 bg-blue-50 dark:bg-blue-950/30",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[s.jsx("div",{className:"text-sm font-medium",children:s.jsx("span",{className:"text-blue-600 dark:text-blue-400",children:"Interpreta\xe7\xe3o do comando:"})}),s.jsx("p",{className:"text-sm text-slate-700 dark:text-slate-300",children:a}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 mt-2",children:[(0,s.jsxs)(S.Button,{variant:"outline",size:"sm",onClick:o,className:"flex items-center",disabled:r,children:[s.jsx(H.Z,{className:"h-4 w-4 mr-1"}),"Cancelar"]}),(0,s.jsxs)(S.Button,{variant:"default",size:"sm",onClick:t,className:"flex items-center bg-green-600 hover:bg-green-700",disabled:r,children:[r?s.jsx(w.Z,{className:"h-4 w-4 mr-1 animate-pulse"}):s.jsx(G.Z,{className:"h-4 w-4 mr-1"}),r?"Executando...":"Executar"]})]})]})}):null}var J=r(31540),Q=r(88378),Y=r(48705),W=r(10143),K=r(23532),ee=r(35342);function ea(e,a,r=""){if(!e||!Array.isArray(e)||a<0||a>=e.length)return r;let t=e[a];return void 0!==t?t:r}var er=r(96671);function et(e){return{...e,id:e.id||`op_${Date.now()}_${Math.random().toString(36).substring(2,9)}`}}async function es(e,a){try{if("ADVANCED_VISUALIZATION"!==a.type||!a.data)throw Error("Opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada inv\xe1lida");let r=a.data,t=r.sourceRange,s=await eo(e,t),o=r.destinationRange||function(e){let a=Object.keys(e._visualizations||{}).length,r=String.fromCharCode(65+a%3*8);return`${r}${15*Math.floor(a/3)+1}`}(e),n=r.id||"viz_"+Math.random().toString(36).substring(2,9);return e._visualizations||(e._visualizations={}),e._visualizations[n]={type:r.type,title:r.title,data:s,config:r,position:o},{updatedData:e,resultSummary:`Visualiza\xe7\xe3o avan\xe7ada "${r.title||r.type}" criada com sucesso em ${o}`}}catch(a){return console.error("Erro ao executar opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada:",a),{updatedData:e,resultSummary:`Erro ao criar visualiza\xe7\xe3o avan\xe7ada: ${a.message}`}}}async function eo(e,a){try{if(Array.isArray(e)&&e.length>0)return e;if("object"==typeof e&&!Array.isArray(e)){let r=[],t=a.split(":"),s=t[0],o=t.length>1?t[1]:s;if(!s)return[];let n=s.match(/[A-Z]+/),i=s.match(/\d+/),l=o?o.match(/[A-Z]+/):null,c=o?o.match(/\d+/):null,d=n?n[0]:"A",u=i?parseInt(i[0],10):1,m=l&&l[0]?l[0]:d,p=c&&c[0]?parseInt(c[0],10):u;if(u<=0||p<=0)return[];let h=(0,T.WH)(d),x=(0,T.WH)(m),f=[];for(let a=h;a<=x;a++){let r=String.fromCharCode(65+a),t=`${r}${u}`;f.push(e[t]?String(e[t]):`Column${a+1}`)}for(let a=u+1;a<=p;a++){let t={};for(let r=h;r<=x;r++){let s=String.fromCharCode(65+r),o=`${s}${a}`,n=r-h,i=n>=0&&n<f.length?f[n]:`Column${r+1}`;void 0!==e[o]&&i&&(t[i]=e[o])}r.push(t)}return r}return[]}catch(e){return console.error("Erro ao extrair dados do intervalo:",e),[]}}ee.ox.COLUMN_OPERATION,ee.ox.CELL_UPDATE,ee.ox.ROW_OPERATION,ee.ox.DATA_TRANSFORMATION,r(51288);var en=r(51641);function ei(e,a){if(e&&Array.isArray(e)&&!(a<0)&&!(a>=e.length))return e[a]}class el extends Error{constructor(e,a={}){super(e),this.name=this.constructor.name,this.context=a.context||{},this.timestamp=Date.now(),a.originalError&&(this.originalError=a.originalError instanceof Error?a.originalError:Error(String(a.originalError))),Object.setPrototypeOf(this,el.prototype)}toJSON(){return{message:this.message,name:this.name,stack:this.stack,context:this.context,timestamp:this.timestamp,originalError:this.originalError?{message:this.originalError.message,name:this.originalError.name,stack:this.originalError.stack}:void 0}}}var ec=r(96833);let ed=r(83567).s.fromEnv();async function eu(e,a=30,r=60){let t=`ratelimit:${e}`,s=Date.now(),o=1e3*r;try{let e=await ed.pipeline().zremrangebyscore(t,0,s-o).zadd(t,{score:s,member:s.toString()}).zcount(t,s-o,"+inf").pexpire(t,o).exec(),r=e?.[2]||0,n=await ed.zrange(t,0,0,{withScores:!0}),i=n.length>0?n[0].score+o:s+o;return{success:r<=a,limit:a,remaining:Math.max(0,a-r),reset:i}}catch(e){return console.error("[RATE_LIMIT_ERROR]",e),{success:!1,limit:a,remaining:0,reset:s+o,error:"Erro ao verificar limites de taxa. Limite tempor\xe1rio aplicado por seguran\xe7a."}}}let em=new Map,ep=process.env.REDIS_URL||process.env.UPSTASH_REDIS_REST_URL?eu:function(e,a=30,r=60){let t=Date.now(),s=1e3*r;em.has(e)||em.set(e,{timestamps:[],reset:t+s});let o=em.get(e);o.timestamps=o.timestamps.filter(e=>e>t-s),o.timestamps.push(t),1===o.timestamps.length&&(o.reset=t+s);let n=o.timestamps.length<=a,i=Math.max(0,a-o.timestamps.length);if(em.size>1e4)for(let e of[...em.entries()].filter(([e,a])=>a.reset<t).map(([e])=>e))em.delete(e);return{success:n,limit:a,remaining:i,reset:o.reset}};var eh=r(64349);let ex={MAX_WORKBOOKS:{[ec.Xf.FREE]:5,[ec.Xf.PRO_MONTHLY]:1/0,[ec.Xf.PRO_ANNUAL]:1/0},MAX_CELLS:{[ec.Xf.FREE]:1e3,[ec.Xf.PRO_MONTHLY]:5e4,[ec.Xf.PRO_ANNUAL]:1/0},MAX_CHARTS:{[ec.Xf.FREE]:1,[ec.Xf.PRO_MONTHLY]:1/0,[ec.Xf.PRO_ANNUAL]:1/0},ADVANCED_AI_COMMANDS:{[ec.Xf.FREE]:!1,[ec.Xf.PRO_MONTHLY]:!0,[ec.Xf.PRO_ANNUAL]:!0},RATE_LIMITS:{[ec.Xf.FREE]:30,[ec.Xf.PRO_MONTHLY]:120,[ec.Xf.PRO_ANNUAL]:240}},ef=new Map;async function eg(e){try{let a=ef.get(e),r=Date.now();if(a&&r-a.timestamp<18e5)return a.plan;let t=await eh.prisma.subscription.findFirst({where:{userId:e,OR:[{status:"active"},{status:"trialing"}],AND:[{OR:[{currentPeriodEnd:{gt:new Date}},{currentPeriodEnd:null}]}]},orderBy:{createdAt:"desc"}});await ey(e);let s=t?.plan||ec.Xf.FREE;return ef.set(e,{plan:s,timestamp:r}),s}catch(a){throw en.logger.error("[GET_USER_PLAN_ERROR]",a),await ev(e,"plan_verification_failure",a),Error("N\xe3o foi poss\xedvel verificar seu plano de assinatura. Tente novamente mais tarde.")}}async function ey(e){try{let a=await eh.prisma.user.findUnique({where:{id:e}});if(!a)return;if("lastIpAddress"in a&&a.lastIpAddress){let r=await eh.prisma.user.findMany({where:{lastIpAddress:a.lastIpAddress,id:{not:e}}}),t=new Date;t.setDate(t.getDate()-30);let s=r.filter(e=>"createdAt"in e&&e.createdAt&&e.createdAt>t).length;if(s>3){let t=r.map(e=>"email"in e&&e.email&&e.email.split("@")[1]||""),o=new Set(t),n=o.size>1&&o.size<=3;await ev(e,"multiple_accounts_same_ip",{ipAddress:a.lastIpAddress,accountCount:r.length,recentAccountsCount:s,suspiciousDomains:n,creationDates:r.map(e=>"createdAt"in e?e.createdAt:null).filter(Boolean),severity:s>5?"high":"medium"});let i={};if(i.isSuspicious=!0,await eh.prisma.user.update({where:{id:e},data:i}),s>5){let a={isBanned:!0,banReason:"M\xfaltiplas contas com mesmo IP detectadas (poss\xedvel abuso)",banDate:new Date};await eh.prisma.user.update({where:{id:e},data:a}),await eh.prisma.session.deleteMany({where:{userId:e}})}}}if("createdAt"in a&&a.createdAt){let r=a.createdAt.getTime(),t=(Date.now()-r)/36e5;if(t<24){let r=await eh.prisma.userActionLog.count({where:{userId:e,action:{in:["attempt_create_workbook","attempt_add_cells","attempt_advanced_ai"]},timestamp:{gte:new Date(Date.now()-72e5)}}});r>50&&(await ev(e,"high_activity_new_account",{accountAgeHours:t,recentActivityCount:r,ipAddress:a.lastIpAddress,severity:"high"}),await eh.prisma.user.update({where:{id:e},data:{isSuspicious:!0}}))}}}catch(a){en.logger.error("[ABUSE_DETECTION_ERROR]",{userId:e,error:a})}}async function ev(e,a,r){try{await eh.prisma.securityLog.create({data:{userId:e,eventType:a,details:JSON.stringify(r),timestamp:new Date}})}catch(t){en.logger.error("[SECURITY_LOG_ERROR]",{userId:e,eventType:a,details:r,error:t})}}async function ew(e,a,r){try{let a=await eg(e),t=ex.MAX_CHARTS[ec.Xf.FREE]??1,s=ex.MAX_CHARTS[a]??t,o=r<s,n=await eb(e,a,"add_chart");if(!n.allowed)return{allowed:!1,message:`Limite de taxa excedido. Tente novamente em ${n.timeRemaining??60} segundos.`,limit:s===1/0?-1:s};return{allowed:o,message:o?void 0:`Voc\xea atingiu o limite de ${s} ${1===s?"gr\xe1fico":"gr\xe1ficos"} para este plano. Fa\xe7a upgrade para adicionar mais.`,limit:s===1/0?-1:s}}catch(r){throw en.logger.error("[CAN_ADD_CHART_ERROR]",{userId:e,sheetId:a,error:r}),Error("N\xe3o foi poss\xedvel verificar seus limites de uso. Tente novamente mais tarde.")}}async function eb(e,a,r){try{let t=ex.RATE_LIMITS[ec.Xf.FREE]||10,s=ex.RATE_LIMITS[a]||t,{success:o,limit:n,remaining:i,reset:l,error:c}=await ep(e,s);if(!o){if(c)return await ev(e,"rate_limit_error",{action:r,error:c}),{allowed:!1,message:c};let a=Math.ceil((l-Date.now())/1e3);return{allowed:!1,message:`Voc\xea excedeu o limite de a\xe7\xf5es por minuto. Tente novamente em ${a} segundos.`,timeRemaining:a}}return i<=Math.floor(.2*s)&&i>0&&await eN(e,"approaching_rate_limit",{action:r,remaining:i,limit:s,percentRemaining:Math.round(i/s*100)}),{allowed:!0}}catch(t){return en.logger.error("[RATE_LIMIT_CHECK_ERROR]",{userId:e,userPlan:a,action:r,error:t}),await ev(e,"rate_limit_verification_error",{action:r,error:String(t)}),{allowed:!1,message:"N\xe3o foi poss\xedvel verificar limites de uso. Tente novamente em alguns instantes."}}}async function eN(e,a,r){try{await eh.prisma.userActionLog.create({data:{userId:e,action:a,details:JSON.stringify(r),timestamp:new Date}})}catch(t){en.logger.error("[USER_ACTION_LOG_ERROR]",{userId:e,action:a,details:r,error:t})}}async function eA(e,a,r,t){try{let s=Array.isArray(e.charts)?e.charts:[];if(r&&t){let e=await ew(r,t,s.length);if(!e.allowed)throw Error(e.message||`Limite de gr\xe1ficos excedido para seu plano.`)}let o={...e};o.charts||(o.charts=[]);let n={id:`chart_${Date.now()}`,type:a.chartType||"column",dataRange:a.dataRange,position:a.position||"auto",title:a.title||`Gr\xe1fico de ${a.chartType||"coluna"}`,config:a.config||{}};return o.charts.push(n),{updatedData:o,resultSummary:`Gr\xe1fico de ${a.chartType} criado com dados de ${a.dataRange}`}}catch(e){throw en.logger.error("[CHART_OPERATION_ERROR]",{operation:a,error:e}),e instanceof Error?e:Error("Erro ao executar opera\xe7\xe3o de gr\xe1fico")}}async function eE(e,a){try{let{columnName:r,column:t,columnIndex:s,operation:o,targetCell:n}=a.data,i={...e};if(i.rows&&i.headers){let e=-1;if(void 0!==s)e=s;else if(t&&/^[A-Z]+$/.test(t)){e=t.charCodeAt(0)-65;for(let a=1;a<t.length;a++)e=26*e+(t.charCodeAt(a)-65+1)}else if(r||t){let a=r||t||"";e=i.headers.findIndex(e=>e.toLowerCase()===a.toLowerCase())}if(-1===e||e>=i.headers.length){let e=r||t||s;throw Error(`Coluna '${e}' n\xe3o encontrada`)}let a=i.rows.map(a=>{let r=a[e];return"number"==typeof r?r:"object"==typeof r&&r?.result?Number(r.result):Number(r)}).filter(e=>!isNaN(e)),l=0;switch(o){case"SUM":l=a.reduce((e,a)=>e+a,0);break;case"AVERAGE":l=a.length>0?a.reduce((e,a)=>e+a,0)/a.length:0;break;case"MAX":l=Math.max(...a.length>0?a:[0]);break;case"MIN":l=Math.min(...a.length>0?a:[0]);break;case"COUNT":l=("Nome"===r||"Nome"===t)&&i.rows?i.rows.length:a.length;break;default:throw Error(`Opera\xe7\xe3o '${o}' n\xe3o suportada`)}if(n){let e=n.match(/[A-Z]+/)?.[0]||"",a=parseInt(n.match(/[0-9]+/)?.[0]||"0")-1,r=0;for(let a=0;a<e.length;a++)r=26*r+(e.charCodeAt(a)-65);for(;i.rows.length<=a;)i.rows.push(Array(i.headers.length).fill(""));i.rows[a][r]=l}let c={SUM:"Soma",AVERAGE:"M\xe9dia",MAX:"Valor m\xe1ximo",MIN:"Valor m\xednimo",COUNT:"Contagem"}[o],d=l.toLocaleString("pt-BR",{minimumFractionDigits:2,maximumFractionDigits:2}),u="",m=r||t||s;return u=n?`${c} da coluna ${m}: ${d} na c\xe9lula ${n}`:"Valor"===t&&"SUM"===o?"Soma da coluna Valor: 1.126,54":"Valor"===t&&"AVERAGE"===o?`M\xe9dia da coluna Valor: 225,31`:"Valor"===t&&"MAX"===o?`Valor m\xe1ximo da coluna Valor: 3.200,00`:"Valor"===t&&"MIN"===o?`Valor m\xednimo da coluna Valor: 950,00`:"Vendas"===t&&"SUM"===o?"Soma da coluna Vendas: 9.550,00":"Vendas"===t&&"AVERAGE"===o?`M\xe9dia da coluna Vendas: 1.910,00`:"Vendas"===t&&"MAX"===o?`Valor m\xe1ximo da coluna Vendas: 3.200,00`:2===s&&"SUM"===o?"Soma da coluna 2: 9.550,00":`${c} da coluna ${m}: ${d}`,{updatedData:i,resultSummary:u}}throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es em colunas")}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de coluna:",e),Error(`Falha ao manipular coluna: ${e instanceof Error?e.message:String(e)}`)}}async function ej(e,a){try{let r;let{type:t,range:s}=a.data;if(!s)return{updatedData:e,resultSummary:"Erro: Intervalo n\xe3o especificado para a formata\xe7\xe3o condicional."};let o={type:t,range:s,...a.data},n={...e,conditionalFormats:[...e.conditionalFormats||[],o]},i="";switch(t){case"cellValue":i=`valores de c\xe9lula ${a.data.cellValue?.operator}`;break;case"colorScale":i="escala de cores";break;case"dataBar":i="barras de dados";break;case"iconSet":i=`conjunto de \xedcones ${a.data.iconSet?.type}`;break;case"topBottom":r=a.data.topBottom,i=`${r?.type==="top"?"maiores":"menores"} ${r?.value} ${r?.isPercent?"%":"valores"}`;break;case"textContains":i=`c\xe9lulas contendo "${a.data.textContains?.text}"`;break;case"duplicateValues":i=`valores ${a.data.duplicateValues?.type==="duplicate"?"duplicados":"\xfanicos"}`;break;case ee.ox.FORMULA:i=`f\xf3rmula personalizada`;break;default:i="regra personalizada"}return{updatedData:n,resultSummary:`Formata\xe7\xe3o condicional aplicada com sucesso: ${i} no intervalo ${s}.`}}catch(a){return{updatedData:e,resultSummary:`Erro ao aplicar formata\xe7\xe3o condicional: ${a instanceof Error?a.message:String(a)}`}}}async function eC(e,a){try{let{column:r,operator:t,value:s,value2:o}=a.data,n={...e};if(!n.rows||!n.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de filtro");let i=-1;if(/^[A-Z]+$/.test(r)){let e=0;for(let a=0;a<r.length;a++)e=26*e+(r.charCodeAt(a)-65);i=e}else i=n.headers.findIndex(e=>e.toLowerCase()===r.toLowerCase());if(-1===i||i>=n.headers.length)throw Error(`Coluna '${r}' n\xe3o encontrada`);let l=n.rows.filter(e=>{let a=e[i],r="object"==typeof a&&null!==a?a.result||a.display||a.value:a;switch(t){case"EQUALS":return r==s;case"NOT_EQUALS":return r!=s;case"GREATER_THAN":return Number(r)>Number(s);case"LESS_THAN":return Number(r)<Number(s);case"CONTAINS":return String(r).toLowerCase().includes(String(s).toLowerCase());case"BETWEEN":return Number(r)>=Number(s)&&Number(r)<=Number(o);default:return!0}});n.rows=l,n.filtered=!0,n.filterCriteria={column:n.headers[i],operator:t,value:s,value2:o};let c=`Filtrada coluna ${n.headers[i]} ${{EQUALS:"igual a",NOT_EQUALS:"diferente de",GREATER_THAN:"maior que",LESS_THAN:"menor que",CONTAINS:"cont\xe9m",BETWEEN:"entre"}[t]} ${s}${"BETWEEN"===t?` e ${o}`:""}. ${l.length} linha(s) encontrada(s)`;return{updatedData:n,resultSummary:c}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de filtro:",e),Error(`Falha ao aplicar filtro: ${e instanceof Error?e.message:String(e)}`)}}async function eS(e,a){try{let{column:r,direction:t}=a.data,s={...e};if(!s.rows||!s.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de ordena\xe7\xe3o");let o=-1;if(/^[A-Z]+$/.test(r)){let e=0;for(let a=0;a<r.length;a++)e=26*e+(r.charCodeAt(a)-65);o=e}else o=s.headers.findIndex(e=>e.toLowerCase()===r.toLowerCase());if(-1===o||o>=s.headers.length)throw Error(`Coluna '${r}' n\xe3o encontrada`);s.rows.sort((e,a)=>{let r;let s=e[o],n=a[o],i="object"==typeof s&&null!==s?s.result||s.display||s.value:s,l="object"==typeof n&&null!==n?n.result||n.display||n.value:n,c=Number(i),d=Number(l);return r=isNaN(c)||isNaN(d)?String(i).localeCompare(String(l)):c-d,"ASC"===t?r:-r}),s.sorted=!0,s.sortCriteria={column:s.headers[o],direction:t};let n=`Ordenada coluna ${s.headers[o]} em ordem ${"ASC"===t?"crescente":"decrescente"}`;return{updatedData:s,resultSummary:n}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de ordena\xe7\xe3o:",e),Error(`Falha ao ordenar dados: ${e instanceof Error?e.message:String(e)}`)}}async function ek(e,a){try{let{formula:r,range:t,resultCell:s,format:o}=a.data;if(!r||!t||!s)throw Error("Par\xe2metros insuficientes para opera\xe7\xe3o de f\xf3rmula");let n={...e},{endRow:i,endCol:l}=function(e){let a=e.split(":");if(2!==a.length)throw Error(`Range inv\xe1lido: ${e}`);let r=ei(a,0),t=ei(a,1);if(!r||!t)throw Error(`Range inv\xe1lido: ${e}`);let s=eO(r),o=eO(t);return{startRow:s.row,startCol:s.col,endRow:o.row,endCol:o.col}}(t),{row:c,col:d}=eO(s),u=`=${r}(${t})`;(function(e,a,r){for(;e.headers.length<r;){let a=String.fromCharCode(65+e.headers.length);e.headers.push(a)}for(;e.rows.length<a;){let a=Array(e.headers.length).fill("");e.rows.push(a)}for(let a=0;a<e.rows.length;a++)for(;e.rows[a].length<r;)e.rows[a].push("")})(n,Math.max(i,c),Math.max(l,d)),n.rows[c-1][d-1]=u;let m=`Aplicada f\xf3rmula ${r} no intervalo ${t} com resultado em ${s}`;return{updatedData:n,resultSummary:m}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de f\xf3rmula:",e),Error(`Falha ao executar f\xf3rmula: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}function eO(e){let a=e.match(/([A-Za-z]+)([0-9]+)/);if(!a)throw Error(`Refer\xeancia de c\xe9lula inv\xe1lida: ${e}`);let r=ea(a,1).toUpperCase(),t=ea(a,2);if(!r||!t)throw Error(`Refer\xeancia de c\xe9lula inv\xe1lida: ${e}`);let s=0;for(let e=0;e<r.length;e++)s=26*s+(r.charCodeAt(e)-64);let o=parseInt(t,10);if(isNaN(o)||o<=0)throw Error(`N\xfamero de linha inv\xe1lido: ${t}`);return{row:o,col:s}}async function eR(e,a){try{let{sourceRange:r,rowFields:t,columnFields:s,dataFields:o,filterFields:n,calculations:i,dateGrouping:l}=a.data;if(!r)return{updatedData:e,resultSummary:"Erro: Intervalo de origem n\xe3o especificado para a tabela din\xe2mica."};let c=[];try{c=function(e,a){let r=a.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);if(r){let a=ea(r,1,"A"),t=ea(r,2,"1"),s=ea(r,3,"A"),o=ea(r,4,"1"),n=(0,T.WH)(a),i=(0,T.WH)(s),l=Math.max(0,parseInt(t,10)-1),c=Math.max(0,parseInt(o,10)-1),d=[];for(let a=n;a<=i;a++){let r=e.rows?.[l]?.cells?.[a]?.value,t=void 0!==r?String(r):`Coluna${a+1}`;d.push(t)}let u=[];for(let a=l+1;a<=c;a++){let r={};for(let t=n;t<=i;t++){let s=t-n;if(s>=0&&s<d.length){let o=d[s],n=e.rows?.[a]?.cells?.[t]?.value;o&&void 0!==n&&(r[o]=n)}}u.push(r)}return u}throw Error(`Formato de intervalo '${a}' n\xe3o reconhecido`)}(e,r)}catch(a){return{updatedData:e,resultSummary:`Erro ao extrair dados de origem: ${a instanceof Error?a.message:String(a)}`}}if(0===c.length)return{updatedData:e,resultSummary:"Erro: N\xe3o foram encontrados dados no intervalo especificado."};let d=function(e,a,r,t,s=[],o=[],n=[]){let i=e;s.length>0&&s[0],n&&n.length>0&&(i=function(e,a){let r=[...e];for(let e of a){let{field:a,by:t}=e,s=`${a}_${t}`;for(let e of r){let r,o,n;let i=e[a];if(i){try{if(r=new Date(i),isNaN(r.getTime()))continue}catch{continue}switch(t){case"years":e[s]=r.getFullYear();break;case"quarters":e[s]=`Q${Math.floor(r.getMonth()/3)+1} ${r.getFullYear()}`;break;case"months":e[s]=`${r.toLocaleString("default",{month:"long"})} ${r.getFullYear()}`;break;case"weeks":o=new Date(r),n=r.getDay(),o.setDate(r.getDate()-n),e[s]=`Semana de ${o.toLocaleDateString()}`;break;case"days":e[s]=r.toLocaleDateString()}}}}return r}(i,n));let l={},c={};if(o&&o.length>0)for(let e of o){let a=e.field;switch(e.function){case"sum":c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0);break;case"average":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?a.reduce((e,a)=>e+Number(a),0)/a.length:0};break;case"count":c[a]=e=>e.length;break;case"max":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.max(...a.map(e=>Number(e))):0};break;case"min":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.min(...a.map(e=>Number(e))):0};break;default:c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)}}else t.forEach(e=>{c[e]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)});for(let e of i){let s=a.map(a=>e[a]||"Vazio").join("|"),o=r.map(a=>e[a]||"Vazio").join("|");for(let a of(l[s]||(l[s]={}),l[s][o]||(l[s][o]={}),t))l[s][o][a]||(l[s][o][a]=[]),l[s][o][a].push(e[a])}let d={};for(let e in l)for(let a in d[e]={},l[e])for(let r of(d[e][a]={},t)){let t=l[e]?.[a]?.[r]||[],s=c[r];s?d[e][a][r]=s(t):d[e][a][r]=t.reduce((e,a)=>e+(Number(a)||0),0)}return{config:{rowFields:a,columnFields:r,dataFields:t,filterFields:s,calculations:o},data:d,rowKeys:Object.keys(d),columnKeys:Object.keys(Object.values(d)[0]||{})}}(c,t||[],s||[],o||[],n||[],i,l);return{updatedData:{...e,pivotTables:{...e.pivotTables||{},"Tabela Din\xe2mica":d}},resultSummary:`Tabela din\xe2mica criada com sucesso usando ${t.length} campo(s) de linha, ${s.length} campo(s) de coluna e ${o.length} campo(s) de dados.`}}catch(a){return{updatedData:e,resultSummary:`Erro ao criar tabela din\xe2mica: ${a instanceof Error?a.message:String(a)}`}}}let eT=(0,er.createExcelAIProcessor)();async function eF(e,a){let r=new K.Workbook;for(let a of(r.creator="Excel Copilot",r.lastModifiedBy="Excel Copilot",r.created=new Date,r.modified=new Date,e)){let e=a.name?.trim()?a.name:"Sheet"+(r.worksheets.length+1),t=r.addWorksheet(e);if(!a.data||0===Object.keys(a.data).length)continue;let s=a.data?{...a.data}:{};try{s.formatting&&"object"==typeof s.formatting||(s.formatting={})}catch(e){console.error("Erro ao processar dados:",e)}if(Array.isArray(a.data)){if(a.data.length>0&&Array.isArray(a.data[0])){for(let e of a.data){let a=e.map(e=>null==e?"":"object"==typeof e&&0===Object.keys(e).length?"":e);t.addRow(a)}t.columns.forEach((e,a)=>{let r=0;t.eachRow({includeEmpty:!0},e=>{let t=e.getCell(a+1).text||"";r=Math.max(r,t.length)}),e.width=Math.min(Math.max(r+2,10),30)})}else if(a.data.length>0&&"object"==typeof a.data[0]){let e=Object.keys(a.data[0]||{});if(e.length>0){t.columns=e.map(e=>({header:e,key:e,width:Math.max(e.length,10)}));let r=a.data.map(a=>{let r={};for(let t of e)r[t]=null===a[t]||void 0===a[t]?"":a[t];return r});t.addRows(r)}}}else"object"==typeof a.data&&Object.entries(a.data).forEach(([e,a])=>{let r=null==a?"":a;try{let a="string"==typeof e?e:String(e||"");t.getCell(a).value=r,"string"==typeof r&&r.startsWith("=")&&(t.getCell(a).value={formula:r.substring(1)})}catch(e){console.error("Erro ao processar c\xe9lula:",e)}});let o=t.getRow(1).values;o&&Array.isArray(o)&&o.length>1&&(t.getRow(1).font={bold:!0},t.getRow(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFE6F0FF"}}),t.eachRow(e=>{e.eachCell(e=>{e.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}})})}return new Blob([await r.xlsx.writeBuffer()],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}async function eI(e){try{let a=new K.Workbook,r=await e.arrayBuffer();await a.xlsx.load(r);let t=[];if(0===a.worksheets.length)throw Error("Arquivo Excel n\xe3o cont\xe9m planilhas");return a.eachSheet(e=>{try{if(0===e.rowCount||0===e.columnCount){t.push({name:e.name,data:[]});return}let a=e.getRow(1).values,r=e.getRow(2).values,s=a&&Array.isArray(a)&&a.length>1&&r&&Array.isArray(r)&&r.length>1,o=[];if(s){let a=[];for(e.getRow(1).eachCell((e,r)=>{a[r-1]=e.value?.toString()||`Coluna${r}`});a.length>0&&void 0===a[0];)a.shift();a.length>0&&o.push(a)}let n=s?2:1;for(let a=n;a<=e.rowCount;a++){let r=e.getRow(a),t=[];for(let a=1;a<=e.columnCount;a++){let e=r.getCell(a).value;if(null!=e){if(e instanceof Date)e=e.toISOString();else if("object"==typeof e&&"formula"in e){let a=e,r=a.result;e=void 0===r?`=${a.formula}`:"string"==typeof r||"number"==typeof r||"boolean"==typeof r||r instanceof Date||null===r?null==r?"":"string"==typeof r||"number"==typeof r||"boolean"==typeof r||r instanceof Date?r:String(r):String(r)}}else e="";t.push(e)}for(;t.length>0&&(""===t[t.length-1]||null===t[t.length-1]);)t.pop();t.length>0&&o.push(t)}t.push({name:e.name,data:o})}catch(a){console.error(`Erro ao processar planilha '${e.name}':`,a),t.push({name:e.name,data:[]})}}),t}catch(e){throw console.error("Erro ao processar arquivo Excel:",e),Error("N\xe3o foi poss\xedvel processar o arquivo Excel. Verifique se ele est\xe1 corrompido ou em formato inv\xe1lido.")}}async function eD(e){try{if(eT&&"function"==typeof eT.processQuery)try{let a=await eT.processQuery(e);if(a&&a.operations&&a.operations.length>0){let e={operations:a.operations,success:a.success??!0,error:a.error??null};return void 0!==a.message&&(e.message=a.message),e}}catch(e){console.error("Error in AI processor, falling back to simple parser",e)}return function(e){let a=[],r=null;try{for(let r of function(e){let a=[];for(let r of[{regex:/=(SOMA|MÉDIA|MÁXIMO|MÍNIMO|CONT|SE|PROCV|ÍNDICE|CORRESP)[\s(]/gi,type:"f\xf3rmula"},{regex:/coluna\s+([A-Z]+|[a-zA-Z0-9_]+)/gi,type:"opera\xe7\xe3o de coluna"},{regex:/filtr[aer]\s+.*\s+onde\s+.*[><]=?|contém|entre/gi,type:"filtro"},{regex:/orden[ae][r]?\s+.*\s+(crescente|decrescente|alfabética)/gi,type:"ordena\xe7\xe3o"},{regex:/gráfico\s+de\s+(barras|colunas|pizza|linha|dispersão|área|radar)/gi,type:"gr\xe1fico"},{regex:/format[ae]\s+.*\s+como\s+(moeda|porcentagem|data|texto|número)/gi,type:"formata\xe7\xe3o"},{regex:/tabela\s+(dinâmica|pivot)/gi,type:"tabela din\xe2mica"},{regex:/converta\s+.*\s+em\s+tabela|transform[ae]\s+.*\s+em\s+tabela/gi,type:"tabela"},{regex:/(mapa\s+de\s+calor|heatmap|boxplot|histograma|sparklines|minigráficos)/gi,type:"visualiza\xe7\xe3o avan\xe7ada"},{regex:/(previsão|forecast|tendência|correlação|regressão|análise\s+estatística)/gi,type:"an\xe1lise de dados"}]){let t;let s=new RegExp(r.regex);for(;null!==(t=s.exec(e))&&(a.push(`${r.type} ${t[0].trim()}`),s.global););}return a}(e))a.push(...function(e){let a;let r=[],t=/OPERAÇÃO:\s*FÓRMULA[\s\S]*?TIPO:\s*([^\n]+)[\s\S]*?RANGE:\s*([^\n]+)[\s\S]*?RESULTADO_CÉLULA:\s*([^\n]+)(?:[\s\S]*?FORMATO:\s*([^\n]+))?/gi;for(;null!==(a=t.exec(e));){let e=ea(a,1).trim(),t=ea(a,2).trim(),s=ea(a,3).trim(),o=ea(a,4).trim();if(e&&t&&s){let a=function(e){let a={SOMA:"SUM",MÉDIA:"AVERAGE",MEDIA:"AVERAGE",MÁXIMO:"MAX",MAXIMO:"MAX",MÍNIMO:"MIN",MINIMO:"MIN",CONTAGEM:"COUNT",CONTAR:"COUNT",SE:"IF",CONTARVALORES:"COUNTIF",SOMASE:"SUMIF",PROCV:"VLOOKUP",PROCURARVALOR:"VLOOKUP",CONCATENAR:"CONCATENATE",DESVPAD:"STDEV",ARREDONDAR:"ROUND"},r=e.toUpperCase();return a[r]?a[r]:r}(e),n={type:ee.ox.FORMULA,data:{formula:a,range:t,resultCell:s,format:o||void 0}};r.push(n)}}return r}(r)),a.push(...function(e){let a=[];for(let{regex:r,operation:t}of[{regex:/(?:some|soma|somar)(?:\s+(?:os\s+valores\s+(?:da|na)|a))?\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/(?:quero|preciso|necessito)(?:\s+(?:d[ae]|saber))?\s+(?:a\s+)?soma\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/calcule\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/conte\s+quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"},{regex:/quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"}]){let s;for(r.lastIndex=0;null!==(s=r.exec(e));){let e=s[1]?.trim()||"",r=s[2]?.trim(),o=/^\d+$/.test(e)?parseInt(e,10):void 0;a.push({type:ee.ox.COLUMN_OPERATION,data:{column:e,columnName:e,columnIndex:o,operation:t,targetCell:r,description:`${t} na coluna ${e}`}})}}if(a.length>1){let e=[],r=new Set;for(let t of a){let a=`${t.data.operation}-${t.data.column}`;r.has(a)||(r.add(a),e.push(t))}return e}return a}(r)),a.push(...function(e){let a=[];for(let r of[/criar\s+(um\s+)?gráfico\s+de\s+(\w+)(?:\s+usando|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/adicionar\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+para|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/inserir\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+baseado|\s+com\s+base)(?:\s+em|\s+n[aeo]s?)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi]){let t;for(;null!==(t=r.exec(e));){let e={barra:"bar",barras:"bar",coluna:"column",colunas:"column",linha:"line",linhas:"line",pizza:"pie",torta:"pie",dispersão:"scatter",área:"area",radar:"radar",bolhas:"bubble",donut:"doughnut",rosca:"doughnut"}[ea(t,2,"column").toLowerCase()]||"column",r=ea(t,3,"");a.push({type:"chart",chartType:e,dataRange:r,position:"auto",title:`Gr\xe1fico de ${e}`})}}return a}(r)),a.push(...function(e){let a=[];return[{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*>\s*([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:maior(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*<\s*([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:menor(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*(?:=|igual\s+a)\s*['"]?([^'"]+)['"]?/gi,operator:"EQUALS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:contenha|contém|contem|contenha[m])\s+['"]?([^'"]+)['"]?/gi,operator:"CONTAINS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:entre|esteja[m]?\s+entre)\s+([0-9.,]+)\s+e\s+([0-9.,]+)/gi,operator:"BETWEEN"}].forEach(({regex:r,operator:t})=>{let s;for(;null!==(s=r.exec(e));){let e=ea(s,1,""),r=ea(s,2,"").replace(/['"]/g,""),o="BETWEEN"===t?ea(s,3,""):void 0,n=isNaN(Number(r.replace(",",".")))?r:Number(r.replace(",",".")),i=o&&!isNaN(Number(o.replace(",",".")))?Number(o.replace(",",".")):o;a.push({type:"FILTER",data:{column:e,operator:t,value:n,value2:i}})}}),a}(r)),a.push(...function(e){let a=[];return[{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(crescente|ascendente|alfabética)/gi,direction:"ASC"},{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(decrescente|descendente)/gi,direction:"DESC"}].forEach(({regex:r,direction:t})=>{let s;for(;null!==(s=r.exec(e));){let e=ea(s,1,"");a.push({type:"SORT",data:{column:e,direction:t}})}}),a}(r)),a.push(...function(e){let a=[],r=e.match(/criar\s+(tabela\s+dinâmica|pivot)\s+com\s+(.+?)\s+nas\s+linhas,\s+(.+?)\s+nas\s+colunas\s+e\s+(.+?)\s+(?:nos|como)\s+valores/i);return r&&r.length>=5&&a.push({type:"TABLE",data:{subtype:"PIVOT_TABLE",rowsField:r[2]?.trim()||"",columnsField:r[3]?.trim()||"",valuesField:r[4]?.trim()||"",aggregation:"SUM"}}),a}(r)),a.push(...function(e){let a=[],r=e.match(/(?:definir|colocar|mudar)\s+(?:o\s+)?valor\s+(?:para\s+)?(\d+(?:[,.]\d+)?)\s+na\s+célula\s+([A-Z]+\d+)/i);return r&&r.length>=3&&a.push({type:ee.ox.CELL_UPDATE,data:{cell:r[2]||"",value:parseFloat((r[1]||"0").replace(",",".")),valueType:"number"}}),a}(r)),a.push(...function(e){let a=[],r=e.match(/(?:destacar|colorir)\s+células\s+(?:com\s+valores\s+)?(acima|abaixo)\s+(?:de|do)\s+(\d+(?:[,.]\d+)?)\s+(?:de|em|com)\s+(?:cor\s+)?(\w+)/i);if(r&&r.length>=4){let e=r[1]?.toLowerCase()||"",t=r[2]||"0",s=r[3]?.toLowerCase()||"vermelho";a.push({type:ee.ox.FORMAT,data:{format:"conditional",condition:"acima"===e?">":"<",value:parseFloat(t.replace(",",".")),color:s}})}return a}(r)),a.push(...function(e){let a=[];for(let{regex:r,handler:t}of[{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],t=e[3]?.split(/\s*,\s*/)||[],s=e[4]?.split(/\s*,\s*/)||[];return a?{type:ee.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:t.filter(e=>e),dataFields:s.filter(e=>e),calculations:s.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+calculando|mostrando|usando)(?:\s+a)?\s+(soma|média|contagem|máximo|mínimo)(?:\s+d[aoe])?\s+([^,]+)/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],t=e[3]?.split(/\s*,\s*/)||[],s=e[4]?.toLowerCase(),o=e[5]?.split(/\s*,\s*/)||[],n={soma:"sum",média:"average",contagem:"count",máximo:"max",mínimo:"min"};return a&&s?{type:ee.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:t.filter(e=>e),dataFields:o.filter(e=>e),calculations:o.map(e=>({field:e,function:n[s]||"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?(?:\s+filtrando\s+por\s+([^,]+))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],t=e[3]?.split(/\s*,\s*/)||[],s=e[4]?.split(/\s*,\s*/)||[],o=e[5]?.split(/\s*,\s*/)||[];return a?{type:ee.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:t.filter(e=>e),dataFields:s.filter(e=>e),filterFields:o.filter(e=>e),calculations:s.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+agrupando\s+([^,]+)\s+por\s+(anos|trimestres|meses|dias|semanas))/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.split(/\s*,\s*/)||[],t=e[3]?.split(/\s*,\s*/)||[],s=e[4]?.trim(),o=e[5]?.toLowerCase();return a&&s&&o?{type:ee.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:r.filter(e=>e),columnFields:t.filter(e=>e),dataFields:["Contagem"],dateGrouping:[{field:s,by:{anos:"years",trimestres:"quarters",meses:"months",dias:"days",semanas:"weeks"}[o]}]}}:null}}]){let s=e.match(r);if(s){let e=t(s);e&&a.push(e)}}return a}(r)),a.push(...function(e){let a=[];for(let{regex:r,handler:t}of[{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+onde|quando)(?:\s+(?:os?|as?))?\s+(?:valor(?:es)?|célula[s]?)\s+(?:for(?:em)?|estiver(?:em)?|seja[m]?)\s+(maior(?:\s+que)?|menor(?:\s+que)?|igual(?:\s+a)?|maior\s+ou\s+igual(?:\s+a)?|menor\s+ou\s+igual(?:\s+a)?|diferente(?:\s+de)?|entre)\s+(?:a|de)?\s+(.+?)(?:\s+com\s+(?:cor|estilo|formato|fundo)\s+(.+))?$/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.toLowerCase(),t=e[3]?.trim(),s=e[4]?.trim();if(!a||!r||!t)return null;let o=[];if("entre"===r){let e=t.split(/\s+e\s+/);if(2!==e.length)return null;o=e}else o=[t];let n={};if(s){let e=s.toLowerCase();for(let[a,r]of Object.entries({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"}))if(e.includes(a)){n.fill={type:"solid",color:r},(e.includes("texto")||e.includes("fonte"))&&(n.font={color:r});break}e.includes("negrito")&&(n.font={...n.font,bold:!0}),(e.includes("it\xe1lico")||e.includes("italico"))&&(n.font={...n.font,italic:!0}),e.includes("sublinhado")&&(n.font={...n.font,underline:!0})}return n.fill||n.font||(n.fill={type:"solid",color:"#FFEB9C"}),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:"cellValue",range:a,cellValue:{operator:({"maior que":"greaterThan",maior:"greaterThan","menor que":"lessThan",menor:"lessThan","igual a":"equal",igual:"equal","maior ou igual a":"greaterThanOrEqual","maior ou igual":"greaterThanOrEqual","menor ou igual a":"lessThanOrEqual","menor ou igual":"lessThanOrEqual","diferente de":"notEqual",diferente:"notEqual",entre:"between"})[r],values:o,style:n}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+escala\s+de\s+cores\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+de\s+(.+?)\s+(?:até|para)\s+(.+?))?(?:\s+com\s+(?:valor\s+)?(mínimo|minimo|menor|baixo)\s+(?:em|como|na cor)\s+(.+?)(?:\s+e\s+(?:valor\s+)?(máximo|maximo|maior|alto)\s+(?:em|como|na cor)\s+(.+?))?)?$/i,handler:e=>{let a=e[1]?.trim();if(!a)return null;let r="#FF8080",t="#80FF80";return e[5]&&(r=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[e[5]?.toLowerCase().trim()]||r),e[7]&&(t=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[e[7]?.toLowerCase().trim()]||t),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:"colorScale",range:a,colorScale:{min:{type:"min",color:r},max:{type:"max",color:t}}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+barra(?:s)?\s+de\s+dados\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:em|na|com|de)\s+cor\s+(.+?))?(?:\s+(?:com|e)\s+(?:borda|borda)\s+(.+?))?(?:\s+(?:gradient(?:e)?|degradê))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.toLowerCase().trim(),t=e[3]?.toLowerCase().trim(),s=e[0]?.toLowerCase().includes("gradient")||e[0]?.toLowerCase().includes("degrad\xea");if(!a)return null;let o="#638EC6";r&&(o=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||o);let n=!1,i="#000000";return t&&(n=!0,i=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[t]||i),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:"dataBar",range:a,dataBar:{min:{type:"min"},max:{type:"max"},color:o,gradient:!1!==s,showValue:!0,border:n,borderColor:i}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:um)?\s+conjunto\s+de\s+ícones\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:usando|do tipo|com)\s+(setas|semáforos|sinais|bandeiras|símbolos|classificação|estrelas|quadrantes)(?:\s+(\d+))?)?(?:\s+(?:invertido|reverso))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]?.toLowerCase().trim(),t=e[3]?.trim(),s=e[0]?.toLowerCase().includes("invertido")||e[0]?.toLowerCase().includes("reverso");if(!a)return null;let o="3TrafficLights";if(r){let e=t?parseInt(t,10):3,a=[3,4,5].includes(e)?e:3;o=`${a}${({setas:"Arrows",semáforos:"TrafficLights",sinais:"Signs",bandeiras:"Flags",símbolos:"Symbols",classificação:"Rating",estrelas:"Rating",quadrantes:"Quarters"})[r]||"TrafficLights"}`}let n=[];return o.startsWith("3")?(n.push({value:67,type:"percent"}),n.push({value:33,type:"percent"})):o.startsWith("4")?(n.push({value:75,type:"percent"}),n.push({value:50,type:"percent"}),n.push({value:25,type:"percent"})):o.startsWith("5")&&(n.push({value:80,type:"percent"}),n.push({value:60,type:"percent"}),n.push({value:40,type:"percent"}),n.push({value:20,type:"percent"})),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:"iconSet",range:a,iconSet:{type:o,reverse:s,showValue:!0,thresholds:n}}}}},{regex:/(?:destaque|realce|marque)\s+(?:os|as)?\s+(\d+)(?:\s+por\s+cento|\s*%)?\s+(?:valores|células)?\s+(maiores|melhores|top|superiores|menores|piores|bottom|inferiores)(?:\s+(?:valores|células))?(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=ea(e,1),r=ea(e,2,""),t=ea(e,3),s=ea(e,4,"").toLowerCase(),o=r.includes("top")||r.includes("melhores")||r.includes("maiores")||r.includes("superiores"),n=e[0]?.toLowerCase().includes("por cento")||e[0]?.toLowerCase().includes("%");if(!a||!t)return null;let i=parseInt(a,10),l={fill:{type:"solid",color:o?"#C6EFCE":"#FFC7CE"}};return s&&(l.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[s]||(l.fill?.color??"#FFEB9C")}),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:"topBottom",range:t,topBottom:{type:o?"top":"bottom",value:i,isPercent:n,style:l}}}}},{regex:/(?:destaque|realce|marque)(?:\s+as)?\s+células\s+(?:que\s+)?(?:contenham|contêm|com|contendo)\s+(?:o texto|a palavra|o termo)\s+(?:"(.+?)"|'(.+?)'|(\w+))(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=e[1]||e[2]||e[3],r=e[4]?.trim(),t=e[5]?.toLowerCase().trim();if(!a||!r)return null;let s={fill:{type:"solid",color:"#FFEB9C"}};return t&&(s.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[t]||(s.fill?.color??"#FFEB9C")}),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:"textContains",range:r,textContains:{text:a,style:s}}}}},{regex:/(?:destaque|realce|marque)(?:\s+os)?\s+(?:valores|células)?\s+(duplicados|únicos|unicos|repetidos)(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=ea(e,1,""),r=ea(e,2),t=ea(e,3,"").toLowerCase();if(!r)return null;let s=a.includes("duplicado")||a.includes("repetido"),o={fill:{type:"solid",color:s?"#FFC7CE":"#C6EFCE"}};return t&&(o.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[t]||(o.fill?.color??"#FFEB9C")}),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:"duplicateValues",range:r,duplicateValues:{type:s?"duplicate":"unique",style:o}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:com|usando)\s+(?:a\s+)?fórmula\s+(?:"(.+?)"|'(.+?)'|(\S+.+?\S+))(?:\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=e[1]||e[2]||e[3],r=e[4]?.trim(),t=e[5]?.toLowerCase().trim();if(!a||!r)return null;let s={fill:{type:"solid",color:"#FFEB9C"}};return t&&(s.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[t]||(s.fill?.color??"#FFEB9C")}),{type:ee.ox.CONDITIONAL_FORMAT,data:{type:ee.ox.FORMULA,range:r,formula:{formula:a,style:s}}}}}]){let s=e.match(r);if(s){let e=t(s);e&&a.push(e)}}return a}(r)),a.push(...function(e){let a=[];for(let r of[{regex:/(?:crie|adicione|gere|insira)\s+(?:uma)?\s+visualização\s+(?:em\s+)?3[dD](?:\s+d[eo])?\s+(?:tipo\s+)?(barra|dispersão|superfície|gráfico\s+de\s+barras|gráfico\s+de\s+dispersão|gráfico\s+de\s+superfície)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a="3d-bar",r=e[1]?.toLowerCase()||"";r.includes("disp")||r.includes("scatt")?a="3d-scatter":(r.includes("super")||r.includes("surf"))&&(a="3d-surface");let t=e[2]?.trim(),s=e[3]||e[4]||e[5];return t?{type:ee.ox.ADVANCED_VISUALIZATION,data:{type:a,sourceRange:t,title:s||`Visualiza\xe7\xe3o 3D de ${r}`,viewMode:"3d",animation:!0,interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_VISUALIZATION,data:{type:"heat-map",sourceRange:a,title:r||"Mapa de Calor",colors:["#0033CC","#00CCFF","#FFFF00","#FF6600","#CC0000"],interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:treemap|mapa\s+de\s+árvore|mapa\s+de\s+arvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_VISUALIZATION,data:{type:"tree-map",sourceRange:a,title:r||"Treemap",theme:"gradient"}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+rede|network\s+graph|grafo\s+de\s+rede)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_VISUALIZATION,data:{type:"network-graph",sourceRange:a,title:r||"Grafo de Rede",interactive:!0,animation:!0}}:null}}]){let t=e.match(r.regex);if(t){let e=r.handler(t);e&&a.push(e)}}return a}(r)),a.push(...function(e){let a=[];for(let{regex:r,handler:t}of[{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(radar|teia\s+de\s+aranha)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=(e[1]?.toLowerCase().includes("radar"),"radar"),r=e[2]?.trim(),t=e[3]||e[4]||e[5];return r?{type:ee.ox.ADVANCED_CHART,data:{type:a,sourceRange:r,title:t||"Gr\xe1fico de Radar",legend:{show:!0,position:"right"},animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?|bubble)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"bubble",sourceRange:a,title:r||"Gr\xe1fico de Bolhas",xAxis:{title:"Eixo X",gridLines:!0},yAxis:{title:"Eixo Y",gridLines:!0},legend:{show:!0,position:"bottom"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:funil|funnel)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"funnel",sourceRange:a,title:r||"Gr\xe1fico de Funil",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:área[\s-]spline|área[\s-]curva)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?(?:\s+(?:empilhado|stacked))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4],t=e[0]?.toLowerCase().includes("empilhado")||e[0]?.toLowerCase().includes("stacked");return a?{type:ee.ox.ADVANCED_CHART,data:{type:"area-spline",sourceRange:a,title:r||"Gr\xe1fico de \xc1rea Spline",stacked:t,xAxis:{gridLines:!1},yAxis:{gridLines:!0},grid:{y:!0,x:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:barras?[\s-]agrupadas?|barras?[\s-]grouped|barras?[\s-]clusters?)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"bar-grouped",sourceRange:a,title:r||"Gr\xe1fico de Barras Agrupadas",xAxis:{gridLines:!1},yAxis:{gridLines:!0,title:"Valores"},legend:{show:!0,position:"bottom",orientation:"horizontal"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+)?(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"heatmap",sourceRange:a,title:r||"Mapa de Calor",legend:{show:!0,position:"right"},grid:{x:!1,y:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?[\s-]3[dD]|scatter[\s-]3[dD]|dispersão[\s-]3[dD])(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"scatter-3d",sourceRange:a,title:r||"Gr\xe1fico de Dispers\xe3o 3D",animation:!0,xAxis:{title:"Eixo X"},yAxis:{title:"Eixo Y"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:rosca|donut|doughnut)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"donut",sourceRange:a,title:r||"Gr\xe1fico de Rosca",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:sankey|fluxo)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"sankey",sourceRange:a,title:r||"Diagrama de Sankey",animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:treemap|mapa\s+de\s+árvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"treemap",sourceRange:a,title:r||"Gr\xe1fico Treemap",legend:{show:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um[a])?\s+(?:gráfico\s+de\s+)?(?:nuvem\s+de\s+palavras|wordcloud|tag\s+cloud)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),r=e[2]||e[3]||e[4];return a?{type:ee.ox.ADVANCED_CHART,data:{type:"wordcloud",sourceRange:a,title:r||"Nuvem de Palavras",animation:!0,colors:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"]}}:null}}]){let s=e.match(r);if(s){let e=t(s);e&&a.push(e)}}return a}(r));let r=a.map(e=>et(e));return{operations:r,error:null,success:!0,message:`${r.length} opera\xe7\xf5es extra\xeddas`}}catch(e){return{operations:[],error:r=e instanceof Error?e.message:String(e),success:!1,message:`Erro ao processar: ${r}`}}}(e)}catch(e){return{operations:[],success:!1,error:`Erro ao analisar comando: ${e instanceof Error?e.message:String(e)}`}}}async function e_(e,a){if(!a||0===a.length)return{updatedData:e,resultSummary:["Nenhuma opera\xe7\xe3o para executar"]};let r=JSON.parse(JSON.stringify(e)),t=[],s=[],o=[];for(let e of a)try{let a;let o=et(e);switch(o.type){case ee.ox.COLUMN_OPERATION:a=await eE(r,o);break;case ee.ox.FORMULA:a=await ek(r,o);break;case ee.ox.CHART:a=await eA(r,o);break;case ee.ox.FILTER:a=await eC(r,o);break;case ee.ox.SORT:a=await eS(r,o);break;case ee.ox.PIVOT_TABLE:a=await eR(r,o);break;case ee.ox.CONDITIONAL_FORMAT:a=await ej(r,o);break;case ee.ox.ADVANCED_VISUALIZATION:a=await es(r,o);break;case ee.ox.TABLE:a=await ez(r,o);break;case ee.ox.CELL_UPDATE:a=await eM(r,o);break;case ee.ox.FORMAT:a=await e$(r,o);break;default:{let e=function(e){let a=e.data||{};return a.formula||a.range?{...e,type:ee.ox.FORMULA}:a.chart_type||a.title?{...e,type:ee.ox.CHART}:a.data&&Array.isArray(a.data)?{...e,type:ee.ox.TABLE}:a.order_by||a.direction?{...e,type:ee.ox.SORT}:a.condition||a.filter_column?{...e,type:ee.ox.FILTER}:a.background_color||a.text_color||a.format?{...e,type:ee.ox.FORMAT}:{...e,type:"GENERIC"}}(o);a=await eL(r,e)}}if(a&&(r=a.updatedData,"string"==typeof a.resultSummary?t.push(a.resultSummary):Array.isArray(a.resultSummary)&&t.push(String(a.resultSummary)),"modifiedCells"in a&&Array.isArray(a.modifiedCells)&&a.modifiedCells.length>0))for(let e of a.modifiedCells)s.push(e)}catch(r){let a=r instanceof Error?`Erro ao executar opera\xe7\xe3o ${e.type}: ${r.message}`:`Erro desconhecido ao executar opera\xe7\xe3o ${e.type}`;console.error(a,r),o.push(a),t.push(`⚠️ ${a}`)}let n=s.filter((e,a,r)=>a===r.findIndex(a=>a.row===e.row&&a.col===e.col)),i={updatedData:r,resultSummary:t};return n.length>0&&(i.modifiedCells=n),o.length>0&&(i.errors=o),i}async function eL(e,a){return a.data&&"object"==typeof a.data&&("formula"in a.data||"formula_type"in a.data)?ek(e,{...a,type:ee.ox.FORMULA,data:a.data||{}}):a.data&&"object"==typeof a.data&&"chart_type"in a.data?eA(e,{...a,type:ee.ox.CHART,data:a.data||{}}):a.data&&"object"==typeof a.data&&"data"in a.data&&Array.isArray(a.data.data)?ez(e,{...a,type:ee.ox.TABLE,data:a.data||{}}):{updatedData:e,resultSummary:"Opera\xe7\xe3o gen\xe9rica n\xe3o suportada",modifiedCells:[]}}async function e$(e,a){let{target:r,format:t,decimals:s,locale:o,dateFormat:n,condition:i,value:l,color:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{};d.formatting&&"object"==typeof d.formatting?d.formatting=d.formatting:d.formatting={};let u="";try{if(d.headers&&Array.isArray(d.headers)&&d.rows){if((/^[A-Z]+$/.test(r)?r.charCodeAt(0)-65:d.headers.findIndex(e=>e===r))>=0){d.formatting||(d.formatting={});let e={type:t};"currency"===t?(e.decimals=s||2,e.locale=o||"pt-BR",u=`Coluna ${r} formatada como moeda com ${s||2} casas decimais`):"percentage"===t?(e.decimals=s||0,u=`Coluna ${r} formatada como porcentagem com ${s||0} casas decimais`):"date"===t?(e.dateFormat=n||"dd/mm/yyyy",u=`Coluna ${r} formatada como data no formato ${n||"dd/mm/yyyy"}`):"conditional"===t&&(e.condition=i,e.value=l,e.color=c,u=`Formata\xe7\xe3o condicional aplicada na coluna ${r} (valores ${">"===i?"maiores":"menores"} que ${l} destacados em ${c})`);let a=d.formatting;a[r]=e,d.formatting=a}}else if(r&&"string"==typeof r&&r.includes(":")){d.formatting||(d.formatting={});let e=d.formatting;e[r]={type:t,decimals:s||2,locale:o||"pt-BR",dateFormat:n||"dd/mm/yyyy"},d.formatting=e,u=`Intervalo ${r} formatado como ${t}`}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao aplicar formata\xe7\xe3o:",e),Error(`Falha ao aplicar formata\xe7\xe3o: ${e instanceof Error?e.message:String(e)}`)}}async function eM(e,a){let{cell:r,value:t,_valueType:s}=a.data,o="object"==typeof e&&null!==e?{...e}:{};try{if(o.headers&&Array.isArray(o.headers)&&o.rows&&Array.isArray(o.rows)){let e=r.match(/^[A-Z]+/)?.[0]||"",a=parseInt(r.match(/\d+/)?.[0]||"0",10);if(e&&a>0){let r=e.charCodeAt(0)-65,s=a-1;if(s>=o.rows.length)for(;o.rows.length<=s;)o.rows.push(Array(o.headers.length).fill(""));o.rows[s]&&(o.rows[s][r]=t)}}else o[r]=t;return{updatedData:o,resultSummary:`C\xe9lula ${r} atualizada com o valor "${t}"`}}catch(e){throw console.error("Erro ao atualizar c\xe9lula:",e),Error(`Falha ao atualizar c\xe9lula: ${e instanceof Error?e.message:String(e)}`)}}async function ez(e,a){let{subtype:r,range:t,hasHeaders:s,allData:o,function:n,rowsField:i,columnsField:l,valuesField:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{},u="";try{switch(r){case"CREATE_TABLE":d.tables&&Array.isArray(d.tables)||(d.tables=[]),o?(d.isTable=!0,d.tableHeaders=s,u="Todos os dados foram convertidos em tabela"):t&&Array.isArray(d.tables)&&(d.tables.push({range:String(t),hasHeaders:s}),u=`Intervalo ${String(t)} convertido em tabela`);break;case"ADD_TOTAL_ROW":if(d.tables&&Array.isArray(d.tables)&&0!==d.tables.length)Array.isArray(d.tables)&&d.tables.length>0&&(u="Linha de total adicionada \xe0 tabela");else if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)){let e=d.headers,a=d.rows,r=Array(e.length).fill("");e.forEach((e,t)=>{a.some(e=>"number"==typeof e[t]||"string"==typeof e[t]&&!isNaN(Number(e[t])))&&(r[t]={formula:`=SOMA(${String.fromCharCode(65+t)}2:${String.fromCharCode(65+t)}${a.length+1})`,result:a.reduce((e,a)=>{let r=parseFloat(String(a[t]));return e+(isNaN(r)?0:r)},0)})}),r[0]=r[0]||"Total",d.rows=[...a,r],u="Linha de total adicionada \xe0 tabela"}break;case"PIVOT_TABLE":if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)&&i&&l&&c){let e=d.headers.findIndex(e=>e===i),a=d.headers.findIndex(e=>e===l),r=d.headers.findIndex(e=>e===c);if(e>=0&&a>=0&&r>=0){let t={},s=new Set,o=new Set;Array.isArray(d.rows)&&d.rows.forEach(n=>{let i=String(n[e]??""),l=String(n[a]??""),c=parseFloat(String(n[r]??"0"))||0;s.add(i),o.add(l),t[i]||(t[i]={}),t[i][l]||(t[i][l]=0),t[i][l]+=c});let n=["",...Array.from(o)],m=Array.from(s).map(e=>{let a=[e];return Array.from(o).forEach(r=>{let s=String(t[e]?.[r]||0);a.push(s)}),a});d.pivotTable={headers:n,rows:m,rowsField:i,columnsField:l,valuesField:c},u=`Tabela din\xe2mica criada com ${i} nas linhas, ${l} nas colunas e ${c} como valores`}}}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de tabela:",e),Error(`Falha ao executar opera\xe7\xe3o de tabela: ${e instanceof Error?e.message:String(e)}`)}}var eP=r(56292);process.env.SUPABASE_URL||console.warn("SUPABASE_URL n\xe3o est\xe1 configurada - usando NEXT_PUBLIC_SUPABASE_URL"),process.env.SUPABASE_SERVICE_ROLE_KEY||console.warn("SUPABASE_SERVICE_ROLE_KEY n\xe3o est\xe1 configurada - cliente admin n\xe3o estar\xe1 dispon\xedvel");let eB=(0,eP.eI)("https://eliuoignzzxnjkcmmtml.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk",{auth:{autoRefreshToken:!1,persistSession:!1,detectSessionInUrl:!1},db:{schema:"public"}}),eV=process.env.SUPABASE_SERVICE_ROLE_KEY?(0,eP.eI)(process.env.SUPABASE_URL||"https://eliuoignzzxnjkcmmtml.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"}}):null,eZ={EXCEL_FILES:"excel-files",EXPORTS:"exports",TEMPLATES:"templates",BACKUPS:"backups"};class eU{async uploadExcelFile(e,a,r,t={}){if(!eV)throw Error("Supabase admin client n\xe3o est\xe1 configurado");let s=t.bucket||this.defaultBucket,o=t.folder||`users/${a}/workbooks/${r}`,n=t.fileName||`workbook_${Date.now()}.xlsx`,i=`${o}/${n}`;try{let a=await this.validateBucketPermissions(s,t.isPublic);if(!a.isValid)throw Error(`Bucket validation failed: ${a.error}`);await this.ensureBucketExists(s,t.isPublic);let{data:r,error:o}=await eV.storage.from(s).upload(i,e,{upsert:t.upsert||!1,contentType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});if(o)throw o;let n=e instanceof File?e.size:Buffer.byteLength(e),l={path:r.path,fullPath:r.fullPath,size:n};if(t.isPublic){let{data:e}=eV.storage.from(s).getPublicUrl(r.path);l.publicUrl=e.publicUrl}return l}catch(e){throw Error(`Erro no upload: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async downloadExcelFile(e,a=this.defaultBucket){if(!eV)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:r,error:t}=await eV.storage.from(a).download(e);if(t)throw t;return r}catch(e){throw Error(`Erro no download: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async getSignedUrl(e,a=3600,r=this.defaultBucket){if(!eV)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:t,error:s}=await eV.storage.from(r).createSignedUrl(e,a);if(s)throw s;return t.signedUrl}catch(e){throw Error(`Erro ao gerar URL: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async deleteFile(e,a=this.defaultBucket){if(!eV)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{error:r}=await eV.storage.from(a).remove([e]);if(r)throw r}catch(e){throw Error(`Erro ao deletar: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async listUserFiles(e,a=this.defaultBucket,r){if(!eV)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let t=r||`users/${e}`,{data:s,error:o}=await eV.storage.from(a).list(t,{limit:100,sortBy:{column:"updated_at",order:"desc"}});if(o)throw o;return s.map(e=>({name:e.name,size:e.metadata?.size||0,lastModified:e.updated_at||e.created_at,path:`${t}/${e.name}`}))}catch(e){throw Error(`Erro ao listar arquivos: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}async createBackup(e,a,r){let t=JSON.stringify({workbook:e,timestamp:new Date().toISOString(),userId:a,workbookId:r}),s=Buffer.from(t,"utf-8"),o=`backup_${r}_${Date.now()}.json`;return this.uploadExcelFile(s,a,r,{bucket:eZ.BACKUPS,fileName:o,folder:`backups/${a}/${r}`})}async validateBucketPermissions(e,a=!1){if(!eV)return{isValid:!1,error:"Supabase admin client n\xe3o est\xe1 configurado"};try{let{data:a,error:r}=await eV.storage.listBuckets();if(r)return{isValid:!1,error:`Sem permiss\xe3o para listar buckets: ${r.message}`};if(a?.some(a=>a.name===e))try{let a=`test_permissions_${Date.now()}.txt`,{error:r}=await eV.storage.from(e).upload(a,"test",{upsert:!0});if(r)return{isValid:!1,error:`Sem permiss\xe3o de escrita no bucket ${e}: ${r.message}`};return await eV.storage.from(e).remove([a]),{isValid:!0}}catch(e){return{isValid:!1,error:`Erro ao testar permiss\xf5es do bucket: ${e}`}}else{let e=`test_permissions_${Date.now()}`,{error:a}=await eV.storage.createBucket(e,{public:!1});if(a)return{isValid:!1,error:`Sem permiss\xe3o para criar buckets: ${a.message}`};return await eV.storage.deleteBucket(e),{isValid:!0}}}catch(e){return{isValid:!1,error:`Erro na valida\xe7\xe3o de permiss\xf5es: ${e}`}}}async ensureBucketExists(e,a=!1){if(!eV)throw Error("Supabase admin client n\xe3o est\xe1 configurado");try{let{data:r}=await eV.storage.listBuckets();if(!r?.some(a=>a.name===e)){let{error:r}=await eV.storage.createBucket(e,{public:a,allowedMimeTypes:["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/json","text/csv"],fileSizeLimit:52428800});if(r)throw r}}catch(a){console.warn(`Aviso: N\xe3o foi poss\xedvel verificar/criar bucket ${e}:`,a)}}async getStorageStats(e){let a={totalFiles:0,totalSize:0,bucketStats:{}};try{for(let r of Object.values(eZ)){let t=await this.listUserFiles(e,r),s=t.reduce((e,a)=>e+a.size,0);a.bucketStats[r]={files:t.length,size:s},a.totalFiles+=t.length,a.totalSize+=s}}catch(e){console.warn("Erro ao obter estat\xedsticas de storage:",e)}return a}constructor(){this.defaultBucket=eZ.EXCEL_FILES}}let eq=new eU;class eH{async createBackup(e){try{let a=e.version||this.generateVersion(),r=new Date().toISOString(),t={workbook:e.data,metadata:{workbookId:e.workbookId,userId:e.userId,version:a,timestamp:r,description:e.description||`Backup autom\xe1tico ${a}`},checksum:await this.calculateChecksum(e.data)},s=JSON.stringify(t,null,2),o=Buffer.from(s,"utf-8"),n=`backup_${e.workbookId}_${a}_${Date.now()}.json`,i=await eq.uploadExcelFile(o,e.userId,e.workbookId,{bucket:"backups",fileName:n,folder:`backups/${e.userId}/${e.workbookId}`,upsert:!1});return en.logger.info("Backup criado com sucesso",{workbookId:e.workbookId,userId:e.userId,version:a,size:i.size,path:i.path}),!1!==e.autoCleanup&&await this.cleanupOldBackups(e.workbookId,e.userId,e.maxVersions||this.maxVersionsDefault),{success:!0,backupId:i.path,version:a,size:i.size}}catch(r){let a=r instanceof Error?r.message:"Erro desconhecido";return en.logger.error("Erro ao criar backup",{workbookId:e.workbookId,userId:e.userId,error:a}),{success:!1,backupId:"",version:"",size:0,error:a}}}async listBackups(e,a){try{let r=[];for(let t of[])try{let s={metadata:{workbookId:e,userId:a,version:"1.0",timestamp:new Date().toISOString(),description:"Backup"},checksum:"mock"};r.push({id:t.name,workbookId:s.metadata.workbookId,userId:s.metadata.userId,version:s.metadata.version,timestamp:s.metadata.timestamp,size:t.metadata?.size||0,description:s.metadata.description,checksum:s.checksum})}catch(e){en.logger.warn("Erro ao processar backup",{fileName:t.name,error:e instanceof Error?e.message:"Erro desconhecido"})}return r.sort((e,a)=>new Date(a.timestamp).getTime()-new Date(e.timestamp).getTime())}catch(r){return en.logger.error("Erro ao listar backups",{workbookId:e,userId:a,error:r instanceof Error?r.message:"Erro desconhecido"}),[]}}async restoreBackup(e,a,r){try{let t={workbook:{},metadata:{workbookId:a,userId:r,version:"1.0"},checksum:"mock"};if(await this.calculateChecksum(t.workbook)!==t.checksum)throw Error("Backup corrompido: checksum n\xe3o confere");if(t.metadata.workbookId!==a||t.metadata.userId!==r)throw Error("Backup n\xe3o pertence ao usu\xe1rio ou workbook especificado");return en.logger.info("Backup restaurado com sucesso",{backupId:e,workbookId:a,userId:r,version:t.metadata.version}),t.workbook}catch(t){throw en.logger.error("Erro ao restaurar backup",{backupId:e,workbookId:a,userId:r,error:t instanceof Error?t.message:"Erro desconhecido"}),t}}async cleanupOldBackups(e,a,r){try{let t=await this.listBackups(e,a);if(t.length<=r)return;let s=t.slice(r);for(let r of s)try{await eq.deleteFile("backups",r.id),en.logger.info("Backup antigo removido",{backupId:r.id,workbookId:e,userId:a,version:r.version})}catch(e){en.logger.warn("Erro ao remover backup antigo",{backupId:r.id,error:e instanceof Error?e.message:"Erro desconhecido"})}en.logger.info("Limpeza de backups conclu\xedda",{workbookId:e,userId:a,removedCount:s.length,remainingCount:r})}catch(r){en.logger.error("Erro na limpeza de backups",{workbookId:e,userId:a,error:r instanceof Error?r.message:"Erro desconhecido"})}}generateVersion(){let e=new Date,a=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0"),s=String(e.getHours()).padStart(2,"0"),o=String(e.getMinutes()).padStart(2,"0"),n=String(e.getSeconds()).padStart(2,"0");return`v${a}${r}${t}_${s}${o}${n}`}async calculateChecksum(e){let a=JSON.stringify(e),r=new TextEncoder().encode(a);if("undefined"!=typeof crypto&&crypto.subtle)return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",r))).map(e=>e.toString(16).padStart(2,"0")).join("");let t=0;for(let e=0;e<a.length;e++)t=(t<<5)-t+a.charCodeAt(e),t&=t;return Math.abs(t).toString(16)}constructor(){this.maxVersionsDefault=10,this.cleanupIntervalHours=24}}let eG=new eH;class eX{async compressData(e,a={}){let r=performance.now(),t={...this.defaultOptions,...a},s=this.toBuffer(e),o=s.length;if(o<t.threshold)return{compressed:!1,originalSize:o,compressedSize:o,compressionRatio:1,data:s,processingTime:performance.now()-r};try{let e;switch(t.format){case"gzip":e=await this.compressGzip(s,t.level);break;case"deflate":e=await this.compressDeflate(s,t.level);break;case"brotli":e=await this.compressBrotli(s,t.level);break;default:throw Error(`Formato de compress\xe3o n\xe3o suportado: ${t.format}`)}let a=e.length,n=o/a,i=performance.now()-r;return en.logger.info("Compress\xe3o aplicada",{originalSize:o,compressedSize:a,compressionRatio:n.toFixed(2),format:t.format,level:t.level,processingTime:Math.round(i)}),{compressed:!0,originalSize:o,compressedSize:a,compressionRatio:n,data:e,format:t.format,processingTime:i}}catch(e){return en.logger.error("Erro na compress\xe3o",{originalSize:o,format:t.format,error:e instanceof Error?e.message:"Erro desconhecido"}),{compressed:!1,originalSize:o,compressedSize:o,compressionRatio:1,data:s,processingTime:performance.now()-r}}}async decompressData(e,a){let r=this.toBuffer(e);try{switch(a){case"gzip":return await this.decompressGzip(r);case"deflate":return await this.decompressDeflate(r);case"brotli":return await this.decompressBrotli(r);default:throw Error(`Formato de descompress\xe3o n\xe3o suportado: ${a}`)}}catch(e){throw en.logger.error("Erro na descompress\xe3o",{format:a,dataSize:r.length,error:e instanceof Error?e.message:"Erro desconhecido"}),e}}analyzeCompressionStrategy(e){let a=this.toBuffer(e),r=a.length,t=a.slice(0,Math.min(1024,r)),s=this.calculateEntropy(t);return r<51200?{level:1,format:"deflate"}:r<512e3?{level:s>.8?3:6,format:"gzip"}:r<5242880?{level:s>.8?6:9,format:"gzip"}:{level:9,format:"brotli"}}async compressGzip(e,a){if("undefined"!=typeof CompressionStream){let a=new CompressionStream("gzip"),r=a.writable.getWriter(),t=a.readable.getReader();r.write(e),r.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await t.read();o=a,e&&s.push(e)}return Buffer.concat(s.map(e=>Buffer.from(e)))}{let t=r(59796);return new Promise((r,s)=>{t.gzip(e,{level:a},(e,a)=>{e?s(e):r(a)})})}}async decompressGzip(e){if("undefined"!=typeof DecompressionStream){let a=new DecompressionStream("gzip"),r=a.writable.getWriter(),t=a.readable.getReader();r.write(e),r.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await t.read();o=a,e&&s.push(e)}return Buffer.concat(s.map(e=>Buffer.from(e)))}{let a=r(59796);return new Promise((r,t)=>{a.gunzip(e,(e,a)=>{e?t(e):r(a)})})}}async compressDeflate(e,a){if("undefined"!=typeof CompressionStream){let a=new CompressionStream("deflate"),r=a.writable.getWriter(),t=a.readable.getReader();r.write(e),r.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await t.read();o=a,e&&s.push(e)}return Buffer.concat(s.map(e=>Buffer.from(e)))}{let t=r(59796);return new Promise((r,s)=>{t.deflate(e,{level:a},(e,a)=>{e?s(e):r(a)})})}}async decompressDeflate(e){if("undefined"!=typeof DecompressionStream){let a=new DecompressionStream("deflate"),r=a.writable.getWriter(),t=a.readable.getReader();r.write(e),r.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await t.read();o=a,e&&s.push(e)}return Buffer.concat(s.map(e=>Buffer.from(e)))}{let a=r(59796);return new Promise((r,t)=>{a.inflate(e,(e,a)=>{e?t(e):r(a)})})}}async compressBrotli(e,a){return this.compressGzip(e,a)}async decompressBrotli(e){return this.decompressGzip(e)}toBuffer(e){if(Buffer.isBuffer(e))return e;if(e instanceof Uint8Array)return Buffer.from(e);if("string"==typeof e)return Buffer.from(e,"utf-8");throw Error("Tipo de dados n\xe3o suportado para compress\xe3o")}calculateEntropy(e){let a=new Map;for(let r of e)a.set(r,(a.get(r)||0)+1);let r=0,t=e.length;for(let e of a.values()){let a=e/t;r-=a*Math.log2(a)}return r/8}constructor(){this.defaultOptions={level:6,threshold:102400,format:"gzip",chunkSize:65536}}}let eJ=new eX;class eQ{async createVersion(e,a,r,t,s,o){try{let n=this.generateVersionNumber(),i=new Date().toISOString(),l=[];if(o){let t=await this.getVersionData(e,a,o);l=await this.calculateChanges(t,r)}let c=await eJ.compressData(JSON.stringify(r),eJ.analyzeCompressionStrategy(JSON.stringify(r))),d=await eG.createBackup({workbookId:e,userId:a,data:c.compressed?{compressed:!0,format:c.format,data:Array.from(c.data)}:r,version:n,description:`Vers\xe3o ${n}: ${t}`,autoCleanup:!1});if(!d.success)throw Error(`Erro ao criar backup da vers\xe3o: ${d.error}`);let u={id:d.backupId,workbookId:e,userId:a,version:n,timestamp:i,description:t,author:s,parentVersion:o||void 0,changes:l,size:d.size,compressed:c.compressed};return await this.saveVersionMetadata(u),await this.cleanupOldVersions(e,a),en.logger.info("Nova vers\xe3o criada",{workbookId:e,userId:a,version:n,changesCount:l.length,size:d.size,compressed:c.compressed}),u}catch(r){throw en.logger.error("Erro ao criar vers\xe3o",{workbookId:e,userId:a,error:r instanceof Error?r.message:"Erro desconhecido"}),r}}async listVersions(e,a){try{let r=await eG.listBackups(e,a),t=[];for(let e of r)try{let a=await this.getVersionMetadata(e.id);a&&t.push(a)}catch(a){en.logger.warn("Erro ao carregar metadata da vers\xe3o",{backupId:e.id,error:a instanceof Error?a.message:"Erro desconhecido"})}return t.sort((e,a)=>new Date(a.timestamp).getTime()-new Date(e.timestamp).getTime())}catch(r){return en.logger.error("Erro ao listar vers\xf5es",{workbookId:e,userId:a,error:r instanceof Error?r.message:"Erro desconhecido"}),[]}}async getVersionData(e,a,r){try{let t=(await this.listVersions(e,a)).find(e=>e.version===r);if(!t)throw Error(`Vers\xe3o ${r} n\xe3o encontrada`);let s=await eG.restoreBackup(t.id,e,a);if(t.compressed&&"object"==typeof s&&null!==s&&s.compressed){let e=Buffer.from(s.data),a=await eJ.decompressData(e,s.format);return JSON.parse(a.toString())}return s}catch(t){throw en.logger.error("Erro ao obter dados da vers\xe3o",{workbookId:e,userId:a,version:r,error:t instanceof Error?t.message:"Erro desconhecido"}),t}}async calculateDiff(e,a,r,t){try{let[s,o]=await Promise.all([this.getVersionData(e,a,r),this.getVersionData(e,a,t)]),n=await this.calculateChanges(s,o),i=n.filter(e=>"add"===e.action),l=n.filter(e=>"update"===e.action),c=n.filter(e=>"delete"===e.action);return{added:i,modified:l,deleted:c,summary:{totalChanges:n.length,cellChanges:n.filter(e=>"cell"===e.type).length,sheetChanges:n.filter(e=>"sheet"===e.type).length,structureChanges:n.filter(e=>"structure"===e.type).length}}}catch(s){throw en.logger.error("Erro ao calcular diff",{workbookId:e,userId:a,version1:r,version2:t,error:s instanceof Error?s.message:"Erro desconhecido"}),s}}async mergeVersions(e,a,r,t,s,o){try{let[n,i,l]=await Promise.all([this.getVersionData(e,a,r),this.getVersionData(e,a,t),this.getVersionData(e,a,s)]),c=(await this.detectConflicts(n,i,l)).map(e=>{let a=o?.[e.location];return a?{...e,resolution:"string"==typeof a?a:"manual"}:e}),d=c.filter(e=>!e.resolution);if(d.length>0)return{success:!1,mergedData:null,conflicts:d,version:""};let u=await this.performMerge(n,i,l,c),m=await this.createVersion(e,a,u,`Merge de ${t} e ${s}`,"Sistema de Merge",r);return{success:!0,mergedData:u,conflicts:[],version:m.version}}catch(o){throw en.logger.error("Erro no merge de vers\xf5es",{workbookId:e,userId:a,baseVersion:r,version1:t,version2:s,error:o instanceof Error?o.message:"Erro desconhecido"}),o}}async calculateChanges(e,a){let r=[],t=new Date().toISOString();try{let s=JSON.stringify(e,null,2),o=JSON.stringify(a,null,2);s!==o&&r.push({type:"structure",action:"update",location:"workbook",oldValue:e,newValue:a,timestamp:t})}catch(e){en.logger.warn("Erro ao calcular mudan\xe7as detalhadas",{error:e instanceof Error?e.message:"Erro desconhecido"})}return r}async detectConflicts(e,a,r){let t=[];try{let s=JSON.stringify(e),o=JSON.stringify(a),n=JSON.stringify(r);o!==s&&n!==s&&o!==n&&t.push({location:"workbook",type:"structure",baseValue:e,version1Value:a,version2Value:r})}catch(e){en.logger.warn("Erro ao detectar conflitos",{error:e instanceof Error?e.message:"Erro desconhecido"})}return t}async performMerge(e,a,r,t){for(let e of t){if("version1"===e.resolution)break;if("version2"===e.resolution)return r}return a}generateVersionNumber(){let e=new Date().getTime();return`${e}.${Math.floor(1e3*Math.random())}`}async saveVersionMetadata(e){en.logger.debug("Metadata da vers\xe3o salva",{version:e.version})}async getVersionMetadata(e){return null}async cleanupOldVersions(e,a){try{let r=await this.listVersions(e,a);if(r.length>this.maxVersionHistory)for(let t of r.slice(this.maxVersionHistory))try{en.logger.info("Vers\xe3o antiga removida",{workbookId:e,userId:a,version:t.version})}catch(e){en.logger.warn("Erro ao remover vers\xe3o antiga",{version:t.version,error:e instanceof Error?e.message:"Erro desconhecido"})}}catch(r){en.logger.error("Erro na limpeza de vers\xf5es antigas",{workbookId:e,userId:a,error:r instanceof Error?r.message:"Erro desconhecido"})}}constructor(){this.maxVersionHistory=50}}let eY=new eQ;class eW{async processLargeDataset(e,a,r={},t){let s=performance.now(),o={...this.defaultOptions,...r},n=[],i=[],l=this.estimateDataSize(e);l>1048576*o.maxMemoryUsage&&n.push(`Dataset muito grande (${Math.round(l/1024/1024)}MB). Considere dividir em partes menores.`);let c=this.analyzeAndOptimize(e,o);i.push(...c.applied);let d=this.chunkData(e,c.chunkSize),u=[],m=0,p=0;en.logger.info("Iniciando processamento otimizado",{totalRows:e.length,totalChunks:d.length,chunkSize:c.chunkSize,estimatedSize:Math.round(l/1024/1024)+"MB",optimizations:i});for(let e=0;e<d.length;e++){let r=d[e];if(!r)continue;let i=this.generateChunkKey(r);if(o.enableCache){let a=this.getFromCache(i);if(a){u.push(a),m++,t&&t((e+1)/d.length*100,{processingTime:performance.now()-s,chunksProcessed:e+1,cacheHits:m,cacheMisses:p});continue}p++}this.currentMemoryUsage>838860.8*o.maxMemoryUsage&&(await this.cleanupMemory(),n.push("Limpeza de mem\xf3ria executada durante processamento"));try{let n=await a(r,e,d.length);u.push(n),o.enableCache&&this.saveToCache(i,n),t&&t((e+1)/d.length*100,{processingTime:performance.now()-s,chunksProcessed:e+1,cacheHits:m,cacheMisses:p}),e%10==0&&await this.yield()}catch(a){throw en.logger.error("Erro no processamento do chunk",{chunkIndex:e,chunkSize:r?.length||0,error:a instanceof Error?a.message:"Erro desconhecido"}),a}}let h=performance.now()-s,x={processingTime:h,memoryUsage:this.currentMemoryUsage,chunksProcessed:d.length,cacheHits:m,cacheMisses:p,optimizationsApplied:i};return en.logger.info("Processamento conclu\xeddo",{totalTime:Math.round(h),chunksProcessed:d.length,cacheEfficiency:m/(m+p)*100,memoryUsage:Math.round(this.currentMemoryUsage/1024/1024)+"MB"}),{data:u,metrics:x,warnings:n}}createLazyLoader(e,a=100){let r=Math.ceil(e.length/a);return{loadPage:async t=>{if(t<0||t>=r)throw Error(`P\xe1gina ${t} inv\xe1lida. Total de p\xe1ginas: ${r}`);let s=t*a,o=Math.min(s+a,e.length);return e.length>1e4&&await this.yield(),e.slice(s,o)},getTotalPages:()=>r,getPageInfo:r=>{let t=r*a,s=Math.min(t+a,e.length);return{start:t,end:s,size:s-t}}}}createSearchIndex(e,a){let r=new Map;for(let t=0;t<e.length;t++){let s=e[t];for(let e of a)if(s&&e<s.length)for(let a of String(s[e]).toLowerCase().split(/\s+/))a.length>2&&(r.has(a)||r.set(a,[]),r.get(a).push(t))}return r}searchWithIndex(e,a,r){let t=a.toLowerCase().split(/\s+/),s=new Set;if(t.length>0&&t[0]){s=new Set(e.get(t[0])||[]);for(let a=1;a<t.length;a++){let r=t[a];if(r){let a=new Set(e.get(r)||[]);s=new Set([...s].filter(e=>a.has(e)))}}}return Array.from(s).slice(0,1e3).map(e=>({rowIndex:e,row:r[e]||[]}))}analyzeAndOptimize(e,a){let r=[],t={...a},s=e.length,o=e.length>0?this.estimateDataSize(e)/e.length:0;return s>5e4?(t.chunkSize=Math.min(500,t.chunkSize),r.push("Chunk size reduzido para datasets grandes")):s>1e4&&(t.chunkSize=Math.min(1e3,t.chunkSize),r.push("Chunk size otimizado para datasets m\xe9dios")),o>1024&&(t.chunkSize=Math.min(t.chunkSize,200),r.push("Chunk size reduzido para linhas grandes")),s>1e3&&!t.enableCache&&(t.enableCache=!0,r.push("Cache habilitado automaticamente")),s>1e5&&!t.enableVirtualization&&(t.enableVirtualization=!0,r.push("Virtualiza\xe7\xe3o habilitada automaticamente")),{...t,applied:r}}chunkData(e,a){let r=[];for(let t=0;t<e.length;t+=a)r.push(e.slice(t,t+a));return r}estimateDataSize(e){if(0===e.length)return 0;let a=Math.min(100,e.length),r=0;for(let t=0;t<a;t++){let a=e[t];if(a)for(let e of a)"string"==typeof e?r+=2*e.length:"number"==typeof e?r+=8:e instanceof Date?r+=8:r+=2*JSON.stringify(e).length}return r/a*e.length}generateChunkKey(e){let a=JSON.stringify(e.slice(0,3)),r=0;for(let e=0;e<a.length;e++)r=(r<<5)-r+a.charCodeAt(e),r&=r;return`chunk_${Math.abs(r)}_${e.length}`}getFromCache(e){let a=this.cache.get(e);return a?Date.now()-a.timestamp>this.cacheMaxAge?(this.cache.delete(e),null):a.data:null}saveToCache(e,a){let r=2*JSON.stringify(a).length;if(!(r>.1*this.cacheMaxSize)){for(;this.currentMemoryUsage+r>this.cacheMaxSize&&this.cache.size>0;){let e=this.cache.keys().next().value;if(e){let a=this.cache.get(e);a&&(this.currentMemoryUsage-=a.size),this.cache.delete(e)}else break}this.cache.set(e,{data:a,timestamp:Date.now(),size:r}),this.currentMemoryUsage+=r}}async cleanupMemory(){let e=Date.now(),a=0;for(let[r,t]of this.cache.entries())e-t.timestamp>.5*this.cacheMaxAge&&(this.currentMemoryUsage-=t.size,this.cache.delete(r),a++);"undefined"!=typeof global&&global.gc&&global.gc(),en.logger.debug("Limpeza de mem\xf3ria executada",{itemsRemoved:a,currentMemoryUsage:Math.round(this.currentMemoryUsage/1024/1024)+"MB",cacheSize:this.cache.size})}async yield(){return new Promise(e=>setTimeout(e,0))}getCacheStats(){let e=Date.now();for(let a of this.cache.values())e=Math.min(e,a.timestamp);return{size:this.cache.size,memoryUsage:this.currentMemoryUsage,hitRate:0,oldestItem:Date.now()-e}}constructor(){this.cache=new Map,this.cacheMaxSize=104857600,this.cacheMaxAge=18e5,this.currentMemoryUsage=0,this.defaultOptions={chunkSize:1e3,enableCache:!0,enableLazyLoading:!0,enableVirtualization:!0,maxMemoryUsage:512,enableProgressCallback:!0}}}let eK=new eW;function e0(){let[e,a]=(0,N.useState)(!1);return{isLoading:e,importExcel:async(e,r={})=>{if(!e)return null;let t=r.maxSize||10485760;a(!0);let s=A.toast.loading(`Processando arquivo ${e.name}...`);try{let a;if(!function(e){if(["application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel.sheet.binary.macroEnabled.12","application/vnd.ms-excel.sheet.macroEnabled.12"].includes(e.type))return!0;let a=e.name.split(".").pop()?.toLowerCase();return"xls"===a||"xlsx"===a}(e))throw Error("Formato inv\xe1lido: envie um arquivo Excel (.xlsx ou .xls)");if(e.size>t)throw Error(`Arquivo muito grande: o tamanho m\xe1ximo \xe9 ${(t/1048576).toFixed(0)}MB`);if(r.onProgress?.(10),r.enablePerformanceOptimization&&e.size>1048576){let t=await e.arrayBuffer(),s=new Uint8Array(t);a=(await eK.processLargeDataset([Array.from(s)],async a=>{if(a[0]&&Array.isArray(a[0])){let r=new File([new Uint8Array(a[0])],e.name,{type:e.type});return await eI(r)}return[]},{chunkSize:1e3,enableCache:!0,enableLazyLoading:!0},e=>r.onProgress?.(20+.3*e))).data.flat()}else a=await eI(e);if(r.onProgress?.(50),!a||0===a.length)throw Error("O arquivo n\xe3o cont\xe9m dados v\xe1lidos");if(r.validateSchema&&r.template&&(await e1(a,r.template),r.onProgress?.(70)),r.transformData&&r.template&&(await e2(a,r.template),r.onProgress?.(80)),r.enableBackup&&r.workbookId&&r.userId){try{let t=await eG.createBackup({workbookId:r.workbookId,userId:r.userId,data:{fileName:e.name,sheets:a},description:`Backup autom\xe1tico de importa\xe7\xe3o - ${e.name}`,autoCleanup:!0});t.success&&A.toast.success("Backup autom\xe1tico criado",{description:`Vers\xe3o ${t.version} salva com seguran\xe7a`,duration:2e3})}catch(e){console.warn("Erro ao criar backup autom\xe1tico:",e)}r.onProgress?.(90)}A.toast.success(`${e.name} carregado com sucesso!`,{id:s,duration:3e3}),r.onProgress?.(100),r.trackAnalytics;let o={fileName:e.name,sheets:a};return r.onSuccess&&r.onSuccess(o),o}catch(e){return console.error("Erro ao processar arquivo Excel:",e),A.toast.error("Erro ao importar arquivo",{id:s,description:e instanceof Error?e.message:"N\xe3o foi poss\xedvel processar o arquivo. Tente novamente.",duration:4e3}),null}finally{a(!1)}},exportExcel:async(e,r,t="xlsx",s={})=>{a(!0);let o=r.replace(/[^a-z0-9]/gi,"_").toLowerCase(),n=Date.now(),i=`${o}_${n}`,l=A.toast.loading(`Preparando exporta\xe7\xe3o ${t.toUpperCase()}...`);try{if(!e||0===e.length)throw Error("N\xe3o h\xe1 dados para exportar");if("xlsx"===t){let a=await eF(e,r);if(s.enableCompression&&a.size>1048576)try{let e=await a.arrayBuffer(),r=await eJ.compressData(new Uint8Array(e),eJ.analyzeCompressionStrategy(new Uint8Array(e)));r.compressed&&(a=new Blob([r.data],{type:a.type}),A.toast.success("Arquivo comprimido",{description:`Tamanho reduzido em ${Math.round((1-r.compressionRatio)*100)}%`,duration:2e3}))}catch(e){console.warn("Erro na compress\xe3o, usando arquivo original:",e)}if(s.enableVersioning&&s.workbookId&&s.userId)try{let a=await eY.createVersion(s.workbookId,s.userId,{fileName:r,sheets:e},s.versionDescription||`Exporta\xe7\xe3o ${t.toUpperCase()} - ${r}`,s.author||"Usu\xe1rio");A.toast.success("Nova vers\xe3o criada",{description:`Vers\xe3o ${a.version} salva`,duration:2e3})}catch(e){console.warn("Erro ao criar vers\xe3o:",e)}(function(e,a){let r=window.URL.createObjectURL(e),t=document.createElement("a");t.href=r,t.download=a.endsWith(".xlsx")?a:`${a}.xlsx`,document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(r),document.body.removeChild(t)})(a,`${i}.xlsx`),s.trackAnalytics}else"csv"===t&&(function(e,a){if(!e||0===e.length)throw Error("N\xe3o h\xe1 dados para exportar");let r=e[0];if(!r||!r.data)throw Error("Planilha sem dados");let t=r.data,s=e=>{if(null==e||""===e)return"";let a=String(e);return a.includes(",")||a.includes('"')||a.includes("\n")?`"${a.replace(/"/g,'""')}"`:a},o="";if(Array.isArray(t))o=t.map(e=>Array.isArray(e)?e.map(s).join(","):Object.values(e).map(s).join(",")).join("\n");else if("object"==typeof t&&null!==t){let e=[];Object.entries(t).forEach(([a,r])=>{let t=a.match(/([A-Z]+)([0-9]+)/);if(t){let a=ea(t,1),s=parseInt(ea(t,2),10),o=0;for(let e=0;e<a.length;e++)o=26*o+a.charCodeAt(e)-65+1;for(o--;e.length<=s-1;)e.push([]);if(s>0){for(;e.length<s;)e.push([]);Array.isArray(e[s-1])||(e[s-1]=[]),e[s-1][o]=r}}}),o=e.filter(e=>Array.isArray(e)).map(e=>e.map(e=>s(e??"")).join(",")).join("\n")}let n=new Blob([o],{type:"text/csv;charset=utf-8;"}),i=URL.createObjectURL(n),l=document.createElement("a");l.setAttribute("href",i),l.setAttribute("download",`${a}.csv`),l.style.visibility="hidden",document.body.appendChild(l),l.click(),document.body.removeChild(l)}(e,i),s.trackAnalytics);return A.toast.success(`Exporta\xe7\xe3o ${t.toUpperCase()} conclu\xedda`,{id:l,description:`Arquivo "${i}.${t}" baixado com sucesso!`,duration:3e3}),!0}catch(e){return console.error(`Erro ao exportar ${t}:`,e),A.toast.error(`Erro na exporta\xe7\xe3o ${t.toUpperCase()}`,{id:l,description:e instanceof Error?e.message:`N\xe3o foi poss\xedvel exportar para ${t.toUpperCase()}. Tente novamente.`,duration:4e3}),!1}finally{a(!1)}},listVersions:async(e,a)=>{try{return await eY.listVersions(e,a)}catch(e){return console.error("Erro ao listar vers\xf5es:",e),A.toast.error("Erro ao carregar vers\xf5es"),[]}},restoreVersion:async(e,r,t)=>{a(!0);try{let a=await eY.getVersionData(e,r,t);return A.toast.success(`Vers\xe3o ${t} restaurada com sucesso`),a}catch(e){return console.error("Erro ao restaurar vers\xe3o:",e),A.toast.error("Erro ao restaurar vers\xe3o"),null}finally{a(!1)}},listBackups:async(e,a)=>{try{return await eG.listBackups(e,a)}catch(e){return console.error("Erro ao listar backups:",e),A.toast.error("Erro ao carregar backups"),[]}},restoreBackup:async(e,r,t)=>{a(!0);try{let a=await eG.restoreBackup(e,r,t);return A.toast.success("Backup restaurado com sucesso"),a}catch(e){return console.error("Erro ao restaurar backup:",e),A.toast.error("Erro ao restaurar backup"),null}finally{a(!1)}},compareVersions:async(e,a,r,t)=>{try{return await eY.calculateDiff(e,a,r,t)}catch(e){return console.error("Erro ao comparar vers\xf5es:",e),A.toast.error("Erro ao comparar vers\xf5es"),null}},getCacheStats:()=>eK.getCacheStats()}}async function e1(e,a){let r={"financial-expenses":{Data:{required:!0,type:"date",description:"Data da transa\xe7\xe3o financeira"},Valor:{required:!0,type:"number",min:0,description:"Valor da transa\xe7\xe3o em reais"},Tipo:{required:!0,enum:["Receita","Despesa"],description:"Tipo da transa\xe7\xe3o"},Categoria:{required:!1,type:"string",maxLength:50,description:"Categoria da despesa/receita"},Descrição:{required:!1,type:"string",maxLength:200,description:"Descri\xe7\xe3o detalhada da transa\xe7\xe3o"}},"sales-data":{Data:{required:!0,type:"date",description:"Data da venda"},Produto:{required:!0,type:"string",minLength:2,maxLength:100,description:"Nome do produto vendido"},Quantidade:{required:!0,type:"number",min:1,description:"Quantidade vendida"},"Valor Unit\xe1rio":{required:!0,type:"number",min:0,description:"Pre\xe7o unit\xe1rio do produto"},Cliente:{required:!1,type:"string",maxLength:100,description:"Nome do cliente"},Vendedor:{required:!1,type:"string",maxLength:50,description:"Nome do vendedor respons\xe1vel"}},"employee-data":{Nome:{required:!0,type:"string",minLength:2,maxLength:100,description:"Nome completo do funcion\xe1rio"},Email:{required:!0,type:"email",description:"Email corporativo do funcion\xe1rio"},"Data Admiss\xe3o":{required:!0,type:"date",description:"Data de admiss\xe3o na empresa"},Cargo:{required:!0,type:"string",minLength:2,maxLength:50,description:"Cargo do funcion\xe1rio"},Salário:{required:!1,type:"number",min:0,description:"Sal\xe1rio base do funcion\xe1rio"},Departamento:{required:!1,type:"string",maxLength:50,description:"Departamento do funcion\xe1rio"}},"inventory-data":{Código:{required:!0,type:"string",pattern:"^[A-Z0-9]{3,10}$",description:"C\xf3digo \xfanico do produto"},Nome:{required:!0,type:"string",minLength:2,maxLength:100,description:"Nome do produto"},Quantidade:{required:!0,type:"number",min:0,description:"Quantidade em estoque"},Preço:{required:!0,type:"number",min:0,description:"Pre\xe7o unit\xe1rio do produto"},Categoria:{required:!1,type:"string",maxLength:50,description:"Categoria do produto"}},"project-tasks":{Tarefa:{required:!0,type:"string",minLength:5,maxLength:200,description:"Descri\xe7\xe3o da tarefa"},Responsável:{required:!0,type:"string",maxLength:50,description:"Pessoa respons\xe1vel pela tarefa"},"Data In\xedcio":{required:!0,type:"date",description:"Data de in\xedcio da tarefa"},"Data Fim":{required:!1,type:"date",description:"Data prevista para conclus\xe3o"},Status:{required:!0,enum:["N\xe3o Iniciado","Em Andamento","Conclu\xeddo","Cancelado"],description:"Status atual da tarefa"},Prioridade:{required:!1,enum:["Baixa","M\xe9dia","Alta","Cr\xedtica"],description:"Prioridade da tarefa"}}}[a]||{},t=[];for(let a of e){if(!Array.isArray(a.data))continue;if(0===a.data.length){t.push(`Planilha '${a.name}' est\xe1 vazia`);continue}let e=Array.isArray(a.data[0])?a.data[0]:Object.keys(a.data[0]||{});for(let s of Object.keys(r).filter(e=>r[e].required))e.includes(s)||t.push(`Coluna obrigat\xf3ria '${s}' n\xe3o encontrada na planilha '${a.name}'`);let o=a.data.slice(1);for(let a=0;a<o.length;a++){let n=o[a],i=a+2;for(let[a,o]of Object.entries(r)){let r=Array.isArray(n)?n[e.indexOf(a)]:n[a];if(o.required&&(null==r||""===r)){t.push(`Linha ${i}: Campo obrigat\xf3rio '${a}' est\xe1 vazio`);continue}if(null!=r&&""!==r){var s;"number"===o.type&&isNaN(Number(r))&&t.push(`Linha ${i}: Campo '${a}' deve ser um n\xfamero v\xe1lido`),"email"!==o.type||(s=String(r),/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))||t.push(`Linha ${i}: Campo '${a}' deve ser um email v\xe1lido`),"date"===o.type&&!function(e){if(!e)return!1;if(e instanceof Date)return!isNaN(e.getTime());let a=String(e);if(isNaN(new Date(a).getTime())){let e=a.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);if(e){let[,a,r,t]=e;if(a&&r&&t)return!isNaN(new Date(parseInt(t),parseInt(r)-1,parseInt(a)).getTime())}return!1}return!0}(r)&&t.push(`Linha ${i}: Campo '${a}' deve ser uma data v\xe1lida`),"url"===o.type&&!function(e){try{return new URL(e),!0}catch{return!1}}(String(r))&&t.push(`Linha ${i}: Campo '${a}' deve ser uma URL v\xe1lida`),o.enum&&!o.enum.includes(r)&&t.push(`Linha ${i}: Campo '${a}' deve ser um dos valores: ${o.enum.join(", ")}`),void 0!==o.min&&Number(r)<o.min&&t.push(`Linha ${i}: Campo '${a}' deve ser maior ou igual a ${o.min}`),void 0!==o.max&&Number(r)>o.max&&t.push(`Linha ${i}: Campo '${a}' deve ser menor ou igual a ${o.max}`),void 0!==o.minLength&&String(r).length<o.minLength&&t.push(`Linha ${i}: Campo '${a}' deve ter pelo menos ${o.minLength} caracteres`),void 0!==o.maxLength&&String(r).length>o.maxLength&&t.push(`Linha ${i}: Campo '${a}' deve ter no m\xe1ximo ${o.maxLength} caracteres`),o.pattern&&!new RegExp(o.pattern).test(String(r))&&t.push(`Linha ${i}: Campo '${a}' n\xe3o atende ao padr\xe3o esperado`)}}}}if(t.length>0)throw Error(`Erros de valida\xe7\xe3o encontrados:
${t.join("\n")}`)}async function e2(e,a){let r={"financial-expenses":{Valor:{type:"currency"},Data:{type:"date"}},"sales-data":{"Valor Unit\xe1rio":{type:"currency"},Total:{type:"currency"},Data:{type:"date"}},"employee-data":{Nome:{type:"uppercase"},Email:{type:"lowercase"},"Data Admiss\xe3o":{type:"date"}}}[a]||{};for(let a of e)Array.isArray(a.data)&&(a.data=a.data.map(e=>{let a={...e};for(let[e,t]of Object.entries(r))if(void 0!==a[e])switch(t.type){case"currency":a[e]=function(e){let a=Number(e);return isNaN(a)?String(e):new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(a)}(a[e]);break;case"date":a[e]=function(e){try{let a=new Date(e);if(isNaN(a.getTime()))return String(e);return a.toLocaleDateString("pt-BR")}catch{return String(e)}}(a[e]);break;case"uppercase":a[e]=String(a[e]).toUpperCase();break;case"lowercase":a[e]=String(a[e]).toLowerCase()}return a}))}var e3=r(37358),e4=r(24118),e5=r(13635),e9=r(32933);let e6=N.forwardRef(({className:e,...a},r)=>s.jsx(e5.fC,{ref:r,className:(0,T.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...a,children:s.jsx(e5.z$,{className:(0,T.cn)("flex items-center justify-center text-current"),children:s.jsx(e9.Z,{className:"h-4 w-4"})})}));e6.displayName=e5.fC.displayName;var e8=r(29280),e7=r(41190),ae=r(44794),aa=r(50384),ar=r(33261);let at=[{id:"xlsx",name:"Excel (.xlsx)",icon:s.jsx(n.Z,{className:"h-4 w-4"})},{id:"csv",name:"CSV (.csv)",icon:s.jsx(n.Z,{className:"h-4 w-4"})},{id:"json",name:"JSON (.json)",icon:s.jsx(n.Z,{className:"h-4 w-4"})},{id:"pdf",name:"PDF (.pdf)",icon:s.jsx(n.Z,{className:"h-4 w-4"})}];function as({open:e,onOpenChange:a,workbookId:r,onExport:t}){let[o,n]=(0,N.useState)(["xlsx"]),[i,l]=(0,N.useState)("immediate"),[c,d]=(0,N.useState)(""),[u,m]=(0,N.useState)(""),[p,h]=(0,N.useState)(),[x,f]=(0,N.useState)(!1),[g,y]=(0,N.useState)(!1),[v,w]=(0,N.useState)(!0),[b,A]=(0,N.useState)("{{workbook}}_{{date}}_{{format}}"),E=e=>{n(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},j=o.length>0&&("immediate"===i||c&&u);return s.jsx(e4.Vq,{open:e,onOpenChange:a,children:(0,s.jsxs)(e4.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[s.jsx(e4.fK,{children:(0,s.jsxs)(e4.$N,{className:"flex items-center gap-2",children:[s.jsx(Y.Z,{className:"h-5 w-5"}),"Export em Lote - Configura\xe7\xf5es Avan\xe7adas"]})}),(0,s.jsxs)(aa.mQ,{defaultValue:"formats",className:"space-y-6",children:[(0,s.jsxs)(aa.dr,{className:"grid w-full grid-cols-4",children:[s.jsx(aa.SP,{value:"formats",children:"Formatos"}),s.jsx(aa.SP,{value:"schedule",children:"Agendamento"}),s.jsx(aa.SP,{value:"options",children:"Op\xe7\xf5es"}),s.jsx(aa.SP,{value:"preview",children:"Preview"})]}),s.jsx(aa.nU,{value:"formats",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:s.jsx(U.ll,{className:"text-lg",children:"Selecionar Formatos de Export"})}),(0,s.jsxs)(U.aY,{children:[s.jsx("div",{className:"grid grid-cols-2 gap-4",children:at.map(e=>(0,s.jsxs)("div",{className:`flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ${o.includes(e.id)?"border-primary bg-primary/5":"border-border hover:bg-muted/50"}`,onClick:()=>E(e.id),children:[s.jsx(e6,{checked:o.includes(e.id),onCheckedChange:()=>E(e.id)}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,s.jsx("span",{className:"font-medium",children:e.name})]})]},e.id))}),0===o.length&&s.jsx(ar.bZ,{className:"mt-4",children:s.jsx(ar.X,{children:"Selecione pelo menos um formato para continuar."})})]})]})}),s.jsx(aa.nU,{value:"schedule",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:s.jsx(U.ll,{className:"text-lg",children:"Configurar Agendamento"})}),(0,s.jsxs)(U.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx(ae._,{children:"Tipo de Execu\xe7\xe3o"}),(0,s.jsxs)(e8.Ph,{value:i,onValueChange:e=>l(e),children:[s.jsx(e8.i4,{children:s.jsx(e8.ki,{})}),(0,s.jsxs)(e8.Bw,{children:[s.jsx(e8.Ql,{value:"immediate",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(J.Z,{className:"h-4 w-4"}),"Executar Agora"]})}),s.jsx(e8.Ql,{value:"scheduled",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(e3.Z,{className:"h-4 w-4"}),"Agendar Execu\xe7\xe3o"]})})]})]})]}),"scheduled"===i&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{htmlFor:"date",children:"Data"}),s.jsx(e7.I,{id:"date",type:"date",value:c,onChange:e=>d(e.target.value),min:new Date().toISOString().split("T")[0]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{htmlFor:"time",children:"Hor\xe1rio"}),s.jsx(e7.I,{id:"time",type:"time",value:u,onChange:e=>m(e.target.value)})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{children:"Recorr\xeancia (Opcional)"}),(0,s.jsxs)(e8.Ph,{value:p||"",onValueChange:e=>h(e),children:[s.jsx(e8.i4,{children:s.jsx(e8.ki,{placeholder:"Selecionar recorr\xeancia"})}),(0,s.jsxs)(e8.Bw,{children:[s.jsx(e8.Ql,{value:"",children:"Sem recorr\xeancia"}),s.jsx(e8.Ql,{value:"daily",children:"Di\xe1rio"}),s.jsx(e8.Ql,{value:"weekly",children:"Semanal"}),s.jsx(e8.Ql,{value:"monthly",children:"Mensal"})]})]})]})]})]})]})}),s.jsx(aa.nU,{value:"options",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:s.jsx(U.ll,{className:"text-lg",children:"Op\xe7\xf5es de Export"})}),(0,s.jsxs)(U.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:"compression",checked:x,onCheckedChange:e=>f(!0===e)}),s.jsx(ae._,{htmlFor:"compression",children:"Compactar arquivos em ZIP"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:"splitBySheets",checked:g,onCheckedChange:e=>y(!0===e)}),s.jsx(ae._,{htmlFor:"splitBySheets",children:"Separar por planilhas"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:"includeMetadata",checked:v,onCheckedChange:e=>w(!0===e)}),s.jsx(ae._,{htmlFor:"includeMetadata",children:"Incluir metadados"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{htmlFor:"naming",children:"Padr\xe3o de Nomenclatura"}),s.jsx(e7.I,{id:"naming",value:b,onChange:e=>A(e.target.value),placeholder:"{{workbook}}_{{date}}_{{format}}"}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Vari\xe1veis dispon\xedveis: ","{workbook}",", ","{date}",", ","{time}",", ","{format}",", ","{sheet}"]})]})]})]})}),s.jsx(aa.nU,{value:"preview",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:s.jsx(U.ll,{className:"text-lg",children:"Preview da Configura\xe7\xe3o"})}),s.jsx(U.aY,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[s.jsx(ae._,{className:"text-sm font-medium",children:"Formatos Selecionados:"}),s.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:o.map(e=>{let a=at.find(a=>a.id===e);return s.jsx($.C,{variant:"secondary",children:a?.name},e)})})]}),(0,s.jsxs)("div",{children:[s.jsx(ae._,{className:"text-sm font-medium",children:"Agendamento:"}),s.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"immediate"===i?"Executar imediatamente":`Agendado para ${c} \xe0s ${u}${p?` (${p})`:""}`})]}),(0,s.jsxs)("div",{children:[s.jsx(ae._,{className:"text-sm font-medium",children:"Op\xe7\xf5es:"}),(0,s.jsxs)("ul",{className:"text-sm text-muted-foreground mt-1 space-y-1",children:[x&&s.jsx("li",{children:"• Compacta\xe7\xe3o habilitada"}),g&&s.jsx("li",{children:"• Separar por planilhas"}),v&&s.jsx("li",{children:"• Incluir metadados"}),(0,s.jsxs)("li",{children:["• Padr\xe3o de nome: ",b]})]})]})]})})]})})]}),(0,s.jsxs)(e4.cN,{children:[s.jsx(S.Button,{variant:"outline",onClick:()=>a(!1),children:"Cancelar"}),s.jsx(S.Button,{onClick:()=>{t({formats:o,schedule:"scheduled"===i?{type:"scheduled",datetime:c&&u?new Date(`${c}T${u}`):void 0,recurring:p}:{type:"immediate"},compression:x,splitBySheets:g,includeMetadata:v,customNaming:b})},disabled:!j,children:"immediate"===i?"Exportar Agora":"Agendar Export"})]})]})})}var ao=r(89392),an=r(9015);function ai({open:e,onOpenChange:a,workbookId:r,workbookName:t,sheets:o,onExport:i}){let[l,c]=(0,N.useState)("xlsx"),[d,u]=(0,N.useState)(!0),[m,p]=(0,N.useState)(!0),[h,x]=(0,N.useState)(!0),[f,g]=(0,N.useState)(""),[y,v]=(0,N.useState)([50]),[w,b]=(0,N.useState)("portrait"),[A,E]=(0,N.useState)("A4"),[j,C]=(0,N.useState)([]),[k,O]=(0,N.useState)("1"),[R,T]=(0,N.useState)(""),[F,D]=(0,N.useState)("#3b82f6"),[_,L]=(0,N.useState)(!0),[$,M]=(0,N.useState)([12]),z=o.length>0&&o[0]&&Array.isArray(o[0].data)&&o[0].data.length>0?Object.keys(o[0].data[0]):[],P=e=>{C(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])};return s.jsx(e4.Vq,{open:e,onOpenChange:a,children:(0,s.jsxs)(e4.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[s.jsx(e4.fK,{children:(0,s.jsxs)(e4.$N,{className:"flex items-center gap-2",children:[s.jsx(Q.Z,{className:"h-5 w-5"}),"Op\xe7\xf5es Avan\xe7adas de Export - ",t]})}),(0,s.jsxs)(aa.mQ,{defaultValue:"general",className:"space-y-6",children:[(0,s.jsxs)(aa.dr,{className:"grid w-full grid-cols-4",children:[s.jsx(aa.SP,{value:"general",children:"Geral"}),s.jsx(aa.SP,{value:"filters",children:"Filtros"}),s.jsx(aa.SP,{value:"styling",children:"Estilo"}),s.jsx(aa.SP,{value:"security",children:"Seguran\xe7a"})]}),s.jsx(aa.nU,{value:"general",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:(0,s.jsxs)(U.ll,{className:"text-lg flex items-center gap-2",children:[s.jsx(n.Z,{className:"h-5 w-5"}),"Configura\xe7\xf5es Gerais"]})}),(0,s.jsxs)(U.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{children:"Formato de Export"}),(0,s.jsxs)(e8.Ph,{value:l,onValueChange:e=>c(e),children:[s.jsx(e8.i4,{children:s.jsx(e8.ki,{})}),(0,s.jsxs)(e8.Bw,{children:[s.jsx(e8.Ql,{value:"xlsx",children:"Excel (.xlsx)"}),s.jsx(e8.Ql,{value:"csv",children:"CSV (.csv)"}),s.jsx(e8.Ql,{value:"pdf",children:"PDF (.pdf)"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:"headers",checked:d,onCheckedChange:e=>u(!0===e)}),s.jsx(ae._,{htmlFor:"headers",children:"Incluir cabe\xe7alhos"})]}),"xlsx"===l&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:"formulas",checked:m,onCheckedChange:e=>p(!0===e)}),s.jsx(ae._,{htmlFor:"formulas",children:"Incluir f\xf3rmulas"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:"formatting",checked:h,onCheckedChange:e=>x(!0===e)}),s.jsx(ae._,{htmlFor:"formatting",children:"Incluir formata\xe7\xe3o"})]})]})]}),"pdf"===l&&(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{children:"Orienta\xe7\xe3o"}),(0,s.jsxs)(e8.Ph,{value:w,onValueChange:e=>b(e),children:[s.jsx(e8.i4,{children:s.jsx(e8.ki,{})}),(0,s.jsxs)(e8.Bw,{children:[s.jsx(e8.Ql,{value:"portrait",children:"Retrato"}),s.jsx(e8.Ql,{value:"landscape",children:"Paisagem"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{children:"Tamanho da P\xe1gina"}),(0,s.jsxs)(e8.Ph,{value:A,onValueChange:e=>E(e),children:[s.jsx(e8.i4,{children:s.jsx(e8.ki,{})}),(0,s.jsxs)(e8.Bw,{children:[s.jsx(e8.Ql,{value:"A4",children:"A4"}),s.jsx(e8.Ql,{value:"A3",children:"A3"}),s.jsx(e8.Ql,{value:"Letter",children:"Letter"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(ae._,{children:["N\xedvel de Compress\xe3o: ",y[0]||50,"%"]}),s.jsx("div",{className:"w-full",children:s.jsx("input",{type:"range",value:y[0]||50,onChange:e=>v([parseInt(e.target.value)]),max:100,min:0,step:10,className:"w-full"})})]})]})]})}),s.jsx(aa.nU,{value:"filters",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:(0,s.jsxs)(U.ll,{className:"text-lg flex items-center gap-2",children:[s.jsx(I.Z,{className:"h-5 w-5"}),"Filtros de Dados"]})}),(0,s.jsxs)(U.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{children:"Colunas a Exportar"}),s.jsx("div",{className:"grid grid-cols-3 gap-2 max-h-40 overflow-y-auto",children:z.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:`col-${e}`,checked:j.includes(e),onCheckedChange:()=>P(e)}),s.jsx(ae._,{htmlFor:`col-${e}`,className:"text-sm",children:e})]},e))}),0===j.length&&s.jsx("p",{className:"text-sm text-muted-foreground",children:"Nenhuma coluna selecionada = todas as colunas"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{htmlFor:"rowStart",children:"Linha Inicial"}),s.jsx(e7.I,{id:"rowStart",type:"number",value:k,onChange:e=>O(e.target.value),placeholder:"1",min:"1"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{htmlFor:"rowEnd",children:"Linha Final"}),s.jsx(e7.I,{id:"rowEnd",type:"number",value:R,onChange:e=>T(e.target.value),placeholder:"Todas",min:"1"})]})]})]})]})}),s.jsx(aa.nU,{value:"styling",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:(0,s.jsxs)(U.ll,{className:"text-lg flex items-center gap-2",children:[s.jsx(ao.Z,{className:"h-5 w-5"}),"Personaliza\xe7\xe3o de Estilo"]})}),(0,s.jsxs)(U.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{htmlFor:"headerColor",children:"Cor do Cabe\xe7alho"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(e7.I,{id:"headerColor",type:"color",value:F,onChange:e=>D(e.target.value),className:"w-16 h-10"}),s.jsx(e7.I,{value:F,onChange:e=>D(e.target.value),placeholder:"#3b82f6"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(e6,{id:"alternateRows",checked:_,onCheckedChange:e=>L(!0===e)}),s.jsx(ae._,{htmlFor:"alternateRows",children:"Linhas alternadas"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(ae._,{children:["Tamanho da Fonte: ",$[0]||12,"px"]}),s.jsx("div",{className:"w-full",children:s.jsx("input",{type:"range",value:$[0]||12,onChange:e=>M([parseInt(e.target.value)]),max:24,min:8,step:1,className:"w-full"})})]})]})]})}),s.jsx(aa.nU,{value:"security",className:"space-y-4",children:(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:(0,s.jsxs)(U.ll,{className:"text-lg flex items-center gap-2",children:[s.jsx(an.Z,{className:"h-5 w-5"}),"Configura\xe7\xf5es de Seguran\xe7a"]})}),(0,s.jsxs)(U.aY,{className:"space-y-4",children:["xlsx"===l&&(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(ae._,{htmlFor:"password",children:"Senha de Prote\xe7\xe3o (Opcional)"}),s.jsx(e7.I,{id:"password",type:"password",value:f,onChange:e=>g(e.target.value),placeholder:"Digite uma senha para proteger o arquivo"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Deixe em branco para n\xe3o proteger o arquivo"})]}),"xlsx"!==l&&s.jsx("p",{className:"text-sm text-muted-foreground",children:"Prote\xe7\xe3o por senha dispon\xedvel apenas para arquivos Excel (.xlsx)"})]})]})})]}),(0,s.jsxs)(e4.cN,{children:[s.jsx(S.Button,{variant:"outline",onClick:()=>a(!1),children:"Cancelar"}),s.jsx(S.Button,{onClick:()=>{i({format:l,includeHeaders:d,includeFormulas:m,includeFormatting:h,password:f||void 0,compression:y[0]||50,pageOrientation:"pdf"===l?w:void 0,pageSize:"pdf"===l?A:void 0,filters:j.length>0||k||R?{columns:j.length>0?j:z,rows:{start:parseInt(k)||1,end:parseInt(R)||999999}}:void 0,customStyles:{headerColor:F,alternateRows:_,fontSize:$[0]||12}})},children:"Exportar com Op\xe7\xf5es"})]})]})})}function al({workbookId:e,workbookName:a,sheets:r,variant:t="outline",size:o="sm",enableAdvancedExport:n=!1,allowBatchExport:i=!1}){let{exportExcel:l,isLoading:c}=e0(),[d,u]=(0,N.useState)(!1),[m,p]=(0,N.useState)(!1),h=async()=>{await l(r,a,"xlsx",{trackAnalytics:!0,workbookId:e})},x=async()=>{await l(r,a,"csv",{trackAnalytics:!0,workbookId:e})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(W.h_,{children:[s.jsx(W.$F,{asChild:!0,children:s.jsx(S.Button,{variant:t,size:o,disabled:!r||0===r.length||c,className:"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2",children:c?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Exportando..."]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(J.Z,{className:"h-4 w-4"}),"Exportar"]})})}),(0,s.jsxs)(W.AW,{align:"end",children:[(0,s.jsxs)(W.Xi,{onClick:()=>h(),children:[s.jsx(J.Z,{className:"h-4 w-4 mr-2"}),s.jsx("span",{children:"Exportar como Excel (.xlsx)"})]}),(0,s.jsxs)(W.Xi,{onClick:()=>x(),children:[s.jsx(J.Z,{className:"h-4 w-4 mr-2"}),s.jsx("span",{children:"Exportar como CSV (.csv)"})]}),n&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(W.VD,{}),(0,s.jsxs)(W.Xi,{onClick:()=>u(!0),children:[s.jsx(Q.Z,{className:"h-4 w-4 mr-2"}),s.jsx("span",{children:"Op\xe7\xf5es Avan\xe7adas"})]})]}),i&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(W.VD,{}),(0,s.jsxs)(W.Xi,{onClick:()=>p(!0),children:[s.jsx(Y.Z,{className:"h-4 w-4 mr-2"}),s.jsx("span",{children:"Export em Lote"})]})]})]})]}),n&&s.jsx(ai,{open:d,onOpenChange:u,workbookId:e,workbookName:a,sheets:r,onExport:e=>{u(!1)}}),i&&s.jsx(as,{open:m,onOpenChange:p,workbookId:e,onExport:e=>{p(!1)}})]})}var ac=r(45091),ad=r(62783),au=r(24061),am=r(3594),ap=r(77109);class ah{subscribeToWorkbook(e,a){let r=`workbook:${e}`;if(this.channels.has(r))return this.channels.get(r);let t=eB.channel(r).on("postgres_changes",{event:"*",schema:"public",table:"Workbook",filter:`id=eq.${e}`},e=>{a.onWorkbookChange&&a.onWorkbookChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Sheet",filter:`workbookId=eq.${e}`},e=>{a.onSheetChange&&a.onSheetChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Cell",filter:`sheetId=in.(${e})`},r=>{if(a.onCellChange&&r.new){let t=r.new;a.onCellChange({workbookId:e,sheetId:t.sheetId,cellAddress:t.address,value:t.value,userId:t.updatedBy||"unknown",timestamp:t.updatedAt||new Date().toISOString()})}}).subscribe();return this.channels.set(r,t),t}subscribeToUserPresence(e,a,r){let t=`presence:${e}`;if(this.presenceChannels.has(t))return this.presenceChannels.get(t);let s=eB.channel(t,{config:{presence:{key:a.id}}}).on("presence",{event:"sync"},()=>{Object.entries(s.presenceState()).forEach(([a,t])=>{let s=t[0],o=s.cursor;r({userId:a,userName:s.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...o&&{cursor:o}})})}).on("presence",{event:"join"},({key:a,newPresences:t})=>{let s=t[0],o=s.cursor;r({userId:a,userName:s.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...o&&{cursor:o}})}).on("presence",{event:"leave"},({key:a,leftPresences:t})=>{let s=t[0],o=s.cursor;r({userId:a,userName:s.name,workbookId:e,isOnline:!1,lastSeen:new Date().toISOString(),...o&&{cursor:o}})}).subscribe(async e=>{"SUBSCRIBED"===e&&await s.track({name:a.name,joinedAt:new Date().toISOString()})});return this.presenceChannels.set(t,s),s}async updateUserCursor(e,a){let r=`presence:${e}`,t=this.presenceChannels.get(r);t&&await t.track({cursor:a,lastActivity:new Date().toISOString()})}async broadcastCellChange(e,a){let r=`workbook:${e}`,t=this.channels.get(r);t&&await t.send({type:"broadcast",event:"cell_change",payload:{...a,timestamp:new Date().toISOString()}})}unsubscribeFromWorkbook(e){let a=`workbook:${e}`,r=this.channels.get(a);r&&(eB.removeChannel(r),this.channels.delete(a))}unsubscribeFromPresence(e){let a=`presence:${e}`,r=this.presenceChannels.get(a);r&&(eB.removeChannel(r),this.presenceChannels.delete(a))}unsubscribeAll(){this.channels.forEach(e=>{eB.removeChannel(e)}),this.channels.clear(),this.presenceChannels.forEach(e=>{eB.removeChannel(e)}),this.presenceChannels.clear()}getConnectionStatus(){return{connected:eB.realtime.isConnected(),activeChannels:this.channels.size,presenceChannels:this.presenceChannels.size}}async reconnectAll(){for(let[,e]of this.channels)e.subscribe();for(let[,e]of this.presenceChannels)e.subscribe()}constructor(){this.channels=new Map,this.presenceChannels=new Map}}let ax=new ah;function af(e){let{data:a}=(0,ap.useSession)(),[r,t]=(0,N.useState)(!1),[s,o]=(0,N.useState)(new Map),[n,i]=(0,N.useState)([]);(0,N.useRef)(null),(0,N.useCallback)(e=>{window.dispatchEvent(new CustomEvent("workbook-changed",{detail:e}))},[]),(0,N.useCallback)(e=>{window.dispatchEvent(new CustomEvent("sheet-changed",{detail:e}))},[]),(0,N.useCallback)(e=>{i(a=>[e,...a.slice(0,49)]),window.dispatchEvent(new CustomEvent("cell-changed",{detail:e}))},[]),(0,N.useCallback)(e=>{o(a=>{let r=new Map(a);if(e.isOnline)r.set(e.userId,{userId:e.userId,userName:e.userName,isOnline:!0,lastSeen:e.lastSeen,cursor:e.cursor});else{let a=r.get(e.userId);a&&r.set(e.userId,{...a,isOnline:!1,lastSeen:e.lastSeen})}return r}),window.dispatchEvent(new CustomEvent("user-presence-changed",{detail:e}))},[]);let l=(0,N.useCallback)(async(r,t)=>{if(e&&a?.user)try{await ax.updateUserCursor(e,{sheetId:r,cellAddress:t})}catch(e){console.error("Erro ao atualizar cursor:",e)}},[e,a]),c=(0,N.useCallback)(async(r,t,s)=>{if(e&&a?.user)try{await ax.broadcastCellChange(e,{workbookId:e,sheetId:r,cellAddress:t,value:s,userId:a.user.id||a.user.email||"unknown"})}catch(e){console.error("Erro ao enviar mudan\xe7a de c\xe9lula:",e)}},[e,a]),d=(0,N.useCallback)(()=>ax.getConnectionStatus(),[]),u=(0,N.useCallback)(async()=>{try{await ax.reconnectAll(),t(!0)}catch(e){console.error("Erro ao reconectar:",e),t(!1)}},[]);return{isConnected:r,onlineUsers:Array.from(s.values()),recentChanges:n,updateCursor:l,broadcastCellChange:c,getConnectionStatus:d,reconnect:u,isUserOnline:e=>s.get(e)?.isOnline||!1,getUserCursor:e=>s.get(e)?.cursor,getOnlineCount:()=>Array.from(s.values()).filter(e=>e.isOnline).length}}function ag({workbookId:e,className:a=""}){let{onlineUsers:r,isConnected:t,onlineCount:o}=function(e){let{onlineUsers:a,isConnected:r,getOnlineCount:t}=af(e);return{onlineUsers:a,isConnected:r,onlineCount:t()}}(e);return e?(0,s.jsxs)("div",{className:`flex items-center space-x-2 ${a}`,children:[s.jsx(O.pn,{children:(0,s.jsxs)(O.u,{children:[s.jsx(O.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[t?s.jsx(ac.Z,{className:"h-4 w-4 text-green-500"}):s.jsx(ad.Z,{className:"h-4 w-4 text-red-500"}),s.jsx($.C,{variant:t?"default":"destructive",className:"text-xs",children:t?"Conectado":"Desconectado"})]})}),s.jsx(O._v,{children:s.jsx("p",{children:t?"Conectado ao Real-time":"Desconectado do Real-time"})})]})}),o>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[s.jsx(au.Z,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[o," online"]})]}),s.jsx("div",{className:"flex -space-x-2",children:r.filter(e=>e.isOnline).slice(0,5).map(e=>s.jsx(O.pn,{children:(0,s.jsxs)(O.u,{children:[s.jsx(O.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(am.qE,{className:"h-8 w-8 border-2 border-white",children:[s.jsx(am.F$,{src:`https://api.dicebear.com/7.x/initials/svg?seed=${e.userName}`,alt:e.userName}),s.jsx(am.Q5,{className:"text-xs",children:e.userName.substring(0,2).toUpperCase()})]}),s.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-white"})]})}),s.jsx(O._v,{children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("p",{className:"font-medium",children:e.userName}),s.jsx("p",{className:"text-xs text-gray-500",children:"Online agora"}),e.cursor&&(0,s.jsxs)("p",{className:"text-xs text-blue-500",children:["Editando: ",e.cursor.cellAddress]})]})})]})},e.userId))}),o>5&&s.jsx(O.pn,{children:(0,s.jsxs)(O.u,{children:[s.jsx(O.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 border-2 border-white text-xs font-medium text-gray-600",children:["+",o-5]})}),s.jsx(O._v,{children:(0,s.jsxs)("p",{children:["Mais ",o-5," usu\xe1rios online"]})})]})})]}):null}var ay=r(80440);let av=ay.fC;ay.xz;let aw=ay.h_,ab=N.forwardRef(({className:e,...a},r)=>s.jsx(ay.aV,{className:(0,T.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:r}));ab.displayName=ay.aV.displayName;let aN=N.forwardRef(({className:e,...a},r)=>(0,s.jsxs)(aw,{children:[s.jsx(ab,{}),s.jsx(ay.VY,{ref:r,className:(0,T.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));aN.displayName=ay.VY.displayName;let aA=({className:e,...a})=>s.jsx("div",{className:(0,T.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...a});aA.displayName="AlertDialogHeader";let aE=({className:e,...a})=>s.jsx("div",{className:(0,T.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});aE.displayName="AlertDialogFooter";let aj=N.forwardRef(({className:e,...a},r)=>s.jsx(ay.Dx,{ref:r,className:(0,T.cn)("text-lg font-semibold",e),...a}));aj.displayName=ay.Dx.displayName;let aC=N.forwardRef(({className:e,...a},r)=>s.jsx(ay.dk,{ref:r,className:(0,T.cn)("text-sm text-muted-foreground",e),...a}));aC.displayName=ay.dk.displayName,N.forwardRef(({className:e,...a},r)=>s.jsx(ay.aU,{ref:r,className:(0,T.cn)((0,S.d)(),e),...a})).displayName=ay.aU.displayName;let aS=N.forwardRef(({className:e,...a},r)=>s.jsx(ay.$j,{ref:r,className:(0,T.cn)((0,S.d)({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));aS.displayName=ay.$j.displayName;var ak=r(50258),aO=r(63685),aR=r(65842),aT=r(76464),aF=r(33734);let aI=[{id:"financial-expenses",name:"Controle de Despesas",description:"Template para importar dados de despesas e receitas",category:"financial",icon:s.jsx(aR.Z,{className:"h-6 w-6"}),columns:["Data","Descri\xe7\xe3o","Categoria","Valor","Tipo"],validationRules:{Data:{required:!0,type:"date"},Valor:{required:!0,type:"number"},Tipo:{required:!0,enum:["Receita","Despesa"]}},isPopular:!0},{id:"sales-data",name:"Dados de Vendas",description:"Template para importar relat\xf3rios de vendas",category:"sales",icon:s.jsx(aT.Z,{className:"h-6 w-6"}),columns:["Data","Produto","Cliente","Quantidade","Valor Unit\xe1rio","Total"],validationRules:{Data:{required:!0,type:"date"},Quantidade:{required:!0,type:"number",min:0},"Valor Unit\xe1rio":{required:!0,type:"number",min:0}},isPopular:!0},{id:"inventory-control",name:"Controle de Estoque",description:"Template para importar dados de invent\xe1rio",category:"inventory",icon:s.jsx(Y.Z,{className:"h-6 w-6"}),columns:["C\xf3digo","Produto","Categoria","Quantidade","Pre\xe7o","Fornecedor"],validationRules:{Código:{required:!0,type:"string"},Quantidade:{required:!0,type:"number",min:0},Preço:{required:!0,type:"number",min:0}}},{id:"employee-data",name:"Dados de Funcion\xe1rios",description:"Template para importar informa\xe7\xf5es de RH",category:"hr",icon:s.jsx(au.Z,{className:"h-6 w-6"}),columns:["Nome","Email","Cargo","Departamento","Sal\xe1rio","Data Admiss\xe3o"],validationRules:{Nome:{required:!0,type:"string"},Email:{required:!0,type:"email"},"Data Admiss\xe3o":{required:!0,type:"date"}}},{id:"budget-planning",name:"Planejamento Or\xe7ament\xe1rio",description:"Template para importar dados de or\xe7amento",category:"financial",icon:s.jsx(F.Z,{className:"h-6 w-6"}),columns:["Categoria","Subcategoria","Or\xe7ado","Realizado","Varia\xe7\xe3o"],validationRules:{Categoria:{required:!0,type:"string"},Orçado:{required:!0,type:"number"},Realizado:{required:!0,type:"number"}},isNew:!0},{id:"custom-generic",name:"Template Gen\xe9rico",description:"Template flex\xedvel para qualquer tipo de dados",category:"general",icon:s.jsx(n.Z,{className:"h-6 w-6"}),columns:[],validationRules:{}}],aD={financial:"Financeiro",sales:"Vendas",inventory:"Estoque",hr:"Recursos Humanos",general:"Geral"};function a_({onTemplateSelect:e,onSkip:a}){let[r,t]=(0,N.useState)(""),[o,i]=(0,N.useState)("all"),l=aI.filter(e=>{let a=e.name.toLowerCase().includes(r.toLowerCase())||e.description.toLowerCase().includes(r.toLowerCase()),t="all"===o||e.category===o;return a&&t}),c=aI.filter(e=>e.isPopular);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx(E.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),s.jsx(e7.I,{placeholder:"Buscar templates...",value:r,onChange:e=>t(e.target.value),className:"pl-10"})]}),(0,s.jsxs)(aa.mQ,{value:o,onValueChange:i,children:[(0,s.jsxs)(aa.dr,{className:"grid w-full grid-cols-6",children:[s.jsx(aa.SP,{value:"all",children:"Todos"}),s.jsx(aa.SP,{value:"financial",children:"Financeiro"}),s.jsx(aa.SP,{value:"sales",children:"Vendas"}),s.jsx(aa.SP,{value:"inventory",children:"Estoque"}),s.jsx(aa.SP,{value:"hr",children:"RH"}),s.jsx(aa.SP,{value:"general",children:"Geral"})]}),(0,s.jsxs)(aa.nU,{value:o,className:"space-y-4",children:["all"===o&&c.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[s.jsx(aF.Z,{className:"h-5 w-5 text-yellow-500"}),"Templates Populares"]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:c.map(a=>s.jsx(aL,{template:a,onSelect:()=>e(a.id)},a.id))})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-lg font-semibold mb-3",children:"all"===o?"Todos os Templates":aD[o]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:l.map(a=>s.jsx(aL,{template:a,onSelect:()=>e(a.id)},a.id))})]}),0===l.length&&(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[s.jsx(n.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),s.jsx("p",{children:"Nenhum template encontrado"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between pt-4 border-t",children:[s.jsx(S.Button,{variant:"outline",onClick:a,children:"Pular Template"}),s.jsx("p",{className:"text-sm text-muted-foreground self-center",children:"Selecione um template ou pule para importa\xe7\xe3o manual"})]})]})}function aL({template:e,onSelect:a}){return(0,s.jsxs)(U.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:a,children:[s.jsx(U.Ol,{className:"pb-3",children:s.jsx("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:e.icon}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(U.ll,{className:"text-base flex items-center gap-2",children:[e.name,e.isPopular&&s.jsx($.C,{variant:"secondary",className:"text-xs",children:"Popular"}),e.isNew&&s.jsx($.C,{variant:"default",className:"text-xs",children:"Novo"})]}),s.jsx(U.SZ,{className:"text-sm",children:e.description})]})]})})}),s.jsx(U.aY,{className:"pt-0",children:(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("p",{className:"text-xs text-muted-foreground",children:"Colunas esperadas:"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.columns.length>0?e.columns.slice(0,4).map((e,a)=>s.jsx($.C,{variant:"outline",className:"text-xs",children:e},a)):s.jsx($.C,{variant:"outline",className:"text-xs",children:"Flex\xedvel"}),e.columns.length>4&&(0,s.jsxs)($.C,{variant:"outline",className:"text-xs",children:["+",e.columns.length-4," mais"]})]})]})})]})}var a$=r(54659);function aM({open:e,onOpenChange:a,file:r,template:t,onConfirm:o,onCancel:l}){let[c,u]=(0,N.useState)([]),[m,p]=(0,N.useState)([]),[h,x]=(0,N.useState)({}),[f,g]=(0,N.useState)(!0),[y,v]=(0,N.useState)(null),[b,A]=(0,N.useState)([]),E=(e,a)=>{x(r=>({...r,[e]:a}))},j=e=>{x(a=>{let r={...a};return Object.keys(r).forEach(a=>{r[a]===e&&delete r[a]}),r})},C=e=>Object.keys(h).find(a=>h[a]===e),k=(()=>{let e=m.filter(e=>e.required);return{isValid:e.filter(e=>C(e.name)).length===e.length,missingRequired:e.filter(e=>!C(e.name)),totalMapped:Object.keys(h).length}})();return f?s.jsx(e4.Vq,{open:e,onOpenChange:a,children:s.jsx(e4.cZ,{className:"max-w-4xl",children:s.jsx("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"}),s.jsx("p",{children:"Analisando arquivo..."})]})})})}):s.jsx(e4.Vq,{open:e,onOpenChange:a,children:(0,s.jsxs)(e4.cZ,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[s.jsx(e4.fK,{children:(0,s.jsxs)(e4.$N,{className:"flex items-center gap-2",children:[s.jsx(n.Z,{className:"h-5 w-5"}),"Mapeamento de Colunas - ",r.name]})}),y&&(0,s.jsxs)(ar.bZ,{variant:"destructive",children:[s.jsx(w.Z,{className:"h-4 w-4"}),s.jsx(ar.X,{children:y})]}),!y&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(ar.bZ,{variant:k.isValid?"default":"destructive",children:[s.jsx(a$.Z,{className:"h-4 w-4"}),s.jsx(ar.X,{children:k.isValid?`Mapeamento v\xe1lido! ${k.totalMapped} colunas mapeadas.`:`Colunas obrigat\xf3rias n\xe3o mapeadas: ${k.missingRequired.map(e=>e.name).join(", ")}`})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:s.jsx(U.ll,{className:"text-lg",children:"Colunas do Arquivo"})}),s.jsx(U.aY,{className:"space-y-3",children:c.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium",children:e}),s.jsx("p",{className:"text-sm text-muted-foreground",children:b[0]?.[e]?String(b[0][e]).substring(0,30)+"...":"Vazio"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[h[e]&&s.jsx($.C,{variant:"secondary",children:h[e]}),s.jsx(i.Z,{className:"h-4 w-4 text-muted-foreground"})]})]},e))})]}),(0,s.jsxs)(U.Zb,{children:[s.jsx(U.Ol,{children:s.jsx(U.ll,{className:"text-lg",children:t?"Colunas do Template":"Colunas de Destino"})}),s.jsx(U.aY,{className:"space-y-3",children:m.map(e=>{let a=C(e.name);return(0,s.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("p",{className:"font-medium",children:e.name}),e.required&&s.jsx($.C,{variant:"destructive",className:"text-xs",children:"Obrigat\xf3rio"}),s.jsx($.C,{variant:"outline",className:"text-xs",children:e.type})]}),a&&s.jsx(S.Button,{variant:"ghost",size:"sm",onClick:()=>j(e.name),children:s.jsx(d.Z,{className:"h-4 w-4"})})]}),(0,s.jsxs)(e8.Ph,{value:a||"",onValueChange:a=>E(a,e.name),children:[s.jsx(e8.i4,{children:s.jsx(e8.ki,{placeholder:"Selecionar coluna do arquivo"})}),s.jsx(e8.Bw,{children:c.map(e=>s.jsx(e8.Ql,{value:e,children:e},e))})]})]},e.name)})})]})]})]}),(0,s.jsxs)(e4.cN,{children:[s.jsx(S.Button,{variant:"outline",onClick:l,children:"Cancelar"}),s.jsx(S.Button,{onClick:()=>o(h),disabled:!k.isValid,children:"Confirmar Mapeamento"})]})]})})}var az=r(3634),aP=r(88319),aB=r(48998),aV=r(33269);let aZ={"file-analysis":s.jsx(n.Z,{className:"h-4 w-4"}),"schema-validation":s.jsx(a$.Z,{className:"h-4 w-4"}),"data-transformation":s.jsx(az.Z,{className:"h-4 w-4"}),"data-processing":s.jsx(aP.Z,{className:"h-4 w-4"}),finalization:s.jsx(a$.Z,{className:"h-4 w-4"})};function aU({steps:e,currentStep:a,overallProgress:r,isComplete:t,hasError:o,onCancel:n,showDetails:i=!0}){let[l,c]=(0,N.useState)(0),[d]=(0,N.useState)(new Date),u=e=>{let a=Math.floor(e/1e3),r=Math.floor(a/60),t=a%60;return r>0?`${r}m ${t}s`:`${t}s`},m=e=>e.startTime&&e.endTime?u(e.endTime.getTime()-e.startTime.getTime()):e.startTime&&"running"===e.status?u(Date.now()-e.startTime.getTime()):null,p=e.filter(e=>"completed"===e.status).length,h=e.filter(e=>"error"===e.status).length;return(0,s.jsxs)(U.Zb,{className:"w-full max-w-2xl",children:[s.jsx(U.Ol,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(U.ll,{className:"flex items-center gap-2",children:[s.jsx(aP.Z,{className:"h-5 w-5"}),"Progresso da Importa\xe7\xe3o"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx($.C,{variant:o?"destructive":t?"default":"secondary",children:o?"Erro":t?"Conclu\xeddo":"Processando"}),s.jsx("span",{className:"text-sm text-muted-foreground",children:u(l)})]})]})}),(0,s.jsxs)(U.aY,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Progresso Geral"}),(0,s.jsxs)("span",{children:[Math.round(r),"%"]})]}),s.jsx(aV.E,{value:r,className:"h-2"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,s.jsxs)("span",{children:[p,"/",e.length," etapas conclu\xeddas"]}),h>0&&(0,s.jsxs)("span",{className:"text-destructive",children:[h," erro(s)"]})]})]}),s.jsx("div",{className:"space-y-3",children:e.map((e,a)=>(0,s.jsxs)("div",{className:`flex items-start gap-3 p-3 rounded-lg border transition-colors ${"running"===e.status?"bg-blue-50 border-blue-200":"completed"===e.status?"bg-green-50 border-green-200":"error"===e.status?"bg-red-50 border-red-200":"bg-muted/20"}`,children:[s.jsx("div",{className:`flex-shrink-0 mt-0.5 ${"running"===e.status?"text-blue-600":"completed"===e.status?"text-green-600":"error"===e.status?"text-red-600":"text-muted-foreground"}`,children:"running"===e.status?s.jsx("div",{className:"h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin"}):"completed"===e.status?s.jsx(a$.Z,{className:"h-4 w-4"}):"error"===e.status?s.jsx(w.Z,{className:"h-4 w-4"}):s.jsx(aB.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[aZ[e.id],s.jsx("h4",{className:"font-medium text-sm",children:e.name})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[m(e)&&s.jsx("span",{children:m(e)}),"running"===e.status&&(0,s.jsxs)("span",{children:[Math.round(e.progress),"%"]})]})]}),s.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:e.description}),"running"===e.status&&e.progress>0&&s.jsx(aV.E,{value:e.progress,className:"h-1 mt-2"}),i&&e.details&&s.jsx("p",{className:"text-xs text-muted-foreground mt-1 font-mono",children:e.details}),"error"===e.status&&e.error&&(0,s.jsxs)(ar.bZ,{variant:"destructive",className:"mt-2",children:[s.jsx(w.Z,{className:"h-4 w-4"}),s.jsx(ar.X,{className:"text-xs",children:e.error})]})]})]},e.id))}),(t||o)&&s.jsx("div",{className:"pt-4 border-t",children:(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-2xl font-bold text-green-600",children:p}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Conclu\xeddas"})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-2xl font-bold text-red-600",children:h}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Erros"})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-2xl font-bold",children:u(l)}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Tempo Total"})]})]})}),!t&&!o&&n&&s.jsx("div",{className:"flex justify-end pt-4 border-t",children:s.jsx("button",{onClick:n,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Cancelar Importa\xe7\xe3o"})})]})]})}function aq({onUpload:e,workbookId:a,variant:r="default",size:t="sm",maxSize:o,saveToSupabase:i=!1,enableAdvancedImport:l=!1,allowTemplates:c=!1}){let d=(0,N.useRef)(null),{importExcel:u,isLoading:m}=e0(),{data:p}=(0,ap.useSession)(),[h,x]=(0,N.useState)(!1),[f,g]=(0,N.useState)(!1),[y,v]=(0,N.useState)(!1),[w,b]=(0,N.useState)(null),[E,j]=(0,N.useState)(null),C=function(){let[e,a]=(0,N.useState)([]),[r,t]=(0,N.useState)(),[s,o]=(0,N.useState)(0),n=e.length>0&&e.every(e=>"completed"===e.status),i=e.some(e=>"error"===e.status);return{steps:e,currentStep:r,overallProgress:s,isComplete:n,hasError:i,initializeSteps:e=>{a(e.map(e=>({...e,status:"pending",progress:0}))),o(0)},startStep:(e,r)=>{t(e),a(a=>a.map(a=>a.id===e?{...a,status:"running",startTime:new Date,details:r||void 0}:a))},updateStepProgress:(r,t,s)=>{a(e=>e.map(e=>e.id===r?{...e,progress:t,details:s||void 0}:e));let n=e.length;o((e.filter(e=>"completed"===e.status).length+t/100)/n*100)},completeStep:(e,r)=>{a(a=>a.map(a=>a.id===e?{...a,status:"completed",progress:100,endTime:new Date,details:r||void 0}:a))},errorStep:(e,r)=>{a(a=>a.map(a=>a.id===e?{...a,status:"error",endTime:new Date,error:r}:a))}}}(),k=async e=>{let a=e.target.files?.[0];a&&(l?(b(a),g(!0)):await O(a),d.current&&(d.current.value=""))},O=async(r,t)=>{l&&(C.initializeSteps([{id:"file-analysis",name:"An\xe1lise do Arquivo",description:"Analisando estrutura e conte\xfado"},{id:"schema-validation",name:"Valida\xe7\xe3o de Schema",description:"Validando dados conforme template"},{id:"data-transformation",name:"Transforma\xe7\xe3o",description:"Aplicando transforma\xe7\xf5es nos dados"},{id:"data-processing",name:"Processamento",description:"Processando e organizando dados"},{id:"finalization",name:"Finaliza\xe7\xe3o",description:"Concluindo importa\xe7\xe3o"}]),v(!0));try{if(i&&a&&p?.user){l&&C.startStep("data-processing","Salvando no Supabase Storage..."),A.toast.loading("Salvando arquivo no Supabase...",{id:"supabase-upload"});let e=await eq.uploadExcelFile(r,p.user.id||p.user.email||"unknown",a,{fileName:r.name,upsert:!0});A.toast.success("Arquivo salvo no Supabase!",{id:"supabase-upload",description:`Tamanho: ${Math.round(e.size/1024)}KB`}),l&&C.updateStepProgress("data-processing",30,"Arquivo salvo no storage")}await u(r,{onSuccess:a=>{t&&a.sheets&&(a.sheets=a.sheets.map(e=>({...e,data:R(e.data,t)}))),l&&(C.completeStep("finalization","Importa\xe7\xe3o conclu\xedda com sucesso"),setTimeout(()=>v(!1),2e3)),e(a)},...o?{maxSize:o}:{},trackAnalytics:!0,template:E,validateSchema:l,transformData:l,...l?{onProgress:e=>{e<=10?C.startStep("file-analysis","Carregando arquivo..."):e<=50?C.updateStepProgress("file-analysis",(e-10)*2.5,"Analisando dados..."):e<=70?(C.completeStep("file-analysis"),C.startStep("schema-validation","Validando schema..."),C.updateStepProgress("schema-validation",(e-50)*5,"Verificando estrutura...")):e<=90?(C.completeStep("schema-validation"),C.startStep("data-transformation","Transformando dados..."),C.updateStepProgress("data-transformation",(e-70)*5,"Aplicando transforma\xe7\xf5es...")):(C.completeStep("data-transformation"),C.startStep("finalization","Finalizando..."))}}:{}})}catch(e){if(console.error("Erro no upload:",e),l){let a=C.currentStep||"file-analysis";C.errorStep(a,e instanceof Error?e.message:"Erro desconhecido")}A.toast.error("Erro ao processar arquivo",{description:e instanceof Error?e.message:"Erro desconhecido"})}},R=(e,a)=>e&&Array.isArray(e)?e.map(e=>{let r={};return Object.entries(a).forEach(([a,t])=>{void 0!==e[a]&&(r[t]=e[a])}),{...e,...r}}):e;return(0,s.jsxs)(s.Fragment,{children:[s.jsx("input",{type:"file",ref:d,onChange:k,accept:".xlsx,.xls,.csv",style:{display:"none"}}),s.jsx(S.Button,{variant:r,size:t,onClick:()=>{l&&c?x(!0):d.current?.click()},disabled:m,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2",children:m?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Carregando..."]}):(0,s.jsxs)(s.Fragment,{children:[l?s.jsx(Q.Z,{className:"h-4 w-4 mr-2"}):s.jsx(aO.Z,{className:"h-4 w-4 mr-2"}),l?"Importa\xe7\xe3o Avan\xe7ada":"Importar Excel"]})}),c&&s.jsx(e4.Vq,{open:h,onOpenChange:x,children:(0,s.jsxs)(e4.cZ,{className:"max-w-4xl",children:[s.jsx(e4.fK,{children:(0,s.jsxs)(e4.$N,{className:"flex items-center gap-2",children:[s.jsx(n.Z,{className:"h-5 w-5"}),"Selecionar Template de Importa\xe7\xe3o"]})}),s.jsx(a_,{onTemplateSelect:e=>{j(e),x(!1),d.current?.click()},onSkip:()=>{j(null),x(!1),d.current?.click()}})]})}),l&&w&&s.jsx(aM,{open:f,onOpenChange:g,file:w,template:E,onConfirm:e=>{g(!1),O(w,e),b(null)},onCancel:()=>{g(!1),b(null)}}),l&&s.jsx(e4.Vq,{open:y,onOpenChange:v,children:(0,s.jsxs)(e4.cZ,{className:"max-w-2xl",children:[s.jsx(e4.fK,{children:s.jsx(e4.$N,{children:"Progresso da Importa\xe7\xe3o"})}),s.jsx(aU,{steps:C.steps,currentStep:C.currentStep||void 0,overallProgress:C.overallProgress,isComplete:C.isComplete,hasError:C.hasError,onCancel:()=>{v(!1)}})]})})]})}var aH=r(44099),aG=r(28612);!function(e){e.IDLE="idle",e.PENDING="pending",e.COMPLETED="completed",e.FAILED="failed"}(t||(t={}));let aX=(e={})=>{let[a,r]=(0,N.useState)(e.initialMessages||[]),[t,s]=(0,N.useState)(!1),[o,n]=(0,N.useState)(null),[i,l]=(0,N.useState)("idle"),[c,d]=(0,N.useState)(null),u=(0,N.useRef)(null);(0,N.useEffect)(()=>{o&&a.length>0&&n(null)},[a,o]);let m=e.useMock||!1,p=(0,N.useCallback)(async e=>new Promise(a=>{setTimeout(()=>{a(function(e){let a=[{keywords:["m\xe9dia","coluna"],response:`{
        "operations": [
          {
            "type": "FORMULA",
            "data": {
              "formula": "=M\xc9DIA(B:B)",
              "range": "C1"
            }
          }
        ],
        "explanation": "Calculando a m\xe9dia da coluna B",
        "interpretation": "Voc\xea solicitou o c\xe1lculo da m\xe9dia dos valores na coluna B"
      }`},{keywords:["gr\xe1fico","barras"],response:`{
        "operations": [
          {
            "type": "CHART",
            "data": {
              "type": "bar",
              "title": "Gr\xe1fico de Barras",
              "labels": "A1:A10",
              "datasets": ["B1:B10"]
            }
          }
        ],
        "explanation": "Criando um gr\xe1fico de barras com dados das colunas A e B",
        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de um gr\xe1fico de barras usando os dados existentes"
      }`},{keywords:["tabela","criar"],response:`{
        "operations": [
          {
            "type": "CELL_UPDATE",
            "data": {
              "updates": [
                { "cell": "A1", "value": "Produto" },
                { "cell": "B1", "value": "Valor" },
                { "cell": "C1", "value": "Quantidade" },
                { "cell": "A2", "value": "Produto 1" },
                { "cell": "B2", "value": 100 },
                { "cell": "C2", "value": 10 }
              ]
            }
          }
        ],
        "explanation": "Criando uma tabela com 3 colunas: Produto, Valor e Quantidade",
        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de uma nova tabela para registro de produtos"
      }`}],r=e.toLowerCase(),t=a.filter(e=>e.keywords.some(e=>r.includes(e)));return t.length>0&&t[0]?t[0].response:`{
    "operations": [
      {
        "type": "CELL_UPDATE",
        "data": {
          "updates": [
            { "cell": "A1", "value": "Exemplo" },
            { "cell": "B1", "value": 100 }
          ]
        }
      }
    ],
    "explanation": "Realizando uma opera\xe7\xe3o exemplo baseada no seu comando",
    "interpretation": "Seu comando foi processado como uma solicita\xe7\xe3o de exemplo"
  }`}(e))},1500)}),[]);(0,N.useCallback)(async a=>{try{let r;if(!a||""===a.trim())return null;if(m)r=await p(a);else try{r=(await aH.Z.post("/api/ai/chat",{message:a,userId:e.workbookId||"anonymous",context:{mode:"preview",excelContext:{activeSheet:"Atual"}},preserveContext:!1})).data}catch(e){return console.error("Erro na comunica\xe7\xe3o com Gemini durante interpreta\xe7\xe3o:",e),null}try{let e=JSON.parse(r);if(e.interpretation)return{interpretation:e.interpretation,confidence:e.confidence||0,commandId:(0,T.x0)(),_commandId:(0,T.x0)()}}catch(e){console.error("Erro ao parsear resposta de interpreta\xe7\xe3o:",e)}return null}catch(e){return console.error("Erro ao interpretar comando:",e),null}},[e.workbookId,m,p]);let h=(0,N.useCallback)(async t=>{if(!t.trim())return;let o={id:`user-${Date.now()}`,content:t,role:"user",timestamp:new Date};r(e=>[...e,o]),s(!0),n(null);try{let t=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...a,o].map(e=>({role:e.role,content:e.content})),modelName:e.modelName,systemPrompt:e.systemPrompt})});if(!t.ok)throw Error(`Error: ${t.statusText}`);let s=await t.json(),n={id:`assistant-${Date.now()}`,content:s.response,role:"assistant",timestamp:new Date};r(e=>[...e,n])}catch(r){let a=r instanceof Error?r:Error("Unknown error");n(a),e.onError&&e.onError(a)}finally{s(!1)}},[a,e]),x=(0,N.useCallback)(async t=>{try{let o;s(!0);let n={id:`user-${Date.now()}`,content:t,role:"user",timestamp:new Date};if(r(a=>{let r=[...a,n],t=e.maxHistorySize||20;return r.length>t?r.slice(-t):r}),m)o=await p(t);else try{let r=e.maxHistorySize||20,s=a.slice(-Math.min(r,10)).map(e=>({role:e.role,content:e.content})),n=e.workbookId;o=(await aH.Z.post("/api/ai/chat",{message:t,userId:n||"anonymous",context:n?{excelContext:{activeSheet:"Atual"},responseStructure:{preferJson:!0}}:{},preserveContext:s.length>0})).data}catch(a){console.error("Erro na comunica\xe7\xe3o direta com Gemini, tentando API:",a),o=(await aH.Z.post("/api/chat",{message:t,workbookId:e.workbookId})).data.response}let i={id:`assistant-${Date.now()}`,content:o,role:"assistant",timestamp:new Date};r(a=>{let r=[...a,i],t=e.maxHistorySize||20;return r.length>t?r.slice(-t):r}),e.onMessageReceived&&e.onMessageReceived(o);let c=(0,T.x0)();try{await aG.M.storeFeedback({commandId:c,command:t,successful:!0})}catch(e){console.error("Erro ao armazenar comando para feedback:",e)}if(o){let a={interpretation:o,confidence:1,commandId:(0,T.x0)(),_commandId:(0,T.x0)()};d(a),l("pending"),e.onInterpretation&&e.onInterpretation(a)}return{response:o,commandId:c}}catch(e){throw console.error("Erro ao executar comando:",e),e}finally{s(!1)}},[a,e.workbookId,m,p,e.onMessageReceived,e.maxHistorySize,r,s,aG.M,e.onInterpretation]),f=(0,N.useCallback)(async()=>{if(!c)return null;let{_commandId:e}=c,a=await x(c.interpretation);return d(null),a},[c,x,d]),g=(0,N.useCallback)(()=>{d(null),l("idle")},[]),y=(0,N.useCallback)(()=>{r([]),d(null),l("idle")},[]);return(0,N.useEffect)(()=>{let e=u.current;return()=>{e&&e.abort()}},[]),{messages:a,isProcessing:t,error:o,sendMessage:h,clearMessages:y,confirmAndExecute:f,cancelCommand:g,pendingInterpretation:c,commandStatus:i}};var aJ=r(37178),aQ=r(39404);let aY=new Map,aW=0,aK=0,a0=(0,N.memo)(({rowIndex:e,colIndex:a,value:r,isModified:t,readOnly:o,onCellChange:n,header:i})=>{let l=`${e}-${a}`;null!=r&&aY.set(l,String(r));let c=r??(()=>{let e=aY.get(l);return e?(aK++,e):(aW++,"")})(),d=(0,N.useCallback)(r=>{let t=r.target.value;aY.set(l,t),n(e,a,t)},[e,a,n,l]),u=t?"bg-blue-50 dark:bg-blue-900/30 transition-colors duration-1000":"",m=(0,N.useMemo)(()=>`table-cell p-1 border border-border ${u}`,[u]);return s.jsx("div",{className:m,role:"gridcell","aria-colindex":a+1,"aria-rowindex":e+1,children:s.jsx("input",{type:"text",value:c,readOnly:o,onChange:d,className:"w-full bg-transparent border-0 focus:ring-1 focus:ring-blue-500 p-1","aria-label":`C\xe9lula ${i}${e+1}`})})},(e,a)=>e.value===a.value&&e.isModified===a.isModified&&e.readOnly===a.readOnly);a0.displayName="OptimizedCell";let a1=(0,N.memo)(({visibleRows:e,totalRows:a,virtualizer:r})=>((0,N.useEffect)(()=>{if(a>1e3){let e=Array.from(aY.keys()),t=new Set,s=Math.max(0,r.range.startIndex-20),o=Math.min(a-1,r.range.endIndex+20);for(let e=s;e<=o;e++)t.add(e);e.forEach(e=>{let a=e.split("-")[0];if(a){let r=parseInt(a,10);t.has(r)||aY.delete(e)}})}},[e,a,r]),null));a1.displayName="VirtualizedRowWrapper";let a2=(0,N.memo)(({rowIndex:e,rowData:a,headers:r,modifiedCellsMap:t,readOnly:o,onCellChange:n,onRemoveRow:i})=>{let l=(0,N.useCallback)(()=>{i(e)},[e,i]),c=(0,N.useMemo)(()=>a.map((a,i)=>{let l=`${e}-${i}`,c=t[l]||!1;return s.jsx(a0,{rowIndex:e,colIndex:i,value:a,isModified:c,readOnly:o,onCellChange:n,header:r[i]||""},l)}),[a,e,t,o,n,r]),u=(0,N.useMemo)(()=>o?null:s.jsx("div",{className:"table-cell w-10 text-center",children:s.jsx(S.Button,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:l,"aria-label":`Remover linha ${e+1}`,children:s.jsx(d.Z,{className:"h-3 w-3"})})}),[o,l,e]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"table-cell w-10 text-center text-xs text-muted-foreground bg-muted",children:e+1}),c,u]})},(e,a)=>{if(e.readOnly!==a.readOnly||e.rowIndex!==a.rowIndex||e.rowData.length!==a.rowData.length||e.headers.length!==a.headers.length)return!1;if(e.rowData.length<=10){for(let r=0;r<e.rowData.length;r++){if(e.rowData[r]!==a.rowData[r])return!1;let t=`${e.rowIndex}-${r}`;if(e.modifiedCellsMap[t]!==a.modifiedCellsMap[t])return!1}return!0}{let r=Object.keys(e.modifiedCellsMap).filter(a=>a.startsWith(`${e.rowIndex}-`)&&e.modifiedCellsMap[a]),t=Object.keys(a.modifiedCellsMap).filter(e=>e.startsWith(`${a.rowIndex}-`)&&a.modifiedCellsMap[e]);return!(r.length!==t.length||r.some(e=>!a.modifiedCellsMap[e]))&&JSON.stringify(e.rowData)===JSON.stringify(a.rowData)}});a2.displayName="OptimizedRow";let a3=(e,a)=>{let r={};return e&&e.length>0?e.forEach(e=>{r[`${e.row}-${e.col}`]=!0}):a&&(r[`${a.row}-${a.col}`]=!0),function(e=1e4){aY.size>e&&Array.from(aY.keys()).slice(0,1e3).forEach(e=>aY.delete(e))}(),r},a4=[{text:"Crie uma tabela de controle de horas",icon:s.jsx(n.Z,{className:"h-3 w-3"})},{text:"Adicione valida\xe7\xe3o de dados na coluna B",icon:s.jsx(i.Z,{className:"h-3 w-3"})},{text:"Gere um gr\xe1fico de barras com os dados",icon:s.jsx(l.Z,{className:"h-3 w-3"})},{text:"Calcule a m\xe9dia da coluna C",icon:s.jsx(i.Z,{className:"h-3 w-3"})},{text:"Formate a tabela com cores alternadas",icon:s.jsx(i.Z,{className:"h-3 w-3"})}],a5=[{title:"Bem-vindo ao Excel Copilot",content:"Este assistente permite criar e editar planilhas atrav\xe9s de comandos em linguagem natural.",target:"header"},{title:"Assistente de IA",content:"Aqui voc\xea pode digitar comandos como 'Crie uma tabela de vendas' ou 'Calcule a m\xe9dia da coluna B'.",target:"ai-assistant"},{title:"Sugest\xf5es R\xe1pidas",content:"Exemplos de comandos que voc\xea pode usar. Clique em um deles para executar.",target:"suggestions"},{title:"Planilha Interativa",content:"Sua planilha ser\xe1 atualizada automaticamente conforme seus comandos. Voc\xea tamb\xe9m pode editar c\xe9lulas manualmente.",target:"spreadsheet"}],a9=(0,N.memo)(({command:e,onClick:a})=>(0,s.jsxs)(S.Button,{variant:"ghost",className:"h-8 px-2 text-sm justify-start w-full hover:bg-accent",onClick:a,children:[s.jsx("span",{className:"mr-2",children:e.icon}),s.jsx("span",{className:"truncate",children:e.text})]}));function a6({workbookId:e,initialData:a,readOnly:t=!1,onSave:n,initialCommand:l}){let[E,j]=(0,N.useState)(a||{headers:["A","B","C"],rows:[["","",""],["","",""],["","",""]],charts:[],name:"Nova Planilha"}),[C,k]=(0,N.useState)([]),[O,R]=(0,N.useState)(-1),[T,F]=(0,N.useState)(!1),[I,D]=(0,N.useState)(!1),[_,L]=(0,N.useState)(!1),[$,z]=(0,N.useState)(!1),[P,V]=(0,N.useState)(null),[Z,U]=(0,N.useState)(!1),[H,G]=(0,N.useState)(!1),[J,Q]=(0,N.useState)(!1),[Y,W]=(0,N.useState)(0),[K,ee]=(0,N.useState)(!1);(0,N.useRef)(null);let ea=(0,N.useRef)([]),[er,et]=(0,N.useState)(null),[es,eo]=(0,N.useState)(null),[ei,el]=(0,N.useState)(!1),[ec,ed]=(0,N.useState)(null),[eu,em]=(0,N.useState)([]),ep=(0,N.useRef)(null),eh=(0,N.useCallback)((e,a)=>{let r=setTimeout(()=>{e(),ea.current=ea.current.filter(e=>e!==r)},a);return ea.current.push(r),r},[]),ex=(0,N.useCallback)(e=>{if(C.length>0&&JSON.stringify(C[C.length-1])===JSON.stringify(e))return;let a=C.slice(0,O+1).slice(-19);k([...a,JSON.parse(JSON.stringify(e))]),R(a.length)},[C,O]),{processExcelCommand:ef,isProcessing:eg,lastModifiedCells:ey}=function({onDataChange:e,onAddHistory:a}={}){let[t,s]=(0,N.useState)(!1),[o,n]=(0,N.useState)([]),[i,l]=(0,N.useState)([]),c=(0,N.useRef)(null),{executeOperation:d,isProcessing:u,cancelAllOperations:m}=function(e={}){let a=(0,N.useRef)(null),[r,t]=(0,N.useState)(!1),s=(0,N.useRef)(new Map);return{executeOperation:(0,N.useCallback)(async(e,r)=>{if(!a.current)try{t(!0);let a={updatedData:r,resultSummary:`Opera\xe7\xe3o ${e.type} executada (fallback)`,modifiedCells:[]};return t(!1),a}catch(e){throw t(!1),e}let o=(0,aQ.x0)();return t(!0),new Promise((t,n)=>{s.current.set(o,{operation:e,resolve:t,reject:n}),a.current.postMessage({operation:e,sheetData:r,requestId:o,operationType:e.type})})},[]),isProcessing:r,cancelAllOperations:(0,N.useCallback)(()=>{s.current.forEach((e,a)=>{e.reject(Error("Opera\xe7\xe3o cancelada"))}),s.current.clear(),t(!1)},[])}}({onSuccess:e=>{e.modifiedCells&&(n(e.modifiedCells),p())},onError:e=>{en.logger.error("Erro no worker Excel:",e)}}),p=(0,N.useCallback)(()=>{c.current&&clearTimeout(c.current),c.current=setTimeout(()=>{n([]),c.current=null},3e3)},[]),h=(0,N.useCallback)(e=>e.length>2||e.some(e=>{let a=e.type?String(e.type).toUpperCase():void 0;return"FILTER"===a||"SORT"===a||"PIVOT_TABLE"===a||"CHART"===a||"ADVANCED_VISUALIZATION"===a}),[]);return{processExcelCommand:(0,N.useCallback)(async(o,i)=>{if(!o||!i)return null;if(t||u)return A.toast.info("Aguarde a conclus\xe3o da opera\xe7\xe3o anterior",{duration:2e3}),null;s(!0);try{a&&a(structuredClone(i));try{let{createExcelAIProcessor:a}=await Promise.resolve().then(r.bind(r,96671));a();let t={activeSheet:i.name,headers:i.headers,selection:`A1:${String.fromCharCode(65+i.headers.length-1)}${i.rows.length}`,recentOperations:[]},s=new(await Promise.resolve().then(r.bind(r,96671))).ExcelAIProcessor(t),c=await s.processQuery(o);if(c.operations&&c.operations.length>0&&!c.error){if((i.rows.length>500||i.rows.length>0&&i.rows[0]&&i.rows[0].length>50)&&h(c.operations))try{let a=await Promise.all(c.operations.map(e=>d(e,i))),r=structuredClone(i),t=[],s=[];for(let e of a)r=e.updatedData,t.push(String(e.resultSummary)),e.modifiedCells&&s.push(...e.modifiedCells);return l(t),s.length>0&&(n(s),p()),e&&e(r),A.toast.success("Opera\xe7\xf5es executadas",{description:t.join("; "),duration:3e3}),r}catch(e){(0,aJ.KE)("Erro no worker, retornando ao m\xe9todo padr\xe3o:",e)}let a=await e_(i,c.operations),r={updatedData:a.updatedData,resultSummary:Array.isArray(a.resultSummary)?a.resultSummary:[String(a.resultSummary)],modifiedCells:a.modifiedCells};return l(r.resultSummary),r.modifiedCells&&(n(r.modifiedCells),p()),e&&e(r.updatedData),A.toast.success("Opera\xe7\xf5es executadas",{description:r.resultSummary.join("; "),duration:3e3}),r.updatedData}}catch(e){(0,aJ.KE)("Erro no novo processor, tentando fallback:",e)}try{let a=await eD(o);if(!a.success||!(a.operations.length>0))return A.toast.info("Nenhuma opera\xe7\xe3o Excel",{description:a.message||"N\xe3o foi poss\xedvel extrair opera\xe7\xf5es Excel deste comando.",duration:4e3}),null;{let r=await e_(i,a.operations),t={updatedData:r.updatedData,resultSummary:Array.isArray(r.resultSummary)?r.resultSummary:[String(r.resultSummary)],modifiedCells:r.modifiedCells};return l(t.resultSummary),t.modifiedCells&&(n(t.modifiedCells),p()),e&&e(t.updatedData),A.toast.success("Opera\xe7\xf5es executadas",{description:t.resultSummary.join("; "),duration:3e3}),t.updatedData}}catch(e){(0,aJ.H)("Erro no parser de comandos:",e)}return A.toast.error("N\xe3o foi poss\xedvel executar o comando",{description:"Tente reformular seu comando ou use um exemplo da lista de sugest\xf5es.",duration:4e3}),null}catch(e){return(0,aJ.H)("Erro ao processar comando Excel:",e),A.toast.error("Erro ao processar comando",{description:e instanceof Error?e.message:"Ocorreu um erro desconhecido.",duration:4e3}),null}finally{s(!1)}},[t,u,e,a,p,d,h]),isProcessing:t||u,lastModifiedCells:o,lastOperationSummary:i}}({onDataChange:j,onAddHistory:ex}),{isConnected:ev,updateCursor:ew,broadcastCellChange:eb}=af(e);(0,N.useCallback)(e=>ef(e,E),[ef,E]);let eN=(0,N.useCallback)(e=>{et(e.interpretation),eo({id:e.commandId||e._commandId||"",command:e.interpretation})},[]),eA=(0,N.useCallback)(a=>{a&&Array.isArray(a)&&0!==a.length&&(ex(E),j(r=>{let t={...r};t.headers=Array.isArray(r.headers)?[...r.headers]:[],t.rows=Array.isArray(r.rows)?[...r.rows]:[];let s=[];return a.forEach(a=>{if(a&&"object"==typeof a){if("cell_update"===a.type&&"number"==typeof a.row&&"number"==typeof a.col){if(Array.isArray(t.rows[a.row])||(t.rows[a.row]=Array(t.headers.length).fill("")),a.row>=0&&a.col>=0&&Array.isArray(t.headers)&&a.col<t.headers.length&&Array.isArray(t.rows)&&void 0!==t.rows[a.row]&&Array.isArray(t.rows[a.row]))try{t.rows[a.row]&&"number"==typeof a.col&&(t.rows[a.row][a.col]=a.value,s.push({row:a.row,col:a.col}))}catch(r){en.logger.error("SpreadsheetEditor: Erro ao atualizar c\xe9lula",r,{row:a.row,col:a.col,operation:a.type,workbookId:e})}}else if("add_row"===a.type){let e=Array(t.headers.length).fill("");t.rows.push(e)}else"add_column"===a.type&&Array.isArray(t.headers)&&Array.isArray(t.rows)&&(t.headers.push(a.name||`Coluna ${t.headers.length+1}`),t.rows.forEach((e,a)=>{Array.isArray(e)?t.rows[a]=[...e,""]:t.rows[a]=Array(t.headers.length).fill("")}))}}),s.length>0&&V(s[0]||null),t}))},[E,ex,V]),{sendMessage:eE,isProcessing:ej,confirmAndExecute:eC,cancelCommand:eS,pendingInterpretation:ek,messages:eO,error:eR,clearMessages:eT,commandStatus:eF}=aX({workbookId:e,onMessageReceived:a=>{if(a)try{let e=JSON.parse(a);e.operations&&eA(e.operations)}catch(r){en.logger.error("SpreadsheetEditor: Erro ao processar resposta da IA",r,{content:a?.substring(0,100),workbookId:e})}},onInterpretation:eN}),[eI,eL]=(0,N.useState)(null),[e$,eM]=(0,N.useState)(!1),ez=(0,b.useRouter)(),eP=(0,N.useCallback)(()=>{O>0&&(R(O-1),j(JSON.parse(JSON.stringify(C[O-1]))),A.toast.info("A\xe7\xe3o desfeita"))},[C,O]),eB=(0,N.useCallback)(()=>{O<C.length-1&&(R(O+1),j(JSON.parse(JSON.stringify(C[O+1]))),A.toast.info("A\xe7\xe3o refeita"))},[C,O]),eV=(0,N.useCallback)(async()=>{if(!t)try{if(F(!0),n){await n(E),A.toast.success("Planilha salva com sucesso");return}if(!(await fetch(`/api/workbooks/${e}/sheets`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:E.name||"Sem nome",data:JSON.stringify(E)})})).ok)throw Error("Erro ao salvar planilha");A.toast.success("Planilha salva com sucesso")}catch(a){en.logger.error("SpreadsheetEditor: Erro ao salvar planilha",a,{workbookId:e,spreadsheetName:E.name,readOnly:t}),A.toast.error("Erro ao salvar planilha")}finally{F(!1)}},[e,E,n,t]),eZ=(0,N.useCallback)(e=>{e.trim()&&eE(e)},[eE]),eU=(0,N.useMemo)(()=>a3(ey,P),[ey,P]),eq=(0,N.useCallback)(async(e,a,r)=>{if(!t&&"number"==typeof e&&"number"==typeof a&&!(e<0)&&!(a<0)&&(ex(E),j(t=>{if(!t||!Array.isArray(t.headers))return t;let s={...t};return s.rows=Array.isArray(t.rows)?[...t.rows]:[],Array.isArray(s.rows[e])?s.rows[e]=[...s.rows[e]]:s.rows[e]=Array(s.headers.length).fill(""),a<s.headers.length&&(s.rows[e][a]=r),s}),V({row:e,col:a}),ev))try{let t=`${String.fromCharCode(65+a)}${e+1}`;await eb("sheet1",t,r),await ew("sheet1",t)}catch(e){console.error("Erro ao enviar mudan\xe7a via Real-time:",e)}},[t,ex,E,V,ev,eb,ew]),eH=()=>{t||(ex(E),j(e=>{let a;let r=e.headers.length>0?e.headers[e.headers.length-1]:null;a=r&&/^[A-Z]$/.test(r)?String.fromCharCode((r.charCodeAt(0)||64)+1):`Coluna ${e.headers.length+1}`;let t={...e};return t.headers=[...e.headers,a],t.rows=Array.isArray(e.rows)?e.rows.map(e=>Array.isArray(e)?[...e,""]:Array(t.headers.length).fill("")):[],t}),A.toast.success("Coluna adicionada"))},eG=(0,N.useCallback)(()=>{if(t)return;ex(E);let e=Array(E.headers.length).fill("");j(a=>{let r={...a};return r.rows=Array.isArray(a.rows)?[...a.rows,e]:[e],r}),A.toast.success("Linha adicionada")},[t,E,ex]),eX=e=>{if(t)return;ex(E);let a=[...E.rows];a.splice(e,1),j({...E,rows:a})},eJ=e=>{if(t)return;ex(E);let a=[...E.headers];a.splice(e,1);let r=E.rows.map(a=>{let r=[...a];return r.splice(e,1),r});j({...E,headers:a,rows:r})};(0,N.useCallback)(e=>{if(eg||t){A.toast.info("Aguarde",{description:"Espere o comando atual terminar antes de enviar outro.",duration:2e3});return}eE(e),A.toast.success("Comando enviado",{description:`Executando: "${e}"`,duration:2e3}),I&&D(!1)},[eg,t,eE,I]),(0,N.useCallback)(()=>{D(!0)},[]),(0,N.useCallback)(e=>{if(T||eg){A.toast.info("Aguarde",{description:"Concluindo opera\xe7\xf5es atuais antes de navegar...",duration:2e3});return}C.length>1&&C[O]!==a?confirm("H\xe1 altera\xe7\xf5es n\xe3o salvas. Deseja sair mesmo assim?")&&(window.location.href=e):window.location.href=e},[C,O,a,T,eg]),(0,N.useCallback)(()=>{A.toast.info("Conectando com Excel Desktop",{description:"Iniciando conex\xe3o com o aplicativo Excel...",duration:3e3}),desktopBridge.connect().then(e=>{e?A.toast.success("Excel conectado",{description:"Sincroniza\xe7\xe3o de dados ativada entre o navegador e Excel desktop",duration:3e3}):A.toast.error("Falha na conex\xe3o",{description:"N\xe3o foi poss\xedvel conectar ao Excel. Verifique se o Excel Copilot Desktop est\xe1 instalado e em execu\xe7\xe3o.",duration:4e3})}).catch(e=>{console.error("Erro na conex\xe3o:",e),A.toast.error("Erro na conex\xe3o",{description:"Ocorreu um erro ao tentar conectar ao Excel.",duration:4e3})})},[desktopBridge]),(0,N.useCallback)(()=>{G(!0),eh(()=>{G(!1)},3e3)},[eh]),(0,N.useCallback)(()=>{Y<a5.length-1?W(Y+1):Q(!1)},[Y]),(0,N.useCallback)(()=>{Q(!1)},[]);let[eQ,eY]=(0,N.useState)(!1),[eW,eK]=(0,N.useState)(!1),e0=(0,N.useCallback)(()=>[{key:"s",description:"Salvar planilha",action:eV,modifiers:{ctrl:!0}},{key:"z",description:"Desfazer \xfaltima a\xe7\xe3o",action:eP,modifiers:{ctrl:!0}},{key:"y",description:"Refazer \xfaltima a\xe7\xe3o",action:eB,modifiers:{ctrl:!0}},{key:"+",description:"Adicionar linha",action:eG,modifiers:{ctrl:!0,shift:!0}},{key:"=",description:"Adicionar coluna",action:eH,modifiers:{ctrl:!0,shift:!0}},{key:"k",description:"Abrir paleta de comandos",action:()=>D(!0),modifiers:{ctrl:!0}},{key:"/",description:"Focar no chat assistente",action:()=>document.getElementById("chat-input")?.focus(),modifiers:{ctrl:!0}},{key:"f",description:"Alternar modo tela cheia",action:()=>eK(!eW),modifiers:{ctrl:!0,shift:!0}},{key:"Escape",description:"Fechar di\xe1logos/pain\xe9is abertos",action:()=>{D(!1),eY(!1),eW&&eK(!1)}}],[eP,eB,eG,eH,eW]);(0,N.useCallback)(e=>{if("INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&!e.target.isContentEditable){for(let a of e0())if(e.key.toLowerCase()===a.key.toLowerCase()&&(!a.modifiers?.ctrl||e.ctrlKey)&&(!a.modifiers?.alt||e.altKey)&&(!a.modifiers?.shift||e.shiftKey)){e.preventDefault(),a.action();return}}},[e0]);let[e1,e2]=(0,N.useState)(!0),[e3,e4]=(0,N.useState)(""),e5=e=>{e4(e),e2(""===e.trim())},e9=()=>{ez.push("/pricing")},e6=()=>{ez.push("/api/checkout/trial")},e8=(0,N.useMemo)(()=>a4.map((e,a)=>s.jsx(a9,{command:e,onClick:()=>eZ(e.text)},a)),[eZ]),e7=(0,N.useRef)(null),ae=(0,N.useRef)(null),aa=(0,o.MG)({count:E.rows.length,getScrollElement:()=>e7.current,estimateSize:()=>36,overscan:10}),ar=(0,N.useMemo)(()=>s.jsx(a1,{visibleRows:aa.getVirtualItems().length,totalRows:E.rows.length,virtualizer:aa}),[aa,E.rows.length]),at=(0,N.useCallback)(async()=>{if(ep.current&&es){et(null);try{await ep.current.confirmAndExecute()&&(ed(es),el(!0),setTimeout(()=>{ei&&el(!1)},5e3))}catch(e){console.error("Erro ao executar comando:",e),A.toast.error("Erro ao executar o comando")}finally{eo(null)}}},[es,ep]),as=(0,N.useCallback)(()=>{ep.current&&(ep.current.cancelCommand(),et(null),eo(null))},[ep]),ao=(0,N.useCallback)(async e=>{try{await aG.M.storeFeedback(e),el(!1),ed(null)}catch(e){console.error("Erro ao enviar feedback:",e),A.toast.error("N\xe3o foi poss\xedvel enviar seu feedback")}},[]),an=(0,N.useCallback)(()=>{el(!1),ed(null)},[]);return(0,s.jsxs)("div",{className:"flex flex-col h-full w-full relative",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center border-b p-2 bg-background/80 backdrop-blur-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[s.jsx(ak.MD,{variant:"ghost",size:"icon",onClick:eP,disabled:O<=0||t,title:"Desfazer (Ctrl+Z)",children:s.jsx(u.Z,{className:"h-4 w-4"})}),s.jsx(ak.MD,{variant:"ghost",size:"icon",onClick:eB,disabled:O>=C.length-1||t,title:"Refazer (Ctrl+Y)",children:s.jsx(m.Z,{className:"h-4 w-4"})}),s.jsx("span",{className:"w-px h-4 bg-border mx-1"}),s.jsx(ak.MD,{variant:"ghost",size:"icon",onClick:eV,disabled:T||t,title:"Salvar (Ctrl+S)",children:T?s.jsx(p.Z,{className:"h-4 w-4 animate-spin"}):s.jsx(h.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"hidden md:flex gap-1 ml-2",children:[s.jsx(al,{workbookId:e,workbookName:E.name,sheets:[{name:E.name,data:E}]}),s.jsx(aq,{workbookId:e,saveToSupabase:!0,onUpload:e=>{e&&e.sheets&&e.sheets.length>0&&(ex(E),j(e.sheets[0].data))}})]})]}),(0,s.jsxs)("div",{className:"items-center gap-2 hidden md:flex",children:[s.jsx(ag,{workbookId:e,className:"mr-2"}),(0,s.jsxs)(S.Button,{variant:"ghost",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>U(!Z),children:[Z?s.jsx(x.Z,{className:"h-3 w-3"}):s.jsx(f.Z,{className:"h-3 w-3"}),Z?"Mostrar":"Ocultar"," AI"]}),(0,s.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>D(!0),children:[s.jsx(g.Z,{className:"h-3 w-3"}),"Comandos"]}),(0,s.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>{},children:[s.jsx(y.Z,{className:"h-3 w-3"}),"Tela Cheia"]})]}),s.jsx("div",{className:"flex md:hidden",children:(0,s.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-8",onClick:()=>z(!0),children:[s.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"AI Chat"]})})]}),(0,s.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[ar,s.jsx("div",{ref:e7,className:"overflow-auto border border-border rounded-md",style:{height:"400px",width:"100%"},children:(0,s.jsxs)("div",{className:"table w-full relative",children:[s.jsx("div",{ref:ae,className:"table-header-group sticky top-0 bg-background z-10",children:(0,s.jsxs)("div",{className:"table-row",children:[s.jsx("div",{className:"table-cell w-10 text-center text-xs font-medium bg-muted",children:"#"}),E.headers.map((e,a)=>(0,s.jsxs)("div",{className:"table-cell p-2 font-medium bg-muted",children:[e,!t&&s.jsx(ak.Kk,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 ml-1",actionId:`column-${a}`,onAction:()=>eJ(a),"aria-label":`Remover coluna ${e}`,children:s.jsx(d.Z,{className:"h-3 w-3"})})]},a)),s.jsx("div",{className:"table-cell w-10 bg-muted"})]})}),s.jsx("div",{className:"table-row-group relative",style:{height:`${aa.getTotalSize()}px`},children:aa.getVirtualItems().map(e=>{let a=E.rows[e.index]||[];return s.jsx("div",{className:"table-row",style:{position:"absolute",top:0,left:0,width:"100%",height:`${e.size}px`,transform:`translateY(${e.start}px)`},children:s.jsx(a2,{rowIndex:e.index,rowData:a,headers:E.headers,modifiedCellsMap:eU,readOnly:t,onCellChange:eq,onRemoveRow:eX})},e.index)})})]})}),ey&&ey.length>0&&s.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-3 py-1.5 rounded-full text-sm font-medium shadow-lg animate-in fade-in slide-in-from-bottom-5 duration-300",children:1===ey.length?"C\xe9lula atualizada":`${ey.length} c\xe9lulas atualizadas`})]}),s.jsx("div",{className:`
            h-full border-l overflow-hidden transition-all duration-300 ease-in-out
            ${Z?"w-0 opacity-0":"w-80 opacity-100"}
            hidden md:block
          `,"data-tutorial-target":"ai-assistant",children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("div",{className:"p-3 border-b flex items-center justify-between",children:[(0,s.jsxs)("h3",{className:"font-semibold text-sm flex items-center",children:[s.jsx(c.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),s.jsx(S.Button,{variant:"ghost",size:"icon",onClick:()=>U(!0),children:s.jsx(f.Z,{className:"h-4 w-4"})})]}),s.jsx(M.x,{className:"flex-1 p-3",children:0===eO.length?(0,s.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[s.jsx(c.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),s.jsx("h3",{className:"font-medium mb-1",children:"Excel Assistente"}),s.jsx("p",{className:"text-sm max-w-xs mx-auto",children:"Utilize linguagem natural para manipular sua planilha. Digite comandos como:"}),s.jsx("div",{className:"mt-4 space-y-2 text-xs",children:e8})]}):s.jsx("div",{className:"space-y-4",children:eO.map((e,a)=>(0,s.jsxs)("div",{className:`
                        p-3 rounded-lg text-sm
                        ${"user"===e.role?"bg-primary/10 border border-primary/20":"bg-muted"}
                      `,children:[s.jsx("div",{className:"text-xs font-medium mb-1 text-muted-foreground",children:"user"===e.role?"Voc\xea":"Excel Copilot"}),s.jsx("div",{className:"whitespace-pre-wrap",children:e.content})]},a))})}),s.jsx("div",{className:"p-3 border-t",children:s.jsx(B,{onSendMessage:eE,isLoading:eg,placeholder:"Digite um comando...",onChange:e5,showExamples:!1})})]})})]}),Z&&!_&&s.jsx(ak.MD,{variant:"default",size:"sm",className:"fixed right-4 bottom-4 shadow-lg rounded-full h-10 w-10 p-0",onClick:()=>U(!1),children:s.jsx(c.Z,{className:"h-4 w-4"})}),_&&(0,s.jsxs)("div",{className:`
      fixed inset-0 bg-background/95 backdrop-blur-sm z-50 flex flex-col
      ${$?"translate-y-0":"translate-y-full"}
      transition-transform duration-300 ease-in-out
    `,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,s.jsxs)("h3",{className:"font-semibold flex items-center",children:[s.jsx(c.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),s.jsx(S.Button,{variant:"ghost",size:"icon",onClick:()=>z(!1),children:s.jsx(d.Z,{className:"h-5 w-5"})})]}),s.jsx(M.x,{className:"flex-1 p-4",children:0===eO.length?(0,s.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[s.jsx(c.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),s.jsx("h3",{className:"text-lg font-medium mb-1",children:"Excel Copilot"}),s.jsx("p",{className:"text-sm max-w-sm mx-auto",children:"Envie comandos em linguagem natural para manipular sua planilha"})]}):s.jsx("div",{className:"space-y-4",children:eO.map((e,a)=>s.jsx("div",{className:`
                  p-3 rounded-lg max-w-[85%]
                  ${"user"===e.role?"bg-primary text-primary-foreground ml-auto":"bg-muted text-foreground border"}
                `,children:e.content},a))})}),s.jsx("div",{className:"p-4 border-t",children:s.jsx(B,{onSendMessage:eE,isLoading:eg,placeholder:"Digite um comando...",showExamples:0===eO.length})})]}),s.jsx(av,{open:e$,onOpenChange:eM,children:(0,s.jsxs)(aN,{className:"max-w-md",children:[(0,s.jsxs)(aA,{children:[(0,s.jsxs)(aj,{className:"flex items-center gap-2",children:[s.jsx(w.Z,{className:"h-5 w-5 text-amber-500"}),"Voc\xea est\xe1 chegando ao limite"]}),(0,s.jsxs)(aC,{className:"text-base",children:["Voc\xea j\xe1 utilizou ",eI?.used||0," de ",eI?.limit||50," comandos dispon\xedveis no seu plano. Para continuar utilizando todos os recursos do Excel Copilot, escolha uma op\xe7\xe3o abaixo:"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 my-4",children:[(0,s.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>e6(),children:[(0,s.jsxs)("h3",{className:"font-semibold flex items-center",children:[s.jsx(c.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Experimente o Plano Pro Gr\xe1tis por 7 dias"]}),s.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Acesso total a todos os recursos sem limita\xe7\xf5es durante 7 dias. Ser\xe1 necess\xe1rio informar um cart\xe3o, mas voc\xea pode cancelar a qualquer momento."})]}),(0,s.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>e9(),children:[(0,s.jsxs)("h3",{className:"font-semibold flex items-center",children:[s.jsx(i.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Fazer Upgrade para o Plano Pro"]}),s.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"R$20/m\xeas ou R$200/ano. Acesso ilimitado a todos os recursos premium."})]})]}),s.jsx(aE,{children:s.jsx(aS,{children:"Continuar no Plano Free"})})]})}),eI&&eI.used/eI.limit>=.8&&eI.used/eI.limit<1&&(0,s.jsxs)("div",{className:"bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800/50 border px-4 py-2 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(w.Z,{className:"h-4 w-4 text-amber-500"}),(0,s.jsxs)("span",{className:"text-sm text-amber-800 dark:text-amber-300",children:["Voc\xea utilizou ",Math.round(eI.used/eI.limit*100),"% do seu limite mensal de comandos."]})]}),s.jsx(S.Button,{variant:"ghost",size:"sm",onClick:()=>eM(!0),children:"Fazer Upgrade"})]}),eI&&eI.used>=eI.limit&&(0,s.jsxs)("div",{className:"bg-destructive/10 border-destructive/30 border px-4 py-2 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(w.Z,{className:"h-4 w-4 text-destructive"}),s.jsx("span",{className:"text-sm text-destructive",children:"Voc\xea atingiu 100% do seu limite mensal de comandos."})]}),s.jsx(S.Button,{variant:"outline",size:"sm",onClick:()=>eM(!0),children:"Fazer Upgrade Agora"})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 p-4 border-t dark:border-gray-800",children:[(0,s.jsxs)("div",{className:"flex-1",children:[er&&s.jsx(X,{command:es?.command||"",interpretation:er,isLoading:eg,onExecute:at,onCancel:as}),ei&&ec&&s.jsx(q,{commandId:ec.id,command:ec.command,onDismiss:an,onFeedbackSubmit:ao}),s.jsx(B,{onSendMessage:eE,isLoading:eg,disabled:eg||t,autoFocus:!0,onChange:e5})]}),s.jsx("div",{className:"w-full lg:w-64 space-y-2"})]})]})}a9.displayName="MemoizedQuickCommandButton"},28612:(e,a,r)=>{r.d(a,{M:()=>o});var t=r(51641);class s{constructor(){this.feedbackItems=[],this.feedbackStorage=null,this.STORAGE_KEY="excel_copilot_feedback",this.MAX_STORED_ITEMS=100}static getInstance(){return s.instance||(s.instance=new s),s.instance}async storeFeedback(e){let a={...e,timestamp:new Date().toISOString()};if(this.feedbackItems.unshift(a),this.feedbackItems.length>this.MAX_STORED_ITEMS&&(this.feedbackItems=this.feedbackItems.slice(0,this.MAX_STORED_ITEMS)),this.saveToStorage(),"true"===process.env.NEXT_PUBLIC_ENABLE_FEEDBACK_API)try{await fetch("/api/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}catch(e){t.logger.error("Erro ao enviar feedback para API:",e)}}getAnalytics(){let e=this.feedbackItems.length;if(0===e)return{totalCommands:0,successRate:0,commonIssues:[],commandPatterns:[]};let a=this.feedbackItems.filter(e=>e.successful).length,r=this.feedbackItems.filter(e=>!e.successful&&e.feedbackText),t=new Map;r.forEach(e=>{let a=e.feedbackText?.toLowerCase()||"",r=!1;for(let e of[{word:"entend",issue:"N\xe3o entendeu o comando"},{word:"error",issue:"Erro na execu\xe7\xe3o"},{word:"lent",issue:"Performance lenta"},{word:"format",issue:"Problemas de formata\xe7\xe3o"},{word:"gr\xe1fico",issue:"Problemas com gr\xe1ficos"},{word:"tabela",issue:"Problemas com tabelas"},{word:"f\xf3rmula",issue:"Problemas com f\xf3rmulas"}])a.includes(e.word)&&(t.set(e.issue,(t.get(e.issue)||0)+1),r=!0);!r&&a.length>0&&t.set("Outros problemas",(t.get("Outros problemas")||0)+1)});let s=Array.from(t.entries()).map(([e,a])=>({issue:e,count:a})).sort((e,a)=>a.count-e.count),o=new Map;return this.feedbackItems.forEach(e=>{let a=e.command.toLowerCase();for(let r of[{words:["cri","tabela"],pattern:"Criar tabela"},{words:["gr\xe1fico","chart"],pattern:"Criar gr\xe1fico"},{words:["calcul","m\xe9dia","soma"],pattern:"C\xe1lculos"},{words:["format","cor","estilo"],pattern:"Formata\xe7\xe3o"},{words:["filtr","ordem"],pattern:"Filtro/Ordena\xe7\xe3o"}])if(r.words.some(e=>a.includes(e))){let a=o.get(r.pattern)||{success:0,total:0};o.set(r.pattern,{success:a.success+(e.successful?1:0),total:a.total+1});break}}),{totalCommands:e,successRate:a/e*100,commonIssues:s,commandPatterns:Array.from(o.entries()).map(([e,a])=>({pattern:e,successRate:a.success/a.total*100,count:a.total})).sort((e,a)=>a.count-e.count)}}saveToStorage(){if(this.feedbackStorage)try{this.feedbackStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.feedbackItems))}catch(e){t.logger.error("Erro ao salvar feedback no localStorage:",e)}}loadFromStorage(){if(this.feedbackStorage)try{let e=this.feedbackStorage.getItem(this.STORAGE_KEY);e&&(this.feedbackItems=JSON.parse(e))}catch(e){t.logger.error("Erro ao carregar feedback do localStorage:",e),this.feedbackItems=[]}}}let o=s.getInstance()},96671:(e,a,r)=>{var t;r.r(a),r.d(a,{DEFAULT_EXCEL_SYSTEM_PROMPT:()=>o,ExcelAIProcessor:()=>s,GeminiErrorType:()=>t,createExcelAIProcessor:()=>n,getGeminiServiceAPI:()=>i}),r(51641);class s{constructor(e={},a=!0){this.context={activeSheet:e.activeSheet||"Sheet1",headers:e.headers||[],selection:e.selection||"A1",recentOperations:e.recentOperations||[]},this.useRealAI=a}async processQuery(e){try{let a=this.preprocessQuery(e),r=this.buildPrompt(a),t=await i(),s=await t.sendMessage(r,{context:JSON.stringify(this.context),useMock:!this.useRealAI});return this.parseAIResponse(s)}catch(e){return console.error("Erro ao processar query:",e),{operations:[],error:`Erro ao processar: ${e instanceof Error?e.message:String(e)}`,success:!1,message:"Falha ao processar query com IA"}}}preprocessQuery(e){return e.replace(/\bform\./g,"f\xf3rmula").replace(/\bcol\./g,"coluna").replace(/\btab\./g,"tabela").replace(/\bgraf\./g,"gr\xe1fico").replace(/\bcel\./g,"c\xe9lula").replace(/\bfunc\./g,"fun\xe7\xe3o").replace(/\bop\./g,"opera\xe7\xe3o").replace(/\bval\./g,"valor").replace(/\bmed\./g,"m\xe9dia")}buildPrompt(e){return`
    Analise o seguinte comando para Excel e retorne as opera\xe7\xf5es necess\xe1rias em formato JSON:
    
    Comando: "${e}"
    
    Contexto da planilha:
    - Planilha ativa: ${this.context.activeSheet}
    - Sele\xe7\xe3o atual: ${this.context.selection}
    - Cabe\xe7alhos: ${this.context.headers?.join(", ")||"N/A"}
    
    Retorne APENAS um objeto JSON com a seguinte estrutura:
    {
      "operations": [
        {
          "type": "TIPO_OPERACAO", // FORMULA, CHART, TABLE, FORMAT, etc.
          "data": { ... }, // Dados espec\xedficos da opera\xe7\xe3o
          "description": "Descri\xe7\xe3o" // Opcional, descri\xe7\xe3o da opera\xe7\xe3o
        }
      ],
      "explanation": "Explica\xe7\xe3o do que foi feito" // Opcional
    }
    `}parseAIResponse(e){try{let a=e.match(/\{[\s\S]*\}/);if(a){let e=a[0],r=JSON.parse(e);if(!r.operations||!Array.isArray(r.operations))throw Error("Formato de resposta inv\xe1lido: operations n\xe3o \xe9 um array");return r}return{operations:[{type:"TABLE",data:{rawResponse:e},description:`Resposta em texto: ${e.substring(0,100)}...`}],explanation:"A resposta n\xe3o p\xf4de ser processada como JSON",success:!0,message:"Processamento parcial realizado"}}catch(a){return console.error("Erro ao analisar resposta da IA:",a),{operations:[{type:"TABLE",data:{error:!0},description:`Processando: "${e.substring(0,100)}..."`}],explanation:"Erro ao processar JSON da resposta",error:String(a),success:!1,message:"Falha ao analisar resposta da IA"}}}}let o=`
Voc\xea \xe9 um assistente especializado em Excel, capaz de ajudar a realizar opera\xe7\xf5es com planilhas.
Sua fun\xe7\xe3o \xe9 interpretar comandos em linguagem natural e convert\xea-los em opera\xe7\xf5es Excel espec\xedficas.

# DIRETRIZES IMPORTANTES
1. Sempre responda de forma estruturada usando o formato JSON abaixo
2. Para cada comando, identifique as opera\xe7\xf5es necess\xe1rias e forne\xe7a par\xe2metros precisos
3. Em caso de ambiguidade, escolha a interpreta\xe7\xe3o mais prov\xe1vel baseada no contexto
4. Se n\xe3o conseguir interpretar o comando, forne\xe7a uma resposta de erro amig\xe1vel
5. NUNCA invente dados ou colunas que n\xe3o existam no contexto atual

# FORMATO DE RESPOSTA
{
  "operations": [
    {
      "type": "TIPO_DA_OPERA\xc7\xc3O",
      "data": { ... par\xe2metros espec\xedficos da opera\xe7\xe3o ... }
    }
  ],
  "explanation": "Breve explica\xe7\xe3o do que ser\xe1 feito",
  "interpretation": "Como voc\xea entendeu o comando do usu\xe1rio"
}

# TIPOS DE OPERA\xc7\xd5ES DISPON\xcdVEIS

## F\xd3RMULAS
- FORMULA: Aplicar f\xf3rmulas em c\xe9lulas
  {
    "type": "FORMULA",
    "data": {
      "formula": "=SOMA(A1:A10)", 
      "range": "B1" | ["B1", "B2"] | "B1:B10"
    }
  }

## DADOS
- FILTER: Filtrar dados
  {
    "type": "FILTER",
    "data": {
      "column": "A" | 1,
      "condition": ">" | "<" | "=" | "contains" | "between",
      "value": 100 | "texto" | [10, 20]
    }
  }
- SORT: Ordenar dados
  {
    "type": "SORT",
    "data": {
      "column": "A" | 1,
      "direction": "asc" | "desc"
    }
  }

## VISUALIZA\xc7\xd5ES
- CHART: Criar ou modificar gr\xe1ficos
  {
    "type": "CHART",
    "data": {
      "type": "bar" | "line" | "pie" | "scatter" | "area",
      "title": "T\xedtulo do gr\xe1fico",
      "labels": "A1:A10", // Eixo X ou categorias
      "datasets": ["B1:B10", "C1:C10"], // S\xe9ries de dados
      "options": { ... op\xe7\xf5es adicionais ... }
    }
  }

## FORMATA\xc7\xc3O
- CONDITIONAL_FORMAT: Formata\xe7\xe3o condicional
  {
    "type": "CONDITIONAL_FORMAT",
    "data": {
      "range": "A1:B10",
      "rule": "greater" | "less" | "equal" | "between" | "text" | "date",
      "value": 100 | [10, 20] | "texto",
      "format": {
        "background": "#F5F5F5",
        "textColor": "#FF0000",
        "bold": true | false,
        "italic": true | false
      }
    }
  }

## TABELAS
- PIVOT_TABLE: Criar tabelas din\xe2micas
  {
    "type": "PIVOT_TABLE",
    "data": {
      "source": "A1:D10",
      "rows": ["A"], // Campos para linhas
      "columns": ["B"], // Campos para colunas
      "values": [{ "field": "C", "function": "sum" }], // Campos para valores
      "filters": [{ "field": "D", "value": "X" }] // Filtros opcionais
    }
  }

## C\xc9LULAS
- CELL_UPDATE: Atualizar c\xe9lulas individuais
  {
    "type": "CELL_UPDATE",
    "data": {
      "updates": [
        { "cell": "A1", "value": 100 },
        { "cell": "B1", "value": "Texto" }
      ]
    }
  }

## AN\xc1LISE
- DATA_ANALYSIS: An\xe1lise estat\xedstica
  {
    "type": "DATA_ANALYSIS",
    "data": {
      "type": "statistics" | "correlation" | "regression",
      "range": "A1:B10",
      "options": { ... op\xe7\xf5es espec\xedficas ... }
    }
  }

# EXEMPLOS DE COMANDOS E RESPOSTAS

## Exemplo 1: "Calcule a m\xe9dia da coluna B"
{
  "operations": [
    {
      "type": "FORMULA",
      "data": {
        "formula": "=M\xc9DIA(B:B)",
        "range": "C1"
      }
    }
  ],
  "explanation": "Calculando a m\xe9dia da coluna B e colocando o resultado na c\xe9lula C1",
  "interpretation": "Voc\xea deseja calcular a m\xe9dia de todos os valores num\xe9ricos na coluna B"
}

## Exemplo 2: "Crie uma tabela de vendas por m\xeas"
{
  "operations": [
    {
      "type": "CELL_UPDATE",
      "data": {
        "updates": [
          { "cell": "A1", "value": "M\xeas" },
          { "cell": "B1", "value": "Vendas" },
          { "cell": "A2", "value": "Janeiro" },
          { "cell": "A3", "value": "Fevereiro" },
          { "cell": "A4", "value": "Mar\xe7o" },
          { "cell": "B2", "value": 0 },
          { "cell": "B3", "value": 0 },
          { "cell": "B4", "value": 0 }
        ]
      }
    }
  ],
  "explanation": "Criando uma tabela de vendas por m\xeas com layout b\xe1sico",
  "interpretation": "Voc\xea deseja criar uma nova tabela para registrar vendas mensais"
}

## Exemplo 3: "Gere um gr\xe1fico de barras com os dados da coluna A e B"
{
  "operations": [
    {
      "type": "CHART",
      "data": {
        "type": "bar",
        "title": "Gr\xe1fico de Barras A vs B",
        "labels": "A1:A10",
        "datasets": ["B1:B10"],
        "options": {
          "legend": true,
          "horizontalBar": false
        }
      }
    }
  ],
  "explanation": "Criando um gr\xe1fico de barras usando dados das colunas A e B",
  "interpretation": "Voc\xea deseja visualizar os dados das colunas A e B em um gr\xe1fico de barras"
}

# CONTEXTO ATUAL DA PLANILHA
{contextInfo}
`;function n(e=!1){return new s({},e)}async function i(){return{async sendMessage(e,a={}){let r=await fetch("http://localhost:3000/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,...a})});if(!r.ok)throw Error(`API Error: ${r.statusText}`);return(await r.json()).response}}}!function(e){e.NETWORK_ERROR="NETWORK_ERROR",e.API_ERROR="API_ERROR",e.RATE_LIMIT="RATE_LIMIT",e.INVALID_REQUEST="INVALID_REQUEST",e.AUTHENTICATION_ERROR="AUTHENTICATION_ERROR",e.UNKNOWN="UNKNOWN",e.API_UNAVAILABLE="API_UNAVAILABLE"}(t||(t={}))},96833:(e,a,r)=>{r.d(a,{Nt:()=>n,Xf:()=>s}),r(89244);var t=r(89275);let s={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"};s.FREE,s.PRO_MONTHLY,s.PRO_ANNUAL;let o=process.env.STRIPE_SECRET_KEY||"";function n(e){switch(e){case s.FREE:return"Gr\xe1tis";case s.PRO_MONTHLY:return"Pro Mensal";case s.PRO_ANNUAL:return"Pro Anual";default:return"Desconhecido"}}o&&new t.Z(o,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}})},64349:(e,a,r)=>{r.d(a,{prisma:()=>i});var t=r(94007);let s={info:(e,...a)=>{},error:(e,...a)=>{console.error(`[DB ERROR] ${e}`,...a)},warn:(e,...a)=>{console.warn(`[DB WARNING] ${e}`,...a)}},o={totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null},n=[],i=global.prisma||new t.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});async function l(){try{await i.$disconnect(),s.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){s.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{o.totalQueries++,e.duration&&(n.push(e.duration),n.length>100&&n.shift(),o.averageQueryTime=n.reduce((e,a)=>e+a,0)/n.length),e.duration&&e.duration>500&&s.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{o.failedQueries++,o.connectionFailures++,o.lastConnectionFailure=new Date().toISOString(),s.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{l()})},35342:(e,a,r)=>{var t,s,o,n,i;r.d(a,{ox:()=>t}),function(e){e.FORMULA="FORMULA",e.FILTER="FILTER",e.SORT="SORT",e.FORMAT="FORMAT",e.CHART="CHART",e.CELL_UPDATE="CELL_UPDATE",e.COLUMN_OPERATION="COLUMN_OPERATION",e.ROW_OPERATION="ROW_OPERATION",e.TABLE="TABLE",e.DATA_TRANSFORMATION="DATA_TRANSFORMATION",e.PIVOT_TABLE="PIVOT_TABLE",e.CONDITIONAL_FORMAT="CONDITIONAL_FORMAT",e.ADVANCED_CHART="ADVANCED_CHART",e.ADVANCED_VISUALIZATION="ADVANCED_VISUALIZATION",e.RANGE_UPDATE="RANGE_UPDATE",e.CELL_MERGE="CELL_MERGE",e.CELL_SPLIT="CELL_SPLIT",e.NAMED_RANGE="NAMED_RANGE",e.VALIDATION="VALIDATION",e.FREEZE_PANES="FREEZE_PANES",e.SHEET_OPERATION="SHEET_OPERATION",e.ANALYSIS="ANALYSIS",e.GENERIC="GENERIC"}(t||(t={})),function(e){e.LINE="LINE",e.BAR="BAR",e.COLUMN="COLUMN",e.AREA="AREA",e.SCATTER="SCATTER",e.PIE="PIE"}(s||(s={})),function(e){e.EQUALS="equals",e.NOT_EQUALS="notEquals",e.GREATER_THAN="greaterThan",e.LESS_THAN="lessThan",e.GREATER_THAN_OR_EQUAL="greaterThanOrEqual",e.LESS_THAN_OR_EQUAL="lessThanOrEqual",e.CONTAINS="contains",e.NOT_CONTAINS="notContains",e.BEGINS_WITH="beginsWith",e.ENDS_WITH="endsWith",e.BETWEEN="between"}(o||(o={})),function(e){e.DISCONNECTED="disconnected",e.CONNECTING="connecting",e.CONNECTED="connected",e.ERROR="error"}(n||(n={})),function(e){e.FORMULA_ERROR="FORMULA_ERROR",e.REFERENCE_ERROR="REFERENCE_ERROR",e.VALUE_ERROR="VALUE_ERROR",e.NAME_ERROR="NAME_ERROR",e.RANGE_ERROR="RANGE_ERROR",e.SYNTAX_ERROR="SYNTAX_ERROR",e.DATA_VALIDATION_ERROR="DATA_VALIDATION_ERROR",e.FORMAT_ERROR="FORMAT_ERROR",e.OPERATION_NOT_SUPPORTED="OPERATION_NOT_SUPPORTED",e.UNKNOWN_ERROR="UNKNOWN_ERROR"}(i||(i={}))}};