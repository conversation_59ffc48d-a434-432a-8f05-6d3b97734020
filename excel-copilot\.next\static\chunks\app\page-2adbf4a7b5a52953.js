(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{63029:function(e,a,t){Promise.resolve().then(t.t.bind(t,231,23)),Promise.resolve().then(t.bind(t,76585)),Promise.resolve().then(t.bind(t,31309)),Promise.resolve().then(t.bind(t,89733))},76585:function(e,a,t){"use strict";t.d(a,{CommandExamplesWrapper:function(){return j}});var s=t(57437),r=t(16463),o=t(34567),n=t(77424),l=t(82064),i=t(92222),d=t(26209),c=t(33907),m=t(74622),u=t(42421),x=t(2265),p=t(79055),h=t(89733),f=t(40584),g=t(77209),v=t(188),N=t(80023),b=t(4919),E=(t(74109),t(92699),t(38296),t(79512),t(89736),t(49354),t(78068));E.s6,p.C,h.Button,f.ZP,g.Z,v.MD,v.Kk,N.x,b.Z,E.V6;var A=t(48185);function y(e){let{onSelect:a}=e,[t,r]=(0,x.useState)(!1),[p,f]=(0,x.useState)(-1),[g,b]=(0,x.useState)(!1),E=[{name:"An\xe1lise Matem\xe1tica",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5"}),examples:["Some os valores da coluna B","Calcule a m\xe9dia da coluna Vendas","Qual \xe9 o maior valor na coluna de Receita?","Encontre o desvio padr\xe3o dos dados na coluna E","Multiplique os valores da coluna A por 2","Calcule o percentual de crescimento m\xeas a m\xeas","Fa\xe7a uma an\xe1lise estat\xedstica completa da coluna Valores"]},{name:"Visualiza\xe7\xe3o",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),examples:["Crie um gr\xe1fico de barras com os dados das colunas A e B","Fa\xe7a um gr\xe1fico de linha de vendas por m\xeas","Gere um gr\xe1fico de pizza com os valores da tabela","Mostre um gr\xe1fico de dispers\xe3o entre pre\xe7o e quantidade","Crie um histograma da coluna Idades","Fa\xe7a um dashboard com 3 gr\xe1ficos: barras, linha e pizza","Crie um mapa de calor com os dados de vendas por regi\xe3o"]},{name:"Filtros e Organiza\xe7\xe3o",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),examples:["Filtre os dados onde Vendas > 1000","Ordene a tabela por valor, do maior para o menor","Mostre apenas registros onde a coluna Regi\xe3o \xe9 'Sul'","Filtre dados do m\xeas de Janeiro","Remova as linhas com valores nulos","Filtre os 10 maiores clientes por volume de compras","Agrupe por categoria e mostre apenas os grupos com mais de 5 itens"]},{name:"Formata\xe7\xe3o",icon:(0,s.jsx)(i.Z,{className:"h-5 w-5"}),examples:["Converta a planilha atual para formato de tabela","Aplique formata\xe7\xe3o condicional na coluna de valores","Destaque c\xe9lulas com valores negativos em vermelho","Formate a coluna de datas no padr\xe3o DD/MM/AAAA","Adicione uma linha de totais ao final da tabela","Formate os cabe\xe7alhos com fundo azul e texto branco em negrito","Adicione bordas em todas as c\xe9lulas da tabela"]},{name:"Tabelas Din\xe2micas",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5"}),examples:["Crie uma tabela din\xe2mica agrupando vendas por regi\xe3o","Fa\xe7a uma tabela din\xe2mica com vendas por produto e vendedor","Gere uma tabela din\xe2mica de receitas mensais por categoria","Crie um resumo de vendas por trimestre e regi\xe3o","Fa\xe7a uma tabela din\xe2mica com subtotais por departamento"]},{name:"An\xe1lise Avan\xe7ada",icon:(0,s.jsx)(c.Z,{className:"h-5 w-5"}),examples:["Fa\xe7a uma an\xe1lise de correla\xe7\xe3o entre as colunas pre\xe7o e demanda","Gere estat\xedsticas descritivas de todas as colunas num\xe9ricas","Crie segmenta\xe7\xe3o de dados por faixa et\xe1ria: 0-18, 19-35, 36-60, 60+","Aplique regress\xe3o linear e preveja valores futuros","Fa\xe7a uma an\xe1lise de sazonalidade nos dados mensais","Identifique outliers na coluna de vendas e sugira tratamentos","Calcule a previs\xe3o de vendas para os pr\xf3ximos 3 meses usando s\xe9rie temporal","Agrupe clientes por comportamento de compra usando K-means","Fa\xe7a uma an\xe1lise de cesta de compras para identificar produtos complementares","Crie um modelo de pontua\xe7\xe3o para classificar leads por potencial"]},{name:"Perguntas em Linguagem Natural",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),examples:["Quais foram os 3 melhores vendedores no \xfaltimo trimestre?","Qual regi\xe3o teve a maior queda nas vendas?","Como est\xe1 o desempenho das vendas comparado com o mesmo per\xedodo do ano passado?","Quais produtos tiveram crescimento acima de 10% nos \xfaltimos 6 meses?","Qual \xe9 a tend\xeancia de vendas para o produto X?"]}],y=E[0]||{name:"An\xe1lise Matem\xe1tica",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5"}),examples:["Some os valores da coluna B"]},j=y.examples[0]||"";(0,x.useEffect)(()=>{let e=()=>{b(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let R=(0,x.useCallback)((e,t,s)=>{let r=e.key;if("Enter"===r||" "===r){e.preventDefault();let r=E[t];r&&r.examples[s]&&a(r.examples[s])}if("ArrowDown"===r||"ArrowUp"===r){var o,n;e.preventDefault();let a=E[t];if(!a)return;let l=a.examples.length-1;"ArrowDown"===r&&s<l?(f(s+1),null===(o=document.getElementById("example-".concat(t,"-").concat(s+1)))||void 0===o||o.focus()):"ArrowUp"===r&&s>0&&(f(s-1),null===(n=document.getElementById("example-".concat(t,"-").concat(s-1)))||void 0===n||n.focus())}},[a]);return(0,s.jsx)("div",{className:"space-y-4 w-full",children:(0,s.jsxs)(A.Zb,{className:"border shadow-sm bg-card",children:[(0,s.jsx)(A.Ol,{className:"pb-2",children:(0,s.jsxs)(A.ll,{className:"text-lg flex items-center",children:[(0,s.jsx)(c.Z,{className:"h-6 w-6 mr-2 text-primary","aria-hidden":"true"}),(0,s.jsx)("span",{className:"text-enhanced-contrast",children:"Exemplos de Comandos"})]})}),(0,s.jsxs)(A.aY,{children:[(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("div",{className:"p-1 rounded-md bg-primary/10 text-primary","aria-hidden":"true",children:y.icon}),(0,s.jsx)("span",{className:"text-xs font-medium text-muted-foreground",children:y.name})]}),(0,s.jsx)(h.Button,{variant:"outline",className:"w-full justify-start text-left font-normal h-auto py-3 border-primary/20 bg-primary/5 hover:bg-primary/10 a11y-focus",onClick:()=>a(j),"aria-label":"Usar comando: ".concat(j),children:(0,s.jsx)("span",{className:"line-clamp-2 text-xs sm:text-sm",children:j})})]}),t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(N.x,{className:"h-[200px] sm:h-[250px] md:h-[300px] pr-4",role:"list","aria-label":"Lista de comandos por categoria",children:(0,s.jsx)("div",{className:"space-y-3 sm:space-y-4",children:E.map((e,t)=>(0,s.jsxs)("div",{className:"space-y-1.5 sm:space-y-2",role:"group","aria-labelledby":"category-".concat(t),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"p-1 sm:p-1.5 rounded-md bg-muted text-muted-foreground","aria-hidden":"true",children:e.icon}),(0,s.jsx)("span",{className:"text-xs sm:text-sm font-medium",id:"category-".concat(t),children:e.name})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-1 sm:gap-1.5 pl-5 sm:pl-7",role:"list",children:e.examples.slice(0,g?4:void 0).map((e,r)=>(0,s.jsx)(v.Kk,{id:"example-".concat(t,"-").concat(r),variant:"ghost",size:"sm",className:"justify-start h-auto py-1 sm:py-1.5 px-1.5 sm:px-2 text-xs sm:text-sm font-normal text-muted-foreground hover:text-foreground mobile-enhanced-tap a11y-focus",actionId:"".concat(t,"-").concat(r),onAction:()=>a(e),onKeyDown:e=>R(e,t,r),"aria-label":"Usar comando: ".concat(e),children:(0,s.jsx)("span",{className:"line-clamp-1",children:e})},r))})]},t))})}),(0,s.jsxs)(h.Button,{onClick:()=>r(!1),variant:"ghost",size:"sm",className:"w-full mt-2 text-sm text-muted-foreground hover:text-foreground flex items-center justify-center","aria-expanded":"true","aria-label":"Mostrar menos exemplos",children:[(0,s.jsx)("span",{children:"Mostrar menos"}),(0,s.jsx)(u.Z,{className:"h-4 w-4 ml-1 transform rotate-180"})]})]}):(0,s.jsxs)(h.Button,{onClick:()=>r(!0),variant:"ghost",size:"sm",className:"w-full text-sm text-muted-foreground hover:text-foreground flex items-center justify-center","aria-expanded":"false","aria-label":"Mostrar mais exemplos",children:[(0,s.jsx)("span",{children:"Explorar mais sugest\xf5es"}),(0,s.jsx)(u.Z,{className:"h-4 w-4 ml-1"})]})]})]})})}function j(){let e=(0,r.useRouter)();return(0,s.jsx)(y,{onSelect:a=>{e.push("/dashboard?command=".concat(encodeURIComponent(a)))}})}},31309:function(e,a,t){"use strict";t.r(a),t.d(a,{HeroSection:function(){return _}});var s=t(57437),r=t(751),o=t(847),n=t(33907),l=t(71976),i=t(22468),d=t(87138),c=t(2265),m=t(46572),u=t(35231),x=t(25974),p=t(54142),h=t(2842),f=t(9542),g=t(85475),v=t(20153),N=t(45745),b=t(92566),E=t(16638),A=t(23909),y=t(85369),j=t(68295),R=t(79055),T=t(89733),O=t(49354),w=t(64451);let C=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658","#8DD1E1"],S=[{type:"message",content:'Posso ajudar voc\xea a trabalhar com suas planilhas usando comandos em linguagem natural. Por exemplo, tente "Crie uma tabela de vendas por regi\xe3o" ou "Calcule a m\xe9dia da coluna B".'},{type:w.ox.TABLE,command:"Crie uma tabela com vendas por regi\xe3o",content:[["Regi\xe3o","Vendas","Meta","% Atingimento"],["Norte","12500","15000","83%"],["Sul","18200","16000","114%"],["Leste","14800","14000","106%"],["Oeste","9300","12000","78%"],["Centro","11700","10000","117%"]]},{type:w.ox.CHART,command:"Crie um gr\xe1fico de vendas por regi\xe3o",chartType:"bar",data:{labels:["Norte","Sul","Leste","Oeste","Centro"],datasets:[{label:"Vendas",data:[12500,18200,14800,9300,11700],backgroundColor:"rgba(53, 162, 235, 0.5)"},{label:"Meta",data:[15e3,16e3,14e3,12e3,1e4],backgroundColor:"rgba(255, 99, 132, 0.5)"}]}},{type:w.ox.TABLE,command:"Fa\xe7a uma an\xe1lise das vendas",content:[["M\xe9trica","Valor","Status"],["Total de Vendas","R$ 66.500,00","↑ 12%"],["M\xe9dia por Regi\xe3o","R$ 13.300,00","-"],["Maior Desempenho","Sul (114%)","★★★"],["Menor Desempenho","Oeste (78%)","⚠️"],["Regi\xf5es Acima da Meta","3","↑ 1"]]}];function _(){var e,a;let[t,_]=(0,c.useState)(0),[D,I]=(0,c.useState)(!1),[L,M]=(0,c.useState)(!1),[F,P]=(0,c.useState)(!1),[z,U]=(0,c.useState)([]),[V,Z]=(0,c.useState)(!1),B=(0,r.J)();(0,c.useEffect)(()=>{let e=()=>{Z(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let G=(0,c.useMemo)(()=>B||V?{initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},disableMotion:!0}:{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},disableMotion:!1},[B,V]),K=(0,c.useCallback)(e=>{I(!0),0===t?U(S.slice(0,2)):1===t?U(S.slice(0,4)):2===t&&U(S)},[t]),q=(0,c.useCallback)(()=>{if(t<S.length-1){P(!0);let e=t+1;_(e),setTimeout(()=>{var a;P(!1),K((null===(a=S[e])||void 0===a?void 0:a.command)||"")},1500)}else _(0),I(!1),U([])},[t,K]);return(0,c.useEffect)(()=>{if(L){let e=setTimeout(()=>{q()},4e3);return()=>clearTimeout(e)}},[t,L,q]),(0,c.useEffect)(()=>{let e=setTimeout(()=>{var e;K((null===(e=S[0])||void 0===e?void 0:e.command)||"")},1e3);return()=>clearTimeout(e)},[K]),(0,c.useEffect)(()=>{var e,a,s;if(!F&&(null===(e=S[t])||void 0===e?void 0:e.command)){let e=setTimeout(()=>{var e;P(!0),K((null===(e=S[t])||void 0===e?void 0:e.command)||"")},50*((null===(s=S[t])||void 0===s?void 0:null===(a=s.command)||void 0===a?void 0:a.length)||0)+500);return()=>clearTimeout(e)}},[F,t,K]),(0,c.useCallback)(()=>{if(t>0){P(!0);let e=t-1;_(e),setTimeout(()=>{var a;P(!1),K((null===(a=S[e])||void 0===a?void 0:a.command)||"")},500)}},[t,K]),(0,c.useEffect)(()=>{let e=e=>{var a,s;"ArrowRight"===e.key?q():"ArrowLeft"===e.key&&t>0?(_(e=>e-1),P(!0),K((null===(s=S[t-1])||void 0===s?void 0:s.command)||"")):" "===e.key&&(null===(a=document.activeElement)||void 0===a?void 0:a.id)==="autoplay-button"&&(e.preventDefault(),M(!L))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[t,L,K,q]),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-8 py-8",children:[(0,s.jsxs)("div",{className:"text-left md:w-1/2",children:[(0,s.jsxs)("div",{className:"inline-flex items-center rounded-full bg-gradient-to-r from-blue-500/10 to-indigo-500/10 backdrop-blur-sm px-3 py-1 mb-4 shadow-sm",children:[(0,s.jsx)(n.Z,{className:"h-3 w-3 mr-1 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:"IA para Excel"})]}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold tracking-tight mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 leading-tight",children:"Planilhas inteligentes com linguagem natural"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl",children:"Excel Copilot transforma comandos em texto simples em planilhas poderosas."}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,s.jsx)(T.Button,{size:"lg",className:"rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0 px-6 py-2 text-sm shadow-md font-medium transition-all",asChild:!0,children:(0,s.jsxs)(d.default,{href:"/dashboard",children:["Come\xe7ar agora ",(0,s.jsx)(l.Z,{className:"ml-1 h-4 w-4"})]})}),(0,s.jsx)(T.Button,{variant:"outline",size:"lg",className:"rounded-full border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700 px-6 py-2 text-sm font-medium transition-all",asChild:!0,children:(0,s.jsx)(d.default,{href:"#exemplos",children:"Ver exemplos"})})]}),(0,s.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(i.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),(0,s.jsx)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Comandos em linguagem natural para criar planilhas"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(i.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),(0,s.jsx)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"F\xf3rmulas complexas geradas automaticamente"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(i.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),(0,s.jsx)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Visualiza\xe7\xf5es e gr\xe1ficos em segundos"})]})]})]}),(0,s.jsx)("div",{className:"md:w-1/2 w-full mt-8 md:mt-0",children:(0,s.jsxs)("div",{className:"rounded-xl border shadow-md bg-white dark:bg-gray-900/60 overflow-hidden backdrop-blur-sm",children:[(0,s.jsxs)("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 border-b flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex space-x-1.5",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"}),(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"}),(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"})]}),(0,s.jsx)(R.C,{variant:"outline",className:"text-xs font-normal hidden xs:inline",children:"Demo Interativa"}),(0,s.jsx)(T.Button,{id:"autoplay-button",variant:"ghost",size:"sm",onClick:()=>M(!L),className:"text-xs","aria-pressed":L,"aria-label":L?"Desativar execu\xe7\xe3o autom\xe1tica":"Ativar execu\xe7\xe3o autom\xe1tica",children:L?"Pausar":"Auto-play"})]}),(0,s.jsxs)("div",{className:"p-2 sm:p-4 min-h-[250px] sm:min-h-[300px] max-h-[350px] sm:max-h-[400px] overflow-y-auto",children:[D&&z.map((e,a)=>{var t;return(0,s.jsx)(o.E.div,{initial:G.initial,animate:G.animate,transition:G.transition,className:"mb-4",children:"message"===e.type?(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 italic",children:e.content}):e.type===w.ox.TABLE?(0,s.jsx)("div",{className:"overflow-auto max-h-[200px] sm:max-h-[250px]",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-700 text-xs sm:text-sm",role:"table","aria-label":"Dados tabulares de exemplo",children:[(0,s.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,s.jsx)("tr",{children:Array.isArray(e.content)&&e.content.length>0?null===(t=e.content[0])||void 0===t?void 0:t.map((e,a)=>(0,s.jsx)("th",{scope:"col",className:"px-2 py-2 sm:px-3 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e},"header-".concat(a))):null})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800",children:Array.isArray(e.content)&&e.content.length>0?e.content.slice(1).map((a,t)=>(0,s.jsx)("tr",{className:t%2==0?"bg-white dark:bg-gray-900/80":"bg-gray-50 dark:bg-gray-900/50",children:a.map((a,r)=>{var o;return(0,s.jsx)("td",{className:(0,O.cn)("px-2 py-2 sm:px-3 sm:py-3 whitespace-nowrap",(null===(o=e.highlightCells)||void 0===o?void 0:o.some(e=>e.row===t+1&&e.col===r))?"bg-blue-100 dark:bg-blue-900/30":""),children:a},r)})},t)):null})]})}):e.type===w.ox.CHART?(0,s.jsx)("div",{className:"w-full h-[150px] sm:h-[200px] md:h-[250px] bg-white dark:bg-gray-800 p-2 rounded-md",children:V||B?(0,s.jsx)(k,{data:e.data,chartType:e.chartType}):(0,s.jsx)(u.h,{width:"100%",height:"100%",children:"bar"===e.chartType?(0,s.jsxs)(x.v,{data:e.data.datasets[0].data.map((a,t)=>{var s,r;return{name:e.data.labels[t],value:a,meta:e.data.datasets[0].label,value2:null===(s=e.data.datasets[1])||void 0===s?void 0:s.data[t],meta2:null===(r=e.data.datasets[1])||void 0===r?void 0:r.label}}),children:[(0,s.jsx)(p.q,{strokeDasharray:"3 3",opacity:.2}),(0,s.jsx)(h.K,{dataKey:"name",fontSize:10,tick:{fill:"currentColor"},tickFormatter:e=>e.length>5?"".concat(e.substring(0,5),"..."):e}),(0,s.jsx)(f.B,{fontSize:10,tick:{fill:"currentColor"}}),(0,s.jsx)(g.u,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),(0,s.jsx)(v.D,{wrapperStyle:{fontSize:"10px"}}),(0,s.jsx)(N.$,{dataKey:"value",name:e.data.datasets[0].label,fill:e.data.datasets[0].backgroundColor,radius:[4,4,0,0]}),e.data.datasets[1]&&(0,s.jsx)(N.$,{dataKey:"value2",name:e.data.datasets[1].label,fill:e.data.datasets[1].backgroundColor,radius:[4,4,0,0]})]}):"line"===e.chartType?(0,s.jsxs)(b.w,{data:e.data.datasets[0].data.map((a,t)=>{var s,r;return{name:e.data.labels[t],value:a,meta:e.data.datasets[0].label,value2:null===(s=e.data.datasets[1])||void 0===s?void 0:s.data[t],meta2:null===(r=e.data.datasets[1])||void 0===r?void 0:r.label}}),children:[(0,s.jsx)(p.q,{strokeDasharray:"3 3",opacity:.2}),(0,s.jsx)(h.K,{dataKey:"name",fontSize:10,tick:{fill:"currentColor"},tickFormatter:e=>e.length>5?"".concat(e.substring(0,5),"..."):e}),(0,s.jsx)(f.B,{fontSize:10,tick:{fill:"currentColor"}}),(0,s.jsx)(g.u,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),(0,s.jsx)(v.D,{wrapperStyle:{fontSize:"10px"}}),(0,s.jsx)(E.x,{type:"monotone",dataKey:"value",name:e.data.datasets[0].label,stroke:e.data.datasets[0].backgroundColor,activeDot:{r:8},strokeWidth:2}),e.data.datasets[1]&&(0,s.jsx)(E.x,{type:"monotone",dataKey:"value2",name:e.data.datasets[1].label,stroke:e.data.datasets[1].backgroundColor,activeDot:{r:8},strokeWidth:2})]}):(0,s.jsxs)(A.u,{children:[(0,s.jsx)(y.b,{data:e.data.datasets[0].data.map((a,t)=>({name:e.data.labels[t],value:a})),cx:"50%",cy:"50%",labelLine:!1,outerRadius:60,fill:"#8884d8",dataKey:"value",nameKey:"name",label:e=>{let{name:a,percent:t}=e;return"".concat(a,": ").concat((100*t).toFixed(0),"%")},children:e.data.datasets[0].data.map((e,a)=>(0,s.jsx)(j.b,{fill:a<C.length?C[a]:C[a%C.length]},"cell-".concat(a)))}),(0,s.jsx)(g.u,{formatter:e=>["".concat(e),""],contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),(0,s.jsx)(v.D,{wrapperStyle:{fontSize:"10px"}})]})})}):null},a)}),!F&&(null===(e=S[t])||void 0===e?void 0:e.command)&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-xs text-white font-medium",children:t+1})}),(0,s.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Digite um comando:"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-1 p-3 bg-gray-100 dark:bg-gray-800 rounded-md text-sm font-mono",children:(0,s.jsx)(m.e,{cursor:!0,sequence:[(null===(a=S[t])||void 0===a?void 0:a.command)||""],wrapper:"span",speed:50,style:{display:"inline-block"},repeat:0,className:"text-gray-800 dark:text-gray-200"})}),(0,s.jsx)("button",{className:"ml-2 p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:()=>q(),"aria-label":"Pr\xf3ximo comando",children:(0,s.jsx)(l.Z,{className:"h-4 w-4","aria-hidden":"true"})})]})]}),(0,s.jsx)("div",{"aria-live":"polite",className:"sr-only",children:F?"Comando digitado. Processando resultado.":""})]})]})})]})}function k(e){let{data:a,chartType:t}=e;if(!a||!a.datasets||!a.datasets[0])return(0,s.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Dados do gr\xe1fico indispon\xedveis"})});let{labels:r,datasets:o}=a,n=o[0],l=o[1];return(0,s.jsxs)("div",{className:"h-full w-full flex flex-col","aria-label":"Gr\xe1fico de ".concat(t),role:"img",children:[(0,s.jsx)("div",{className:"text-xs font-medium mb-2 text-center",children:"bar"===t?"Gr\xe1fico de Barras":"line"===t?"Gr\xe1fico de Linha":"Gr\xe1fico de Pizza"}),(0,s.jsx)("div",{className:"flex-1 flex items-end space-x-1 px-2 overflow-hidden",children:r.map((e,a)=>{let t=Math.max(...n.data),r=Math.max(10,n.data[a]/t*100);return(0,s.jsxs)("div",{className:"flex flex-col items-center flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"w-full relative rounded-t-sm focus:outline-none focus:ring-2 focus:ring-primary",style:{height:"".concat(r,"%"),backgroundColor:n.backgroundColor},"aria-label":"".concat(e,": ").concat(n.data[a]),tabIndex:0,role:"button",onKeyDown:t=>{("Enter"===t.key||" "===t.key)&&(t.preventDefault(),alert("".concat(e,": ").concat(n.data[a])))}}),(0,s.jsx)("div",{className:"text-[8px] mt-1 truncate w-full text-center",title:e,children:e})]},a)})}),(0,s.jsxs)("div",{className:"flex justify-center mt-2 gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2",style:{backgroundColor:n.backgroundColor},"aria-hidden":"true"}),(0,s.jsx)("span",{className:"text-[9px]",children:n.label})]}),l&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2",style:{backgroundColor:l.backgroundColor},"aria-hidden":"true"}),(0,s.jsx)("span",{className:"text-[9px]",children:l.label})]})]})]})}},40584:function(e,a,t){"use strict";t.d(a,{Bc:function(){return c}});var s=t(57437),r=t(59738),o=t(26032),n=t(30690),l=t(2265),i=t(49354);let d={error:{className:"bg-destructive/15 text-destructive",iconClassName:"text-destructive",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"})},warning:{className:"bg-warning/15 text-warning",iconClassName:"text-warning",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5"})},info:{className:"bg-info/15 text-info",iconClassName:"text-info",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"})},success:{className:"bg-success/15 text-success",iconClassName:"text-success",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"})}},c=l.forwardRef((e,a)=>{let{type:t="error",message:r,description:o,className:n,iconClassName:l,_iconClassName:c,_icon:m}=e,u=d[t],x=m||u.icon;return(0,s.jsxs)("div",{ref:a,className:(0,i.cn)("flex items-start gap-3 rounded-md border p-3",u.className,n),role:"alert",children:[(0,s.jsx)("div",{className:(0,i.cn)("mt-0.5 shrink-0",u.iconClassName,l||c),children:x}),(0,s.jsxs)("div",{className:"grid gap-1",children:[(0,s.jsx)("div",{className:"font-medium leading-none tracking-tight",children:r}),o&&(0,s.jsx)("div",{className:"text-sm opacity-80",children:o})]})]})});c.displayName="ErrorMessage",a.ZP=c},80023:function(e,a,t){"use strict";t.d(a,{x:function(){return l}});var s=t(57437),r=t(26770),o=t(2265),n=t(49354);let l=o.forwardRef((e,a)=>{let{className:t,children:o,...l}=e;return(0,s.jsxs)(r.fC,{ref:a,className:(0,n.cn)("relative overflow-hidden",t),...l,children:[(0,s.jsx)(r.l_,{className:"h-full w-full rounded-[inherit]",children:o}),(0,s.jsx)(i,{}),(0,s.jsx)(r.Ns,{})]})});l.displayName=r.fC.displayName;let i=o.forwardRef((e,a)=>{let{className:t,orientation:o="vertical",...l}=e;return(0,s.jsx)(r.gb,{ref:a,orientation:o,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===o&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===o&&"h-2.5 border-t border-t-transparent p-[1px]",t),...l,children:(0,s.jsx)(r.q4,{className:"relative flex-1 rounded-full bg-border"})})});i.displayName=r.gb.displayName},4919:function(e,a,t){"use strict";var s=t(57437),r=t(2265),o=t(6432);let n=r.forwardRef((e,a)=>{let{className:t,wrapperClassName:r,variant:n="default",fieldSize:l="md",textareaSize:i,...d}=e,c=(0,s.jsx)("textarea",{className:(0,o.RM)(n,i||l,!0,t),ref:a,...d});return(0,o.aF)(c,r)});n.displayName="Textarea",a.Z=n},89736:function(e,a,t){"use strict";t.d(a,{_v:function(){return c},aJ:function(){return d},pn:function(){return l},u:function(){return i}});var s=t(57437),r=t(27071),o=t(2265),n=t(49354);let l=r.zt,i=r.fC,d=r.xz,c=o.forwardRef((e,a)=>{let{className:t,sideOffset:o=4,...l}=e;return(0,s.jsx)(r.VY,{ref:a,sideOffset:o,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})});c.displayName=r.VY.displayName},78068:function(e,a,t){"use strict";t.d(a,{V6:function(){return x},pm:function(){return p},s6:function(){return u}});var s=t(2265);let r={ADD_TOAST:"ADD_TOAST",UPDATE_TOAST:"UPDATE_TOAST",DISMISS_TOAST:"DISMISS_TOAST",REMOVE_TOAST:"REMOVE_TOAST"},o=0,n=new Map,l=e=>{if(n.has(e))return;let a=setTimeout(()=>{n.delete(e),m({type:r.REMOVE_TOAST,toastId:e})},1e6);n.set(e,a)},i=(e,a)=>{switch(a.type){case r.ADD_TOAST:return{...e,toasts:[a.toast,...e.toasts].slice(0,5)};case r.UPDATE_TOAST:return{...e,toasts:e.toasts.map(e=>{var t;return e.id===(null===(t=a.toast)||void 0===t?void 0:t.id)?{...e,...a.toast}:e})};case r.DISMISS_TOAST:{let{toastId:t}=a;return t?l(t):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case r.REMOVE_TOAST:if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)};default:return e}},d=[],c={toasts:[]};function m(e){c=i(c,e),d.forEach(e=>{e(c)})}function u(e){let{...a}=e,t=(o=(o+1)%Number.MAX_VALUE).toString(),s=()=>m({type:r.DISMISS_TOAST,toastId:t});return m({type:r.ADD_TOAST,toast:{...a,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>m({type:r.UPDATE_TOAST,toast:{...e,id:t}})}}function x(){let[e,a]=s.useState(c);return s.useEffect(()=>(d.push(a),()=>{let e=d.indexOf(a);e>-1&&d.splice(e,1)}),[e]),{toast:u,dismiss:e=>m({type:r.DISMISS_TOAST,toastId:void 0===e?"":e}),toasts:e.toasts}}let p=x},64451:function(e,a,t){"use strict";var s,r,o,n,l,i,d,c,m,u;t.d(a,{ox:function(){return s}}),(i=s||(s={})).FORMULA="FORMULA",i.FILTER="FILTER",i.SORT="SORT",i.FORMAT="FORMAT",i.CHART="CHART",i.CELL_UPDATE="CELL_UPDATE",i.COLUMN_OPERATION="COLUMN_OPERATION",i.ROW_OPERATION="ROW_OPERATION",i.TABLE="TABLE",i.DATA_TRANSFORMATION="DATA_TRANSFORMATION",i.PIVOT_TABLE="PIVOT_TABLE",i.CONDITIONAL_FORMAT="CONDITIONAL_FORMAT",i.ADVANCED_CHART="ADVANCED_CHART",i.ADVANCED_VISUALIZATION="ADVANCED_VISUALIZATION",i.RANGE_UPDATE="RANGE_UPDATE",i.CELL_MERGE="CELL_MERGE",i.CELL_SPLIT="CELL_SPLIT",i.NAMED_RANGE="NAMED_RANGE",i.VALIDATION="VALIDATION",i.FREEZE_PANES="FREEZE_PANES",i.SHEET_OPERATION="SHEET_OPERATION",i.ANALYSIS="ANALYSIS",i.GENERIC="GENERIC",(d=r||(r={})).LINE="LINE",d.BAR="BAR",d.COLUMN="COLUMN",d.AREA="AREA",d.SCATTER="SCATTER",d.PIE="PIE",(c=o||(o={})).EQUALS="equals",c.NOT_EQUALS="notEquals",c.GREATER_THAN="greaterThan",c.LESS_THAN="lessThan",c.GREATER_THAN_OR_EQUAL="greaterThanOrEqual",c.LESS_THAN_OR_EQUAL="lessThanOrEqual",c.CONTAINS="contains",c.NOT_CONTAINS="notContains",c.BEGINS_WITH="beginsWith",c.ENDS_WITH="endsWith",c.BETWEEN="between",(m=n||(n={})).DISCONNECTED="disconnected",m.CONNECTING="connecting",m.CONNECTED="connected",m.ERROR="error",(u=l||(l={})).FORMULA_ERROR="FORMULA_ERROR",u.REFERENCE_ERROR="REFERENCE_ERROR",u.VALUE_ERROR="VALUE_ERROR",u.NAME_ERROR="NAME_ERROR",u.RANGE_ERROR="RANGE_ERROR",u.SYNTAX_ERROR="SYNTAX_ERROR",u.DATA_VALIDATION_ERROR="DATA_VALIDATION_ERROR",u.FORMAT_ERROR="FORMAT_ERROR",u.OPERATION_NOT_SUPPORTED="OPERATION_NOT_SUPPORTED",u.UNKNOWN_ERROR="UNKNOWN_ERROR"}},function(e){e.O(0,[7142,8638,4462,231,8195,7391,5043,8194,4800,2971,7023,1744],function(){return e(e.s=63029)}),_N_E=e.O()}]);