(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[813],{24654:function(){},33995:function(e,r,t){Promise.resolve().then(t.bind(t,47508))},77424:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(81066).Z)("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},56935:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(81066).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},23787:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(81066).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},74109:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(81066).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},20500:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(81066).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},55430:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(81066).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},47508:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return b}});var n=t(57437),a=t(74109),s=t(30998),o=t(2265),i=t(27776),l=t(20500),c=t(77424),d=t(56935),u=t(23787),m=t(55430),f=t(89733),p=t(48185),x=t(61617),h=t(58743);function v(e){let{subscription:r,onCreatePortalSession:t}=e,[a,s]=(0,o.useState)(!1),v=Math.min(Math.round(r.apiCallsUsed/r.apiCallsLimit*100),100),b=e=>e?new Intl.DateTimeFormat("pt-BR",{day:"numeric",month:"long",year:"numeric"}).format(new Date(e)):"N/A",g=async()=>{try{s(!0);let e=await t();if(e)window.location.href=e;else throw Error("N\xe3o foi poss\xedvel obter o link do portal")}catch(e){console.error("Erro ao abrir portal de assinatura:",e),i.toast.error("N\xe3o foi poss\xedvel acessar o portal de faturamento. Tente novamente mais tarde.")}finally{s(!1)}};return(0,n.jsxs)(p.Zb,{className:"border-primary/30",children:[(0,n.jsxs)(p.Ol,{className:"pb-2",children:[(0,n.jsxs)(p.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(l.Z,{className:"free"!==r.plan?"text-primary":"text-muted-foreground",size:18}),(0,h.Nt)(r.plan),"past_due"===r.status&&(0,n.jsx)("span",{className:"text-xs font-normal ml-2 py-0.5 px-2 rounded bg-amber-100 text-amber-800 dark:bg-amber-900/60 dark:text-amber-200",children:"Pagamento pendente"}),r.cancelAtPeriodEnd&&(0,n.jsx)("span",{className:"text-xs font-normal ml-2 py-0.5 px-2 rounded bg-rose-100 text-rose-800 dark:bg-rose-900/60 dark:text-rose-200",children:"Cancelamento agendado"})]}),(0,n.jsx)(p.SZ,{children:"free"===r.id?"Plano gratuito com recursos b\xe1sicos":"Acesso a todos os recursos pro"})]}),(0,n.jsxs)(p.aY,{className:"pb-2 space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1.5 font-medium",children:[(0,n.jsx)(c.Z,{size:16,className:"text-muted-foreground"}),(0,n.jsx)("span",{children:"Uso da API"})]}),(0,n.jsxs)("span",{children:[r.apiCallsUsed," / ",r.apiCallsLimit," chamadas"]})]}),(0,n.jsx)(x.E,{value:v,className:"h-2"}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:v>=80?"Voc\xea est\xe1 pr\xf3ximo do seu limite mensal.":"Seu uso est\xe1 dentro do esperado."})]}),"free"!==r.id&&(0,n.jsxs)("div",{className:"space-y-4 pt-2",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-muted-foreground mb-1",children:"Status"}),(0,n.jsxs)("p",{className:"font-medium",children:["active"===r.status&&"Ativo","trialing"===r.status&&"Em trial","past_due"===r.status&&"Pagamento pendente","canceled"===r.status&&"Cancelado","incomplete"===r.status&&"Incompleto"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-muted-foreground mb-1",children:"Pr\xf3xima cobran\xe7a"}),(0,n.jsx)("p",{className:"font-medium",children:b(r.currentPeriodEnd)})]})]}),r.cancelAtPeriodEnd&&(0,n.jsx)("div",{className:"rounded-md bg-amber-50 dark:bg-amber-950/30 p-3 text-sm text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-800/40",children:(0,n.jsxs)("p",{children:["Sua assinatura ser\xe1 encerrada em ",b(r.currentPeriodEnd),"."]})})]})]}),(0,n.jsx)(p.eW,{className:"pt-2",children:"free"!==r.id?(0,n.jsx)(f.Button,{onClick:()=>g(),className:"w-full",disabled:a,children:a?"Carregando...":(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.Z,{className:"mr-2 h-4 w-4"}),"Gerenciar Assinatura",(0,n.jsx)(u.Z,{className:"ml-2 h-3.5 w-3.5"})]})}):(0,n.jsxs)(f.Button,{variant:"outline",className:"w-full",onClick:()=>window.location.href="/pricing",children:[(0,n.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Fazer Upgrade"]})})]})}function b(){var e,r;let{data:t}=(0,s.useSession)(),[l,c]=(0,o.useState)(null),[d,u]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/user/subscription"),r=await e.json();e.ok?c(r.subscription):c({id:"free",plan:"free",status:"active",currentPeriodEnd:null,apiCallsUsed:0,apiCallsLimit:50,cancelAtPeriodEnd:!1})}catch(e){i.toast.error("N\xe3o foi poss\xedvel carregar os dados da assinatura")}finally{u(!1)}};t&&e()},[t]);let m=async()=>{try{let e=await fetch("/api/billing/customer-portal",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({returnUrl:"".concat(window.location.origin,"/dashboard/account")})}),r=await e.json();if(!e.ok)throw Error(r.error||"Erro ao acessar o portal");return r.url}catch(e){return i.toast.error("N\xe3o foi poss\xedvel acessar o portal de gerenciamento"),null}};return d?(0,n.jsx)("div",{className:"flex items-center justify-center h-[calc(100vh-200px)]",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)(a.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Carregando informa\xe7\xf5es da conta..."})]})}):(0,n.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Sua Conta"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsxs)(p.Zb,{children:[(0,n.jsxs)(p.Ol,{children:[(0,n.jsx)(p.ll,{children:"Perfil"}),(0,n.jsx)(p.SZ,{children:"Suas informa\xe7\xf5es pessoais"})]}),(0,n.jsx)(p.aY,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-muted-foreground",children:"Nome"}),(0,n.jsx)("p",{children:(null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.name)||"N\xe3o informado"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-muted-foreground",children:"Email"}),(0,n.jsx)("p",{children:(null==t?void 0:null===(r=t.user)||void 0===r?void 0:r.email)||"N\xe3o informado"})]})]})})]})}),(0,n.jsxs)("div",{className:"md:col-span-2",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Sua Assinatura"}),l&&(0,n.jsx)(v,{subscription:l,onCreatePortalSession:m})]})]})]})}},89733:function(e,r,t){"use strict";t.d(r,{Button:function(){return u},d:function(){return d}});var n=t(57437),a=t(71538),s=t(13027),o=t(847),i=t(2265),l=t(18043),c=t(49354);let d=(0,s.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),u=i.forwardRef((e,r)=>{let{className:t,variant:s,size:i,rounded:u,cssFeedback:m,asChild:f=!1,animated:p=!1,icon:x,iconPosition:h="left",children:v,...b}=e,g=f?a.g7:"button",y=(0,n.jsxs)("span",{className:"inline-flex items-center justify-center",children:[x&&"left"===h&&(0,n.jsx)("span",{className:"mr-2",children:x}),v,x&&"right"===h&&(0,n.jsx)("span",{className:"ml-2",children:x})]});if(p){let e={whileTap:{scale:.97},whileHover:["link","ghost"].includes(s)?void 0:{y:-2},transition:{duration:.67*l.zn,ease:l.d}},a=(0,c.cn)(d({variant:s,size:i,rounded:u,cssFeedback:"none",className:t})),m={...b,className:a,...e};return(0,n.jsx)(o.E.button,{ref:r,...m,children:y})}return(0,n.jsx)(g,{className:(0,c.cn)(d({variant:s,size:i,rounded:u,cssFeedback:m,className:t})),ref:r,...b,children:y})});u.displayName="Button"},48185:function(e,r,t){"use strict";t.d(r,{Ol:function(){return c},SZ:function(){return u},Zb:function(){return l},aY:function(){return m},eW:function(){return f},ll:function(){return d}});var n=t(57437),a=t(847),s=t(2265),o=t(18043),i=t(49354);let l=(0,s.forwardRef)((e,r)=>{let{className:t,children:s,hoverable:l=!1,variant:c="default",noPadding:d=!1,animated:u=!1,...m}=e,f=(0,i.cn)("rounded-xl border shadow-sm",{"p-6":!d,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":l&&!u,"border-border bg-card":"default"===c,"border-border/50 bg-transparent":"outline"===c,"bg-card/90 backdrop-blur-md border-border/50":"glass"===c,"bg-gradient-primary text-primary-foreground border-none":"gradient"===c},t);return u?(0,n.jsx)(a.E.div,{ref:r,className:f,...(0,o.Ph)("card"),whileHover:l?o.q.hover:void 0,whileTap:l?o.q.tap:void 0,...m,children:s}):(0,n.jsx)("div",{ref:r,className:f,...m,children:s})});l.displayName="Card";let c=(0,s.forwardRef)((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,i.cn)("mb-4 flex flex-col space-y-1.5",t),...a})});c.displayName="CardHeader";let d=(0,s.forwardRef)((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:r,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle";let u=(0,s.forwardRef)((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",t),...a})});u.displayName="CardDescription";let m=(0,s.forwardRef)((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,i.cn)("card-content",t),...a})});m.displayName="CardContent";let f=(0,s.forwardRef)((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center pt-4 mt-auto",t),...a})});f.displayName="CardFooter"},61617:function(e,r,t){"use strict";t.d(r,{E:function(){return i}});var n=t(57437),a=t(52431),s=t(2265),o=t(49354);let i=s.forwardRef((e,r)=>{let{className:t,value:s,...i}=e;return(0,n.jsx)(a.fC,{ref:r,className:(0,o.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...i,children:(0,n.jsx)(a.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});i.displayName=a.fC.displayName},58743:function(e,r,t){"use strict";t.d(r,{Nt:function(){return i},Xf:function(){return s},tt:function(){return l}}),t(13537);var n=t(62848),a=t(25566);let s={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"};s.FREE,s.PRO_MONTHLY,s.PRO_ANNUAL;let o=a.env.STRIPE_SECRET_KEY||"";function i(e){switch(e){case s.FREE:return"Gr\xe1tis";case s.PRO_MONTHLY:return"Pro Mensal";case s.PRO_ANNUAL:return"Pro Anual";default:return"Desconhecido"}}o&&new n.Z(o,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}});let l=()=>"pk_live_51RGJ6nRrKLXtzZkMtpujgPAZR4MmRmQQrImSNrq6vdCLe6gfWulXfJDaDl1K2u3DKeKUegsXvzceFVi8xwnwroic00ER63lsVr"},98324:function(e,r,t){"use strict";t.d(r,{b:function(){return o},k:function(){return s}});var n=t(2265),a=t(57437);function s(e,r){let t=n.createContext(r),s=e=>{let{children:r,...s}=e,o=n.useMemo(()=>s,Object.values(s));return(0,a.jsx)(t.Provider,{value:o,children:r})};return s.displayName=e+"Provider",[s,function(a){let s=n.useContext(t);if(s)return s;if(void 0!==r)return r;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function o(e,r=[]){let t=[],s=()=>{let r=t.map(e=>n.createContext(e));return function(t){let a=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return s.scopeName=e,[function(r,s){let o=n.createContext(s),i=t.length;t=[...t,s];let l=r=>{let{scope:t,children:s,...l}=r,c=t?.[e]?.[i]||o,d=n.useMemo(()=>l,Object.values(l));return(0,a.jsx)(c.Provider,{value:d,children:s})};return l.displayName=r+"Provider",[l,function(t,a){let l=a?.[e]?.[i]||o,c=n.useContext(l);if(c)return c;if(void 0!==s)return s;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((r,{useScope:t,scopeName:n})=>{let a=t(e)[`__scope${n}`];return{...r,...a}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:a}),[a])}};return t.scopeName=r.scopeName,t}(s,...r)]}},25171:function(e,r,t){"use strict";t.d(r,{WV:function(){return i},jH:function(){return l}});var n=t(2265),a=t(54887),s=t(71538),o=t(57437),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,s.Z8)(`Primitive.${r}`),a=n.forwardRef((e,n)=>{let{asChild:a,...s}=e,i=a?t:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i,{...s,ref:n})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function l(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},52431:function(e,r,t){"use strict";t.d(r,{fC:function(){return y},z$:function(){return j}});var n=t(2265),a=t(98324),s=t(25171),o=t(57437),i="Progress",[l,c]=(0,a.b)(i),[d,u]=l(i),m=n.forwardRef((e,r)=>{var t,n,a,i;let{__scopeProgress:l,value:c=null,max:u,getValueLabel:m=x,...f}=e;(u||0===u)&&!b(u)&&console.error((t="".concat(u),n="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=b(u)?u:100;null===c||g(c,p)||console.error((a="".concat(c),i="Progress","Invalid prop `value` of value `".concat(a,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let y=g(c,p)?c:null,j=v(y)?m(y,p):void 0;return(0,o.jsx)(d,{scope:l,value:y,max:p,children:(0,o.jsx)(s.WV.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":v(y)?y:void 0,"aria-valuetext":j,role:"progressbar","data-state":h(y,p),"data-value":null!=y?y:void 0,"data-max":p,...f,ref:r})})});m.displayName=i;var f="ProgressIndicator",p=n.forwardRef((e,r)=>{var t;let{__scopeProgress:n,...a}=e,i=u(f,n);return(0,o.jsx)(s.WV.div,{"data-state":h(i.value,i.max),"data-value":null!==(t=i.value)&&void 0!==t?t:void 0,"data-max":i.max,...a,ref:r})});function x(e,r){return"".concat(Math.round(e/r*100),"%")}function h(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function v(e){return"number"==typeof e}function b(e){return v(e)&&!isNaN(e)&&e>0}function g(e,r){return v(e)&&!isNaN(e)&&e<=r&&e>=0}p.displayName=f;var y=m,j=p},13537:function(e,r,t){"use strict";t.d(r,{J:function(){return b}});var n,a="basil",s="https://js.stripe.com",o="".concat(s,"/").concat(a,"/stripe.js"),i=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,l=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,c=function(){for(var e=document.querySelectorAll('script[src^="'.concat(s,'"]')),r=0;r<e.length;r++){var t,n=e[r];if(t=n.src,i.test(t)||l.test(t))return n}return null},d=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",t=document.createElement("script");t.src="".concat(o).concat(r);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(t),t},u=function(e,r){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.2.0",startTime:r})},m=null,f=null,p=null,x=function(e,r,t){if(null===e)return null;var n,s=r[0].match(/^pk_test/),o=3===(n=e.version)?"v3":n;s&&o!==a&&console.warn("Stripe.js@".concat(o," was loaded on the page, but @stripe/stripe-js@").concat("7.2.0"," expected Stripe.js@").concat(a,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var i=e.apply(void 0,r);return u(i,t),i},h=!1,v=function(){return n||(n=(null!==m?m:(m=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,n=c();n?n&&null!==p&&null!==f&&(n.removeEventListener("load",p),n.removeEventListener("error",f),null===(t=n.parentNode)||void 0===t||t.removeChild(n),n=d(null)):n=d(null),p=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},f=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",p),n.addEventListener("error",f)}catch(e){r(e);return}})).catch(function(e){return m=null,Promise.reject(e)})).catch(function(e){return n=null,Promise.reject(e)}))};Promise.resolve().then(function(){return v()}).catch(function(e){h||console.warn(e)});var b=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];h=!0;var n=Date.now();return v().then(function(e){return x(e,r,n)})}}},function(e){e.O(0,[7142,8638,7776,5660,2848,8194,2971,7023,1744],function(){return e(e.s=33995)}),_N_E=e.O()}]);