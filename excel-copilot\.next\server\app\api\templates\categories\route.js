"use strict";(()=>{var e={};e.id=6363,e.ids=[6363],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},89284:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>b,patchFetch:()=>v,requestAsyncStorage:()=>x,routeModule:()=>g,serverHooks:()=>z,staticGenerationAsyncStorage:()=>f});var o={};t.r(o),t.d(o,{GET:()=>d,POST:()=>p});var a=t(49303),s=t(88716),n=t(60670),i=t(75571),c=t(81628),l=t(63841),u=t(24522),m=t(540);async function d(e){try{let{searchParams:r}=new URL(e.url),t="true"===r.get("includeCount"),o=(await l.prisma.templateCategory.findMany({where:{isActive:!0},orderBy:[{sortOrder:"asc"},{name:"asc"}],...t&&{include:{_count:{select:{templates:{where:{isActive:!0,isPublic:!0}}}}}}})).map(e=>({id:e.id,name:e.name,slug:e.slug,description:e.description,icon:e.icon,color:e.color,sortOrder:e.sortOrder,...t&&{templateCount:e._count?.templates||0}}));return m.R.success({categories:o})}catch(e){return console.error("Erro ao buscar categorias de templates:",e),m.R.error(e instanceof Error?e.message:"Erro interno do servidor",500)}}async function p(e){try{let r=await (0,i.getServerSession)(c.L);if(!r?.user)return m.R.error("N\xe3o autorizado",401);let t=await e.json(),o=u.J5.parse(t);if(await l.prisma.templateCategory.findFirst({where:{OR:[{name:o.name},{slug:o.slug}]}}))return m.R.error("J\xe1 existe uma categoria com este nome ou slug",400);let a=await l.prisma.templateCategory.create({data:{name:o.name,slug:o.slug,description:o.description||null,icon:o.icon||null,color:o.color||null,sortOrder:o.sortOrder}});return m.R.success(a,201)}catch(e){return console.error("Erro ao criar categoria de template:",e),m.R.error(e instanceof Error?e.message:"Erro interno do servidor",500)}}let g=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/templates/categories/route",pathname:"/api/templates/categories",filename:"route",bundlePath:"app/api/templates/categories/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\templates\\categories\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:x,staticGenerationAsyncStorage:f,serverHooks:z}=g,b="/api/templates/categories/route";function v(){return(0,n.patchFetch)({serverHooks:z,staticGenerationAsyncStorage:f})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var i=a?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(o,s,i):o[s]=e[s]}return o.default=e,t&&t.set(e,o),o}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})},540:(e,r,t)=>{t.d(r,{R:()=>a});var o=t(87070);class a{static success(e,r=200){return o.NextResponse.json({success:!0,data:e},{status:r})}static error(e,r=400,t){return o.NextResponse.json({success:!1,error:{message:e,...t&&{details:t}}},{status:r})}static validation(e){return o.NextResponse.json({success:!1,error:{message:"Dados inv\xe1lidos",validation:e}},{status:422})}static unauthorized(e="N\xe3o autorizado"){return o.NextResponse.json({success:!1,error:{message:e}},{status:401})}static notFound(e="Recurso n\xe3o encontrado"){return o.NextResponse.json({success:!1,error:{message:e}},{status:404})}static conflict(e="Conflito de dados"){return o.NextResponse.json({success:!1,error:{message:e}},{status:409})}static internal(e="Erro interno do servidor"){return o.NextResponse.json({success:!1,error:{message:e}},{status:500})}}},24522:(e,r,t)=>{t.d(r,{J5:()=>n,L8:()=>s,XZ:()=>a,re:()=>i});var o=t(7410);o.z.object({id:o.z.string().cuid({message:"ID de template inv\xe1lido"})});let a=o.z.object({name:o.z.string().min(1,{message:"Nome do template \xe9 obrigat\xf3rio"}).max(100,{message:"Nome do template deve ter no m\xe1ximo 100 caracteres"}),title:o.z.string().min(1,{message:"T\xedtulo do template \xe9 obrigat\xf3rio"}).max(150,{message:"T\xedtulo do template deve ter no m\xe1ximo 150 caracteres"}),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),icon:o.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),isPublic:o.z.boolean().default(!0),isFeatured:o.z.boolean().default(!1),isNew:o.z.boolean().default(!1),data:o.z.any(),categoryIds:o.z.array(o.z.string().cuid()).optional()});o.z.object({name:o.z.string().min(1,{message:"Nome do template \xe9 obrigat\xf3rio"}).max(100,{message:"Nome do template deve ter no m\xe1ximo 100 caracteres"}).optional(),title:o.z.string().min(1,{message:"T\xedtulo do template \xe9 obrigat\xf3rio"}).max(150,{message:"T\xedtulo do template deve ter no m\xe1ximo 150 caracteres"}).optional(),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),icon:o.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),isPublic:o.z.boolean().optional(),isFeatured:o.z.boolean().optional(),isNew:o.z.boolean().optional(),data:o.z.any().optional(),categoryIds:o.z.array(o.z.string().cuid()).optional()});let s=o.z.object({categoryId:o.z.string().cuid().optional(),isPublic:o.z.boolean().optional(),isFeatured:o.z.boolean().optional(),isNew:o.z.boolean().optional(),search:o.z.string().max(100).optional(),sortBy:o.z.enum(["popularity","recent","name","usage"]).default("popularity"),sortOrder:o.z.enum(["asc","desc"]).default("desc"),limit:o.z.number().int().min(1).max(100).default(20),offset:o.z.number().int().min(0).default(0)}),n=o.z.object({name:o.z.string().min(1,{message:"Nome da categoria \xe9 obrigat\xf3rio"}).max(50,{message:"Nome da categoria deve ter no m\xe1ximo 50 caracteres"}),slug:o.z.string().min(1,{message:"Slug da categoria \xe9 obrigat\xf3rio"}).max(50,{message:"Slug da categoria deve ter no m\xe1ximo 50 caracteres"}).regex(/^[a-z0-9-]+$/,{message:"Slug deve conter apenas letras min\xfasculas, n\xfameros e h\xedfens"}),description:o.z.string().max(200,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 200 caracteres"}).optional(),icon:o.z.string().max(50,{message:"\xcdcone deve ter no m\xe1ximo 50 caracteres"}).optional(),color:o.z.string().regex(/^#[0-9A-Fa-f]{6}$/,{message:"Cor deve estar no formato hexadecimal (#RRGGBB)"}).optional(),sortOrder:o.z.number().int().min(0).default(0)});o.z.object({templateId:o.z.string().cuid({message:"ID de template inv\xe1lido"}),rating:o.z.number().int().min(1,{message:"Avalia\xe7\xe3o deve ser entre 1 e 5"}).max(5,{message:"Avalia\xe7\xe3o deve ser entre 1 e 5"}),comment:o.z.string().max(1e3,{message:"Coment\xe1rio deve ter no m\xe1ximo 1000 caracteres"}).optional()});let i=o.z.object({templateId:o.z.string().cuid({message:"ID de template inv\xe1lido"}),workbookName:o.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}).optional(),workbookDescription:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional()});o.z.object({sheets:o.z.array(o.z.object({name:o.z.string().min(1,{message:"Nome da sheet \xe9 obrigat\xf3rio"}),data:o.z.object({headers:o.z.array(o.z.string()),rows:o.z.array(o.z.array(o.z.any()))})}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>t(89284));module.exports=o})();