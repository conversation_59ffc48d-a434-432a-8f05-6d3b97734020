"use strict";exports.id=5108,exports.ids=[5108],exports.modules={941:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},96633:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},75290:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},88378:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},37125:(e,t,r)=>{r.d(t,{u:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},34478:(e,t,r)=>{r.d(t,{f:()=>i});var n=r(17577),l=r(45226),o=r(10326),a=n.forwardRef((e,t)=>(0,o.jsx)(l.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},44875:(e,t,r)=>{r.d(t,{$G:()=>ez,B4:()=>eP,JO:()=>eE,VY:()=>eW,Z0:()=>eO,ZA:()=>e_,__:()=>eA,ck:()=>eH,eT:()=>eB,fC:()=>eV,h_:()=>eN,l_:()=>eL,u_:()=>eK,wU:()=>eF,xz:()=>eI});var n=r(17577),l=r(60962),o=r(37125),a=r(82561),i=r(70545),s=r(48051),d=r(93095),u=r(17124),c=r(825),p=r(80699),f=r(10441),v=r(88957),h=r(17103),m=r(83078),w=r(45226),g=r(34214),x=r(55049),y=r(52067),b=r(65819),C=r(53405),S=r(6009),M=r(35664),j=r(58260),T=r(10326),R=[" ","Enter","ArrowUp","ArrowDown"],k=[" ","Enter"],D="Select",[V,I,P]=(0,i.B)(D),[E,N]=(0,d.b)(D,[P,h.D7]),W=(0,h.D7)(),[L,_]=E(D),[A,H]=E(D),B=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=W(t),[b,C]=n.useState(null),[S,M]=n.useState(null),[j,R]=n.useState(!1),k=(0,u.gm)(c),[I,P]=(0,y.T)({prop:l,defaultProp:o??!1,onChange:a,caller:D}),[E,N]=(0,y.T)({prop:i,defaultProp:s,onChange:d,caller:D}),_=n.useRef(null),H=!b||g||!!b.closest("form"),[B,F]=n.useState(new Set),K=Array.from(B).map(e=>e.props.value).join(";");return(0,T.jsx)(h.fC,{...x,children:(0,T.jsxs)(L,{required:w,scope:t,trigger:b,onTriggerChange:C,valueNode:S,onValueNodeChange:M,valueNodeHasChildren:j,onValueNodeHasChildrenChange:R,contentId:(0,v.M)(),value:E,onValueChange:N,open:I,onOpenChange:P,dir:k,triggerPointerDownPosRef:_,disabled:m,children:[(0,T.jsx)(V.Provider,{scope:t,children:(0,T.jsx)(A,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),H?(0,T.jsxs)(eT,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:E,onChange:e=>N(e.target.value),disabled:m,form:g,children:[void 0===E?(0,T.jsx)("option",{value:""}):null,Array.from(B)]},K):null]})})};B.displayName=D;var F="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=W(r),d=_(F,r),u=d.disabled||l,c=(0,s.e)(t,d.onTriggerChange),p=I(r),f=n.useRef("touch"),[v,m,g]=ek(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eD(t,e,r);void 0!==n&&d.onValueChange(n.value)}),x=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(h.ee,{asChild:!0,...i,children:(0,T.jsx)(w.WV.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eR(d.value)?"":void 0,...o,ref:c,onClick:(0,a.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.M)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.M)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(x(),e.preventDefault())})})})});K.displayName=F;var z="SelectValue",O=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=_(z,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.e)(t,d.onValueNodeChange);return(0,b.b)(()=>{u(c)},[u,c]),(0,T.jsx)(w.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:eR(d.value)?(0,T.jsx)(T.Fragment,{children:a}):o})});O.displayName=z;var Z=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,T.jsx)(w.WV.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});Z.displayName="SelectIcon";var U=e=>(0,T.jsx)(m.h,{asChild:!0,...e});U.displayName="SelectPortal";var q="SelectContent",Y=n.forwardRef((e,t)=>{let r=_(q,e.__scopeSelect),[o,a]=n.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,T.jsx)(J,{...e,ref:t}):o?l.createPortal((0,T.jsx)($,{scope:e.__scopeSelect,children:(0,T.jsx)(V.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),o):null});Y.displayName=q;var[$,X]=E(q),G=(0,g.Z8)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:C,...S}=e,R=_(q,r),[k,D]=n.useState(null),[V,P]=n.useState(null),E=(0,s.e)(t,e=>D(e)),[N,W]=n.useState(null),[L,A]=n.useState(null),H=I(r),[B,F]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(k)return(0,M.Ry)(k)},[k]),(0,p.EW)();let z=n.useCallback(e=>{let[t,...r]=H().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&V&&(V.scrollTop=0),r===n&&V&&(V.scrollTop=V.scrollHeight),r?.focus(),document.activeElement!==l))return},[H,V]),O=n.useCallback(()=>z([N,k]),[z,N,k]);n.useEffect(()=>{B&&O()},[B,O]);let{onOpenChange:Z,triggerPointerDownPosRef:U}=R;n.useEffect(()=>{if(k){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(U.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():k.contains(r.target)||Z(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[k,Z,U]),n.useEffect(()=>{let e=()=>Z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[Z]);let[Y,X]=ek(e=>{let t=H().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eD(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==R.value&&R.value===t||n)&&(W(e),n&&(K.current=!0))},[R.value]),et=n.useCallback(()=>k?.focus(),[k]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==R.value&&R.value===t||n)&&A(e)},[R.value]),en="popper"===l?ee:Q,el=en===ee?{side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:C}:{};return(0,T.jsx)($,{scope:r,content:k,viewport:V,onViewportChange:P,itemRefCallback:J,selectedItem:N,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:O,selectedItemText:L,position:l,isPositioned:B,searchRef:Y,children:(0,T.jsx)(j.Z,{as:G,allowPinchZoom:!0,children:(0,T.jsx)(f.M,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(o,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,T.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,T.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...S,...el,onPlaced:()=>F(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,a.M)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(q,r),d=X(q,r),[u,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.e)(t,e=>f(e)),h=I(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:C,focusSelectedItem:S}=d,M=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&x&&y&&C){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=C.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.left=p+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.right=p+"px"}let a=h(),s=window.innerHeight-20,d=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),S=window.getComputedStyle(x),M=parseInt(S.paddingTop,10),j=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,R=y.offsetHeight/2,k=f+v+(y.offsetTop+R);if(k<=T){let e=a.length>0&&y===a[a.length-1].ref.current;u.style.bottom="0px";let t=p.clientHeight-x.offsetTop-x.offsetHeight;u.style.height=k+Math.max(s-T,R+(e?j:0)+t+w)+"px"}else{let e=a.length>0&&y===a[0].ref.current;u.style.top="0px";let t=Math.max(T,f+x.offsetTop+(e?M:0)+R);u.style.height=t+(g-k)+"px",x.scrollTop=k-T+x.offsetTop}u.style.margin="10px 0",u.style.minHeight=b+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,p,x,y,C,i.dir,l]);(0,b.b)(()=>M(),[M]);let[j,R]=n.useState();(0,b.b)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let k=n.useCallback(e=>{e&&!0===g.current&&(M(),S?.(),g.current=!1)},[M,S]);return(0,T.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:k,children:(0,T.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,T.jsx)(w.WV.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=W(r);return(0,T.jsx)(h.VY,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=E(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=X(en,r),d=er(en,r),u=(0,s.e)(t,i.onViewportChange),c=n.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,T.jsx)(V.Slot,{scope:r,children:(0,T.jsx)(w.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=E(eo),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.M)();return(0,T.jsx)(ea,{scope:r,id:l,children:(0,T.jsx)(w.WV.div,{role:"group","aria-labelledby":l,...n,ref:t})})});es.displayName=eo;var ed="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(ed,r);return(0,T.jsx)(w.WV.div,{id:l.id,...n,ref:t})});eu.displayName=ed;var ec="SelectItem",[ep,ef]=E(ec),ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=_(ec,r),c=X(ec,r),p=u.value===l,[f,h]=n.useState(i??""),[m,g]=n.useState(!1),x=(0,s.e)(t,e=>c.itemRefCallback?.(e,l,o)),y=(0,v.M)(),b=n.useRef("touch"),C=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(ep,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,T.jsx)(V.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,T.jsx)(w.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:x,onFocus:(0,a.M)(d.onFocus,()=>g(!0)),onBlur:(0,a.M)(d.onBlur,()=>g(!1)),onClick:(0,a.M)(d.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,a.M)(d.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,a.M)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(d.onPointerMove,e=>{b.current=e.pointerType,o?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.M)(d.onKeyDown,e=>{c.searchRef?.current!==""&&" "===e.key||(k.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ec;var eh="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=_(eh,r),u=X(eh,r),c=ef(eh,r),p=H(eh,r),[f,v]=n.useState(null),h=(0,s.e)(t,e=>v(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,g=n.useMemo(()=>(0,T.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.b)(()=>(x(g),()=>y(g)),[x,y,g]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(w.WV.span,{id:c.textId,...i,ref:h}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});em.displayName=eh;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,T.jsx)(w.WV.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ex="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=X(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eb="SelectScrollDownButton",eC=n.forwardRef((e,t)=>{let r=X(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eC.displayName=eb;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=X("SelectScrollButton",r),s=n.useRef(null),d=I(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.b)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,T.jsx)(w.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.M)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.M)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.M)(o.onPointerLeave,()=>{u()})})}),eM=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,T.jsx)(w.WV.div,{"aria-hidden":!0,...n,ref:t})});eM.displayName="SelectSeparator";var ej="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=W(r),o=_(ej,r),a=X(ej,r);return o.open&&"popper"===a.position?(0,T.jsx)(h.Eh,{...l,...n,ref:t}):null}).displayName=ej;var eT=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),a=(0,s.e)(l,o),i=(0,C.D)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,T.jsx)(w.WV.select,{...r,style:{...S.C2,...r.style},ref:a,defaultValue:t})});function eR(e){return""===e||void 0===e}function ek(e){let t=(0,x.W)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eD(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==r?a:void 0}eT.displayName="SelectBubbleInput";var eV=B,eI=K,eP=O,eE=Z,eN=U,eW=Y,eL=el,e_=es,eA=eu,eH=ev,eB=em,eF=eg,eK=ey,ez=eC,eO=eM},28407:(e,t,r)=>{r.d(t,{VY:()=>I,aV:()=>D,fC:()=>k,xz:()=>V});var n=r(17577),l=r(82561),o=r(93095),a=r(15594),i=r(9815),s=r(45226),d=r(17124),u=r(52067),c=r(88957),p=r(10326),f="Tabs",[v,h]=(0,o.b)(f,[a.Pc]),m=(0,a.Pc)(),[w,g]=v(f),x=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:l,defaultValue:o,orientation:a="horizontal",dir:i,activationMode:v="automatic",...h}=e,m=(0,d.gm)(i),[g,x]=(0,u.T)({prop:n,onChange:l,defaultProp:o??"",caller:f});return(0,p.jsx)(w,{scope:r,baseId:(0,c.M)(),value:g,onValueChange:x,orientation:a,dir:m,activationMode:v,children:(0,p.jsx)(s.WV.div,{dir:m,"data-orientation":a,...h,ref:t})})});x.displayName=f;var y="TabsList",b=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...l}=e,o=g(y,r),i=m(r);return(0,p.jsx)(a.fC,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:n,children:(0,p.jsx)(s.WV.div,{role:"tablist","aria-orientation":o.orientation,...l,ref:t})})});b.displayName=y;var C="TabsTrigger",S=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...i}=e,d=g(C,r),u=m(r),c=T(d.baseId,n),f=R(d.baseId,n),v=n===d.value;return(0,p.jsx)(a.ck,{asChild:!0,...u,focusable:!o,active:v,children:(0,p.jsx)(s.WV.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":f,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...i,ref:t,onMouseDown:(0,l.M)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,l.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,l.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;v||o||!e||d.onValueChange(n)})})})});S.displayName=C;var M="TabsContent",j=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:l,forceMount:o,children:a,...d}=e,u=g(M,r),c=T(u.baseId,l),f=R(u.baseId,l),v=l===u.value,h=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.z,{present:o||v,children:({present:r})=>(0,p.jsx)(s.WV.div,{"data-state":v?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&a})})});function T(e,t){return`${e}-trigger-${t}`}function R(e,t){return`${e}-content-${t}`}j.displayName=M;var k=x,D=b,V=S,I=j},53405:(e,t,r)=>{r.d(t,{D:()=>l});var n=r(17577);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6009:(e,t,r)=>{r.d(t,{C2:()=>a,TX:()=>i,fC:()=>s});var n=r(17577),l=r(45226),o=r(10326),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=n.forwardRef((e,t)=>(0,o.jsx)(l.WV.span,{...e,ref:t,style:{...a,...e.style}}));i.displayName="VisuallyHidden";var s=i}};