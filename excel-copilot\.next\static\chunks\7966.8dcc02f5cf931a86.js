"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7966],{67966:function(e,t,r){r.r(t),r.d(t,{GitHubClient:function(){return i},GitHubMonitoringService:function(){return l}});var o=r(9821),a=r(18473),n=r(25566),s=r(9109).lW;class i{shouldUseOAuth(){return"OAUTH_MODE"===n.env.MCP_GITHUB_TOKEN&&!!n.env.AUTH_GITHUB_CLIENT_ID&&!!n.env.AUTH_GITHUB_CLIENT_SECRET}async getOAuthToken(){return s.from("".concat(n.env.AUTH_GITHUB_CLIENT_ID,":").concat(n.env.AUTH_GITHUB_CLIENT_SECRET)).toString("base64")}async restRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.token)throw Error("GitHub token n\xe3o configurado");let r="".concat(this.baseUrl).concat(e),o={Authorization:"Bearer ".concat(this.token),Accept:"application/vnd.github+json","X-GitHub-Api-Version":"2022-11-28",...t.headers};try{let e=await fetch(r,{...t,headers:o});if(!e.ok){let t=await e.text();throw Error("GitHub API error: ".concat(e.status," - ").concat(t))}return await e.json()}catch(t){throw a.logger.error("Erro na requisi\xe7\xe3o GitHub API",{endpoint:e,error:t}),t}}async graphqlQuery(e,t){if(!this.token)throw Error("GitHub token n\xe3o configurado");let r=await fetch(this.graphqlUrl,{method:"POST",headers:{Authorization:"Bearer ".concat(this.token),"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t})});if(!r.ok)throw Error("GitHub GraphQL error: ".concat(r.status," ").concat(r.statusText));let o=await r.json();if(o.errors)throw Error("GitHub GraphQL error: ".concat(o.errors.map(e=>e.message).join(", ")));return o.data}async getRateLimit(){return(await this.restRequest("/rate_limit")).rate}async getRepositories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{type:t="owner",sort:r="updated",direction:o="desc",per_page:a=30,page:n=1}=e,s=this.owner?"/orgs/".concat(this.owner,"/repos"):"/user/repos",i=new URLSearchParams({type:t,sort:r,direction:o,per_page:a.toString(),page:n.toString()}),l=await this.restRequest("".concat(s,"?").concat(i));return{repositories:l,total:l.length}}async getRepository(e,t){return await this.restRequest("/repos/".concat(e,"/").concat(t))}async getIssues(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.owner||this.owner,r=e.repo||this.repo;if(!t||!r)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let{state:o="open",sort:a="updated",direction:n="desc",per_page:s=30,page:i=1}=e,l=new URLSearchParams({state:o,sort:a,direction:n,per_page:s.toString(),page:i.toString()});e.labels&&l.append("labels",e.labels),e.since&&l.append("since",e.since);let c=(await this.restRequest("/repos/".concat(t,"/").concat(r,"/issues?").concat(l))).filter(e=>!("pull_request"in e));return{issues:c,total:c.length}}async getPullRequests(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.owner||this.owner,r=e.repo||this.repo;if(!t||!r)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let{state:o="open",sort:a="updated",direction:n="desc",per_page:s=30,page:i=1}=e,l=new URLSearchParams({state:o,sort:a,direction:n,per_page:s.toString(),page:i.toString()});e.head&&l.append("head",e.head),e.base&&l.append("base",e.base);let c=await this.restRequest("/repos/".concat(t,"/").concat(r,"/pulls?").concat(l));return{pullRequests:c,total:c.length}}async getWorkflowRuns(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.owner||this.owner,r=e.repo||this.repo;if(!t||!r)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let{per_page:o=30,page:a=1}=e,n=new URLSearchParams({per_page:o.toString(),page:a.toString()});e.workflow_id&&n.append("workflow_id",e.workflow_id),e.actor&&n.append("actor",e.actor),e.branch&&n.append("branch",e.branch),e.event&&n.append("event",e.event),e.status&&n.append("status",e.status);let s=await this.restRequest("/repos/".concat(t,"/").concat(r,"/actions/runs?").concat(n));return{workflowRuns:s.workflow_runs,total:s.total_count}}async getAuthenticatedUser(){return await this.restRequest("/user")}async checkHealth(){try{if(!this.token)return{configured:!1,tokenValid:!1,repositoryAccessible:!1,rateLimitRemaining:0,rateLimitReset:new Date().toISOString(),repositoryCount:0,issueCount:0,pullRequestCount:0,recentActivity:0};let e=await this.getRateLimit();await this.getAuthenticatedUser();let t=!0,r=0,o=0,n=0,s=0;try{if(this.owner&&this.repo){await this.getRepository(this.owner,this.repo),r=1;let[e,t]=await Promise.all([this.getIssues({per_page:10}),this.getPullRequests({per_page:10})]);o=e.total,n=t.total;let a=new Date(Date.now()-864e5);s=[...e.issues,...t.pullRequests].filter(e=>new Date(e.updated_at)>a).length}else r=(await this.getRepositories({per_page:10})).total}catch(e){a.logger.warn("Erro ao acessar reposit\xf3rio espec\xedfico:",e),t=!1}return{configured:!0,tokenValid:!0,repositoryAccessible:t,rateLimitRemaining:e.remaining,rateLimitReset:new Date(1e3*e.reset).toISOString(),lastSync:new Date().toISOString(),repositoryCount:r,issueCount:o,pullRequestCount:n,recentActivity:s}}catch(e){return a.logger.error("GitHub health check failed:",e),{configured:!!this.token,tokenValid:!1,repositoryAccessible:!1,rateLimitRemaining:0,rateLimitReset:new Date().toISOString(),repositoryCount:0,issueCount:0,pullRequestCount:0,recentActivity:0}}}constructor(e){this.baseUrl="https://api.github.com",this.graphqlUrl="https://api.github.com/graphql",this.token=(null==e?void 0:e.token)||o.Vi.GITHUB_TOKEN||"";let t=(null==e?void 0:e.owner)||o.Vi.GITHUB_OWNER;t&&(this.owner=t);let r=(null==e?void 0:e.repo)||o.Vi.GITHUB_REPO;r&&(this.repo=r),this.token||a.logger.warn("GitHub token n\xe3o configurado")}}class l{async getRepositoryDashboard(e,t){let r=e||o.Vi.GITHUB_OWNER,a=t||o.Vi.GITHUB_REPO;if(!r||!a)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let[n,s,i,l]=await Promise.all([this.client.getRepository(r,a),this.client.getIssues({owner:r,repo:a,state:"open",per_page:5}),this.client.getPullRequests({owner:r,repo:a,state:"open",per_page:5}),this.client.getWorkflowRuns({owner:r,repo:a,per_page:5})]),c=[...s.issues,...i.pullRequests].sort((e,t)=>new Date(t.updated_at).getTime()-new Date(e.updated_at).getTime()).slice(0,10),u="healthy",p=l.workflowRuns.filter(e=>"failure"===e.conclusion).length;return p>2?u="critical":p>0&&(u="warning"),{repository:n,openIssues:s.total,openPullRequests:i.total,recentWorkflowRuns:l.workflowRuns,recentActivity:c,healthStatus:u}}async getCICDMetrics(e,t){let r=e||o.Vi.GITHUB_OWNER,a=t||o.Vi.GITHUB_REPO;if(!r||!a)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let n=await this.client.getWorkflowRuns({owner:r,repo:a,per_page:100}),s=new Date(Date.now()-2592e6),i=n.workflowRuns.filter(e=>new Date(e.created_at)>s),l=i.length,c=i.filter(e=>"success"===e.conclusion).length,u=i.filter(e=>"completed"===e.status&&e.run_started_at),p=u.length>0?u.reduce((e,t)=>{let r=new Date(t.run_started_at).getTime();return e+(new Date(t.updated_at).getTime()-r)},0)/u.length/1e3/60:0,h=i.filter(e=>"failure"===e.conclusion).slice(0,5),g=new Map;return i.forEach(e=>{if(!e.created_at)return;let t=new Date(e.created_at).toISOString().split("T")[0];if(!t)return;let r=g.get(t)||{runs:0,failures:0};r.runs++,"failure"===e.conclusion&&r.failures++,g.set(t,r)}),{totalRuns:l,successRate:l>0?c/l*100:0,averageDuration:p,recentFailures:h,trendsLast30Days:Array.from(g.entries()).map(e=>{let[t,r]=e;return{date:t,...r}}).sort((e,t)=>e.date.localeCompare(t.date))}}constructor(e){this.client=new i(e)}}}}]);