"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8194],{9821:function(e,t,n){n.d(t,{Vi:function(){return I}});var i=n(59772),E=n(25566);let o=i.z.string().url("Deve ser uma URL v\xe1lida"),_=i.z.string().min(8,"Deve ter pelo menos 8 caracteres"),r=i.z.string().min(1,"N\xe3o pode estar vazio"),a=i.z.object({NODE_ENV:i.z.enum(["development","production","test"]),APP_NAME:i.z.string().default("Excel Copilot"),APP_VERSION:i.z.string().default("1.0.0"),APP_URL:o,AUTH_NEXTAUTH_SECRET:_,AUTH_NEXTAUTH_URL:o,AUTH_GOOGLE_CLIENT_ID:r.optional(),AUTH_GOOGLE_CLIENT_SECRET:_.optional(),AUTH_GITHUB_CLIENT_ID:r.optional(),AUTH_GITHUB_CLIENT_SECRET:_.optional(),AUTH_SKIP_PROVIDERS:i.z.boolean().default(!1),DB_DATABASE_URL:i.z.string().min(1,"URL do banco \xe9 obrigat\xf3ria"),DB_DIRECT_URL:i.z.string().optional(),DB_PROVIDER:i.z.enum(["postgresql","sqlite"]).default("postgresql"),AI_ENABLED:i.z.boolean().default(!0),AI_USE_MOCK:i.z.boolean().default(!1),AI_VERTEX_PROJECT_ID:i.z.string().optional(),AI_VERTEX_LOCATION:i.z.string().default("us-central1"),AI_VERTEX_MODEL:i.z.string().default("gemini-2.0-flash-001"),STRIPE_ENABLED:i.z.boolean().default(!0),STRIPE_SECRET_KEY:i.z.string().optional(),STRIPE_WEBHOOK_SECRET:i.z.string().optional(),STRIPE_PUBLISHABLE_KEY:i.z.string().optional(),SUPABASE_URL:o.optional(),SUPABASE_ANON_KEY:i.z.string().optional(),SUPABASE_SERVICE_ROLE_KEY:i.z.string().optional(),MCP_VERCEL_TOKEN:i.z.string().optional(),MCP_VERCEL_PROJECT_ID:i.z.string().optional(),MCP_VERCEL_TEAM_ID:i.z.string().optional(),MCP_LINEAR_API_KEY:i.z.string().optional(),MCP_GITHUB_TOKEN:i.z.string().optional(),DEV_DISABLE_VALIDATION:i.z.boolean().default(!1),DEV_FORCE_PRODUCTION:i.z.boolean().default(!1),DEV_LOG_LEVEL:i.z.enum(["debug","info","warn","error"]).default("info"),SECURITY_CSRF_SECRET:i.z.string().optional(),SECURITY_RATE_LIMIT_ENABLED:i.z.boolean().default(!0),SECURITY_CORS_ORIGINS:i.z.string().optional()});function s(e,t){return E.env[e]||t}function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e?["true","1","yes","on"].includes(e.toLowerCase()):t}class A{static getInstance(){return A.instance||(A.instance=new A),A.instance}loadConfiguration(){return{NODE_ENV:s("NODE_ENV","development"),APP_NAME:s("APP_NAME","Excel Copilot"),APP_VERSION:s("APP_VERSION","1.0.0"),APP_URL:s("NEXT_PUBLIC_APP_URL")||s("APP_URL","http://localhost:3000"),AUTH_NEXTAUTH_SECRET:s("AUTH_NEXTAUTH_SECRET"),AUTH_NEXTAUTH_URL:s("AUTH_NEXTAUTH_URL"),AUTH_GOOGLE_CLIENT_ID:s("AUTH_GOOGLE_CLIENT_ID"),AUTH_GOOGLE_CLIENT_SECRET:s("AUTH_GOOGLE_CLIENT_SECRET"),AUTH_GITHUB_CLIENT_ID:s("AUTH_GITHUB_CLIENT_ID"),AUTH_GITHUB_CLIENT_SECRET:s("AUTH_GITHUB_CLIENT_SECRET"),AUTH_SKIP_PROVIDERS:T(s("AUTH_SKIP_PROVIDERS")),DB_DATABASE_URL:s("DB_DATABASE_URL"),DB_DIRECT_URL:s("DB_DIRECT_URL"),DB_PROVIDER:s("DB_PROVIDER","postgresql"),AI_ENABLED:this.resolveAIConfiguration(),AI_USE_MOCK:this.resolveAIMockConfiguration(),AI_VERTEX_PROJECT_ID:s("AI_VERTEX_PROJECT_ID"),AI_VERTEX_LOCATION:s("AI_VERTEX_LOCATION","us-central1"),AI_VERTEX_MODEL:s("AI_VERTEX_MODEL","gemini-2.0-flash-001"),STRIPE_ENABLED:!T(s("DISABLE_STRIPE")),STRIPE_SECRET_KEY:s("STRIPE_SECRET_KEY"),STRIPE_WEBHOOK_SECRET:s("STRIPE_WEBHOOK_SECRET"),STRIPE_PUBLISHABLE_KEY:s("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"),SUPABASE_URL:s("SUPABASE_URL")||s("NEXT_PUBLIC_SUPABASE_URL"),SUPABASE_ANON_KEY:s("SUPABASE_ANON_KEY")||s("NEXT_PUBLIC_SUPABASE_ANON_KEY"),SUPABASE_SERVICE_ROLE_KEY:s("SUPABASE_SERVICE_ROLE_KEY"),MCP_VERCEL_TOKEN:s("MCP_VERCEL_TOKEN"),MCP_VERCEL_PROJECT_ID:s("MCP_VERCEL_PROJECT_ID"),MCP_VERCEL_TEAM_ID:s("MCP_VERCEL_TEAM_ID"),MCP_LINEAR_API_KEY:s("MCP_LINEAR_API_KEY"),MCP_GITHUB_TOKEN:s("MCP_GITHUB_TOKEN"),DEV_DISABLE_VALIDATION:!1,DEV_FORCE_PRODUCTION:T(s("DEV_FORCE_PRODUCTION")),DEV_LOG_LEVEL:s("DEV_LOG_LEVEL","info"),SECURITY_CSRF_SECRET:s("SECURITY_CSRF_SECRET"),SECURITY_RATE_LIMIT_ENABLED:!T(s("SECURITY_RATE_LIMIT_ENABLED")),SECURITY_CORS_ORIGINS:s("SECURITY_CORS_ORIGINS")}}resolveAIConfiguration(){if(T(s("NEXT_PUBLIC_DISABLE_VERTEX_AI")))return!1;let e=s("AI_ENABLED");return void 0!==e?T(e,!0):!!s("AI_VERTEX_PROJECT_ID")||"production"===s("NODE_ENV","development")}resolveAIMockConfiguration(){return!!(T(s("FORCE_GOOGLE_MOCKS"))||T(s("AI_USE_MOCK")))||!this.resolveAIConfiguration()||!(s("AI_VERTEX_PROJECT_ID")||s("VERTEX_AI_CREDENTIALS"))||"development"===s("NODE_ENV","development")}validateConfiguration(){let e={valid:!0,errors:[],warnings:[],missing:[],conflicts:[]};try{a.parse(this.config),this.validateByEnvironment(e),this.validateDependencies(e),this.validateConflicts(e),this.validateSecurity(e)}catch(t){t instanceof i.z.ZodError?e.errors.push(...t.errors.map(e=>"".concat(e.path.join("."),": ").concat(e.message))):e.errors.push("Erro de valida\xe7\xe3o: ".concat(t))}return e.valid=0===e.errors.length,e}validateByEnvironment(e){let t=this.config.NODE_ENV;if("production"===t){for(let t of["AUTH_NEXTAUTH_SECRET","AUTH_NEXTAUTH_URL","DB_DATABASE_URL"])this.config[t]||e.errors.push("".concat(t," \xe9 obrigat\xf3ria em produ\xe7\xe3o"));this.config.AUTH_GOOGLE_CLIENT_ID||this.config.AUTH_GITHUB_CLIENT_ID||e.errors.push("Pelo menos um provider OAuth deve estar configurado em produ\xe7\xe3o"),this.config.DEV_DISABLE_VALIDATION&&e.errors.push("CR\xcdTICO: DISABLE_ENV_VALIDATION est\xe1 ativo em produ\xe7\xe3o - RISCO DE SEGURAN\xc7A"),(s("DISABLE_ENV_VALIDATION")||s("DEV_DISABLE_VALIDATION"))&&e.errors.push("CR\xcdTICO: Tentativa de bypass de valida\xe7\xe3o detectada em produ\xe7\xe3o")}"development"!==t||this.config.AUTH_GOOGLE_CLIENT_ID||this.config.AUTH_GITHUB_CLIENT_ID||e.warnings.push("Nenhum provider OAuth configurado - usando modo de desenvolvimento")}validateDependencies(e){var t;this.config.STRIPE_ENABLED&&(this.config.STRIPE_SECRET_KEY||e.errors.push("STRIPE_SECRET_KEY \xe9 obrigat\xf3ria quando Stripe est\xe1 habilitado"),this.config.STRIPE_PUBLISHABLE_KEY||e.errors.push("STRIPE_PUBLISHABLE_KEY \xe9 obrigat\xf3ria quando Stripe est\xe1 habilitado")),!this.config.AI_ENABLED||this.config.AI_USE_MOCK||this.config.AI_VERTEX_PROJECT_ID||e.warnings.push("AI habilitada sem VERTEX_AI_PROJECT_ID - usando modo mock"),(null===(t=this.config.DB_DATABASE_URL)||void 0===t?void 0:t.includes("supabase"))&&(this.config.SUPABASE_URL||e.warnings.push("Usando Supabase mas SUPABASE_URL n\xe3o configurada"),this.config.SUPABASE_ANON_KEY||e.warnings.push("Usando Supabase mas SUPABASE_ANON_KEY n\xe3o configurada"))}validateConflicts(e){this.config.AI_ENABLED&&T(s("FORCE_GOOGLE_MOCKS"))&&e.conflicts.push("AI_ENABLED=true mas FORCE_GOOGLE_MOCKS=true - usando mocks"),[s("FORCE_GOOGLE_MOCKS"),s("AI_USE_MOCK"),s("NEXT_PUBLIC_DISABLE_VERTEX_AI")].filter(Boolean).length>1&&e.conflicts.push("M\xfaltiplas flags de configura\xe7\xe3o de IA detectadas - usando hierarquia de preced\xeancia"),"production"===this.config.NODE_ENV&&this.config.AUTH_SKIP_PROVIDERS&&e.conflicts.push("SKIP_AUTH_PROVIDERS=true em produ\xe7\xe3o - pode causar problemas de autentica\xe7\xe3o")}validateSecurity(e){if(this.config.AUTH_NEXTAUTH_SECRET&&this.config.AUTH_NEXTAUTH_SECRET.length<32&&e.warnings.push("NEXTAUTH_SECRET deveria ter pelo menos 32 caracteres para m\xe1xima seguran\xe7a"),"production"===this.config.NODE_ENV){var t,n;(null===(t=this.config.AUTH_NEXTAUTH_URL)||void 0===t?void 0:t.includes("localhost"))&&e.errors.push("NEXTAUTH_URL n\xe3o pode ser localhost em produ\xe7\xe3o"),(null===(n=this.config.APP_URL)||void 0===n?void 0:n.includes("localhost"))&&e.errors.push("APP_URL n\xe3o pode ser localhost em produ\xe7\xe3o")}"production"!==this.config.NODE_ENV||this.config.SECURITY_CORS_ORIGINS||e.warnings.push("CORS_ORIGINS n\xe3o configurado em produ\xe7\xe3o - pode causar problemas de seguran\xe7a")}getConfig(){return{...this.config}}getValidationResult(){return{...this.validationResult}}isValid(){return this.validationResult.valid}getAuthConfig(){return{enabled:!this.config.AUTH_SKIP_PROVIDERS,status:this.config.AUTH_SKIP_PROVIDERS?"disabled":"enabled",credentials:{nextAuthSecret:this.config.AUTH_NEXTAUTH_SECRET||"",nextAuthUrl:this.config.AUTH_NEXTAUTH_URL||"",googleClientId:this.config.AUTH_GOOGLE_CLIENT_ID||"",googleClientSecret:this.config.AUTH_GOOGLE_CLIENT_SECRET||"",githubClientId:this.config.AUTH_GITHUB_CLIENT_ID||"",githubClientSecret:this.config.AUTH_GITHUB_CLIENT_SECRET||""}}}getAIConfig(){let e=this.config.AI_ENABLED,t=this.config.AI_USE_MOCK,n="enabled";return e?t&&(n="mock"):n="disabled",{enabled:e,status:n,credentials:{projectId:this.config.AI_VERTEX_PROJECT_ID||"",location:this.config.AI_VERTEX_LOCATION,model:this.config.AI_VERTEX_MODEL}}}getDatabaseConfig(){return{enabled:!!this.config.DB_DATABASE_URL,status:this.config.DB_DATABASE_URL?"enabled":"disabled",credentials:{databaseUrl:this.config.DB_DATABASE_URL||"",directUrl:this.config.DB_DIRECT_URL||"",provider:this.config.DB_PROVIDER}}}getStripeConfig(){return{enabled:this.config.STRIPE_ENABLED,status:this.config.STRIPE_ENABLED?"enabled":"disabled",credentials:{secretKey:this.config.STRIPE_SECRET_KEY||"",webhookSecret:this.config.STRIPE_WEBHOOK_SECRET||"",publishableKey:this.config.STRIPE_PUBLISHABLE_KEY||""}}}getMCPConfig(){return{vercel:{enabled:!!this.config.MCP_VERCEL_TOKEN,status:this.config.MCP_VERCEL_TOKEN?"enabled":"disabled",credentials:{token:this.config.MCP_VERCEL_TOKEN||"",projectId:this.config.MCP_VERCEL_PROJECT_ID||"",teamId:this.config.MCP_VERCEL_TEAM_ID||""}},linear:{enabled:!!this.config.MCP_LINEAR_API_KEY,status:this.config.MCP_LINEAR_API_KEY?"enabled":"disabled",credentials:{apiKey:this.config.MCP_LINEAR_API_KEY||""}},github:{enabled:!!this.config.MCP_GITHUB_TOKEN,status:this.config.MCP_GITHUB_TOKEN?"enabled":"disabled",credentials:{token:this.config.MCP_GITHUB_TOKEN||""}}}}revalidate(){return this.config=this.loadConfiguration(),this.validationResult=this.validateConfiguration(),this.getValidationResult()}generateReport(){var e,t,n,i,E,o;let _=this.validationResult,r=this.config,a="\uD83D\uDD27 RELAT\xd3RIO DE CONFIGURA\xc7\xc3O - EXCEL COPILOT\n";a+="=".repeat(60)+"\n\n"+"✅ Status Geral: ".concat(_.valid?"V\xc1LIDA":"INV\xc1LIDA","\n")+"\uD83C\uDF0D Ambiente: ".concat(r.NODE_ENV,"\n")+"\uD83D\uDCF1 Aplica\xe7\xe3o: ".concat(r.APP_NAME," v").concat(r.APP_VERSION,"\n")+"\uD83D\uDD17 URL: ".concat(r.APP_URL,"\n\n")+"\uD83D\uDCCB STATUS DOS SERVI\xc7OS:\n"+"-".repeat(30)+"\n";let s=this.getAuthConfig();a+="\uD83D\uDD10 Autentica\xe7\xe3o: ".concat(s.status.toUpperCase(),"\n");let T=this.getAIConfig();a+="\uD83E\uDD16 Intelig\xeancia Artificial: ".concat(T.status.toUpperCase(),"\n");let A=this.getDatabaseConfig();a+="\uD83D\uDDC4️  Banco de Dados: ".concat(A.status.toUpperCase(),"\n");let l=this.getStripeConfig();a+="\uD83D\uDCB3 Stripe: ".concat(l.status.toUpperCase(),"\n");let I=this.getMCPConfig();return a+="\uD83D\uDD0C Vercel MCP: ".concat((null===(t=I.vercel)||void 0===t?void 0:null===(e=t.status)||void 0===e?void 0:e.toUpperCase())||"DISABLED","\n")+"\uD83D\uDD0C Linear MCP: ".concat((null===(i=I.linear)||void 0===i?void 0:null===(n=i.status)||void 0===n?void 0:n.toUpperCase())||"DISABLED","\n")+"\uD83D\uDD0C GitHub MCP: ".concat((null===(o=I.github)||void 0===o?void 0:null===(E=o.status)||void 0===E?void 0:E.toUpperCase())||"DISABLED","\n\n"),_.errors.length>0&&(a+="❌ ERROS:\n",_.errors.forEach(e=>a+="  • ".concat(e,"\n")),a+="\n"),_.warnings.length>0&&(a+="⚠️  AVISOS:\n",_.warnings.forEach(e=>a+="  • ".concat(e,"\n")),a+="\n"),_.conflicts.length>0&&(a+="\uD83D\uDD04 CONFLITOS RESOLVIDOS:\n",_.conflicts.forEach(e=>a+="  • ".concat(e,"\n")),a+="\n"),a}constructor(){this.initialized=!1,this.config=this.loadConfiguration(),this.validationResult=this.validateConfiguration()}}let l=A.getInstance(),I={NODE_ENV:l.getConfig().NODE_ENV,IS_DEVELOPMENT:"development"===l.getConfig().NODE_ENV,IS_PRODUCTION:"production"===l.getConfig().NODE_ENV,IS_TEST:"test"===l.getConfig().NODE_ENV,IS_SERVER:!1,APP:{NAME:l.getConfig().APP_NAME,VERSION:l.getConfig().APP_VERSION,URL:l.getConfig().APP_URL},NEXTAUTH_SECRET:l.getConfig().AUTH_NEXTAUTH_SECRET,NEXTAUTH_URL:l.getConfig().AUTH_NEXTAUTH_URL,API_KEYS:{GOOGLE_CLIENT_ID:l.getConfig().AUTH_GOOGLE_CLIENT_ID||"",GOOGLE_CLIENT_SECRET:l.getConfig().AUTH_GOOGLE_CLIENT_SECRET||"",GITHUB_CLIENT_ID:l.getConfig().AUTH_GITHUB_CLIENT_ID||"",GITHUB_CLIENT_SECRET:l.getConfig().AUTH_GITHUB_CLIENT_SECRET||""},DATABASE_URL:l.getConfig().DB_DATABASE_URL,VERTEX_AI:{ENABLED:l.getAIConfig().enabled,PROJECT_ID:l.getConfig().AI_VERTEX_PROJECT_ID||"",LOCATION:l.getConfig().AI_VERTEX_LOCATION,MODEL_NAME:l.getConfig().AI_VERTEX_MODEL,CREDENTIALS_PATH:E.env.VERTEX_AI_CREDENTIALS_PATH||E.env.GOOGLE_APPLICATION_CREDENTIALS},TIMEOUTS:{API_CALL:3e4,HEALTH_CHECK:{DATABASE:5e3,AI_SERVICE:1e4,EXTERNAL_DEPS:15e3}},CACHE:{DEFAULT_TTL:300,EXCEL_CACHE_SIZE:50,EXCEL_CACHE_TTL:1800,AI_CACHE_SIZE:200,AI_CACHE_TTL:86400},LIMITS:{API_RATE_LIMIT:"production"===l.getConfig().NODE_ENV?60:120},FEATURES:{USE_MOCK_AI:"mock"===l.getAIConfig().status,SKIP_AUTH_PROVIDERS:l.getConfig().AUTH_SKIP_PROVIDERS,TELEMETRY_SAMPLE_RATE:.1,ENABLE_REALTIME_COLLABORATION:!0,ENABLE_DESKTOP_INTEGRATION:!0,ENABLE_STRIPE_INTEGRATION:l.getStripeConfig().enabled},VERCEL_API_TOKEN:l.getConfig().MCP_VERCEL_TOKEN,VERCEL_PROJECT_ID:l.getConfig().MCP_VERCEL_PROJECT_ID,VERCEL_TEAM_ID:l.getConfig().MCP_VERCEL_TEAM_ID,LINEAR_API_KEY:l.getConfig().MCP_LINEAR_API_KEY,GITHUB_TOKEN:l.getConfig().MCP_GITHUB_TOKEN,GITHUB_OWNER:E.env.GITHUB_OWNER||E.env.MCP_GITHUB_OWNER,GITHUB_REPO:E.env.GITHUB_REPO||E.env.MCP_GITHUB_REPO||"excel-copilot",SUPABASE_SERVICE_ROLE_KEY:l.getConfig().SUPABASE_SERVICE_ROLE_KEY,SUPABASE_URL:l.getConfig().SUPABASE_URL,validate:()=>l.getValidationResult()}},18043:function(e,t,n){n.d(t,{Ph:function(){return r},d:function(){return E},q:function(){return o},zn:function(){return i}});let i=.5,E=[.23,.1,.36,1],o={hover:{y:-10,scale:1.02,boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},tap:{scale:.98}},_={fadeIn:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.5,ease:E}},slideIn:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.5,ease:E}},list:{initial:{},animate:{transition:{staggerChildren:.1}}},listItem:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0},transition:{duration:.5,ease:E}},card:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0},transition:{duration:.5,ease:E}},page:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3,ease:E}}};function r(e){return _[e]}},67217:function(e,t,n){n.d(t,{OI:function(){return r}});var i=n(9821),E=n(18473),o=n(25566);o.env.AUTH_GOOGLE_CLIENT_ID,o.env.AUTH_GOOGLE_CLIENT_SECRET,o.env.AUTH_GITHUB_CLIENT_ID,o.env.AUTH_GITHUB_CLIENT_SECRET,o.env.AUTH_NEXTAUTH_SECRET,o.env.AUTH_NEXTAUTH_URL,o.env.DB_DATABASE_URL,o.env.AI_VERTEX_PROJECT_ID,o.env.AI_VERTEX_LOCATION;let _={all:["DB_DATABASE_URL","AUTH_NEXTAUTH_SECRET","AUTH_NEXTAUTH_URL"],production:["AUTH_GOOGLE_CLIENT_ID","AUTH_GOOGLE_CLIENT_SECRET","AI_VERTEX_PROJECT_ID"]};function r(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=i.Vi.IS_PRODUCTION,n="1"===o.env.VERCEL,r="true"===o.env.DEV_DISABLE_VALIDATION;if(n&&!t)return E.logger.warn("Valida\xe7\xe3o de ambiente: Ignorando valida\xe7\xf5es para build Vercel (desenvolvimento)"),{valid:!0,missing:[],details:{environment:i.Vi.NODE_ENV,strict:e,checkedVars:0,validationDisabled:!0,reason:"Vercel build-time bypass"}};if(t&&r)E.logger.error("SEGURAN\xc7A: Tentativa de bypass de valida\xe7\xe3o em produ\xe7\xe3o bloqueada - DEV_DISABLE_VALIDATION ignorado");else if(r&&!t)return E.logger.warn("Valida\xe7\xe3o de ambiente: Bypass ativado para desenvolvimento (DEV_DISABLE_VALIDATION=true)"),{valid:!0,missing:[],details:{environment:i.Vi.NODE_ENV,strict:e,checkedVars:0,validationDisabled:!0,reason:"Development bypass"}};let a=[..._.all];(t||e)&&a.push(..._.production);let s=a.filter(e=>!o.env[e]),T={valid:0===s.length,missing:s,details:{environment:i.Vi.NODE_ENV,strict:e,checkedVars:a.length}};return T.valid,T}},18473:function(e,t,n){let i;n.r(t),n.d(t,{devLogger:function(){return g},extractErrorData:function(){return a},initLogger:function(){return u},logger:function(){return l},safeConsoleError:function(){return I},safeConsoleInfo:function(){return C},safeConsoleLog:function(){return T},safeConsoleWarn:function(){return c},serviceLogger:function(){return R},toError:function(){return r},toMetadata:function(){return s}});var E=n(78227),o=n.n(E),_=n(25566);function r(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch(e){return Error("Unknown error")}}}function a(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let t=["name","message","stack"],n={};return Object.keys(e).forEach(i=>{t.includes(i)||(n[i]=e[i])}),{normalizedError:e,extractedMetadata:n}}return"object"==typeof e&&null!==e?{normalizedError:r(e),extractedMetadata:e}:{normalizedError:r(e),extractedMetadata:{}}}function s(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}function T(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]}let A={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err},timestamp:()=>',"time":"'.concat(new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}),'"')},test:{level:"error",enabled:"true"===_.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err}}};try{let e=A.production;i=o()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),i=o()({level:"info",formatters:{level:e=>({level:e})}})}let l={trace:(e,t)=>{i.trace(t||{},e)},debug:(e,t)=>{i.debug(t||{},e)},info:(e,t)=>{i.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:n}=a(t);i.warn(n,e)}else i.warn(s(t)||{},e)},error:(e,t,n)=>{let{normalizedError:E,extractedMetadata:o}=a(t),_={...n||{},...o,...E&&{error:{message:E.message,stack:E.stack,name:E.name}}};i.error(_,e)},fatal:(e,t,n)=>{let{normalizedError:E,extractedMetadata:o}=a(t),_={...n||{},...o,...E&&{error:{message:E.message,stack:E.stack,name:E.name}}};i.fatal(_,e)},createChild:e=>{let t=i.child(e);return{trace:(e,n)=>{t.trace(n||{},e)},debug:(e,n)=>{t.debug(n||{},e)},info:(e,n)=>{t.info(n||{},e)},warn:(e,n)=>{if(n instanceof Error||"object"==typeof n&&null!==n){let{extractedMetadata:i}=a(n);t.warn(i,e)}else t.warn(s(n)||{},e)},error:(e,n,i)=>{let{normalizedError:E,extractedMetadata:o}=a(n),_={...i||{},...o,...E&&{error:{message:E.message,stack:E.stack,name:E.name}}};t.error(_,e)},fatal:(e,n,i)=>{let{normalizedError:E,extractedMetadata:o}=a(n),_={...i||{},...o,...E&&{error:{message:E.message,stack:E.stack,name:E.name}}};t.fatal(_,e)}}},child:function(e){return this.createChild(e)}},I=()=>{},c=()=>{},C=()=>{},u={info:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i]},warn:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i]},error:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];console.error("[INIT] ".concat(e),...n)},debug:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i]}},R={environment:()=>{},rateLimiters:()=>{},telemetry:()=>{},aiService:()=>{},logger:()=>{}},g={startup:(e,t)=>{},config:(e,t,n)=>{},section:e=>{},progress:(e,t,n)=>{},error:(e,t)=>{},success:(e,t)=>{}};t.default=l},49354:function(e,t,n){n.d(t,{WH:function(){return _},cn:function(){return o},x0:function(){return r}});var i=n(44839),E=n(96164);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,E.m6)((0,i.W)(t))}function _(e){let t=0;for(let n=0;n<e.length;n++)t=26*t+e.charCodeAt(n)-64;return t-1}function r(){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="";for(let n=0;n<10;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}n(67217)}}]);