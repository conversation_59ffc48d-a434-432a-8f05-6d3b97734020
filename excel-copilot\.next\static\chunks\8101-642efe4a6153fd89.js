"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8101],{38711:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},24241:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},59061:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},58184:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},74697:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},46246:function(e,t,n){/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,i=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),o=r[0].inst,d=r[1];return l(function(){o.value=n,o.getSnapshot=t,s(o)&&d({inst:o})},[e,n,t]),i(function(){return s(o)&&d({inst:o}),e(function(){s(o)&&d({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},10554:function(e,t,n){e.exports=n(46246)},81464:function(e,t,n){n.d(t,{NY:function(){return j},Ee:function(){return D},fC:function(){return k}});var r=n(2265),o=n(98324),a=n(75137),i=n(1336),l=n(25171),u=n(10554);function s(){return()=>{}}var d=n(57437),c="Avatar",[f,p]=(0,o.b)(c),[v,g]=f(c),y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[a,i]=r.useState("idle");return(0,d.jsx)(v,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,d.jsx)(l.WV.span,{...o,ref:t})})});y.displayName=c;var h="AvatarImage",m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...f}=e,p=g(h,n),v=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,a=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),l=r.useRef(null),d=a?(l.current||(l.current=new window.Image),l.current):null,[c,f]=r.useState(()=>b(d,e));return(0,i.b)(()=>{f(b(d,e))},[d,e]),(0,i.b)(()=>{let e=e=>()=>{f(e)};if(!d)return;let t=e("loaded"),r=e("error");return d.addEventListener("load",t),d.addEventListener("error",r),n&&(d.referrerPolicy=n),"string"==typeof o&&(d.crossOrigin=o),()=>{d.removeEventListener("load",t),d.removeEventListener("error",r)}},[d,o,n]),c}(o,f),y=(0,a.W)(e=>{c(e),p.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==v&&y(v)},[v,y]),"loaded"===v?(0,d.jsx)(l.WV.img,{...f,ref:t,src:o}):null});m.displayName=h;var x="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...a}=e,i=g(x,n),[u,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==i.imageLoadingStatus?(0,d.jsx)(l.WV.span,{...a,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=x;var k=y,D=m,j=w},13304:function(e,t,n){n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return Q},h_:function(){return ee},jm:function(){return Y},p8:function(){return b},x8:function(){return ea},xz:function(){return $}});var r=n(2265),o=n(78149),a=n(1584),i=n(98324),l=n(53201),u=n(91715),s=n(53938),d=n(80467),c=n(7715),f=n(31383),p=n(25171),v=n(20589),g=n(49418),y=n(78369),h=n(71538),m=n(57437),x="Dialog",[w,b]=(0,i.b)(x),[k,D]=w(x),j=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:s=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:x});return(0,m.jsx)(k,{scope:t,triggerRef:d,contentRef:c,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};j.displayName=x;var R="DialogTrigger",E=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=D(R,n),l=(0,a.e)(t,i.triggerRef);return(0,m.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":U(i.open),...r,ref:l,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});E.displayName=R;var C="DialogPortal",[I,S]=w(C,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,i=D(C,t);return(0,m.jsx)(I,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,m.jsx)(f.z,{present:n||i.open,children:(0,m.jsx)(c.h,{asChild:!0,container:a,children:e})}))})};M.displayName=C;var N="DialogOverlay",O=r.forwardRef((e,t)=>{let n=S(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=D(N,e.__scopeDialog);return a.modal?(0,m.jsx)(f.z,{present:r||a.open,children:(0,m.jsx)(V,{...o,ref:t})}):null});O.displayName=N;var _=(0,h.Z8)("DialogOverlay.RemoveScroll"),V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=D(N,n);return(0,m.jsx)(g.Z,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(p.WV.div,{"data-state":U(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),F="DialogContent",W=r.forwardRef((e,t)=>{let n=S(F,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=D(F,e.__scopeDialog);return(0,m.jsx)(f.z,{present:r||a.open,children:a.modal?(0,m.jsx)(Z,{...o,ref:t}):(0,m.jsx)(A,{...o,ref:t})})});W.displayName=F;var Z=r.forwardRef((e,t)=>{let n=D(F,e.__scopeDialog),i=r.useRef(null),l=(0,a.e)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,y.Ry)(e)},[]),(0,m.jsx)(P,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),A=r.forwardRef((e,t)=>{let n=D(F,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,m.jsx)(P,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),P=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,c=D(F,n),f=r.useRef(null),p=(0,a.e)(t,f);return(0,v.EW)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(d.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,m.jsx)(s.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":U(c.open),...u,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(G,{titleId:c.titleId}),(0,m.jsx)(J,{contentRef:f,descriptionId:c.descriptionId})]})]})}),L="DialogTitle",T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=D(L,n);return(0,m.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});T.displayName=L;var z="DialogDescription",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=D(z,n);return(0,m.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});B.displayName=z;var H="DialogClose",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=D(H,n);return(0,m.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})});function U(e){return e?"open":"closed"}q.displayName=H;var X="DialogTitleWarning",[Y,K]=(0,i.k)(X,{contentName:F,titleName:L,docsSlug:"dialog"}),G=e=>{let{titleId:t}=e,n=K(X),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(a)},[a,t,n]),null},Q=j,$=E,ee=M,et=O,en=W,er=T,eo=B,ea=q}}]);