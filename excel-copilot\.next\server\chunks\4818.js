"use strict";exports.id=4818,exports.ids=[4818],exports.modules={75367:(e,t,s)=>{s.d(t,{FN:()=>f,Mi:()=>x,VW:()=>u,_i:()=>l,gD:()=>p,lj:()=>v,sA:()=>m});var a=s(10326),r=s(10321),o=s(79360),d=s(94019),i=s(17577),n=s(51223);let u=r.zt,l=i.forwardRef(({className:e,...t},s)=>a.jsx(r.l_,{ref:s,className:(0,n.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));l.displayName=r.l_.displayName;let c=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=i.forwardRef(({className:e,variant:t,...s},o)=>a.jsx(r.fC,{ref:o,className:(0,n.cn)(c({variant:t}),e),...s}));f.displayName=r.fC.displayName;let p=i.forwardRef(({className:e,...t},s)=>a.jsx(r.aU,{ref:s,className:(0,n.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));p.displayName=r.aU.displayName;let m=i.forwardRef(({className:e,...t},s)=>a.jsx(r.x8,{ref:s,className:(0,n.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:a.jsx(d.Z,{className:"h-4 w-4"})}));m.displayName=r.x8.displayName;let x=i.forwardRef(({className:e,...t},s)=>a.jsx(r.Dx,{ref:s,className:(0,n.cn)("text-sm font-semibold",e),...t}));x.displayName=r.Dx.displayName;let v=i.forwardRef(({className:e,...t},s)=>a.jsx(r.dk,{ref:s,className:(0,n.cn)("text-sm opacity-90",e),...t}));v.displayName=r.dk.displayName},84818:(e,t,s)=>{s.r(t),s.d(t,{Toaster:()=>d});var a=s(10326),r=s(75367),o=s(56627);function d(){let{toasts:e}=(0,o.pm)();return(0,a.jsxs)(r.VW,{children:[e.map(function({id:e,title:t,description:s,action:o,...d}){return(0,a.jsxs)(r.FN,{...d,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&a.jsx(r.Mi,{children:t}),s&&a.jsx(r.lj,{children:s})]}),o,a.jsx(r.sA,{})]},e)}),a.jsx(r._i,{})]})}},56627:(e,t,s)=>{s.d(t,{V6:()=>p,pm:()=>m,s6:()=>f});var a=s(17577);let r={ADD_TOAST:"ADD_TOAST",UPDATE_TOAST:"UPDATE_TOAST",DISMISS_TOAST:"DISMISS_TOAST",REMOVE_TOAST:"REMOVE_TOAST"},o=0,d=new Map,i=e=>{if(d.has(e))return;let t=setTimeout(()=>{d.delete(e),c({type:r.REMOVE_TOAST,toastId:e})},1e6);d.set(e,t)},n=(e,t)=>{switch(t.type){case r.ADD_TOAST:return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case r.UPDATE_TOAST:return{...e,toasts:e.toasts.map(e=>e.id===t.toast?.id?{...e,...t.toast}:e)};case r.DISMISS_TOAST:{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case r.REMOVE_TOAST:if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};default:return e}},u=[],l={toasts:[]};function c(e){l=n(l,e),u.forEach(e=>{e(l)})}function f({...e}){let t=(o=(o+1)%Number.MAX_VALUE).toString(),s=()=>c({type:r.DISMISS_TOAST,toastId:t});return c({type:r.ADD_TOAST,toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:r.UPDATE_TOAST,toast:{...e,id:t}})}}function p(){let[e,t]=a.useState(l);return a.useEffect(()=>(u.push(t),()=>{let e=u.indexOf(t);e>-1&&u.splice(e,1)}),[e]),{toast:f,dismiss:e=>c({type:r.DISMISS_TOAST,toastId:void 0===e?"":e}),toasts:e.toasts}}let m=p}};