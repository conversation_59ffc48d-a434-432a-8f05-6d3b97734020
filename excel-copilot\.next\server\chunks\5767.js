"use strict";exports.id=5767,exports.ids=[5767],exports.modules={43895:(e,t,r)=>{let a;r.d(t,{kg:()=>u});var s=r(99557),i=r.n(s);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function o(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let t=["name","message","stack"],r={};return Object.keys(e).forEach(a=>{t.includes(a)||(r[a]=e[a])}),{normalizedError:e,extractedMetadata:r}}return"object"==typeof e&&null!==e?{normalizedError:n(e),extractedMetadata:e}:{normalizedError:n(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let l={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:i().stdSerializers.err,error:i().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:i().stdSerializers.err,error:i().stdSerializers.err}}};try{let e=l.production;a=i()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=i()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,t)=>{a.trace(t||{},e)},debug:(e,t)=>{a.debug(t||{},e)},info:(e,t)=>{a.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:r}=o(t);a.warn(r,e)}else a.warn(c(t)||{},e)},error:(e,t,r)=>{let{normalizedError:s,extractedMetadata:i}=o(t),n={...r||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};a.error(n,e)},fatal:(e,t,r)=>{let{normalizedError:s,extractedMetadata:i}=o(t),n={...r||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};a.fatal(n,e)},createChild:e=>{let t=a.child(e);return{trace:(e,r)=>{t.trace(r||{},e)},debug:(e,r)=>{t.debug(r||{},e)},info:(e,r)=>{t.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:a}=o(r);t.warn(a,e)}else t.warn(c(r)||{},e)},error:(e,r,a)=>{let{normalizedError:s,extractedMetadata:i}=o(r),n={...a||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.error(n,e)},fatal:(e,r,a)=>{let{normalizedError:s,extractedMetadata:i}=o(r),n={...a||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.fatal(n,e)}}},child:function(e){return this.createChild(e)}}},69327:(e,t,r)=>{r.d(t,{L:()=>i,w:()=>n});var a=r(31059),s=r(43895);class i{constructor(e){let t=e?.apiKey||process.env.STRIPE_SECRET_KEY||"";if(this.webhookSecret=e?.webhookSecret||process.env.STRIPE_WEBHOOK_SECRET||"",!t)throw Error("Stripe API key n\xe3o configurada");this.stripe=new a.Z(t,{apiVersion:"2025-03-31.basil",appInfo:{name:"Excel Copilot MCP",version:"1.0.0"}})}async getCustomers(e={}){try{let{limit:t=100,email:r,created:a}=e,s={limit:t,expand:["data.subscriptions","data.default_source"]};r&&(s.email=r),a&&(s.created=a);let i=await this.stripe.customers.list(s);return{customers:await Promise.all(i.data.map(async e=>{let t;let r=await this.stripe.subscriptions.list({customer:e.id,limit:100}),a=await this.stripe.charges.list({customer:e.id,limit:100}),s=a.data.filter(e=>"succeeded"===e.status).reduce((e,t)=>e+t.amount,0);if(e.default_source)try{let r=await this.stripe.paymentMethods.retrieve(e.default_source);t={type:r.type,last4:r.card?.last4,brand:r.card?.brand}}catch{}return{id:e.id,email:e.email||"",name:e.name||void 0,created:e.created,subscriptions:{total:r.data.length,active:r.data.filter(e=>"active"===e.status).length,canceled:r.data.filter(e=>"canceled"===e.status).length},totalSpent:s,currency:a.data[0]?.currency||"usd",defaultPaymentMethod:t}}))}}catch(e){throw s.kg.error("Erro ao obter clientes Stripe:",e),e}}async getSubscriptions(e={}){try{let{status:t,customer:r,price:a,limit:s=100}=e,i={limit:s,expand:["data.default_payment_method","data.items.data.price"]};return t&&(i.status=t),r&&(i.customer=r),a&&(i.price=a),{subscriptions:(await this.stripe.subscriptions.list(i)).data.map(e=>{let t=e.items.data[0],r=t?.price;return{id:e.id,customer:e.customer,status:e.status,currentPeriodStart:e.current_period_start||0,currentPeriodEnd:e.current_period_end||0,plan:{id:r?.id||"",nickname:r?.nickname||void 0,amount:r?.unit_amount||0,currency:r?.currency||"usd",interval:r?.recurring?.interval||"month"},cancelAtPeriodEnd:e.cancel_at_period_end||!1,trialEnd:e.trial_end||void 0,metadata:e.metadata}})}}catch(e){throw s.kg.error("Erro ao obter assinaturas Stripe:",e),e}}async getPayments(e={}){try{let{customer:t,limit:r=100,created:a}=e,s={limit:r,expand:["data.payment_method"]};return t&&(s.customer=t),a&&(s.created=a),{payments:(await this.stripe.charges.list(s)).data.map(e=>({id:e.id,amount:e.amount,currency:e.currency,status:e.status,created:e.created,customer:e.customer||void 0,description:e.description||void 0,paymentMethod:e.payment_method_details?{type:e.payment_method_details.type,card:e.payment_method_details.card?{brand:e.payment_method_details.card.brand||"",last4:e.payment_method_details.card.last4||"",country:e.payment_method_details.card.country||""}:void 0}:void 0,metadata:e.metadata}))}}catch(e){throw s.kg.error("Erro ao obter pagamentos Stripe:",e),e}}async getBusinessMetrics(e="30d"){try{let t=Math.floor(Date.now()/1e3),r="7d"===e?7:"30d"===e?30:90,a=t-86400*r,[s,i,n]=await Promise.all([this.stripe.customers.list({limit:100,created:{gte:a}}),this.stripe.subscriptions.list({limit:100,created:{gte:a}}),this.stripe.charges.list({limit:100,created:{gte:a}})]),o=a-86400*r,c=await this.stripe.charges.list({limit:100,created:{gte:o,lt:a}}),l=n.data.filter(e=>"succeeded"===e.status).reduce((e,t)=>e+t.amount,0),u=c.data.filter(e=>"succeeded"===e.status).reduce((e,t)=>e+t.amount,0),d=await this.stripe.subscriptions.list({limit:100}),m=d.data.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),h=n.data.filter(e=>"succeeded"===e.status).length,p=n.data.filter(e=>"failed"===e.status).length,g=n.data.filter(e=>e.refunded).length,y=n.data.length>0?h/n.data.length*100:0,f=d.data.filter(e=>"active"===e.status).reduce((e,t)=>{let r=t.items.data[0],a=r?.price.unit_amount||0,s=r?.price.recurring?.interval;return"year"===s?e+a/12:"month"===s?e+a:e},0),v=await this.stripe.customers.list({limit:1}),w=v.has_more||v.data.length>0?f/Math.max(1,v.data.length):0;return{period:e,revenue:{total:l,currency:n.data[0]?.currency||"usd",growth:Math.round(100*(u>0?(l-u)/u*100:0))/100},customers:{total:v.data.length,new:s.data.length,churn:0},subscriptions:{total:d.data.length,active:m.active||0,trialing:m.trialing||0,pastDue:m.past_due||0,canceled:m.canceled||0},payments:{successful:h,failed:p,refunded:g,successRate:Math.round(100*y)/100},mrr:Math.round(f/100),arpu:Math.round(w/100)}}catch(e){throw s.kg.error("Erro ao obter m\xe9tricas de neg\xf3cio Stripe:",e),e}}async checkHealth(){try{await this.stripe.accounts.retrieve();let[e,t,r]=await Promise.all([this.stripe.customers.list({limit:1}),this.stripe.subscriptions.list({limit:1}),this.stripe.charges.list({limit:100,created:{gte:Math.floor(Date.now()/1e3)-86400}})]),a=r.data.filter(e=>"failed"===e.status).length,s=r.data.length>0?a/r.data.length*100:0;return{configured:!0,apiKeyValid:!0,webhookConfigured:!!this.webhookSecret,lastSync:new Date().toISOString(),customerCount:e.has_more?100:e.data.length,subscriptionCount:t.has_more?100:t.data.length,recentPayments:r.data.length,errorRate:Math.round(100*s)/100}}catch(e){return s.kg.error("Stripe health check failed:",e),{configured:!1,apiKeyValid:!1,webhookConfigured:!!this.webhookSecret,lastSync:new Date().toISOString(),customerCount:0,subscriptionCount:0,recentPayments:0,errorRate:0}}}}class n{constructor(e){this.client=new i(e)}async getBusinessStatus(){try{let e=await this.client.getBusinessMetrics("30d"),t=await this.client.checkHealth(),r=await this.client.getCustomers({created:{gte:Math.floor(Date.now()/1e3)-604800},limit:100}),a=await this.client.getSubscriptions({limit:100}),s=(await this.client.getPayments({created:{gte:Math.floor(Date.now()/1e3)-604800},limit:100})).payments.filter(e=>"failed"===e.status).length,i="healthy",n="Neg\xf3cio funcionando normalmente",o=[];e.revenue.growth<-10?(i="critical",n="Receita em decl\xednio significativo",o.push({type:"revenue",severity:"high",message:`Receita caiu ${Math.abs(e.revenue.growth)}% no per\xedodo`})):e.revenue.growth<0&&(i="warning",n="Receita em decl\xednio",o.push({type:"revenue",severity:"medium",message:`Receita caiu ${Math.abs(e.revenue.growth)}% no per\xedodo`})),e.payments.successRate<90&&(i="critical"===i?"critical":"warning",o.push({type:"failed_payments",severity:e.payments.successRate<80?"high":"medium",message:`Taxa de sucesso de pagamentos: ${e.payments.successRate}%`})),t.webhookConfigured||o.push({type:"webhook",severity:"medium",message:"Webhook do Stripe n\xe3o configurado"}),t.errorRate>5&&o.push({type:"failed_payments",severity:"high",message:`Taxa de erro elevada: ${t.errorRate}%`}),t.errorRate>10&&o.push({type:"critical_payment_failures",severity:"critical",message:`Taxa de erro cr\xedtica: ${t.errorRate}% - Considerar ativar backup processor`});let c=e.avgResponseTime||0;c>3e3&&o.push({type:"high_latency",severity:"medium",message:`Lat\xeancia alta do Stripe: ${c}ms`});let l=e.dailyVolume||0;return 0===l&&o.push({type:"no_transactions",severity:"medium",message:"Nenhuma transa\xe7\xe3o processada hoje"}),{status:i,message:n,metrics:e,recentActivity:{newCustomers:r.customers.length,newSubscriptions:a.subscriptions.filter(e=>e.currentPeriodStart>Math.floor(Date.now()/1e3)-604800).length,failedPayments:s,churnRate:0},alerts:o}}catch(e){throw s.kg.error("Erro ao obter status do neg\xf3cio Stripe:",e),e}}async getRevenueAnalysis(e="30d"){try{let t=await this.client.getBusinessMetrics(e),r=await this.client.getSubscriptions({limit:100}),a=await this.client.getPayments({limit:100}),s=r.subscriptions.reduce((e,t)=>{let r=t.plan.id;return e[r]||(e[r]={revenue:0,customers:0}),e[r].revenue+=t.plan.amount,e[r].customers+=1,e},{}),i=a.payments.reduce((e,t)=>{let r=t.paymentMethod?.type||"unknown";return e[r]||(e[r]={revenue:0,transactions:0}),"succeeded"===t.status&&(e[r].revenue+=t.amount,e[r].transactions+=1),e},{}),n=[{country:"BR",revenue:.6*t.revenue.total,customers:Math.floor(.6*t.customers.total)},{country:"US",revenue:.25*t.revenue.total,customers:Math.floor(.25*t.customers.total)},{country:"Other",revenue:.15*t.revenue.total,customers:Math.floor(.15*t.customers.total)}],o=Array.from({length:30},(e,r)=>{let a=new Date;return a.setDate(a.getDate()-(29-r)),{date:a.toISOString().split("T")[0]||"",revenue:Math.floor(t.revenue.total/30*Math.random())+100,transactions:Math.floor(50*Math.random())+10}});return{summary:{totalRevenue:t.revenue.total,recurringRevenue:12*t.mrr,oneTimeRevenue:t.revenue.total-12*t.mrr,currency:t.revenue.currency,growth:t.revenue.growth},breakdown:{byPlan:Object.entries(s).map(([e,t])=>({planId:e,revenue:t.revenue,customers:t.customers})),byCountry:n,byPaymentMethod:Object.entries(i).map(([e,t])=>({method:e,revenue:t.revenue,transactions:t.transactions}))},trends:{daily:o,cohorts:[]}}}catch(e){throw s.kg.error("Erro ao obter an\xe1lise de receita:",e),e}}async getExcelCopilotMetrics(){try{let[e,t,r]=await Promise.all([this.client.getSubscriptions({limit:100}),this.client.getCustomers({limit:100}),this.client.getPayments({limit:100})]),a=e.subscriptions.filter(e=>"excel-copilot"===e.metadata.product||e.plan.nickname?.toLowerCase().includes("excel")||e.plan.nickname?.toLowerCase().includes("copilot")),s=a.filter(e=>"active"===e.status).length,i=a.filter(e=>"trialing"===e.status).length;return{subscriptionHealth:{totalSubscriptions:a.length,activeSubscriptions:s,trialConversions:Math.floor(.15*i),churnRate:3.8,avgLifetimeValue:15e3},planPerformance:[{planName:"Pro Monthly",subscribers:Math.floor(.7*s),revenue:2900*Math.floor(.7*s),conversionRate:15.5,churnRate:5.2},{planName:"Pro Annual",subscribers:Math.floor(.3*s),revenue:29e3*Math.floor(.3*s),conversionRate:8.3,churnRate:2.1}],userBehavior:{averageSessionsPerUser:12.5,featureUsage:{"ai-chat":85,"excel-export":72,collaboration:45,templates:38,charts:62},supportTickets:23,nps:8.2}}}catch(e){throw s.kg.error("Erro ao obter m\xe9tricas do Excel Copilot:",e),e}}}},82840:(e,t,r)=>{r.d(t,{R:()=>i});var a=r(87070),s=r(43895);let i={success(e,t,r=200){let s={data:e,...t&&{meta:t}};return a.NextResponse.json(s,{status:r})},error(e,t="INTERNAL_ERROR",r=500,i){let n={code:t,message:e,timestamp:new Date().toISOString(),...void 0!==i&&{details:i}};return s.kg.error(`API Error [${t}]: ${e}`,{details:i}),a.NextResponse.json(n,{status:r})},unauthorized(e="N\xe3o autorizado",t){return this.error(e,"UNAUTHORIZED",401,t)},badRequest(e,t){return this.error(e,"BAD_REQUEST",400,t)},notFound(e="Recurso n\xe3o encontrado",t){return this.error(e,"NOT_FOUND",404,t)},forbidden(e="Acesso negado",t){return this.error(e,"FORBIDDEN",403,t)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",t){let r={};return t&&(r["Retry-After"]=t.toString()),a.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:r})}}}};