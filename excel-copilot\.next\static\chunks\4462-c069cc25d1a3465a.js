"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4462],{22468:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},16463:function(e,t,n){var r=n(71169);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},53938:function(e,t,n){n.d(t,{I0:function(){return g},XB:function(){return d},fC:function(){return v}});var r,i=n(2265),o=n(78149),l=n(25171),a=n(1584),u=n(75137),s=n(57437),f="dismissableLayer.update",c=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,E=i.useContext(c),[R,A]=i.useState(null),L=null!==(d=null==R?void 0:R.ownerDocument)&&void 0!==d?d:null===(n=globalThis)||void 0===n?void 0:n.document,[,P]=i.useState({}),C=(0,a.e)(t,e=>A(e)),O=Array.from(E.layers),[S]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=O.indexOf(S),D=R?O.indexOf(R):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,W=D>=T,H=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.W)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!W||n||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},L),F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.W)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},L);return!function(e,t=globalThis?.document){let n=(0,u.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D!==E.layers.size-1||(null==v||v(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},L),i.useEffect(()=>{if(R)return p&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=L.body.style.pointerEvents,L.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),h(),()=>{p&&1===E.layersWithOutsidePointerEventsDisabled.size&&(L.body.style.pointerEvents=r)}},[R,L,p,E]),i.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),h())},[R,E]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,s.jsx)(l.WV.div,{...b,ref:C,style:{pointerEvents:k?W?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,F.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,F.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,H.onPointerDownCapture)})});d.displayName="DismissableLayer";var p=i.forwardRef((e,t)=>{let n=i.useContext(c),r=i.useRef(null),o=(0,a.e)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.WV.div,{...e,ref:o})});function h(){let e=new CustomEvent(f);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,l.jH)(o,a):o.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var v=d,g=p},25510:function(e,t,n){n.d(t,{ee:function(){return eJ},Eh:function(){return eQ},VY:function(){return eK},fC:function(){return eG},D7:function(){return eW}});var r=n(2265);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,u=Math.floor,s=e=>({x:e,y:e}),f={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>c[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>f[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,l=g(t),a=m(g(t)),u=v(a),s=p(t),f="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[u]/2-o[u]/2;switch(s){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=y*(n&&f?-1:1);break;case"end":r[a]+=y*(n&&f?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:c}=E(s,r,u),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:f,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});f=null!=v?v:f,c=null!=g?g:c,p={...p,[o]:{...p[o],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:f,y:c}=E(s,d,u)),n=-1)}return{x:f,y:c,placement:d,strategy:i,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),v=a[p?"floating"===c?"reference":"floating":c],g=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:f,strategy:u})),y="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),E=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-R.top+m.top)/E.y,bottom:(R.bottom-g.bottom+m.bottom)/E.y,left:(g.left-R.left+m.left)/E.x,right:(R.right-g.right+m.right)/E.x}}function L(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return i.some(t=>e[t]>=0)}async function C(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),u="y"===g(n),s=["left","top"].includes(l)?-1:1,f=o&&u?-1:1,c=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),u?{x:v*f,y:m*s}:{x:m*s,y:v*f}}function O(){return"undefined"!=typeof window}function S(e){return k(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(k(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function k(e){return!!O()&&(e instanceof Node||e instanceof T(e).Node)}function W(e){return!!O()&&(e instanceof Element||e instanceof T(e).Element)}function H(e){return!!O()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function F(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function j(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=N(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function B(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function M(e){let t=z(),n=W(e)?N(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(S(e))}function N(e){return T(e).getComputedStyle(e)}function _(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function I(e){if("html"===S(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||D(e);return F(t)?t.host:t}function Y(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=I(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&j(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=T(i);if(o){let e=X(l);return t.concat(l,l.visualViewport||[],j(i)?i:[],e&&n?Y(e):[])}return t.concat(i,Y(i,[],n))}function X(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function $(e){let t=N(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=H(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,u=a(n)!==o||a(r)!==l;return u&&(n=o,r=l),{width:n,height:r,$:u}}function q(e){return W(e)?e:e.contextElement}function Z(e){let t=q(e);if(!H(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=$(t),l=(o?a(n.width):n.width)/r,u=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let G=s(0);function J(e){let t=T(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:G}function K(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=q(e),a=s(1);t&&(r?W(r)&&(a=Z(r)):a=Z(e));let u=(void 0===(i=n)&&(i=!1),r&&(!i||r===T(l))&&i)?J(l):s(0),f=(o.left+u.x)/a.x,c=(o.top+u.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=T(l),t=r&&W(r)?T(r):r,n=e,i=X(n);for(;i&&r&&t!==n;){let e=Z(i),t=i.getBoundingClientRect(),r=N(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;f*=e.x,c*=e.y,d*=e.x,p*=e.y,f+=o,c+=l,i=X(n=T(i))}}return b({width:d,height:p,x:f,y:c})}function Q(e,t){let n=_(e).scrollLeft;return t?t.left+n:K(D(e)).left+n}function U(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=D(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,u=0;if(i){o=i.width,l=i.height;let e=z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=_(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(e),u=-n.scrollTop;return"rtl"===N(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:u}}(D(e));else if(W(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=H(e)?Z(e):s(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=J(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===N(e).position}function en(e,t){if(!H(e)||"fixed"===N(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=T(e);if(B(e))return n;if(!H(e)){let t=I(e);for(;t&&!V(t);){if(W(t)&&!et(t))return t;t=I(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(S(r))&&et(r);)r=en(r,t);return r&&V(r)&&et(r)&&!M(r)?n:r||function(e){let t=I(e);for(;H(t)&&!V(t);){if(M(t))return t;if(B(t))break;t=I(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),i=D(t),o="fixed"===n,l=K(e,!0,o,t),a={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!o){if(("body"!==S(t)||j(i))&&(a=_(t)),r){let e=K(t,!0,o,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else i&&(u.x=Q(i))}let f=!i||r||o?s(0):U(i,a);return{x:l.left+a.scrollLeft-u.x-f.x,y:l.top+a.scrollTop-u.y-f.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=D(r),a=!!t&&B(t.floating);if(r===l||a&&o)return n;let u={scrollLeft:0,scrollTop:0},f=s(1),c=s(0),d=H(r);if((d||!d&&!o)&&(("body"!==S(r)||j(l))&&(u=_(r)),H(r))){let e=K(r);f=Z(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let p=!l||d||o?s(0):U(l,u,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-u.scrollLeft*f.x+c.x+p.x,y:n.y*f.y-u.scrollTop*f.y+c.y+p.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?B(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=Y(e,[],!1).filter(e=>W(e)&&"body"!==S(e)),i=null,o="fixed"===N(e).position,l=o?I(e):e;for(;W(l)&&!V(l);){let t=N(l),n=M(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||j(l)&&!n&&function e(t,n){let r=I(t);return!(r===n||!W(r)||V(r))&&("fixed"===N(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=I(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],s=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=$(e);return{width:t,height:n}},getScale:Z,isElement:W,isRTL:function(e){return"rtl"===N(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:u,elements:s,middlewareData:f}=t,{element:c,padding:p=0}=d(e,t)||{};if(null==c)return{};let y=x(p),w={x:n,y:r},b=m(g(i)),E=v(b),R=await u.getDimensions(c),A="y"===b,L=A?"clientHeight":"clientWidth",P=a.reference[E]+a.reference[b]-w[b]-a.floating[E],C=w[b]-a.reference[b],O=await (null==u.getOffsetParent?void 0:u.getOffsetParent(c)),S=O?O[L]:0;S&&await (null==u.isElement?void 0:u.isElement(O))||(S=s.floating[L]||a.floating[E]);let T=S/2-R[E]/2-1,D=o(y[A?"top":"left"],T),k=o(y[A?"bottom":"right"],T),W=S-R[E]-k,H=S/2-R[E]/2+(P/2-C/2),F=l(D,o(H,W)),j=!f.arrow&&null!=h(i)&&H!==F&&a.reference[E]/2-(H<D?D:k)-R[E]/2<0,B=j?H<D?H-D:H-W:0;return{[b]:w[b]+B,data:{[b]:F,centerOffset:H-F-B,...j&&{alignmentOffset:B}},reset:j}}}),eu=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var es=n(54887),ef="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ec(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ec(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ec(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ef(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:i,y:o,placement:l,middlewareData:a}=e,u=await C(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}),options:[e,t]}},eg=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:i}=e,{mainAxis:a=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=d(n,e),c={x:t,y:r},h=await A(e,f),v=g(p(i)),y=m(v),w=c[y],x=c[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,o(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=s.fn({...e,[y]:w,[v]:x});return{...b,data:{x:b.x-t,y:b.y-r,enabled:{[y]:a,[v]:u}}}}}),options:[e,t]}},ey=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=d(n,e),f={x:t,y:r},c=g(i),h=m(c),v=f[h],y=f[c],w=d(a,e),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var b,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:x.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[c])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[c]:y}}}),options:[e,t]}},ew=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,i,o,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:f,platform:c,elements:x}=e,{mainAxis:b=!0,crossAxis:E=!0,fallbackPlacements:R,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:C=!0,...O}=d(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let S=p(a),T=g(f),D=p(f)===f,k=await (null==c.isRTL?void 0:c.isRTL(x.floating)),W=R||(D||!C?[w(f)]:function(e){let t=w(e);return[y(e),t,y(t)]}(f)),H="none"!==P;!R&&H&&W.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(y)))),o}(f,C,P,k));let F=[f,...W],j=await A(e,O),B=[],M=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&B.push(j[S]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(g(e)),o=v(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=w(l)),[l,w(l)]}(a,s,k);B.push(j[e[0]],j[e[1]])}if(M=[...M,{placement:a,overflows:B}],!B.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=F[e];if(t)return{data:{index:e,overflows:M},reset:{placement:t}};let n=null==(o=M.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(L){case"bestFit":{let e=null==(l=M.filter(e=>{if(H){let t=g(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=f}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},ex=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let i,a;let{placement:u,rects:s,platform:f,elements:c}=e,{apply:m=()=>{},...v}=d(n,e),y=await A(e,v),w=p(u),x=h(u),b="y"===g(u),{width:E,height:R}=s.floating;"top"===w||"bottom"===w?(i=w,a=x===(await (null==f.isRTL?void 0:f.isRTL(c.floating))?"start":"end")?"left":"right"):(a=w,i="end"===x?"top":"bottom");let L=R-y.top-y.bottom,P=E-y.left-y.right,C=o(R-y[i],L),O=o(E-y[a],P),S=!e.middlewareData.shift,T=C,D=O;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(D=P),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(T=L),S&&!x){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?D=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):T=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await m({...e,availableWidth:D,availableHeight:T});let k=await f.getDimensions(c.floating);return E!==k.width||R!==k.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},eb=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...i}=d(n,e);switch(r){case"referenceHidden":{let n=L(await A(e,{...i,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:P(n)}}}case"escaped":{let n=L(await A(e,{...i,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:P(n)}}}default:return{}}}}),options:[e,t]}},eE=(e,t)=>({...em(e),options:[e,t]});var eR=n(25171),eA=n(57437),eL=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eA.jsx)(eR.WV.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eL.displayName="Arrow";var eP=n(1584),eC=n(98324),eO=n(75137),eS=n(1336),eT=n(75238),eD="Popper",[ek,eW]=(0,eC.b)(eD),[eH,eF]=ek(eD),ej=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eA.jsx)(eH,{scope:t,anchor:i,onAnchorChange:o,children:n})};ej.displayName=eD;var eB="PopperAnchor",eM=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eF(eB,n),a=r.useRef(null),u=(0,eP.e)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eA.jsx)(eR.WV.div,{...o,ref:u})});eM.displayName=eB;var ez="PopperContent",[eV,eN]=ek(ez),e_=r.forwardRef((e,t)=>{var n,i,a,s,f,c,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:L="optimized",onPlaced:P,...C}=e,O=eF(ez,h),[S,T]=r.useState(null),k=(0,eP.e)(t,e=>T(e)),[W,H]=r.useState(null),F=(0,eT.t)(W),j=null!==(d=null==F?void 0:F.width)&&void 0!==d?d:0,B=null!==(p=null==F?void 0:F.height)&&void 0!==p?p:0,M="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},z=Array.isArray(b)?b:[b],V=z.length>0,N={padding:M,boundary:z.filter(e$),altBoundary:V},{refs:_,floatingStyles:I,placement:X,isPositioned:$,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:s,open:f}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);ec(p,i)||h(i);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),b=l||m,E=a||g,R=r.useRef(null),A=r.useRef(null),L=r.useRef(c),P=null!=s,C=eh(s),O=eh(o),S=eh(f),T=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};O.current&&(e.platform=O.current),eu(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==S.current};D.current&&!ec(L.current,t)&&(L.current=t,es.flushSync(()=>{d(t)}))})},[p,t,n,O,S]);ef(()=>{!1===f&&L.current.isPositioned&&(L.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[f]);let D=r.useRef(!1);ef(()=>(D.current=!0,()=>{D.current=!1}),[]),ef(()=>{if(b&&(R.current=b),E&&(A.current=E),b&&E){if(C.current)return C.current(b,E,T);T()}},[b,E,T,C,P]);let k=r.useMemo(()=>({reference:R,floating:A,setReference:w,setFloating:x}),[w,x]),W=r.useMemo(()=>({reference:b,floating:E}),[b,E]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!W.floating)return e;let t=ep(W.floating,c.x),r=ep(W.floating,c.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(W.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,W.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:T,refs:k,elements:W,floatingStyles:H}),[c,T,k,W,H])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(e),h=a||s?[...p?Y(p):[],...Y(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=p&&c?function(e,t){let n,r=null,i=D(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(f,c){void 0===f&&(f=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=d;if(f||t(),!m||!v)return;let g=u(h),y=u(i.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-u(i.clientHeight-(h+v))+"px "+-u(p)+"px",threshold:l(0,o(1,c))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;f&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?K(e):null;return d&&function t(){let r=K(e);y&&!el(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===L})},elements:{reference:O.anchor},middleware:[ev({mainAxis:v+B,alignmentAxis:y}),x&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ey():void 0,...N}),x&&ew({...N}),ex({...N,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),W&&eE({element:W,padding:w}),eq({arrowWidth:j,arrowHeight:B}),A&&eb({strategy:"referenceHidden",...N})]}),[G,J]=eZ(X),Q=(0,eO.W)(P);(0,eS.b)(()=>{$&&(null==Q||Q())},[$,Q]);let U=null===(n=Z.arrow)||void 0===n?void 0:n.x,ee=null===(i=Z.arrow)||void 0===i?void 0:i.y,et=(null===(a=Z.arrow)||void 0===a?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eS.b)(()=>{S&&er(window.getComputedStyle(S).zIndex)},[S]),(0,eA.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:$?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(s=Z.transformOrigin)||void 0===s?void 0:s.x,null===(f=Z.transformOrigin)||void 0===f?void 0:f.y].join(" "),...(null===(c=Z.hide)||void 0===c?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eV,{scope:h,placedSide:G,onArrowChange:H,arrowX:U,arrowY:ee,shouldHideArrow:et,children:(0,eA.jsx)(eR.WV.div,{"data-side":G,"data-align":J,...C,ref:k,style:{...C.style,animation:$?void 0:"none"}})})})});e_.displayName=ez;var eI="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eX=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eN(eI,n),o=eY[i.placedSide];return(0,eA.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eL,{...r,ref:t,style:{...r.style,display:"block"}})})});function e$(e){return null!==e}eX.displayName=eI;var eq=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:u,middlewareData:s}=t,f=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=f?0:e.arrowWidth,d=f?0:e.arrowHeight,[p,h]=eZ(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!==(o=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+c/2,g=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,y="",w="";return"bottom"===p?(y=f?m:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=f?m:"".concat(v,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=f?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=f?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function eZ(e){let[t,n="center"]=e.split("-");return[t,n]}var eG=ej,eJ=eM,eK=e_,eQ=eX},7715:function(e,t,n){n.d(t,{h:function(){return u}});var r=n(2265),i=n(54887),o=n(25171),l=n(1336),a=n(57437),u=r.forwardRef((e,t)=>{var n,u;let{container:s,...f}=e,[c,d]=r.useState(!1);(0,l.b)(()=>d(!0),[]);let p=s||c&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return p?i.createPortal((0,a.jsx)(o.WV.div,{...f,ref:t}),p):null});u.displayName="Portal"},75238:function(e,t,n){n.d(t,{t:function(){return o}});var r=n(2265),i=n(1336);function o(e){let[t,n]=r.useState(void 0);return(0,i.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);