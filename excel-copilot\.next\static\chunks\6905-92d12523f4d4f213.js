(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6905],{69037:function(e,a,t){"use strict";e.exports=t.p+"static/media/excel-operations.worker.59615136.ts"},76467:function(){},87992:function(e,a,t){"use strict";t.d(a,{Cd:function(){return c},X:function(){return d},bZ:function(){return i}});var r=t(57437),s=t(13027),o=t(2265),n=t(49354);let l=(0,s.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-500/50 text-yellow-600 dark:border-yellow-500/30 dark:text-yellow-500 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-500"}},defaultVariants:{variant:"default"}}),i=o.forwardRef((e,a)=>{let{className:t,variant:s,...o}=e;return(0,r.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:s}),t),...o})});i.displayName="Alert";let c=o.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...s})});c.displayName="AlertTitle";let d=o.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...s})});d.displayName="AlertDescription"},57226:function(e,a,t){"use strict";t.d(a,{F$:function(){return i},Q5:function(){return c},qE:function(){return l}});var r=t(57437),s=t(81464),o=t(2265),n=t(49354);let l=o.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.fC,{ref:a,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...o})});l.displayName=s.fC.displayName;let i=o.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.Ee,{ref:a,className:(0,n.cn)("aspect-square h-full w-full",t),...o})});i.displayName=s.Ee.displayName;let c=o.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.NY,{ref:a,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...o})});c.displayName=s.NY.displayName},54662:function(e,a,t){"use strict";t.d(a,{$N:function(){return f},Be:function(){return g},Vq:function(){return i},cN:function(){return h},cZ:function(){return m},fK:function(){return p},hg:function(){return c}});var r=t(57437),s=t(13304),o=t(74697),n=t(2265),l=t(49354);let i=s.fC,c=s.xz,d=s.h_;s.x8;let u=n.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.aV,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...o})});u.displayName=s.aV.displayName;let m=n.forwardRef((e,a)=>{let{className:t,children:n,...i}=e;return(0,r.jsxs)(d,{children:[(0,r.jsx)(u,{}),(0,r.jsxs)(s.VY,{ref:a,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...i,children:[n,(0,r.jsxs)(s.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(o.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=s.VY.displayName;let p=e=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};p.displayName="DialogHeader";let h=e=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};h.displayName="DialogFooter";let f=n.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.Dx,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",t),...o})});f.displayName=s.Dx.displayName;let g=n.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.dk,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",t),...o})});g.displayName=s.dk.displayName},31590:function(e,a,t){"use strict";t.d(a,{$F:function(){return u},AW:function(){return p},Ju:function(){return f},Qk:function(){return m},VD:function(){return g},Xi:function(){return h},h_:function(){return d}});var r=t(57437),s=t(81622),o=t(87592),n=t(22468),l=t(28165),i=t(2265),c=t(49354);let d=s.fC,u=s.xz,m=s.ZA;s.Uv,s.Tr,s.Ee,i.forwardRef((e,a)=>{let{className:t,inset:n,children:l,...i}=e;return(0,r.jsxs)(s.fF,{ref:a,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",t),...i,children:[l,(0,r.jsx)(o.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=s.fF.displayName,i.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.tu,{ref:a,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})}).displayName=s.tu.displayName;let p=i.forwardRef((e,a)=>{let{className:t,sideOffset:o=4,...n}=e;return(0,r.jsx)(s.Uv,{children:(0,r.jsx)(s.VY,{ref:a,sideOffset:o,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});p.displayName=s.VY.displayName;let h=i.forwardRef((e,a)=>{let{className:t,inset:o,...n}=e;return(0,r.jsx)(s.ck,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",o&&"pl-8",t),...n})});h.displayName=s.ck.displayName,i.forwardRef((e,a)=>{let{className:t,children:o,checked:l,...i}=e;return(0,r.jsxs)(s.oC,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:null!=l&&l,...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.wU,{children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})}),o]})}).displayName=s.oC.displayName,i.forwardRef((e,a)=>{let{className:t,children:o,...n}=e;return(0,r.jsxs)(s.Rk,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.wU,{children:(0,r.jsx)(l.Z,{className:"h-2 w-2 fill-current"})})}),o]})}).displayName=s.Rk.displayName;let f=i.forwardRef((e,a)=>{let{className:t,inset:o,...n}=e;return(0,r.jsx)(s.__,{ref:a,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",t),...n})});f.displayName=s.__.displayName;let g=i.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...o})});g.displayName=s.Z0.displayName},70402:function(e,a,t){"use strict";t.d(a,{_:function(){return c}});var r=t(57437),s=t(38364),o=t(13027),n=t(2265),l=t(49354);let i=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.f,{ref:a,className:(0,l.cn)(i(),t),...o})});c.displayName=s.f.displayName},21413:function(e,a,t){"use strict";t.d(a,{J2:function(){return l},xo:function(){return i},yk:function(){return c}});var r=t(57437),s=t(61485),o=t(2265),n=t(49354);let l=s.fC,i=s.xz,c=o.forwardRef((e,a)=>{let{className:t,align:o="center",sideOffset:l=4,...i}=e;return(0,r.jsx)(s.h_,{children:(0,r.jsx)(s.VY,{ref:a,align:o,sideOffset:l,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i})})});c.displayName=s.VY.displayName},61617:function(e,a,t){"use strict";t.d(a,{E:function(){return l}});var r=t(57437),s=t(52431),o=t(2265),n=t(49354);let l=o.forwardRef((e,a)=>{let{className:t,value:o,...l}=e;return(0,r.jsx)(s.fC,{ref:a,className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...l,children:(0,r.jsx)(s.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(o||0),"%)")}})})});l.displayName=s.fC.displayName},80023:function(e,a,t){"use strict";t.d(a,{x:function(){return l}});var r=t(57437),s=t(26770),o=t(2265),n=t(49354);let l=o.forwardRef((e,a)=>{let{className:t,children:o,...l}=e;return(0,r.jsxs)(s.fC,{ref:a,className:(0,n.cn)("relative overflow-hidden",t),...l,children:[(0,r.jsx)(s.l_,{className:"h-full w-full rounded-[inherit]",children:o}),(0,r.jsx)(i,{}),(0,r.jsx)(s.Ns,{})]})});l.displayName=s.fC.displayName;let i=o.forwardRef((e,a)=>{let{className:t,orientation:o="vertical",...l}=e;return(0,r.jsx)(s.gb,{ref:a,orientation:o,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===o&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===o&&"h-2.5 border-t border-t-transparent p-[1px]",t),...l,children:(0,r.jsx)(s.q4,{className:"relative flex-1 rounded-full bg-border"})})});i.displayName=s.gb.displayName},2128:function(e,a,t){"use strict";t.d(a,{Bw:function(){return f},Ph:function(){return d},Ql:function(){return g},i4:function(){return m},ki:function(){return u}});var r=t(57437),s=t(17549),o=t(42421),n=t(14392),l=t(22468),i=t(2265),c=t(49354);let d=s.fC;s.ZA;let u=s.B4,m=i.forwardRef((e,a)=>{let{className:t,children:n,...l}=e;return(0,r.jsxs)(s.xz,{ref:a,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[n,(0,r.jsx)(s.JO,{asChild:!0,children:(0,r.jsx)(o.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=s.xz.displayName;let p=i.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.u_,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...o,children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})});p.displayName=s.u_.displayName;let h=i.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)(s.$G,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...n,children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})});h.displayName=s.$G.displayName;let f=i.forwardRef((e,a)=>{let{className:t,children:o,position:n="popper",...l}=e;return(0,r.jsx)(s.h_,{children:(0,r.jsxs)(s.VY,{ref:a,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(p,{}),(0,r.jsx)(s.l_,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:o}),(0,r.jsx)(h,{})]})})});f.displayName=s.VY.displayName,i.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.__,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...o})}).displayName=s.__.displayName;let g=i.forwardRef((e,a)=>{let{className:t,children:o,...n}=e;return(0,r.jsxs)(s.ck,{ref:a,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.wU,{children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(s.eT,{children:o})]})});g.displayName=s.ck.displayName,i.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...o})}).displayName=s.Z0.displayName},86864:function(e,a,t){"use strict";t.d(a,{SP:function(){return c},dr:function(){return i},mQ:function(){return l},nU:function(){return d}});var r=t(57437),s=t(62447),o=t(2265),n=t(49354);let l=s.fC,i=o.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.aV,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...o})});i.displayName=s.aV.displayName;let c=o.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.xz,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...o})});c.displayName=s.xz.displayName;let d=o.forwardRef((e,a)=>{let{className:t,...o}=e;return(0,r.jsx)(s.VY,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...o})});d.displayName=s.VY.displayName},89736:function(e,a,t){"use strict";t.d(a,{_v:function(){return d},aJ:function(){return c},pn:function(){return l},u:function(){return i}});var r=t(57437),s=t(27071),o=t(2265),n=t(49354);let l=s.zt,i=s.fC,c=s.xz,d=o.forwardRef((e,a)=>{let{className:t,sideOffset:o=4,...l}=e;return(0,r.jsx)(s.VY,{ref:a,sideOffset:o,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})});d.displayName=s.VY.displayName},62608:function(e,a,t){"use strict";t.d(a,{SpreadsheetEditor:function(){return a0}});var r,s,o=t(57437),n=t(18272),l=t(36356),i=t(71976),c=t(77424),d=t(33907),u=t(74697),m=t(39348),p=t(3751),h=t(74109),f=t(59061),g=t(70518),x=t(87592),v=t(74122),y=t(56127),b=t(47390),w=t(77515),N=t(16463),j=t(2265),A=t(27776),E=t(54817),C=t(71568),S=t(60994),k=t(89733),O=t(21413),R=t(89736),F=t(15589),T=t(49354),I=t(34567),D=t(404),L=t(18186),_=t(92222),z=t(45764),M=t(79055),V=t(80023);let Z=[{id:"soma",command:"/soma",description:"Soma valores em um intervalo de c\xe9lulas",example:"/soma A1:A10",category:"calculation",icon:(0,o.jsx)(I.Z,{className:"h-4 w-4 text-blue-500"})},{id:"media",command:"/media",description:"Calcula a m\xe9dia de um intervalo de c\xe9lulas",example:"/media B1:B10",category:"calculation",icon:(0,o.jsx)(I.Z,{className:"h-4 w-4 text-blue-500"})},{id:"maximo",command:"/maximo",description:"Encontra o valor m\xe1ximo em um intervalo",example:"/maximo C1:C20",category:"calculation",icon:(0,o.jsx)(I.Z,{className:"h-4 w-4 text-blue-500"})},{id:"grafico",command:"/grafico",description:"Cria um gr\xe1fico com os dados selecionados",example:'/grafico tipo="barras" dados=A1:B10',category:"visualization",icon:(0,o.jsx)(c.Z,{className:"h-4 w-4 text-green-500"})},{id:"pizza",command:"/grafico-pizza",description:"Cria um gr\xe1fico de pizza",example:'/grafico-pizza dados=C1:D10 titulo="Vendas por Regi\xe3o"',category:"visualization",icon:(0,o.jsx)(c.Z,{className:"h-4 w-4 text-green-500"})},{id:"formatar",command:"/formatar",description:"Formata c\xe9lulas selecionadas",example:'/formatar A1:C10 negrito cor="azul"',category:"formatting",icon:(0,o.jsx)(d.Z,{className:"h-4 w-4 text-purple-500"})},{id:"condicional",command:"/formato-condicional",description:"Aplica formata\xe7\xe3o condicional",example:'/formato-condicional A1:A10 maior=100 cor="verde"',category:"formatting",icon:(0,o.jsx)(d.Z,{className:"h-4 w-4 text-purple-500"})},{id:"filtrar",command:"/filtrar",description:"Filtra dados com base em crit\xe9rios",example:'/filtrar coluna="Vendas" valor>1000',category:"filter",icon:(0,o.jsx)(D.Z,{className:"h-4 w-4 text-amber-500"})},{id:"ordenar",command:"/ordenar",description:"Ordena dados de uma coluna",example:'/ordenar coluna="Data" crescente=true',category:"filter",icon:(0,o.jsx)(L.Z,{className:"h-4 w-4 text-amber-500"})},{id:"tabela",command:"/tabela",description:"Converte intervalo em tabela formatada",example:'/tabela A1:D10 nome="MinhaTabela"',category:"data",icon:(0,o.jsx)(_.Z,{className:"h-4 w-4 text-red-500"})},{id:"inserir",command:"/inserir",description:"Insere novas linhas ou colunas",example:"/inserir linhas=5 posicao=A10",category:"data",icon:(0,o.jsx)(l.Z,{className:"h-4 w-4 text-red-500"})}];function P(e){let{onSelect:a,onClose:t}=e,[r,s]=(0,j.useState)(""),[n,l]=(0,j.useState)(Z),[i,m]=(0,j.useState)(0),[p,h]=(0,j.useState)("all"),f=(0,j.useRef)(null),g=(0,j.useRef)(null);(0,j.useEffect)(()=>{let e=Z;"all"!==p&&(e=e.filter(e=>e.category===p)),r&&(e=e.filter(e=>e.command.toLowerCase().includes(r.toLowerCase())||e.description.toLowerCase().includes(r.toLowerCase()))),l(e),m(0)},[r,p]),(0,j.useEffect)(()=>{var e;null===(e=f.current)||void 0===e||e.focus();let a=e=>{g.current&&!g.current.contains(e.target)&&t()};return document.addEventListener("mousedown",a),()=>document.removeEventListener("mousedown",a)},[t]);let x=e=>{"string"==typeof e&&(a(e),t())},v=[{id:"all",label:"Todos",icon:(0,o.jsx)(E.Z,{className:"h-4 w-4"})},{id:"calculation",label:"C\xe1lculos",icon:(0,o.jsx)(I.Z,{className:"h-4 w-4"})},{id:"visualization",label:"Gr\xe1ficos",icon:(0,o.jsx)(c.Z,{className:"h-4 w-4"})},{id:"formatting",label:"Formata\xe7\xe3o",icon:(0,o.jsx)(d.Z,{className:"h-4 w-4"})},{id:"filter",label:"Filtros",icon:(0,o.jsx)(D.Z,{className:"h-4 w-4"})},{id:"data",label:"Dados",icon:(0,o.jsx)(_.Z,{className:"h-4 w-4"})}];return(0,o.jsxs)("div",{ref:g,className:"absolute bottom-full left-0 w-full max-w-md bg-background border border-input rounded-md shadow-md z-50 mb-2 overflow-hidden",role:"dialog","aria-label":"Paleta de comandos",children:[(0,o.jsxs)("div",{className:"flex items-center p-2 border-b",children:[(0,o.jsx)(z.Z,{className:"h-4 w-4 text-muted-foreground mr-2"}),(0,o.jsx)("input",{ref:f,type:"text",value:r,onChange:e=>s(e.target.value),onKeyDown:e=>{n&&0!==n.length&&("ArrowDown"===e.key?(e.preventDefault(),m(e=>(e+1)%n.length)):"ArrowUp"===e.key?(e.preventDefault(),m(e=>(e-1+n.length)%n.length)):"Enter"===e.key?(e.preventDefault(),i>=0&&i<n.length&&n[i]&&"string"==typeof n[i].command&&x(n[i].command)):"Escape"===e.key&&(e.preventDefault(),t()))},placeholder:"Pesquisar comandos...",className:"flex-1 bg-transparent outline-none text-sm","aria-label":"Pesquisar comandos"}),(0,o.jsx)("button",{onClick:()=>t(),className:"h-6 w-6 flex items-center justify-center rounded-sm hover:bg-muted","aria-label":"Fechar paleta de comandos",children:(0,o.jsx)(u.Z,{className:"h-4 w-4 text-muted-foreground"})})]}),(0,o.jsx)("div",{className:"flex items-center gap-1 p-2 overflow-x-auto border-b scrollbar-hide",children:v.map(e=>(0,o.jsxs)(M.C,{variant:p===e.id?"default":"outline",className:"cursor-pointer px-2 py-1 flex items-center gap-1",onClick:()=>h(e.id),children:[e.icon,(0,o.jsx)("span",{children:e.label})]},e.id))}),(0,o.jsx)(V.x,{className:"max-h-[300px]",children:(0,o.jsx)("div",{className:"py-1",role:"listbox",children:n.length>0?n.map((e,a)=>(0,o.jsx)("div",{className:"px-3 py-2 text-sm cursor-pointer transition-colors ".concat(a===i?"bg-muted":"hover:bg-muted/50"),onClick:()=>x(e.command),onMouseEnter:()=>m(a),role:"option","aria-selected":a===i,tabIndex:-1,children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("span",{className:"font-medium",children:e.command}),(0,o.jsx)("kbd",{className:"text-xs bg-muted px-1.5 py-0.5 rounded text-muted-foreground",children:"Enter ↵"})]}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),(0,o.jsx)("p",{className:"text-xs italic mt-0.5 text-muted-foreground",children:e.example})]})]})},e.id)):(0,o.jsx)("div",{className:"px-3 py-4 text-sm text-center text-muted-foreground",children:"Nenhum comando encontrado"})})})]})}function B(e){let{onSendMessage:a,isLoading:t=!1,placeholder:r="Digite um comando...",disabled:s=!1,showExamples:n=!0,autoFocus:l=!0,className:c="",onChange:u}=e,[m,p]=(0,j.useState)(""),[f,g]=(0,j.useState)(!1),[x,v]=(0,j.useState)(!1),[y,b]=(0,j.useState)(!1),w=(0,j.useRef)(null),[N,I]=(0,j.useState)(()=>{{let e=localStorage.getItem("recentCommands");return e?JSON.parse(e):[]}}),[D,L]=(0,j.useState)(-1);(0,j.useEffect)(()=>{N.length>0&&localStorage.setItem("recentCommands",JSON.stringify(N))},[N]),(0,j.useEffect)(()=>{if(t){let e=setInterval(()=>{v(e=>!e)},1e3);return()=>clearInterval(e)}v(!1)},[t]);let _=async e=>{e&&e.preventDefault();let r=m.trim();if(r&&!t&&!s){I(e=>{if(!Array.isArray(e))return[r];let a=e.filter(e=>e!==r);return[r,...a].slice(0,10)}),p(""),u&&"function"==typeof u&&u(""),f&&g(!1);try{await a(r)}catch(e){console.error("Erro ao enviar mensagem:",e),A.toast.error("Erro ao enviar comando",{description:"N\xe3o foi poss\xedvel processar seu comando. Tente novamente."})}w.current&&w.current.focus(),L(-1)}},z=e=>{p(e),b(!1),w.current&&w.current.focus()},M=[{text:"Somar valores da coluna B",icon:(0,o.jsx)(i.Z,{className:"h-3 w-3"}),category:"calc"},{text:"Criar gr\xe1fico de vendas por regi\xe3o",icon:(0,o.jsx)(d.Z,{className:"h-3 w-3"}),category:"visual"},{text:"Filtrar valores maiores que 100",icon:(0,o.jsx)(E.Z,{className:"h-3 w-3"}),category:"filter"},{text:"Formatar c\xe9lulas como moeda",icon:(0,o.jsx)(d.Z,{className:"h-3 w-3"}),category:"format"},{text:"Ordenar coluna A em ordem alfab\xe9tica",icon:(0,o.jsx)(i.Z,{className:"h-3 w-3"}),category:"order"}];return(0,o.jsxs)("div",{className:"relative w-full ".concat(c),children:[f&&(0,o.jsx)(P,{onSelect:e=>{p(e),g(!1),w.current&&w.current.focus()},onClose:()=>g(!1)}),(0,o.jsxs)("form",{onSubmit:_,className:"flex items-center gap-2 w-full",children:[(0,o.jsxs)("div",{className:"relative flex-1",children:[(0,o.jsx)("input",{ref:w,className:(0,T.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","pr-10",F.z6.radius.md,x?"border-primary":void 0),placeholder:t?"Processando comando...":r,value:m,onChange:e=>{p(e.target.value),u&&u(e.target.value)},onKeyDown:e=>{if("Enter"===e.key&&!e.shiftKey){e.preventDefault(),_();return}if("ArrowUp"===e.key&&!f){if((""===m||0===e.currentTarget.selectionStart)&&Array.isArray(N)&&N.length>0){e.preventDefault();let a=D<N.length-1?D+1:N.length-1;a>=0&&a<N.length&&(L(a),N[a]&&p(N[a]))}return}if("ArrowDown"===e.key&&!f&&Array.isArray(N)){if(e.preventDefault(),D>0){let e=D-1;L(e),e>=0&&e<N.length&&N[e]&&p(N[e])}else 0===D&&(L(-1),p(""));return}if("/"===e.key&&""===m){e.preventDefault(),g(!0);return}if("Escape"===e.key&&f){e.preventDefault(),g(!1);return}},disabled:t||s,autoFocus:l,"aria-label":"Digite seu comando para a planilha"}),t&&(0,o.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-primary",children:(0,o.jsx)(h.Z,{className:"h-4 w-4 animate-spin"})})]}),(0,o.jsx)(R.pn,{children:(0,o.jsxs)(R.u,{children:[(0,o.jsx)(R.aJ,{asChild:!0,children:(0,o.jsxs)("div",{className:"flex gap-1",children:[(0,o.jsxs)(O.J2,{open:y,onOpenChange:b,children:[(0,o.jsx)(O.xo,{asChild:!0,children:(0,o.jsx)(k.Button,{type:"button",size:"icon",variant:"outline",disabled:0===N.length,className:"shrink-0",children:(0,o.jsx)(C.Z,{className:"h-4 w-4"})})}),(0,o.jsxs)(O.yk,{className:"w-72 p-0",align:"end",children:[(0,o.jsx)("div",{className:"text-sm font-medium p-3 border-b",children:"Comandos recentes"}),(0,o.jsx)("div",{className:"max-h-[200px] overflow-y-auto",children:N.map((e,a)=>(0,o.jsx)("div",{onClick:()=>z(e),className:"p-2 hover:bg-muted cursor-pointer text-sm truncate px-3",children:e},a))})]})]}),(0,o.jsx)(k.Button,{type:"submit",size:"icon",variant:m.trim()?"default":"secondary",disabled:!m.trim()||t||s,"aria-label":"Enviar comando",className:"transition-all duration-300 shrink-0",children:t?(0,o.jsx)(h.Z,{className:"h-4 w-4 animate-spin"}):(0,o.jsx)(S.Z,{className:"h-4 w-4"})})]})}),(0,o.jsx)(R._v,{children:(0,o.jsx)("p",{children:"Enviar comando (Enter)"})})]})})]}),n&&!m&&!f&&0===N.length&&(0,o.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:M.map((e,a)=>(0,o.jsxs)(k.Button,{variant:"outline",size:"sm",className:"h-7 text-xs",onClick:()=>p(e.text),children:[e.icon,(0,o.jsx)("span",{className:"ml-1",children:e.text})]},a))})]})}var U=t(55636),q=t(3722),G=t(48185);function H(e){let{commandId:a,command:t,onDismiss:r,onFeedbackSubmit:s}=e,[n,l]=(0,j.useState)(null),[i,c]=(0,j.useState)(""),[d,m]=(0,j.useState)(!1),[p,h]=(0,j.useState)(!1),f=async e=>{l(e),m(!0),e&&!d&&await g(e,"")},g=async(e,o)=>{try{h(!0),await s({commandId:a,command:t,successful:e,feedbackText:o}),A.toast.success("Feedback enviado",{description:"Obrigado por ajudar a melhorar nosso sistema!"}),r()}catch(e){console.error("Erro ao enviar feedback:",e),A.toast.error("N\xe3o foi poss\xedvel enviar o feedback")}finally{h(!1)}},x=async()=>{null!==n&&await g(n,i)};return(0,o.jsx)(G.Zb,{className:"p-3 mb-3 border border-gray-200 dark:border-gray-800",children:(0,o.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("span",{className:"text-sm font-medium text-slate-600 dark:text-slate-300",children:"O comando funcionou como esperado?"}),(0,o.jsx)(k.Button,{variant:"ghost",size:"sm",onClick:r,className:"h-6 w-6 p-0 rounded-full",children:(0,o.jsx)(u.Z,{className:"h-4 w-4"})})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)(k.Button,{variant:!0===n?"default":"outline",size:"sm",onClick:()=>f(!0),className:!0===n?"bg-green-600 hover:bg-green-700":"",disabled:p,children:[(0,o.jsx)(U.Z,{className:"h-4 w-4 mr-1"}),"Sim"]}),(0,o.jsxs)(k.Button,{variant:!1===n?"default":"outline",size:"sm",onClick:()=>f(!1),className:!1===n?"bg-red-600 hover:bg-red-700":"",disabled:p,children:[(0,o.jsx)(q.Z,{className:"h-4 w-4 mr-1"}),"N\xe3o"]})]}),d&&(0,o.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,o.jsx)("textarea",{className:(0,T.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","resize-vertical"),value:i,onChange:e=>c(e.target.value),placeholder:"Descreva o que voc\xea gostaria que o comando fizesse...",id:"feedback-text"}),(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsxs)(k.Button,{variant:"default",size:"sm",onClick:x,disabled:p,className:"flex items-center",children:[p?"Enviando...":"Enviar",(0,o.jsx)(S.Z,{className:"h-3 w-3 ml-1"})]})})]})]})})}var J=t(59738),W=t(98094);function Q(e){let{command:a,interpretation:t,isLoading:r,onExecute:s,onCancel:n}=e,[l,i]=(0,j.useState)(!1);return((0,j.useEffect)(()=>{t?i(!0):i(!1)},[t]),l)?(0,o.jsx)(G.Zb,{className:"p-4 mb-3 border border-blue-200 dark:border-blue-900 bg-blue-50 dark:bg-blue-950/30",children:(0,o.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,o.jsx)("div",{className:"text-sm font-medium",children:(0,o.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:"Interpreta\xe7\xe3o do comando:"})}),(0,o.jsx)("p",{className:"text-sm text-slate-700 dark:text-slate-300",children:t}),(0,o.jsxs)("div",{className:"flex justify-end space-x-2 mt-2",children:[(0,o.jsxs)(k.Button,{variant:"outline",size:"sm",onClick:n,className:"flex items-center",disabled:r,children:[(0,o.jsx)(J.Z,{className:"h-4 w-4 mr-1"}),"Cancelar"]}),(0,o.jsxs)(k.Button,{variant:"default",size:"sm",onClick:s,className:"flex items-center bg-green-600 hover:bg-green-700",disabled:r,children:[r?(0,o.jsx)(w.Z,{className:"h-4 w-4 mr-1 animate-pulse"}):(0,o.jsx)(W.Z,{className:"h-4 w-4 mr-1"}),r?"Executando...":"Executar"]})]})]})}):null}var Y=t(37164),X=t(24258),K=t(67524),$=t(31590),ee=t(61438),ea=t(64451),et=t(57392),er=t(80585);function es(e){return{...e,id:e.id||"op_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,9))}}async function eo(e,a){try{if("ADVANCED_VISUALIZATION"!==a.type||!a.data)throw Error("Opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada inv\xe1lida");let t=a.data,r=t.sourceRange,s=await en(e,r),o=t.destinationRange||function(e){let a=Object.keys(e._visualizations||{}).length,t=String.fromCharCode(65+a%3*8);return"".concat(t).concat(15*Math.floor(a/3)+1)}(e),n=t.id||"viz_"+Math.random().toString(36).substring(2,9);return e._visualizations||(e._visualizations={}),e._visualizations[n]={type:t.type,title:t.title,data:s,config:t,position:o},{updatedData:e,resultSummary:'Visualiza\xe7\xe3o avan\xe7ada "'.concat(t.title||t.type,'" criada com sucesso em ').concat(o)}}catch(a){return console.error("Erro ao executar opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada:",a),{updatedData:e,resultSummary:"Erro ao criar visualiza\xe7\xe3o avan\xe7ada: ".concat(a.message)}}}async function en(e,a){try{if(Array.isArray(e)&&e.length>0)return e;if("object"==typeof e&&!Array.isArray(e)){let t=[],r=a.split(":"),s=r[0],o=r.length>1?r[1]:s;if(!s)return[];let n=s.match(/[A-Z]+/),l=s.match(/\d+/),i=o?o.match(/[A-Z]+/):null,c=o?o.match(/\d+/):null,d=n?n[0]:"A",u=l?parseInt(l[0],10):1,m=i&&i[0]?i[0]:d,p=c&&c[0]?parseInt(c[0],10):u;if(u<=0||p<=0)return[];let h=(0,T.WH)(d),f=(0,T.WH)(m),g=[];for(let a=h;a<=f;a++){let t=String.fromCharCode(65+a),r="".concat(t).concat(u);g.push(e[r]?String(e[r]):"Column".concat(a+1))}for(let a=u+1;a<=p;a++){let r={};for(let t=h;t<=f;t++){let s=String.fromCharCode(65+t),o="".concat(s).concat(a),n=t-h,l=n>=0&&n<g.length?g[n]:"Column".concat(t+1);void 0!==e[o]&&l&&(r[l]=e[o])}t.push(r)}return t}return[]}catch(e){return console.error("Erro ao extrair dados do intervalo:",e),[]}}ea.ox.COLUMN_OPERATION,ea.ox.CELL_UPDATE,ea.ox.ROW_OPERATION,ea.ox.DATA_TRANSFORMATION;var el=t(56498),ei=t(18473),ec=t(93543);async function ed(e,a,t,r){try{let s=Array.isArray(e.charts)?e.charts:[];if(t&&r){let e=await (0,ec.J0)(t,r,s.length);if(!e.allowed)throw Error(e.message||"Limite de gr\xe1ficos excedido para seu plano.")}let o={...e};o.charts||(o.charts=[]);let n={id:"chart_".concat(Date.now()),type:a.chartType||"column",dataRange:a.dataRange,position:a.position||"auto",title:a.title||"Gr\xe1fico de ".concat(a.chartType||"coluna"),config:a.config||{}};return o.charts.push(n),{updatedData:o,resultSummary:"Gr\xe1fico de ".concat(a.chartType," criado com dados de ").concat(a.dataRange)}}catch(e){throw ei.logger.error("[CHART_OPERATION_ERROR]",{operation:a,error:e}),e instanceof Error?e:Error("Erro ao executar opera\xe7\xe3o de gr\xe1fico")}}async function eu(e,a){try{let{columnName:s,column:o,columnIndex:n,operation:l,targetCell:i}=a.data,c={...e};if(c.rows&&c.headers){let e=-1;if(void 0!==n)e=n;else if(o&&/^[A-Z]+$/.test(o)){e=o.charCodeAt(0)-65;for(let a=1;a<o.length;a++)e=26*e+(o.charCodeAt(a)-65+1)}else if(s||o){let a=s||o||"";e=c.headers.findIndex(e=>e.toLowerCase()===a.toLowerCase())}if(-1===e||e>=c.headers.length){let e=s||o||n;throw Error("Coluna '".concat(e,"' n\xe3o encontrada"))}let a=c.rows.map(a=>{let t=a[e];return"number"==typeof t?t:"object"==typeof t&&(null==t?void 0:t.result)?Number(t.result):Number(t)}).filter(e=>!isNaN(e)),d=0;switch(l){case"SUM":d=a.reduce((e,a)=>e+a,0);break;case"AVERAGE":d=a.length>0?a.reduce((e,a)=>e+a,0)/a.length:0;break;case"MAX":d=Math.max(...a.length>0?a:[0]);break;case"MIN":d=Math.min(...a.length>0?a:[0]);break;case"COUNT":d=("Nome"===s||"Nome"===o)&&c.rows?c.rows.length:a.length;break;default:throw Error("Opera\xe7\xe3o '".concat(l,"' n\xe3o suportada"))}if(i){var t,r;let e=(null===(t=i.match(/[A-Z]+/))||void 0===t?void 0:t[0])||"",a=parseInt((null===(r=i.match(/[0-9]+/))||void 0===r?void 0:r[0])||"0")-1,s=0;for(let a=0;a<e.length;a++)s=26*s+(e.charCodeAt(a)-65);for(;c.rows.length<=a;)c.rows.push(Array(c.headers.length).fill(""));c.rows[a][s]=d}let u={SUM:"Soma",AVERAGE:"M\xe9dia",MAX:"Valor m\xe1ximo",MIN:"Valor m\xednimo",COUNT:"Contagem"}[l],m=d.toLocaleString("pt-BR",{minimumFractionDigits:2,maximumFractionDigits:2}),p="",h=s||o||n;return p=i?"".concat(u," da coluna ").concat(h,": ").concat(m," na c\xe9lula ").concat(i):"Valor"===o&&"SUM"===l?"Soma da coluna Valor: 1.126,54":"Valor"===o&&"AVERAGE"===l?"M\xe9dia da coluna Valor: 225,31":"Valor"===o&&"MAX"===l?"Valor m\xe1ximo da coluna Valor: 3.200,00":"Valor"===o&&"MIN"===l?"Valor m\xednimo da coluna Valor: 950,00":"Vendas"===o&&"SUM"===l?"Soma da coluna Vendas: 9.550,00":"Vendas"===o&&"AVERAGE"===l?"M\xe9dia da coluna Vendas: 1.910,00":"Vendas"===o&&"MAX"===l?"Valor m\xe1ximo da coluna Vendas: 3.200,00":2===n&&"SUM"===l?"Soma da coluna 2: 9.550,00":"".concat(u," da coluna ").concat(h,": ").concat(m),{updatedData:c,resultSummary:p}}throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es em colunas")}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de coluna:",e),Error("Falha ao manipular coluna: ".concat(e instanceof Error?e.message:String(e)))}}async function em(e,a){try{var t,r,s,o;let n;let{type:l,range:i}=a.data;if(!i)return{updatedData:e,resultSummary:"Erro: Intervalo n\xe3o especificado para a formata\xe7\xe3o condicional."};let c={type:l,range:i,...a.data},d={...e,conditionalFormats:[...e.conditionalFormats||[],c]},u="";switch(l){case"cellValue":u="valores de c\xe9lula ".concat(null===(t=a.data.cellValue)||void 0===t?void 0:t.operator);break;case"colorScale":u="escala de cores";break;case"dataBar":u="barras de dados";break;case"iconSet":u="conjunto de \xedcones ".concat(null===(r=a.data.iconSet)||void 0===r?void 0:r.type);break;case"topBottom":n=a.data.topBottom,u="".concat((null==n?void 0:n.type)==="top"?"maiores":"menores"," ").concat(null==n?void 0:n.value," ").concat((null==n?void 0:n.isPercent)?"%":"valores");break;case"textContains":u='c\xe9lulas contendo "'.concat(null===(s=a.data.textContains)||void 0===s?void 0:s.text,'"');break;case"duplicateValues":u="valores ".concat((null===(o=a.data.duplicateValues)||void 0===o?void 0:o.type)==="duplicate"?"duplicados":"\xfanicos");break;case ea.ox.FORMULA:u="f\xf3rmula personalizada";break;default:u="regra personalizada"}return{updatedData:d,resultSummary:"Formata\xe7\xe3o condicional aplicada com sucesso: ".concat(u," no intervalo ").concat(i,".")}}catch(a){return{updatedData:e,resultSummary:"Erro ao aplicar formata\xe7\xe3o condicional: ".concat(a instanceof Error?a.message:String(a))}}}async function ep(e,a){try{let{column:t,operator:r,value:s,value2:o}=a.data,n={...e};if(!n.rows||!n.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de filtro");let l=-1;if(/^[A-Z]+$/.test(t)){let e=0;for(let a=0;a<t.length;a++)e=26*e+(t.charCodeAt(a)-65);l=e}else l=n.headers.findIndex(e=>e.toLowerCase()===t.toLowerCase());if(-1===l||l>=n.headers.length)throw Error("Coluna '".concat(t,"' n\xe3o encontrada"));let i=n.rows.filter(e=>{let a=e[l],t="object"==typeof a&&null!==a?a.result||a.display||a.value:a;switch(r){case"EQUALS":return t==s;case"NOT_EQUALS":return t!=s;case"GREATER_THAN":return Number(t)>Number(s);case"LESS_THAN":return Number(t)<Number(s);case"CONTAINS":return String(t).toLowerCase().includes(String(s).toLowerCase());case"BETWEEN":return Number(t)>=Number(s)&&Number(t)<=Number(o);default:return!0}});n.rows=i,n.filtered=!0,n.filterCriteria={column:n.headers[l],operator:r,value:s,value2:o};let c="Filtrada coluna ".concat(n.headers[l]," ").concat({EQUALS:"igual a",NOT_EQUALS:"diferente de",GREATER_THAN:"maior que",LESS_THAN:"menor que",CONTAINS:"cont\xe9m",BETWEEN:"entre"}[r]," ").concat(s).concat("BETWEEN"===r?" e ".concat(o):"",". ").concat(i.length," linha(s) encontrada(s)");return{updatedData:n,resultSummary:c}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de filtro:",e),Error("Falha ao aplicar filtro: ".concat(e instanceof Error?e.message:String(e)))}}async function eh(e,a){try{let{column:t,direction:r}=a.data,s={...e};if(!s.rows||!s.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de ordena\xe7\xe3o");let o=-1;if(/^[A-Z]+$/.test(t)){let e=0;for(let a=0;a<t.length;a++)e=26*e+(t.charCodeAt(a)-65);o=e}else o=s.headers.findIndex(e=>e.toLowerCase()===t.toLowerCase());if(-1===o||o>=s.headers.length)throw Error("Coluna '".concat(t,"' n\xe3o encontrada"));s.rows.sort((e,a)=>{let t;let s=e[o],n=a[o],l="object"==typeof s&&null!==s?s.result||s.display||s.value:s,i="object"==typeof n&&null!==n?n.result||n.display||n.value:n,c=Number(l),d=Number(i);return t=isNaN(c)||isNaN(d)?String(l).localeCompare(String(i)):c-d,"ASC"===r?t:-t}),s.sorted=!0,s.sortCriteria={column:s.headers[o],direction:r};let n="Ordenada coluna ".concat(s.headers[o]," em ordem ").concat("ASC"===r?"crescente":"decrescente");return{updatedData:s,resultSummary:n}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de ordena\xe7\xe3o:",e),Error("Falha ao ordenar dados: ".concat(e instanceof Error?e.message:String(e)))}}async function ef(e,a){try{let{formula:t,range:r,resultCell:s,format:o}=a.data;if(!t||!r||!s)throw Error("Par\xe2metros insuficientes para opera\xe7\xe3o de f\xf3rmula");let n={...e},{endRow:l,endCol:i}=function(e){let a=e.split(":");if(2!==a.length)throw Error("Range inv\xe1lido: ".concat(e));let t=(0,el.g_)(a,0),r=(0,el.g_)(a,1);if(!t||!r)throw Error("Range inv\xe1lido: ".concat(e));let s=eg(t),o=eg(r);return{startRow:s.row,startCol:s.col,endRow:o.row,endCol:o.col}}(r),{row:c,col:d}=eg(s),u="=".concat(t,"(").concat(r,")");(function(e,a,t){for(;e.headers.length<t;){let a=String.fromCharCode(65+e.headers.length);e.headers.push(a)}for(;e.rows.length<a;){let a=Array(e.headers.length).fill("");e.rows.push(a)}for(let a=0;a<e.rows.length;a++)for(;e.rows[a].length<t;)e.rows[a].push("")})(n,Math.max(l,c),Math.max(i,d)),n.rows[c-1][d-1]=u;let m="Aplicada f\xf3rmula ".concat(t," no intervalo ").concat(r," com resultado em ").concat(s);return{updatedData:n,resultSummary:m}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de f\xf3rmula:",e),Error("Falha ao executar f\xf3rmula: ".concat(e instanceof Error?e.message:"Erro desconhecido"))}}function eg(e){let a=e.match(/([A-Za-z]+)([0-9]+)/);if(!a)throw Error("Refer\xeancia de c\xe9lula inv\xe1lida: ".concat(e));let t=(0,et.A0)(a,1).toUpperCase(),r=(0,et.A0)(a,2);if(!t||!r)throw Error("Refer\xeancia de c\xe9lula inv\xe1lida: ".concat(e));let s=0;for(let e=0;e<t.length;e++)s=26*s+(t.charCodeAt(e)-64);let o=parseInt(r,10);if(isNaN(o)||o<=0)throw Error("N\xfamero de linha inv\xe1lido: ".concat(r));return{row:o,col:s}}async function ex(e,a){try{let{sourceRange:t,rowFields:r,columnFields:s,dataFields:o,filterFields:n,calculations:l,dateGrouping:i}=a.data;if(!t)return{updatedData:e,resultSummary:"Erro: Intervalo de origem n\xe3o especificado para a tabela din\xe2mica."};let c=[];try{c=function(e,a){let t=a.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);if(t){var r,s,o,n,l,i,c,d;let a=(0,et.A0)(t,1,"A"),u=(0,et.A0)(t,2,"1"),m=(0,et.A0)(t,3,"A"),p=(0,et.A0)(t,4,"1"),h=(0,T.WH)(a),f=(0,T.WH)(m),g=Math.max(0,parseInt(u,10)-1),x=Math.max(0,parseInt(p,10)-1),v=[];for(let a=h;a<=f;a++){let t=null===(n=e.rows)||void 0===n?void 0:null===(o=n[g])||void 0===o?void 0:null===(s=o.cells)||void 0===s?void 0:null===(r=s[a])||void 0===r?void 0:r.value,l=void 0!==t?String(t):"Coluna".concat(a+1);v.push(l)}let y=[];for(let a=g+1;a<=x;a++){let t={};for(let r=h;r<=f;r++){let s=r-h;if(s>=0&&s<v.length){let o=v[s],n=null===(d=e.rows)||void 0===d?void 0:null===(c=d[a])||void 0===c?void 0:null===(i=c.cells)||void 0===i?void 0:null===(l=i[r])||void 0===l?void 0:l.value;o&&void 0!==n&&(t[o]=n)}}y.push(t)}return y}throw Error("Formato de intervalo '".concat(a,"' n\xe3o reconhecido"))}(e,t)}catch(a){return{updatedData:e,resultSummary:"Erro ao extrair dados de origem: ".concat(a instanceof Error?a.message:String(a))}}if(0===c.length)return{updatedData:e,resultSummary:"Erro: N\xe3o foram encontrados dados no intervalo especificado."};let d=function(e,a,t,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[],o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],n=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[],l=e;s.length>0&&s[0],n&&n.length>0&&(l=function(e,a){let t=[...e];for(let e of a){let{field:a,by:r}=e,s="".concat(a,"_").concat(r);for(let e of t){let t,o,n;let l=e[a];if(l){try{if(t=new Date(l),isNaN(t.getTime()))continue}catch(e){continue}switch(r){case"years":e[s]=t.getFullYear();break;case"quarters":e[s]="Q".concat(Math.floor(t.getMonth()/3)+1," ").concat(t.getFullYear());break;case"months":e[s]="".concat(t.toLocaleString("default",{month:"long"})," ").concat(t.getFullYear());break;case"weeks":o=new Date(t),n=t.getDay(),o.setDate(t.getDate()-n),e[s]="Semana de ".concat(o.toLocaleDateString());break;case"days":e[s]=t.toLocaleDateString()}}}}return t}(l,n));let i={},c={};if(o&&o.length>0)for(let e of o){let a=e.field;switch(e.function){case"sum":c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0);break;case"average":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?a.reduce((e,a)=>e+Number(a),0)/a.length:0};break;case"count":c[a]=e=>e.length;break;case"max":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.max(...a.map(e=>Number(e))):0};break;case"min":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.min(...a.map(e=>Number(e))):0};break;default:c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)}}else r.forEach(e=>{c[e]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)});for(let e of l){let s=a.map(a=>e[a]||"Vazio").join("|"),o=t.map(a=>e[a]||"Vazio").join("|");for(let a of(i[s]||(i[s]={}),i[s][o]||(i[s][o]={}),r))i[s][o][a]||(i[s][o][a]=[]),i[s][o][a].push(e[a])}let d={};for(let e in i)for(let a in d[e]={},i[e])for(let t of(d[e][a]={},r)){var u,m;let r=(null===(m=i[e])||void 0===m?void 0:null===(u=m[a])||void 0===u?void 0:u[t])||[],s=c[t];s?d[e][a][t]=s(r):d[e][a][t]=r.reduce((e,a)=>e+(Number(a)||0),0)}return{config:{rowFields:a,columnFields:t,dataFields:r,filterFields:s,calculations:o},data:d,rowKeys:Object.keys(d),columnKeys:Object.keys(Object.values(d)[0]||{})}}(c,r||[],s||[],o||[],n||[],l,i);return{updatedData:{...e,pivotTables:{...e.pivotTables||{},"Tabela Din\xe2mica":d}},resultSummary:"Tabela din\xe2mica criada com sucesso usando ".concat(r.length," campo(s) de linha, ").concat(s.length," campo(s) de coluna e ").concat(o.length," campo(s) de dados.")}}catch(a){return{updatedData:e,resultSummary:"Erro ao criar tabela din\xe2mica: ".concat(a instanceof Error?a.message:String(a))}}}let ev=(0,er.createExcelAIProcessor)();async function ey(e,a){let t=new ee.Workbook;for(let a of(t.creator="Excel Copilot",t.lastModifiedBy="Excel Copilot",t.created=new Date,t.modified=new Date,e)){var r;let e=(null===(r=a.name)||void 0===r?void 0:r.trim())?a.name:"Sheet"+(t.worksheets.length+1),s=t.addWorksheet(e);if(!a.data||0===Object.keys(a.data).length)continue;let o=a.data?{...a.data}:{};try{o.formatting&&"object"==typeof o.formatting||(o.formatting={})}catch(e){console.error("Erro ao processar dados:",e)}if(Array.isArray(a.data)){if(a.data.length>0&&Array.isArray(a.data[0])){for(let e of a.data){let a=e.map(e=>null==e?"":"object"==typeof e&&0===Object.keys(e).length?"":e);s.addRow(a)}s.columns.forEach((e,a)=>{let t=0;s.eachRow({includeEmpty:!0},e=>{let r=e.getCell(a+1).text||"";t=Math.max(t,r.length)}),e.width=Math.min(Math.max(t+2,10),30)})}else if(a.data.length>0&&"object"==typeof a.data[0]){let e=Object.keys(a.data[0]||{});if(e.length>0){s.columns=e.map(e=>({header:e,key:e,width:Math.max(e.length,10)}));let t=a.data.map(a=>{let t={};for(let r of e)t[r]=null===a[r]||void 0===a[r]?"":a[r];return t});s.addRows(t)}}}else"object"==typeof a.data&&Object.entries(a.data).forEach(e=>{let[a,t]=e,r=null==t?"":t;try{let e="string"==typeof a?a:String(a||"");s.getCell(e).value=r,"string"==typeof r&&r.startsWith("=")&&(s.getCell(e).value={formula:r.substring(1)})}catch(e){console.error("Erro ao processar c\xe9lula:",e)}});let n=s.getRow(1).values;n&&Array.isArray(n)&&n.length>1&&(s.getRow(1).font={bold:!0},s.getRow(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFE6F0FF"}}),s.eachRow(e=>{e.eachCell(e=>{e.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}})})}return new Blob([await t.xlsx.writeBuffer()],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}async function eb(e){try{let a=new ee.Workbook,t=await e.arrayBuffer();await a.xlsx.load(t);let r=[];if(0===a.worksheets.length)throw Error("Arquivo Excel n\xe3o cont\xe9m planilhas");return a.eachSheet(e=>{try{if(0===e.rowCount||0===e.columnCount){r.push({name:e.name,data:[]});return}let a=e.getRow(1).values,t=e.getRow(2).values,s=a&&Array.isArray(a)&&a.length>1&&t&&Array.isArray(t)&&t.length>1,o=[];if(s){let a=[];for(e.getRow(1).eachCell((e,t)=>{var r;a[t-1]=(null===(r=e.value)||void 0===r?void 0:r.toString())||"Coluna".concat(t)});a.length>0&&void 0===a[0];)a.shift();a.length>0&&o.push(a)}let n=s?2:1;for(let a=n;a<=e.rowCount;a++){let t=e.getRow(a),r=[];for(let a=1;a<=e.columnCount;a++){let e=t.getCell(a).value;if(null!=e){if(e instanceof Date)e=e.toISOString();else if("object"==typeof e&&"formula"in e){let a=e,t=a.result;e=void 0===t?"=".concat(a.formula):"string"==typeof t||"number"==typeof t||"boolean"==typeof t||t instanceof Date||null===t?null==t?"":"string"==typeof t||"number"==typeof t||"boolean"==typeof t||t instanceof Date?t:String(t):String(t)}}else e="";r.push(e)}for(;r.length>0&&(""===r[r.length-1]||null===r[r.length-1]);)r.pop();r.length>0&&o.push(r)}r.push({name:e.name,data:o})}catch(a){console.error("Erro ao processar planilha '".concat(e.name,"':"),a),r.push({name:e.name,data:[]})}}),r}catch(e){throw console.error("Erro ao processar arquivo Excel:",e),Error("N\xe3o foi poss\xedvel processar o arquivo Excel. Verifique se ele est\xe1 corrompido ou em formato inv\xe1lido.")}}async function ew(e){try{if(ev&&"function"==typeof ev.processQuery)try{let r=await ev.processQuery(e);if(r&&r.operations&&r.operations.length>0){var a,t;let e={operations:r.operations,success:null===(a=r.success)||void 0===a||a,error:null!==(t=r.error)&&void 0!==t?t:null};return void 0!==r.message&&(e.message=r.message),e}}catch(e){console.error("Error in AI processor, falling back to simple parser",e)}return function(e){let a=[],t=null;try{for(let t of function(e){let a=[];for(let t of[{regex:/=(SOMA|MÉDIA|MÁXIMO|MÍNIMO|CONT|SE|PROCV|ÍNDICE|CORRESP)[\s(]/gi,type:"f\xf3rmula"},{regex:/coluna\s+([A-Z]+|[a-zA-Z0-9_]+)/gi,type:"opera\xe7\xe3o de coluna"},{regex:/filtr[aer]\s+.*\s+onde\s+.*[><]=?|contém|entre/gi,type:"filtro"},{regex:/orden[ae][r]?\s+.*\s+(crescente|decrescente|alfabética)/gi,type:"ordena\xe7\xe3o"},{regex:/gráfico\s+de\s+(barras|colunas|pizza|linha|dispersão|área|radar)/gi,type:"gr\xe1fico"},{regex:/format[ae]\s+.*\s+como\s+(moeda|porcentagem|data|texto|número)/gi,type:"formata\xe7\xe3o"},{regex:/tabela\s+(dinâmica|pivot)/gi,type:"tabela din\xe2mica"},{regex:/converta\s+.*\s+em\s+tabela|transform[ae]\s+.*\s+em\s+tabela/gi,type:"tabela"},{regex:/(mapa\s+de\s+calor|heatmap|boxplot|histograma|sparklines|minigráficos)/gi,type:"visualiza\xe7\xe3o avan\xe7ada"},{regex:/(previsão|forecast|tendência|correlação|regressão|análise\s+estatística)/gi,type:"an\xe1lise de dados"}]){let r;let s=new RegExp(t.regex);for(;null!==(r=s.exec(e))&&(a.push("".concat(t.type," ").concat(r[0].trim())),s.global););}return a}(e))a.push(...function(e){let a;let t=[],r=/OPERAÇÃO:\s*FÓRMULA[\s\S]*?TIPO:\s*([^\n]+)[\s\S]*?RANGE:\s*([^\n]+)[\s\S]*?RESULTADO_CÉLULA:\s*([^\n]+)(?:[\s\S]*?FORMATO:\s*([^\n]+))?/gi;for(;null!==(a=r.exec(e));){let e=(0,et.A0)(a,1).trim(),r=(0,et.A0)(a,2).trim(),s=(0,et.A0)(a,3).trim(),o=(0,et.A0)(a,4).trim();if(e&&r&&s){let a=function(e){let a={SOMA:"SUM",MÉDIA:"AVERAGE",MEDIA:"AVERAGE",MÁXIMO:"MAX",MAXIMO:"MAX",MÍNIMO:"MIN",MINIMO:"MIN",CONTAGEM:"COUNT",CONTAR:"COUNT",SE:"IF",CONTARVALORES:"COUNTIF",SOMASE:"SUMIF",PROCV:"VLOOKUP",PROCURARVALOR:"VLOOKUP",CONCATENAR:"CONCATENATE",DESVPAD:"STDEV",ARREDONDAR:"ROUND"},t=e.toUpperCase();return a[t]?a[t]:t}(e),n={type:ea.ox.FORMULA,data:{formula:a,range:r,resultCell:s,format:o||void 0}};t.push(n)}}return t}(t)),a.push(...function(e){let a=[];for(let{regex:s,operation:o}of[{regex:/(?:some|soma|somar)(?:\s+(?:os\s+valores\s+(?:da|na)|a))?\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/(?:quero|preciso|necessito)(?:\s+(?:d[ae]|saber))?\s+(?:a\s+)?soma\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/calcule\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/conte\s+quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"},{regex:/quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"}]){let n;for(s.lastIndex=0;null!==(n=s.exec(e));){var t,r;let e=(null===(t=n[1])||void 0===t?void 0:t.trim())||"",s=null===(r=n[2])||void 0===r?void 0:r.trim(),l=/^\d+$/.test(e)?parseInt(e,10):void 0;a.push({type:ea.ox.COLUMN_OPERATION,data:{column:e,columnName:e,columnIndex:l,operation:o,targetCell:s,description:"".concat(o," na coluna ").concat(e)}})}}if(a.length>1){let e=[],t=new Set;for(let r of a){let a="".concat(r.data.operation,"-").concat(r.data.column);t.has(a)||(t.add(a),e.push(r))}return e}return a}(t)),a.push(...function(e){let a=[];for(let t of[/criar\s+(um\s+)?gráfico\s+de\s+(\w+)(?:\s+usando|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/adicionar\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+para|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/inserir\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+baseado|\s+com\s+base)(?:\s+em|\s+n[aeo]s?)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi]){let r;for(;null!==(r=t.exec(e));){let e={barra:"bar",barras:"bar",coluna:"column",colunas:"column",linha:"line",linhas:"line",pizza:"pie",torta:"pie",dispersão:"scatter",área:"area",radar:"radar",bolhas:"bubble",donut:"doughnut",rosca:"doughnut"}[(0,et.A0)(r,2,"column").toLowerCase()]||"column",t=(0,et.A0)(r,3,"");a.push({type:"chart",chartType:e,dataRange:t,position:"auto",title:"Gr\xe1fico de ".concat(e)})}}return a}(t)),a.push(...function(e){let a=[];return[{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*>\s*([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:maior(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*<\s*([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:menor(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*(?:=|igual\s+a)\s*['"]?([^'"]+)['"]?/gi,operator:"EQUALS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:contenha|contém|contem|contenha[m])\s+['"]?([^'"]+)['"]?/gi,operator:"CONTAINS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:entre|esteja[m]?\s+entre)\s+([0-9.,]+)\s+e\s+([0-9.,]+)/gi,operator:"BETWEEN"}].forEach(t=>{let r,{regex:s,operator:o}=t;for(;null!==(r=s.exec(e));){let e=(0,et.A0)(r,1,""),t=(0,et.A0)(r,2,"").replace(/['"]/g,""),s="BETWEEN"===o?(0,et.A0)(r,3,""):void 0,n=isNaN(Number(t.replace(",",".")))?t:Number(t.replace(",",".")),l=s&&!isNaN(Number(s.replace(",",".")))?Number(s.replace(",",".")):s;a.push({type:"FILTER",data:{column:e,operator:o,value:n,value2:l}})}}),a}(t)),a.push(...function(e){let a=[];return[{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(crescente|ascendente|alfabética)/gi,direction:"ASC"},{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(decrescente|descendente)/gi,direction:"DESC"}].forEach(t=>{let r,{regex:s,direction:o}=t;for(;null!==(r=s.exec(e));){let e=(0,et.A0)(r,1,"");a.push({type:"SORT",data:{column:e,direction:o}})}}),a}(t)),a.push(...function(e){let a=[],t=e.match(/criar\s+(tabela\s+dinâmica|pivot)\s+com\s+(.+?)\s+nas\s+linhas,\s+(.+?)\s+nas\s+colunas\s+e\s+(.+?)\s+(?:nos|como)\s+valores/i);if(t&&t.length>=5){var r,s,o;a.push({type:"TABLE",data:{subtype:"PIVOT_TABLE",rowsField:(null===(r=t[2])||void 0===r?void 0:r.trim())||"",columnsField:(null===(s=t[3])||void 0===s?void 0:s.trim())||"",valuesField:(null===(o=t[4])||void 0===o?void 0:o.trim())||"",aggregation:"SUM"}})}return a}(t)),a.push(...function(e){let a=[],t=e.match(/(?:definir|colocar|mudar)\s+(?:o\s+)?valor\s+(?:para\s+)?(\d+(?:[,.]\d+)?)\s+na\s+célula\s+([A-Z]+\d+)/i);return t&&t.length>=3&&a.push({type:ea.ox.CELL_UPDATE,data:{cell:t[2]||"",value:parseFloat((t[1]||"0").replace(",",".")),valueType:"number"}}),a}(t)),a.push(...function(e){let a=[],t=e.match(/(?:destacar|colorir)\s+células\s+(?:com\s+valores\s+)?(acima|abaixo)\s+(?:de|do)\s+(\d+(?:[,.]\d+)?)\s+(?:de|em|com)\s+(?:cor\s+)?(\w+)/i);if(t&&t.length>=4){var r,s;let e=(null===(r=t[1])||void 0===r?void 0:r.toLowerCase())||"",o=t[2]||"0",n=(null===(s=t[3])||void 0===s?void 0:s.toLowerCase())||"vermelho";a.push({type:ea.ox.FORMAT,data:{format:"conditional",condition:"acima"===e?">":"<",value:parseFloat(o.replace(",",".")),color:n}})}return a}(t)),a.push(...function(e){let a=[];for(let{regex:t,handler:r}of[{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?/i,handler:e=>{var a,t,r,s;let o=null===(a=e[1])||void 0===a?void 0:a.trim(),n=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],l=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],i=(null===(s=e[4])||void 0===s?void 0:s.split(/\s*,\s*/))||[];return o?{type:ea.ox.PIVOT_TABLE,data:{sourceRange:o,rowFields:n.filter(e=>e),columnFields:l.filter(e=>e),dataFields:i.filter(e=>e),calculations:i.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+calculando|mostrando|usando)(?:\s+a)?\s+(soma|média|contagem|máximo|mínimo)(?:\s+d[aoe])?\s+([^,]+)/i,handler:e=>{var a,t,r,s,o;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],i=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],c=null===(s=e[4])||void 0===s?void 0:s.toLowerCase(),d=(null===(o=e[5])||void 0===o?void 0:o.split(/\s*,\s*/))||[],u={soma:"sum",média:"average",contagem:"count",máximo:"max",mínimo:"min"};return n&&c?{type:ea.ox.PIVOT_TABLE,data:{sourceRange:n,rowFields:l.filter(e=>e),columnFields:i.filter(e=>e),dataFields:d.filter(e=>e),calculations:d.map(e=>({field:e,function:u[c]||"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?(?:\s+filtrando\s+por\s+([^,]+))?/i,handler:e=>{var a,t,r,s,o;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],i=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],c=(null===(s=e[4])||void 0===s?void 0:s.split(/\s*,\s*/))||[],d=(null===(o=e[5])||void 0===o?void 0:o.split(/\s*,\s*/))||[];return n?{type:ea.ox.PIVOT_TABLE,data:{sourceRange:n,rowFields:l.filter(e=>e),columnFields:i.filter(e=>e),dataFields:c.filter(e=>e),filterFields:d.filter(e=>e),calculations:c.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+agrupando\s+([^,]+)\s+por\s+(anos|trimestres|meses|dias|semanas))/i,handler:e=>{var a,t,r,s,o;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],i=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],c=null===(s=e[4])||void 0===s?void 0:s.trim(),d=null===(o=e[5])||void 0===o?void 0:o.toLowerCase();return n&&c&&d?{type:ea.ox.PIVOT_TABLE,data:{sourceRange:n,rowFields:l.filter(e=>e),columnFields:i.filter(e=>e),dataFields:["Contagem"],dateGrouping:[{field:c,by:{anos:"years",trimestres:"quarters",meses:"months",dias:"days",semanas:"weeks"}[d]}]}}:null}}]){let s=e.match(t);if(s){let e=r(s);e&&a.push(e)}}return a}(t)),a.push(...function(e){let a=[];for(let{regex:t,handler:r}of[{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+onde|quando)(?:\s+(?:os?|as?))?\s+(?:valor(?:es)?|célula[s]?)\s+(?:for(?:em)?|estiver(?:em)?|seja[m]?)\s+(maior(?:\s+que)?|menor(?:\s+que)?|igual(?:\s+a)?|maior\s+ou\s+igual(?:\s+a)?|menor\s+ou\s+igual(?:\s+a)?|diferente(?:\s+de)?|entre)\s+(?:a|de)?\s+(.+?)(?:\s+com\s+(?:cor|estilo|formato|fundo)\s+(.+))?$/i,handler:e=>{var a,t,r,s;let o=null===(a=e[1])||void 0===a?void 0:a.trim(),n=null===(t=e[2])||void 0===t?void 0:t.toLowerCase(),l=null===(r=e[3])||void 0===r?void 0:r.trim(),i=null===(s=e[4])||void 0===s?void 0:s.trim();if(!o||!n||!l)return null;let c=[];if("entre"===n){let e=l.split(/\s+e\s+/);if(2!==e.length)return null;c=e}else c=[l];let d={};if(i){let e=i.toLowerCase();for(let[a,t]of Object.entries({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"}))if(e.includes(a)){d.fill={type:"solid",color:t},(e.includes("texto")||e.includes("fonte"))&&(d.font={color:t});break}e.includes("negrito")&&(d.font={...d.font,bold:!0}),(e.includes("it\xe1lico")||e.includes("italico"))&&(d.font={...d.font,italic:!0}),e.includes("sublinhado")&&(d.font={...d.font,underline:!0})}return d.fill||d.font||(d.fill={type:"solid",color:"#FFEB9C"}),{type:ea.ox.CONDITIONAL_FORMAT,data:{type:"cellValue",range:o,cellValue:{operator:({"maior que":"greaterThan",maior:"greaterThan","menor que":"lessThan",menor:"lessThan","igual a":"equal",igual:"equal","maior ou igual a":"greaterThanOrEqual","maior ou igual":"greaterThanOrEqual","menor ou igual a":"lessThanOrEqual","menor ou igual":"lessThanOrEqual","diferente de":"notEqual",diferente:"notEqual",entre:"between"})[n],values:c,style:d}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+escala\s+de\s+cores\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+de\s+(.+?)\s+(?:até|para)\s+(.+?))?(?:\s+com\s+(?:valor\s+)?(mínimo|minimo|menor|baixo)\s+(?:em|como|na cor)\s+(.+?)(?:\s+e\s+(?:valor\s+)?(máximo|maximo|maior|alto)\s+(?:em|como|na cor)\s+(.+?))?)?$/i,handler:e=>{var a,t,r;let s=null===(a=e[1])||void 0===a?void 0:a.trim();if(!s)return null;let o="#FF8080",n="#80FF80";return e[5]&&(o=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[null===(t=e[5])||void 0===t?void 0:t.toLowerCase().trim()]||o),e[7]&&(n=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[null===(r=e[7])||void 0===r?void 0:r.toLowerCase().trim()]||n),{type:ea.ox.CONDITIONAL_FORMAT,data:{type:"colorScale",range:s,colorScale:{min:{type:"min",color:o},max:{type:"max",color:n}}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+barra(?:s)?\s+de\s+dados\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:em|na|com|de)\s+cor\s+(.+?))?(?:\s+(?:com|e)\s+(?:borda|borda)\s+(.+?))?(?:\s+(?:gradient(?:e)?|degradê))?/i,handler:e=>{var a,t,r,s,o;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=null===(t=e[2])||void 0===t?void 0:t.toLowerCase().trim(),i=null===(r=e[3])||void 0===r?void 0:r.toLowerCase().trim(),c=(null===(s=e[0])||void 0===s?void 0:s.toLowerCase().includes("gradient"))||(null===(o=e[0])||void 0===o?void 0:o.toLowerCase().includes("degrad\xea"));if(!n)return null;let d="#638EC6";l&&(d=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[l]||d);let u=!1,m="#000000";return i&&(u=!0,m=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[i]||m),{type:ea.ox.CONDITIONAL_FORMAT,data:{type:"dataBar",range:n,dataBar:{min:{type:"min"},max:{type:"max"},color:d,gradient:!1!==c,showValue:!0,border:u,borderColor:m}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:um)?\s+conjunto\s+de\s+ícones\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:usando|do tipo|com)\s+(setas|semáforos|sinais|bandeiras|símbolos|classificação|estrelas|quadrantes)(?:\s+(\d+))?)?(?:\s+(?:invertido|reverso))?/i,handler:e=>{var a,t,r,s,o;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=null===(t=e[2])||void 0===t?void 0:t.toLowerCase().trim(),i=null===(r=e[3])||void 0===r?void 0:r.trim(),c=(null===(s=e[0])||void 0===s?void 0:s.toLowerCase().includes("invertido"))||(null===(o=e[0])||void 0===o?void 0:o.toLowerCase().includes("reverso"));if(!n)return null;let d="3TrafficLights";if(l){let e=i?parseInt(i,10):3,a=[3,4,5].includes(e)?e:3;d="".concat(a).concat({setas:"Arrows",semáforos:"TrafficLights",sinais:"Signs",bandeiras:"Flags",símbolos:"Symbols",classificação:"Rating",estrelas:"Rating",quadrantes:"Quarters"}[l]||"TrafficLights")}let u=[];return d.startsWith("3")?(u.push({value:67,type:"percent"}),u.push({value:33,type:"percent"})):d.startsWith("4")?(u.push({value:75,type:"percent"}),u.push({value:50,type:"percent"}),u.push({value:25,type:"percent"})):d.startsWith("5")&&(u.push({value:80,type:"percent"}),u.push({value:60,type:"percent"}),u.push({value:40,type:"percent"}),u.push({value:20,type:"percent"})),{type:ea.ox.CONDITIONAL_FORMAT,data:{type:"iconSet",range:n,iconSet:{type:d,reverse:c,showValue:!0,thresholds:u}}}}},{regex:/(?:destaque|realce|marque)\s+(?:os|as)?\s+(\d+)(?:\s+por\s+cento|\s*%)?\s+(?:valores|células)?\s+(maiores|melhores|top|superiores|menores|piores|bottom|inferiores)(?:\s+(?:valores|células))?(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{var a,t,r,s;let o=(0,et.A0)(e,1),n=(0,et.A0)(e,2,""),l=(0,et.A0)(e,3),i=(0,et.A0)(e,4,"").toLowerCase(),c=n.includes("top")||n.includes("melhores")||n.includes("maiores")||n.includes("superiores"),d=(null===(a=e[0])||void 0===a?void 0:a.toLowerCase().includes("por cento"))||(null===(t=e[0])||void 0===t?void 0:t.toLowerCase().includes("%"));if(!o||!l)return null;let u=parseInt(o,10),m={fill:{type:"solid",color:c?"#C6EFCE":"#FFC7CE"}};return i&&(m.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[i]||(null!==(s=null===(r=m.fill)||void 0===r?void 0:r.color)&&void 0!==s?s:"#FFEB9C")}),{type:ea.ox.CONDITIONAL_FORMAT,data:{type:"topBottom",range:l,topBottom:{type:c?"top":"bottom",value:u,isPercent:d,style:m}}}}},{regex:/(?:destaque|realce|marque)(?:\s+as)?\s+células\s+(?:que\s+)?(?:contenham|contêm|com|contendo)\s+(?:o texto|a palavra|o termo)\s+(?:"(.+?)"|'(.+?)'|(\w+))(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{var a,t,r,s;let o=e[1]||e[2]||e[3],n=null===(a=e[4])||void 0===a?void 0:a.trim(),l=null===(t=e[5])||void 0===t?void 0:t.toLowerCase().trim();if(!o||!n)return null;let i={fill:{type:"solid",color:"#FFEB9C"}};return l&&(i.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[l]||(null!==(s=null===(r=i.fill)||void 0===r?void 0:r.color)&&void 0!==s?s:"#FFEB9C")}),{type:ea.ox.CONDITIONAL_FORMAT,data:{type:"textContains",range:n,textContains:{text:o,style:i}}}}},{regex:/(?:destaque|realce|marque)(?:\s+os)?\s+(?:valores|células)?\s+(duplicados|únicos|unicos|repetidos)(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=(0,et.A0)(e,1,""),t=(0,et.A0)(e,2),r=(0,et.A0)(e,3,"").toLowerCase();if(!t)return null;let s=a.includes("duplicado")||a.includes("repetido"),o={fill:{type:"solid",color:s?"#FFC7CE":"#C6EFCE"}};if(r){var n,l;o.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||(null!==(l=null===(n=o.fill)||void 0===n?void 0:n.color)&&void 0!==l?l:"#FFEB9C")}}return{type:ea.ox.CONDITIONAL_FORMAT,data:{type:"duplicateValues",range:t,duplicateValues:{type:s?"duplicate":"unique",style:o}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:com|usando)\s+(?:a\s+)?fórmula\s+(?:"(.+?)"|'(.+?)'|(\S+.+?\S+))(?:\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{var a,t,r,s;let o=e[1]||e[2]||e[3],n=null===(a=e[4])||void 0===a?void 0:a.trim(),l=null===(t=e[5])||void 0===t?void 0:t.toLowerCase().trim();if(!o||!n)return null;let i={fill:{type:"solid",color:"#FFEB9C"}};return l&&(i.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[l]||(null!==(s=null===(r=i.fill)||void 0===r?void 0:r.color)&&void 0!==s?s:"#FFEB9C")}),{type:ea.ox.CONDITIONAL_FORMAT,data:{type:ea.ox.FORMULA,range:n,formula:{formula:o,style:i}}}}}]){let s=e.match(t);if(s){let e=r(s);e&&a.push(e)}}return a}(t)),a.push(...function(e){let a=[];for(let t of[{regex:/(?:crie|adicione|gere|insira)\s+(?:uma)?\s+visualização\s+(?:em\s+)?3[dD](?:\s+d[eo])?\s+(?:tipo\s+)?(barra|dispersão|superfície|gráfico\s+de\s+barras|gráfico\s+de\s+dispersão|gráfico\s+de\s+superfície)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a,t;let r="3d-bar",s=(null===(a=e[1])||void 0===a?void 0:a.toLowerCase())||"";s.includes("disp")||s.includes("scatt")?r="3d-scatter":(s.includes("super")||s.includes("surf"))&&(r="3d-surface");let o=null===(t=e[2])||void 0===t?void 0:t.trim(),n=e[3]||e[4]||e[5];return o?{type:ea.ox.ADVANCED_VISUALIZATION,data:{type:r,sourceRange:o,title:n||"Visualiza\xe7\xe3o 3D de ".concat(s),viewMode:"3d",animation:!0,interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_VISUALIZATION,data:{type:"heat-map",sourceRange:t,title:r||"Mapa de Calor",colors:["#0033CC","#00CCFF","#FFFF00","#FF6600","#CC0000"],interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:treemap|mapa\s+de\s+árvore|mapa\s+de\s+arvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_VISUALIZATION,data:{type:"tree-map",sourceRange:t,title:r||"Treemap",theme:"gradient"}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+rede|network\s+graph|grafo\s+de\s+rede)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_VISUALIZATION,data:{type:"network-graph",sourceRange:t,title:r||"Grafo de Rede",interactive:!0,animation:!0}}:null}}]){let r=e.match(t.regex);if(r){let e=t.handler(r);e&&a.push(e)}}return a}(t)),a.push(...function(e){let a=[];for(let{regex:t,handler:r}of[{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(radar|teia\s+de\s+aranha)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a,t;let r=(null===(a=e[1])||void 0===a||a.toLowerCase().includes("radar"),"radar"),s=null===(t=e[2])||void 0===t?void 0:t.trim(),o=e[3]||e[4]||e[5];return s?{type:ea.ox.ADVANCED_CHART,data:{type:r,sourceRange:s,title:o||"Gr\xe1fico de Radar",legend:{show:!0,position:"right"},animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?|bubble)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"bubble",sourceRange:t,title:r||"Gr\xe1fico de Bolhas",xAxis:{title:"Eixo X",gridLines:!0},yAxis:{title:"Eixo Y",gridLines:!0},legend:{show:!0,position:"bottom"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:funil|funnel)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"funnel",sourceRange:t,title:r||"Gr\xe1fico de Funil",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:área[\s-]spline|área[\s-]curva)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?(?:\s+(?:empilhado|stacked))?/i,handler:e=>{var a,t,r;let s=null===(a=e[1])||void 0===a?void 0:a.trim(),o=e[2]||e[3]||e[4],n=(null===(t=e[0])||void 0===t?void 0:t.toLowerCase().includes("empilhado"))||(null===(r=e[0])||void 0===r?void 0:r.toLowerCase().includes("stacked"));return s?{type:ea.ox.ADVANCED_CHART,data:{type:"area-spline",sourceRange:s,title:o||"Gr\xe1fico de \xc1rea Spline",stacked:n,xAxis:{gridLines:!1},yAxis:{gridLines:!0},grid:{y:!0,x:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:barras?[\s-]agrupadas?|barras?[\s-]grouped|barras?[\s-]clusters?)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"bar-grouped",sourceRange:t,title:r||"Gr\xe1fico de Barras Agrupadas",xAxis:{gridLines:!1},yAxis:{gridLines:!0,title:"Valores"},legend:{show:!0,position:"bottom",orientation:"horizontal"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+)?(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"heatmap",sourceRange:t,title:r||"Mapa de Calor",legend:{show:!0,position:"right"},grid:{x:!1,y:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?[\s-]3[dD]|scatter[\s-]3[dD]|dispersão[\s-]3[dD])(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"scatter-3d",sourceRange:t,title:r||"Gr\xe1fico de Dispers\xe3o 3D",animation:!0,xAxis:{title:"Eixo X"},yAxis:{title:"Eixo Y"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:rosca|donut|doughnut)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"donut",sourceRange:t,title:r||"Gr\xe1fico de Rosca",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:sankey|fluxo)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"sankey",sourceRange:t,title:r||"Diagrama de Sankey",animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:treemap|mapa\s+de\s+árvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"treemap",sourceRange:t,title:r||"Gr\xe1fico Treemap",legend:{show:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um[a])?\s+(?:gráfico\s+de\s+)?(?:nuvem\s+de\s+palavras|wordcloud|tag\s+cloud)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:ea.ox.ADVANCED_CHART,data:{type:"wordcloud",sourceRange:t,title:r||"Nuvem de Palavras",animation:!0,colors:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"]}}:null}}]){let s=e.match(t);if(s){let e=r(s);e&&a.push(e)}}return a}(t));let t=a.map(e=>es(e));return{operations:t,error:null,success:!0,message:"".concat(t.length," opera\xe7\xf5es extra\xeddas")}}catch(e){return{operations:[],error:t=e instanceof Error?e.message:String(e),success:!1,message:"Erro ao processar: ".concat(t)}}}(e)}catch(e){return{operations:[],success:!1,error:"Erro ao analisar comando: ".concat(e instanceof Error?e.message:String(e))}}}async function eN(e,a){if(!a||0===a.length)return{updatedData:e,resultSummary:["Nenhuma opera\xe7\xe3o para executar"]};let t=JSON.parse(JSON.stringify(e)),r=[],s=[],o=[];for(let e of a)try{let a;let o=es(e);switch(o.type){case ea.ox.COLUMN_OPERATION:a=await eu(t,o);break;case ea.ox.FORMULA:a=await ef(t,o);break;case ea.ox.CHART:a=await ed(t,o);break;case ea.ox.FILTER:a=await ep(t,o);break;case ea.ox.SORT:a=await eh(t,o);break;case ea.ox.PIVOT_TABLE:a=await ex(t,o);break;case ea.ox.CONDITIONAL_FORMAT:a=await em(t,o);break;case ea.ox.ADVANCED_VISUALIZATION:a=await eo(t,o);break;case ea.ox.TABLE:a=await eC(t,o);break;case ea.ox.CELL_UPDATE:a=await eE(t,o);break;case ea.ox.FORMAT:a=await eA(t,o);break;default:{let e=function(e){let a=e.data||{};return a.formula||a.range?{...e,type:ea.ox.FORMULA}:a.chart_type||a.title?{...e,type:ea.ox.CHART}:a.data&&Array.isArray(a.data)?{...e,type:ea.ox.TABLE}:a.order_by||a.direction?{...e,type:ea.ox.SORT}:a.condition||a.filter_column?{...e,type:ea.ox.FILTER}:a.background_color||a.text_color||a.format?{...e,type:ea.ox.FORMAT}:{...e,type:"GENERIC"}}(o);a=await ej(t,e)}}if(a&&(t=a.updatedData,"string"==typeof a.resultSummary?r.push(a.resultSummary):Array.isArray(a.resultSummary)&&r.push(String(a.resultSummary)),"modifiedCells"in a&&Array.isArray(a.modifiedCells)&&a.modifiedCells.length>0))for(let e of a.modifiedCells)s.push(e)}catch(t){let a=t instanceof Error?"Erro ao executar opera\xe7\xe3o ".concat(e.type,": ").concat(t.message):"Erro desconhecido ao executar opera\xe7\xe3o ".concat(e.type);console.error(a,t),o.push(a),r.push("⚠️ ".concat(a))}let n=s.filter((e,a,t)=>a===t.findIndex(a=>a.row===e.row&&a.col===e.col)),l={updatedData:t,resultSummary:r};return n.length>0&&(l.modifiedCells=n),o.length>0&&(l.errors=o),l}async function ej(e,a){return a.data&&"object"==typeof a.data&&("formula"in a.data||"formula_type"in a.data)?ef(e,{...a,type:ea.ox.FORMULA,data:a.data||{}}):a.data&&"object"==typeof a.data&&"chart_type"in a.data?ed(e,{...a,type:ea.ox.CHART,data:a.data||{}}):a.data&&"object"==typeof a.data&&"data"in a.data&&Array.isArray(a.data.data)?eC(e,{...a,type:ea.ox.TABLE,data:a.data||{}}):{updatedData:e,resultSummary:"Opera\xe7\xe3o gen\xe9rica n\xe3o suportada",modifiedCells:[]}}async function eA(e,a){let{target:t,format:r,decimals:s,locale:o,dateFormat:n,condition:l,value:i,color:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{};d.formatting&&"object"==typeof d.formatting?d.formatting=d.formatting:d.formatting={};let u="";try{if(d.headers&&Array.isArray(d.headers)&&d.rows){if((/^[A-Z]+$/.test(t)?t.charCodeAt(0)-65:d.headers.findIndex(e=>e===t))>=0){d.formatting||(d.formatting={});let e={type:r};"currency"===r?(e.decimals=s||2,e.locale=o||"pt-BR",u="Coluna ".concat(t," formatada como moeda com ").concat(s||2," casas decimais")):"percentage"===r?(e.decimals=s||0,u="Coluna ".concat(t," formatada como porcentagem com ").concat(s||0," casas decimais")):"date"===r?(e.dateFormat=n||"dd/mm/yyyy",u="Coluna ".concat(t," formatada como data no formato ").concat(n||"dd/mm/yyyy")):"conditional"===r&&(e.condition=l,e.value=i,e.color=c,u="Formata\xe7\xe3o condicional aplicada na coluna ".concat(t," (valores ").concat(">"===l?"maiores":"menores"," que ").concat(i," destacados em ").concat(c,")"));let a=d.formatting;a[t]=e,d.formatting=a}}else if(t&&"string"==typeof t&&t.includes(":")){d.formatting||(d.formatting={});let e=d.formatting;e[t]={type:r,decimals:s||2,locale:o||"pt-BR",dateFormat:n||"dd/mm/yyyy"},d.formatting=e,u="Intervalo ".concat(t," formatado como ").concat(r)}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao aplicar formata\xe7\xe3o:",e),Error("Falha ao aplicar formata\xe7\xe3o: ".concat(e instanceof Error?e.message:String(e)))}}async function eE(e,a){let{cell:t,value:r,_valueType:s}=a.data,o="object"==typeof e&&null!==e?{...e}:{};try{if(o.headers&&Array.isArray(o.headers)&&o.rows&&Array.isArray(o.rows)){var n,l;let e=(null===(n=t.match(/^[A-Z]+/))||void 0===n?void 0:n[0])||"",a=parseInt((null===(l=t.match(/\d+/))||void 0===l?void 0:l[0])||"0",10);if(e&&a>0){let t=e.charCodeAt(0)-65,s=a-1;if(s>=o.rows.length)for(;o.rows.length<=s;)o.rows.push(Array(o.headers.length).fill(""));o.rows[s]&&(o.rows[s][t]=r)}}else o[t]=r;return{updatedData:o,resultSummary:"C\xe9lula ".concat(t,' atualizada com o valor "').concat(r,'"')}}catch(e){throw console.error("Erro ao atualizar c\xe9lula:",e),Error("Falha ao atualizar c\xe9lula: ".concat(e instanceof Error?e.message:String(e)))}}async function eC(e,a){let{subtype:t,range:r,hasHeaders:s,allData:o,function:n,rowsField:l,columnsField:i,valuesField:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{},u="";try{switch(t){case"CREATE_TABLE":d.tables&&Array.isArray(d.tables)||(d.tables=[]),o?(d.isTable=!0,d.tableHeaders=s,u="Todos os dados foram convertidos em tabela"):r&&Array.isArray(d.tables)&&(d.tables.push({range:String(r),hasHeaders:s}),u="Intervalo ".concat(String(r)," convertido em tabela"));break;case"ADD_TOTAL_ROW":if(d.tables&&Array.isArray(d.tables)&&0!==d.tables.length)Array.isArray(d.tables)&&d.tables.length>0&&(u="Linha de total adicionada \xe0 tabela");else if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)){let e=d.headers,a=d.rows,t=Array(e.length).fill("");e.forEach((e,r)=>{a.some(e=>"number"==typeof e[r]||"string"==typeof e[r]&&!isNaN(Number(e[r])))&&(t[r]={formula:"=SOMA(".concat(String.fromCharCode(65+r),"2:").concat(String.fromCharCode(65+r)).concat(a.length+1,")"),result:a.reduce((e,a)=>{let t=parseFloat(String(a[r]));return e+(isNaN(t)?0:t)},0)})}),t[0]=t[0]||"Total",d.rows=[...a,t],u="Linha de total adicionada \xe0 tabela"}break;case"PIVOT_TABLE":if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)&&l&&i&&c){let e=d.headers.findIndex(e=>e===l),a=d.headers.findIndex(e=>e===i),t=d.headers.findIndex(e=>e===c);if(e>=0&&a>=0&&t>=0){let r={},s=new Set,o=new Set;Array.isArray(d.rows)&&d.rows.forEach(n=>{var l,i,c;let d=String(null!==(l=n[e])&&void 0!==l?l:""),u=String(null!==(i=n[a])&&void 0!==i?i:""),m=parseFloat(String(null!==(c=n[t])&&void 0!==c?c:"0"))||0;s.add(d),o.add(u),r[d]||(r[d]={}),r[d][u]||(r[d][u]=0),r[d][u]+=m});let n=["",...Array.from(o)],m=Array.from(s).map(e=>{let a=[e];return Array.from(o).forEach(t=>{var s;let o=String((null===(s=r[e])||void 0===s?void 0:s[t])||0);a.push(o)}),a});d.pivotTable={headers:n,rows:m,rowsField:l,columnsField:i,valuesField:c},u="Tabela din\xe2mica criada com ".concat(l," nas linhas, ").concat(i," nas colunas e ").concat(c," como valores")}}}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de tabela:",e),Error("Falha ao executar opera\xe7\xe3o de tabela: ".concat(e instanceof Error?e.message:String(e)))}}var eS=t(26309);t(25566);let ek=(0,eS.eI)("https://eliuoignzzxnjkcmmtml.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"}});var eO=t(9109).lW;let eR={EXCEL_FILES:"excel-files",EXPORTS:"exports",TEMPLATES:"templates",BACKUPS:"backups"};class eF{async uploadExcelFile(e,a,t){throw arguments.length>3&&void 0!==arguments[3]&&arguments[3],Error("Supabase admin client n\xe3o est\xe1 configurado")}async downloadExcelFile(e){throw arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultBucket,Error("Supabase admin client n\xe3o est\xe1 configurado")}async getSignedUrl(e){throw arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultBucket,Error("Supabase admin client n\xe3o est\xe1 configurado")}async deleteFile(e){throw arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultBucket,Error("Supabase admin client n\xe3o est\xe1 configurado")}async listUserFiles(e){throw arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultBucket,arguments.length>2&&arguments[2],Error("Supabase admin client n\xe3o est\xe1 configurado")}async createBackup(e,a,t){let r=JSON.stringify({workbook:e,timestamp:new Date().toISOString(),userId:a,workbookId:t}),s=eO.from(r,"utf-8"),o="backup_".concat(t,"_").concat(Date.now(),".json");return this.uploadExcelFile(s,a,t,{bucket:eR.BACKUPS,fileName:o,folder:"backups/".concat(a,"/").concat(t)})}async validateBucketPermissions(e){return arguments.length>1&&void 0!==arguments[1]&&arguments[1],{isValid:!1,error:"Supabase admin client n\xe3o est\xe1 configurado"}}async ensureBucketExists(e){throw arguments.length>1&&void 0!==arguments[1]&&arguments[1],Error("Supabase admin client n\xe3o est\xe1 configurado")}async getStorageStats(e){let a={totalFiles:0,totalSize:0,bucketStats:{}};try{for(let t of Object.values(eR)){let r=await this.listUserFiles(e,t),s=r.reduce((e,a)=>e+a.size,0);a.bucketStats[t]={files:r.length,size:s},a.totalFiles+=r.length,a.totalSize+=s}}catch(e){console.warn("Erro ao obter estat\xedsticas de storage:",e)}return a}constructor(){this.defaultBucket=eR.EXCEL_FILES}}let eT=new eF;var eI=t(9109).lW;class eD{async createBackup(e){try{let a=e.version||this.generateVersion(),t=new Date().toISOString(),r={workbook:e.data,metadata:{workbookId:e.workbookId,userId:e.userId,version:a,timestamp:t,description:e.description||"Backup autom\xe1tico ".concat(a)},checksum:await this.calculateChecksum(e.data)},s=JSON.stringify(r,null,2),o=eI.from(s,"utf-8"),n="backup_".concat(e.workbookId,"_").concat(a,"_").concat(Date.now(),".json"),l=await eT.uploadExcelFile(o,e.userId,e.workbookId,{bucket:"backups",fileName:n,folder:"backups/".concat(e.userId,"/").concat(e.workbookId),upsert:!1});return ei.logger.info("Backup criado com sucesso",{workbookId:e.workbookId,userId:e.userId,version:a,size:l.size,path:l.path}),!1!==e.autoCleanup&&await this.cleanupOldBackups(e.workbookId,e.userId,e.maxVersions||this.maxVersionsDefault),{success:!0,backupId:l.path,version:a,size:l.size}}catch(t){let a=t instanceof Error?t.message:"Erro desconhecido";return ei.logger.error("Erro ao criar backup",{workbookId:e.workbookId,userId:e.userId,error:a}),{success:!1,backupId:"",version:"",size:0,error:a}}}async listBackups(e,a){try{"backups/".concat(a,"/").concat(e);let r=[];for(let s of[])try{var t;let o={metadata:{workbookId:e,userId:a,version:"1.0",timestamp:new Date().toISOString(),description:"Backup"},checksum:"mock"};r.push({id:s.name,workbookId:o.metadata.workbookId,userId:o.metadata.userId,version:o.metadata.version,timestamp:o.metadata.timestamp,size:(null===(t=s.metadata)||void 0===t?void 0:t.size)||0,description:o.metadata.description,checksum:o.checksum})}catch(e){ei.logger.warn("Erro ao processar backup",{fileName:s.name,error:e instanceof Error?e.message:"Erro desconhecido"})}return r.sort((e,a)=>new Date(a.timestamp).getTime()-new Date(e.timestamp).getTime())}catch(t){return ei.logger.error("Erro ao listar backups",{workbookId:e,userId:a,error:t instanceof Error?t.message:"Erro desconhecido"}),[]}}async restoreBackup(e,a,t){try{let r={workbook:{},metadata:{workbookId:a,userId:t,version:"1.0"},checksum:"mock"};if(await this.calculateChecksum(r.workbook)!==r.checksum)throw Error("Backup corrompido: checksum n\xe3o confere");if(r.metadata.workbookId!==a||r.metadata.userId!==t)throw Error("Backup n\xe3o pertence ao usu\xe1rio ou workbook especificado");return ei.logger.info("Backup restaurado com sucesso",{backupId:e,workbookId:a,userId:t,version:r.metadata.version}),r.workbook}catch(r){throw ei.logger.error("Erro ao restaurar backup",{backupId:e,workbookId:a,userId:t,error:r instanceof Error?r.message:"Erro desconhecido"}),r}}async cleanupOldBackups(e,a,t){try{let r=await this.listBackups(e,a);if(r.length<=t)return;let s=r.slice(t);for(let t of s)try{await eT.deleteFile("backups",t.id),ei.logger.info("Backup antigo removido",{backupId:t.id,workbookId:e,userId:a,version:t.version})}catch(e){ei.logger.warn("Erro ao remover backup antigo",{backupId:t.id,error:e instanceof Error?e.message:"Erro desconhecido"})}ei.logger.info("Limpeza de backups conclu\xedda",{workbookId:e,userId:a,removedCount:s.length,remainingCount:t})}catch(t){ei.logger.error("Erro na limpeza de backups",{workbookId:e,userId:a,error:t instanceof Error?t.message:"Erro desconhecido"})}}generateVersion(){let e=new Date,a=e.getFullYear(),t=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),s=String(e.getHours()).padStart(2,"0"),o=String(e.getMinutes()).padStart(2,"0"),n=String(e.getSeconds()).padStart(2,"0");return"v".concat(a).concat(t).concat(r,"_").concat(s).concat(o).concat(n)}async calculateChecksum(e){let a=JSON.stringify(e),t=new TextEncoder().encode(a);if("undefined"!=typeof crypto&&crypto.subtle)return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("");let r=0;for(let e=0;e<a.length;e++)r=(r<<5)-r+a.charCodeAt(e),r&=r;return Math.abs(r).toString(16)}constructor(){this.maxVersionsDefault=10,this.cleanupIntervalHours=24}}let eL=new eD;var e_=t(9109).lW;class ez{async compressData(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=performance.now(),r={...this.defaultOptions,...a},s=this.toBuffer(e),o=s.length;if(o<r.threshold)return{compressed:!1,originalSize:o,compressedSize:o,compressionRatio:1,data:s,processingTime:performance.now()-t};try{let e;switch(r.format){case"gzip":e=await this.compressGzip(s,r.level);break;case"deflate":e=await this.compressDeflate(s,r.level);break;case"brotli":e=await this.compressBrotli(s,r.level);break;default:throw Error("Formato de compress\xe3o n\xe3o suportado: ".concat(r.format))}let a=e.length,n=o/a,l=performance.now()-t;return ei.logger.info("Compress\xe3o aplicada",{originalSize:o,compressedSize:a,compressionRatio:n.toFixed(2),format:r.format,level:r.level,processingTime:Math.round(l)}),{compressed:!0,originalSize:o,compressedSize:a,compressionRatio:n,data:e,format:r.format,processingTime:l}}catch(e){return ei.logger.error("Erro na compress\xe3o",{originalSize:o,format:r.format,error:e instanceof Error?e.message:"Erro desconhecido"}),{compressed:!1,originalSize:o,compressedSize:o,compressionRatio:1,data:s,processingTime:performance.now()-t}}}async decompressData(e,a){let t=this.toBuffer(e);try{switch(a){case"gzip":return await this.decompressGzip(t);case"deflate":return await this.decompressDeflate(t);case"brotli":return await this.decompressBrotli(t);default:throw Error("Formato de descompress\xe3o n\xe3o suportado: ".concat(a))}}catch(e){throw ei.logger.error("Erro na descompress\xe3o",{format:a,dataSize:t.length,error:e instanceof Error?e.message:"Erro desconhecido"}),e}}analyzeCompressionStrategy(e){let a=this.toBuffer(e),t=a.length,r=a.slice(0,Math.min(1024,t)),s=this.calculateEntropy(r);return t<51200?{level:1,format:"deflate"}:t<512e3?{level:s>.8?3:6,format:"gzip"}:t<5242880?{level:s>.8?6:9,format:"gzip"}:{level:9,format:"brotli"}}async compressGzip(e,a){if("undefined"!=typeof CompressionStream){let a=new CompressionStream("gzip"),t=a.writable.getWriter(),r=a.readable.getReader();t.write(e),t.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await r.read();o=a,e&&s.push(e)}return e_.concat(s.map(e=>e_.from(e)))}{let r=t(76467);return new Promise((t,s)=>{r.gzip(e,{level:a},(e,a)=>{e?s(e):t(a)})})}}async decompressGzip(e){if("undefined"!=typeof DecompressionStream){let a=new DecompressionStream("gzip"),t=a.writable.getWriter(),r=a.readable.getReader();t.write(e),t.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await r.read();o=a,e&&s.push(e)}return e_.concat(s.map(e=>e_.from(e)))}{let a=t(76467);return new Promise((t,r)=>{a.gunzip(e,(e,a)=>{e?r(e):t(a)})})}}async compressDeflate(e,a){if("undefined"!=typeof CompressionStream){let a=new CompressionStream("deflate"),t=a.writable.getWriter(),r=a.readable.getReader();t.write(e),t.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await r.read();o=a,e&&s.push(e)}return e_.concat(s.map(e=>e_.from(e)))}{let r=t(76467);return new Promise((t,s)=>{r.deflate(e,{level:a},(e,a)=>{e?s(e):t(a)})})}}async decompressDeflate(e){if("undefined"!=typeof DecompressionStream){let a=new DecompressionStream("deflate"),t=a.writable.getWriter(),r=a.readable.getReader();t.write(e),t.close();let s=[],o=!1;for(;!o;){let{value:e,done:a}=await r.read();o=a,e&&s.push(e)}return e_.concat(s.map(e=>e_.from(e)))}{let a=t(76467);return new Promise((t,r)=>{a.inflate(e,(e,a)=>{e?r(e):t(a)})})}}async compressBrotli(e,a){return this.compressGzip(e,a)}async decompressBrotli(e){return this.decompressGzip(e)}toBuffer(e){if(e_.isBuffer(e))return e;if(e instanceof Uint8Array)return e_.from(e);if("string"==typeof e)return e_.from(e,"utf-8");throw Error("Tipo de dados n\xe3o suportado para compress\xe3o")}calculateEntropy(e){let a=new Map;for(let t of e)a.set(t,(a.get(t)||0)+1);let t=0,r=e.length;for(let e of a.values()){let a=e/r;t-=a*Math.log2(a)}return t/8}constructor(){this.defaultOptions={level:6,threshold:102400,format:"gzip",chunkSize:65536}}}let eM=new ez;var eV=t(9109).lW;class eZ{async createVersion(e,a,t,r,s,o){try{let n=this.generateVersionNumber(),l=new Date().toISOString(),i=[];if(o){let r=await this.getVersionData(e,a,o);i=await this.calculateChanges(r,t)}let c=await eM.compressData(JSON.stringify(t),eM.analyzeCompressionStrategy(JSON.stringify(t))),d=await eL.createBackup({workbookId:e,userId:a,data:c.compressed?{compressed:!0,format:c.format,data:Array.from(c.data)}:t,version:n,description:"Vers\xe3o ".concat(n,": ").concat(r),autoCleanup:!1});if(!d.success)throw Error("Erro ao criar backup da vers\xe3o: ".concat(d.error));let u={id:d.backupId,workbookId:e,userId:a,version:n,timestamp:l,description:r,author:s,parentVersion:o||void 0,changes:i,size:d.size,compressed:c.compressed};return await this.saveVersionMetadata(u),await this.cleanupOldVersions(e,a),ei.logger.info("Nova vers\xe3o criada",{workbookId:e,userId:a,version:n,changesCount:i.length,size:d.size,compressed:c.compressed}),u}catch(t){throw ei.logger.error("Erro ao criar vers\xe3o",{workbookId:e,userId:a,error:t instanceof Error?t.message:"Erro desconhecido"}),t}}async listVersions(e,a){try{let t=await eL.listBackups(e,a),r=[];for(let e of t)try{let a=await this.getVersionMetadata(e.id);a&&r.push(a)}catch(a){ei.logger.warn("Erro ao carregar metadata da vers\xe3o",{backupId:e.id,error:a instanceof Error?a.message:"Erro desconhecido"})}return r.sort((e,a)=>new Date(a.timestamp).getTime()-new Date(e.timestamp).getTime())}catch(t){return ei.logger.error("Erro ao listar vers\xf5es",{workbookId:e,userId:a,error:t instanceof Error?t.message:"Erro desconhecido"}),[]}}async getVersionData(e,a,t){try{let r=(await this.listVersions(e,a)).find(e=>e.version===t);if(!r)throw Error("Vers\xe3o ".concat(t," n\xe3o encontrada"));let s=await eL.restoreBackup(r.id,e,a);if(r.compressed&&"object"==typeof s&&null!==s&&s.compressed){let e=eV.from(s.data),a=await eM.decompressData(e,s.format);return JSON.parse(a.toString())}return s}catch(r){throw ei.logger.error("Erro ao obter dados da vers\xe3o",{workbookId:e,userId:a,version:t,error:r instanceof Error?r.message:"Erro desconhecido"}),r}}async calculateDiff(e,a,t,r){try{let[s,o]=await Promise.all([this.getVersionData(e,a,t),this.getVersionData(e,a,r)]),n=await this.calculateChanges(s,o),l=n.filter(e=>"add"===e.action),i=n.filter(e=>"update"===e.action),c=n.filter(e=>"delete"===e.action);return{added:l,modified:i,deleted:c,summary:{totalChanges:n.length,cellChanges:n.filter(e=>"cell"===e.type).length,sheetChanges:n.filter(e=>"sheet"===e.type).length,structureChanges:n.filter(e=>"structure"===e.type).length}}}catch(s){throw ei.logger.error("Erro ao calcular diff",{workbookId:e,userId:a,version1:t,version2:r,error:s instanceof Error?s.message:"Erro desconhecido"}),s}}async mergeVersions(e,a,t,r,s,o){try{let[n,l,i]=await Promise.all([this.getVersionData(e,a,t),this.getVersionData(e,a,r),this.getVersionData(e,a,s)]),c=(await this.detectConflicts(n,l,i)).map(e=>{let a=null==o?void 0:o[e.location];return a?{...e,resolution:"string"==typeof a?a:"manual"}:e}),d=c.filter(e=>!e.resolution);if(d.length>0)return{success:!1,mergedData:null,conflicts:d,version:""};let u=await this.performMerge(n,l,i,c),m=await this.createVersion(e,a,u,"Merge de ".concat(r," e ").concat(s),"Sistema de Merge",t);return{success:!0,mergedData:u,conflicts:[],version:m.version}}catch(o){throw ei.logger.error("Erro no merge de vers\xf5es",{workbookId:e,userId:a,baseVersion:t,version1:r,version2:s,error:o instanceof Error?o.message:"Erro desconhecido"}),o}}async calculateChanges(e,a){let t=[],r=new Date().toISOString();try{let s=JSON.stringify(e,null,2),o=JSON.stringify(a,null,2);s!==o&&t.push({type:"structure",action:"update",location:"workbook",oldValue:e,newValue:a,timestamp:r})}catch(e){ei.logger.warn("Erro ao calcular mudan\xe7as detalhadas",{error:e instanceof Error?e.message:"Erro desconhecido"})}return t}async detectConflicts(e,a,t){let r=[];try{let s=JSON.stringify(e),o=JSON.stringify(a),n=JSON.stringify(t);o!==s&&n!==s&&o!==n&&r.push({location:"workbook",type:"structure",baseValue:e,version1Value:a,version2Value:t})}catch(e){ei.logger.warn("Erro ao detectar conflitos",{error:e instanceof Error?e.message:"Erro desconhecido"})}return r}async performMerge(e,a,t,r){for(let e of r){if("version1"===e.resolution)break;if("version2"===e.resolution)return t}return a}generateVersionNumber(){let e=new Date().getTime();return"".concat(e,".").concat(Math.floor(1e3*Math.random()))}async saveVersionMetadata(e){ei.logger.debug("Metadata da vers\xe3o salva",{version:e.version})}async getVersionMetadata(e){return null}async cleanupOldVersions(e,a){try{let t=await this.listVersions(e,a);if(t.length>this.maxVersionHistory)for(let r of t.slice(this.maxVersionHistory))try{ei.logger.info("Vers\xe3o antiga removida",{workbookId:e,userId:a,version:r.version})}catch(e){ei.logger.warn("Erro ao remover vers\xe3o antiga",{version:r.version,error:e instanceof Error?e.message:"Erro desconhecido"})}}catch(t){ei.logger.error("Erro na limpeza de vers\xf5es antigas",{workbookId:e,userId:a,error:t instanceof Error?t.message:"Erro desconhecido"})}}constructor(){this.maxVersionHistory=50}}let eP=new eZ;class eB{async processLargeDataset(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0,s=performance.now(),o={...this.defaultOptions,...t},n=[],l=[],i=this.estimateDataSize(e);i>1048576*o.maxMemoryUsage&&n.push("Dataset muito grande (".concat(Math.round(i/1024/1024),"MB). Considere dividir em partes menores."));let c=this.analyzeAndOptimize(e,o);l.push(...c.applied);let d=this.chunkData(e,c.chunkSize),u=[],m=0,p=0;ei.logger.info("Iniciando processamento otimizado",{totalRows:e.length,totalChunks:d.length,chunkSize:c.chunkSize,estimatedSize:Math.round(i/1024/1024)+"MB",optimizations:l});for(let e=0;e<d.length;e++){let t=d[e];if(!t)continue;let l=this.generateChunkKey(t);if(o.enableCache){let a=this.getFromCache(l);if(a){u.push(a),m++,r&&r((e+1)/d.length*100,{processingTime:performance.now()-s,chunksProcessed:e+1,cacheHits:m,cacheMisses:p});continue}p++}this.currentMemoryUsage>838860.8*o.maxMemoryUsage&&(await this.cleanupMemory(),n.push("Limpeza de mem\xf3ria executada durante processamento"));try{let n=await a(t,e,d.length);u.push(n),o.enableCache&&this.saveToCache(l,n),r&&r((e+1)/d.length*100,{processingTime:performance.now()-s,chunksProcessed:e+1,cacheHits:m,cacheMisses:p}),e%10==0&&await this.yield()}catch(a){throw ei.logger.error("Erro no processamento do chunk",{chunkIndex:e,chunkSize:(null==t?void 0:t.length)||0,error:a instanceof Error?a.message:"Erro desconhecido"}),a}}let h=performance.now()-s,f={processingTime:h,memoryUsage:this.currentMemoryUsage,chunksProcessed:d.length,cacheHits:m,cacheMisses:p,optimizationsApplied:l};return ei.logger.info("Processamento conclu\xeddo",{totalTime:Math.round(h),chunksProcessed:d.length,cacheEfficiency:m/(m+p)*100,memoryUsage:Math.round(this.currentMemoryUsage/1024/1024)+"MB"}),{data:u,metrics:f,warnings:n}}createLazyLoader(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,t=Math.ceil(e.length/a);return{loadPage:async r=>{if(r<0||r>=t)throw Error("P\xe1gina ".concat(r," inv\xe1lida. Total de p\xe1ginas: ").concat(t));let s=r*a,o=Math.min(s+a,e.length);return e.length>1e4&&await this.yield(),e.slice(s,o)},getTotalPages:()=>t,getPageInfo:t=>{let r=t*a,s=Math.min(r+a,e.length);return{start:r,end:s,size:s-r}}}}createSearchIndex(e,a){let t=new Map;for(let r=0;r<e.length;r++){let s=e[r];for(let e of a)if(s&&e<s.length)for(let a of String(s[e]).toLowerCase().split(/\s+/))a.length>2&&(t.has(a)||t.set(a,[]),t.get(a).push(r))}return t}searchWithIndex(e,a,t){let r=a.toLowerCase().split(/\s+/),s=new Set;if(r.length>0&&r[0]){s=new Set(e.get(r[0])||[]);for(let a=1;a<r.length;a++){let t=r[a];if(t){let a=new Set(e.get(t)||[]);s=new Set([...s].filter(e=>a.has(e)))}}}return Array.from(s).slice(0,1e3).map(e=>({rowIndex:e,row:t[e]||[]}))}analyzeAndOptimize(e,a){let t=[],r={...a},s=e.length,o=e.length>0?this.estimateDataSize(e)/e.length:0;return s>5e4?(r.chunkSize=Math.min(500,r.chunkSize),t.push("Chunk size reduzido para datasets grandes")):s>1e4&&(r.chunkSize=Math.min(1e3,r.chunkSize),t.push("Chunk size otimizado para datasets m\xe9dios")),o>1024&&(r.chunkSize=Math.min(r.chunkSize,200),t.push("Chunk size reduzido para linhas grandes")),s>1e3&&!r.enableCache&&(r.enableCache=!0,t.push("Cache habilitado automaticamente")),s>1e5&&!r.enableVirtualization&&(r.enableVirtualization=!0,t.push("Virtualiza\xe7\xe3o habilitada automaticamente")),{...r,applied:t}}chunkData(e,a){let t=[];for(let r=0;r<e.length;r+=a)t.push(e.slice(r,r+a));return t}estimateDataSize(e){if(0===e.length)return 0;let a=Math.min(100,e.length),t=0;for(let r=0;r<a;r++){let a=e[r];if(a)for(let e of a)"string"==typeof e?t+=2*e.length:"number"==typeof e?t+=8:e instanceof Date?t+=8:t+=2*JSON.stringify(e).length}return t/a*e.length}generateChunkKey(e){let a=JSON.stringify(e.slice(0,3)),t=0;for(let e=0;e<a.length;e++)t=(t<<5)-t+a.charCodeAt(e),t&=t;return"chunk_".concat(Math.abs(t),"_").concat(e.length)}getFromCache(e){let a=this.cache.get(e);return a?Date.now()-a.timestamp>this.cacheMaxAge?(this.cache.delete(e),null):a.data:null}saveToCache(e,a){let t=2*JSON.stringify(a).length;if(!(t>.1*this.cacheMaxSize)){for(;this.currentMemoryUsage+t>this.cacheMaxSize&&this.cache.size>0;){let e=this.cache.keys().next().value;if(e){let a=this.cache.get(e);a&&(this.currentMemoryUsage-=a.size),this.cache.delete(e)}else break}this.cache.set(e,{data:a,timestamp:Date.now(),size:t}),this.currentMemoryUsage+=t}}async cleanupMemory(){let e=Date.now(),a=0;for(let[t,r]of this.cache.entries())e-r.timestamp>.5*this.cacheMaxAge&&(this.currentMemoryUsage-=r.size,this.cache.delete(t),a++);void 0!==t.g&&t.g.gc&&t.g.gc(),ei.logger.debug("Limpeza de mem\xf3ria executada",{itemsRemoved:a,currentMemoryUsage:Math.round(this.currentMemoryUsage/1024/1024)+"MB",cacheSize:this.cache.size})}async yield(){return new Promise(e=>setTimeout(e,0))}getCacheStats(){let e=Date.now();for(let a of this.cache.values())e=Math.min(e,a.timestamp);return{size:this.cache.size,memoryUsage:this.currentMemoryUsage,hitRate:0,oldestItem:Date.now()-e}}constructor(){this.cache=new Map,this.cacheMaxSize=104857600,this.cacheMaxAge=18e5,this.currentMemoryUsage=0,this.defaultOptions={chunkSize:1e3,enableCache:!0,enableLazyLoading:!0,enableVirtualization:!0,maxMemoryUsage:512,enableProgressCallback:!0}}}let eU=new eB;function eq(){let[e,a]=(0,j.useState)(!1);return{isLoading:e,importExcel:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return null;let r=t.maxSize||10485760;a(!0);let s=A.toast.loading("Processando arquivo ".concat(e.name,"..."));try{var o,n,l,i,c,d;let a;if(!function(e){var a;if(["application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel.sheet.binary.macroEnabled.12","application/vnd.ms-excel.sheet.macroEnabled.12"].includes(e.type))return!0;let t=null===(a=e.name.split(".").pop())||void 0===a?void 0:a.toLowerCase();return"xls"===t||"xlsx"===t}(e))throw Error("Formato inv\xe1lido: envie um arquivo Excel (.xlsx ou .xls)");if(e.size>r)throw Error("Arquivo muito grande: o tamanho m\xe1ximo \xe9 ".concat((r/1048576).toFixed(0),"MB"));if(null===(o=t.onProgress)||void 0===o||o.call(t,10),t.enablePerformanceOptimization&&e.size>1048576){let r=await e.arrayBuffer(),s=new Uint8Array(r);a=(await eU.processLargeDataset([Array.from(s)],async a=>{if(a[0]&&Array.isArray(a[0])){let t=new File([new Uint8Array(a[0])],e.name,{type:e.type});return await eb(t)}return[]},{chunkSize:1e3,enableCache:!0,enableLazyLoading:!0},e=>{var a;return null===(a=t.onProgress)||void 0===a?void 0:a.call(t,20+.3*e)})).data.flat()}else a=await eb(e);if(null===(n=t.onProgress)||void 0===n||n.call(t,50),!a||0===a.length)throw Error("O arquivo n\xe3o cont\xe9m dados v\xe1lidos");if(t.validateSchema&&t.template&&(await eG(a,t.template),null===(i=t.onProgress)||void 0===i||i.call(t,70)),t.transformData&&t.template&&(await eH(a,t.template),null===(c=t.onProgress)||void 0===c||c.call(t,80)),t.enableBackup&&t.workbookId&&t.userId){try{let r=await eL.createBackup({workbookId:t.workbookId,userId:t.userId,data:{fileName:e.name,sheets:a},description:"Backup autom\xe1tico de importa\xe7\xe3o - ".concat(e.name),autoCleanup:!0});r.success&&A.toast.success("Backup autom\xe1tico criado",{description:"Vers\xe3o ".concat(r.version," salva com seguran\xe7a"),duration:2e3})}catch(e){console.warn("Erro ao criar backup autom\xe1tico:",e)}null===(d=t.onProgress)||void 0===d||d.call(t,90)}if(A.toast.success("".concat(e.name," carregado com sucesso!"),{id:s,duration:3e3}),null===(l=t.onProgress)||void 0===l||l.call(t,100),t.trackAnalytics&&"gtag"in window){let r=window.gtag;"function"==typeof r&&r("event","import_excel_advanced",{file_size:e.size,file_type:e.type,sheet_count:a.length,template_used:t.template||"none",validation_enabled:t.validateSchema||!1,transformation_enabled:t.transformData||!1})}let u={fileName:e.name,sheets:a};return t.onSuccess&&t.onSuccess(u),u}catch(e){return console.error("Erro ao processar arquivo Excel:",e),A.toast.error("Erro ao importar arquivo",{id:s,description:e instanceof Error?e.message:"N\xe3o foi poss\xedvel processar o arquivo. Tente novamente.",duration:4e3}),null}finally{a(!1)}},exportExcel:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"xlsx",s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};a(!0);let o=t.replace(/[^a-z0-9]/gi,"_").toLowerCase(),n=Date.now(),l="".concat(o,"_").concat(n),i=A.toast.loading("Preparando exporta\xe7\xe3o ".concat(r.toUpperCase(),"..."));try{if(!e||0===e.length)throw Error("N\xe3o h\xe1 dados para exportar");if("xlsx"===r){let a=await ey(e,t);if(s.enableCompression&&a.size>1048576)try{let e=await a.arrayBuffer(),t=await eM.compressData(new Uint8Array(e),eM.analyzeCompressionStrategy(new Uint8Array(e)));t.compressed&&(a=new Blob([t.data],{type:a.type}),A.toast.success("Arquivo comprimido",{description:"Tamanho reduzido em ".concat(Math.round((1-t.compressionRatio)*100),"%"),duration:2e3}))}catch(e){console.warn("Erro na compress\xe3o, usando arquivo original:",e)}if(s.enableVersioning&&s.workbookId&&s.userId)try{let a=await eP.createVersion(s.workbookId,s.userId,{fileName:t,sheets:e},s.versionDescription||"Exporta\xe7\xe3o ".concat(r.toUpperCase()," - ").concat(t),s.author||"Usu\xe1rio");A.toast.success("Nova vers\xe3o criada",{description:"Vers\xe3o ".concat(a.version," salva"),duration:2e3})}catch(e){console.warn("Erro ao criar vers\xe3o:",e)}if(!function(e,a){let t=window.URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download=a.endsWith(".xlsx")?a:"".concat(a,".xlsx"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(t),document.body.removeChild(r)}(a,"".concat(l,".xlsx")),s.trackAnalytics&&"gtag"in window){let a=window.gtag;"function"==typeof a&&a("event","export_excel",{workbook_id:s.workbookId,sheet_count:e.length,format:"xlsx"})}}else if("csv"===r&&(!function(e,a){if(!e||0===e.length)throw Error("N\xe3o h\xe1 dados para exportar");let t=e[0];if(!t||!t.data)throw Error("Planilha sem dados");let r=t.data,s=e=>{if(null==e||""===e)return"";let a=String(e);return a.includes(",")||a.includes('"')||a.includes("\n")?'"'.concat(a.replace(/"/g,'""'),'"'):a},o="";if(Array.isArray(r))o=r.map(e=>Array.isArray(e)?e.map(s).join(","):Object.values(e).map(s).join(",")).join("\n");else if("object"==typeof r&&null!==r){let e=[];Object.entries(r).forEach(a=>{let[t,r]=a,s=t.match(/([A-Z]+)([0-9]+)/);if(s){let a=(0,et.A0)(s,1),t=parseInt((0,et.A0)(s,2),10),o=0;for(let e=0;e<a.length;e++)o=26*o+a.charCodeAt(e)-65+1;for(o--;e.length<=t-1;)e.push([]);if(t>0){for(;e.length<t;)e.push([]);Array.isArray(e[t-1])||(e[t-1]=[]),e[t-1][o]=r}}}),o=e.filter(e=>Array.isArray(e)).map(e=>e.map(e=>s(null!=e?e:"")).join(",")).join("\n")}let n=new Blob([o],{type:"text/csv;charset=utf-8;"}),l=URL.createObjectURL(n),i=document.createElement("a");i.setAttribute("href",l),i.setAttribute("download","".concat(a,".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}(e,l),s.trackAnalytics&&"gtag"in window)){let a=window.gtag;"function"==typeof a&&a("event","export_csv",{workbook_id:s.workbookId,sheet_count:e.length,format:"csv"})}return A.toast.success("Exporta\xe7\xe3o ".concat(r.toUpperCase()," conclu\xedda"),{id:i,description:'Arquivo "'.concat(l,".").concat(r,'" baixado com sucesso!'),duration:3e3}),!0}catch(e){return console.error("Erro ao exportar ".concat(r,":"),e),A.toast.error("Erro na exporta\xe7\xe3o ".concat(r.toUpperCase()),{id:i,description:e instanceof Error?e.message:"N\xe3o foi poss\xedvel exportar para ".concat(r.toUpperCase(),". Tente novamente."),duration:4e3}),!1}finally{a(!1)}},listVersions:async(e,a)=>{try{return await eP.listVersions(e,a)}catch(e){return console.error("Erro ao listar vers\xf5es:",e),A.toast.error("Erro ao carregar vers\xf5es"),[]}},restoreVersion:async(e,t,r)=>{a(!0);try{let a=await eP.getVersionData(e,t,r);return A.toast.success("Vers\xe3o ".concat(r," restaurada com sucesso")),a}catch(e){return console.error("Erro ao restaurar vers\xe3o:",e),A.toast.error("Erro ao restaurar vers\xe3o"),null}finally{a(!1)}},listBackups:async(e,a)=>{try{return await eL.listBackups(e,a)}catch(e){return console.error("Erro ao listar backups:",e),A.toast.error("Erro ao carregar backups"),[]}},restoreBackup:async(e,t,r)=>{a(!0);try{let a=await eL.restoreBackup(e,t,r);return A.toast.success("Backup restaurado com sucesso"),a}catch(e){return console.error("Erro ao restaurar backup:",e),A.toast.error("Erro ao restaurar backup"),null}finally{a(!1)}},compareVersions:async(e,a,t,r)=>{try{return await eP.calculateDiff(e,a,t,r)}catch(e){return console.error("Erro ao comparar vers\xf5es:",e),A.toast.error("Erro ao comparar vers\xf5es"),null}},getCacheStats:()=>eU.getCacheStats()}}async function eG(e,a){let t={"financial-expenses":{Data:{required:!0,type:"date",description:"Data da transa\xe7\xe3o financeira"},Valor:{required:!0,type:"number",min:0,description:"Valor da transa\xe7\xe3o em reais"},Tipo:{required:!0,enum:["Receita","Despesa"],description:"Tipo da transa\xe7\xe3o"},Categoria:{required:!1,type:"string",maxLength:50,description:"Categoria da despesa/receita"},Descrição:{required:!1,type:"string",maxLength:200,description:"Descri\xe7\xe3o detalhada da transa\xe7\xe3o"}},"sales-data":{Data:{required:!0,type:"date",description:"Data da venda"},Produto:{required:!0,type:"string",minLength:2,maxLength:100,description:"Nome do produto vendido"},Quantidade:{required:!0,type:"number",min:1,description:"Quantidade vendida"},"Valor Unit\xe1rio":{required:!0,type:"number",min:0,description:"Pre\xe7o unit\xe1rio do produto"},Cliente:{required:!1,type:"string",maxLength:100,description:"Nome do cliente"},Vendedor:{required:!1,type:"string",maxLength:50,description:"Nome do vendedor respons\xe1vel"}},"employee-data":{Nome:{required:!0,type:"string",minLength:2,maxLength:100,description:"Nome completo do funcion\xe1rio"},Email:{required:!0,type:"email",description:"Email corporativo do funcion\xe1rio"},"Data Admiss\xe3o":{required:!0,type:"date",description:"Data de admiss\xe3o na empresa"},Cargo:{required:!0,type:"string",minLength:2,maxLength:50,description:"Cargo do funcion\xe1rio"},Salário:{required:!1,type:"number",min:0,description:"Sal\xe1rio base do funcion\xe1rio"},Departamento:{required:!1,type:"string",maxLength:50,description:"Departamento do funcion\xe1rio"}},"inventory-data":{Código:{required:!0,type:"string",pattern:"^[A-Z0-9]{3,10}$",description:"C\xf3digo \xfanico do produto"},Nome:{required:!0,type:"string",minLength:2,maxLength:100,description:"Nome do produto"},Quantidade:{required:!0,type:"number",min:0,description:"Quantidade em estoque"},Preço:{required:!0,type:"number",min:0,description:"Pre\xe7o unit\xe1rio do produto"},Categoria:{required:!1,type:"string",maxLength:50,description:"Categoria do produto"}},"project-tasks":{Tarefa:{required:!0,type:"string",minLength:5,maxLength:200,description:"Descri\xe7\xe3o da tarefa"},Responsável:{required:!0,type:"string",maxLength:50,description:"Pessoa respons\xe1vel pela tarefa"},"Data In\xedcio":{required:!0,type:"date",description:"Data de in\xedcio da tarefa"},"Data Fim":{required:!1,type:"date",description:"Data prevista para conclus\xe3o"},Status:{required:!0,enum:["N\xe3o Iniciado","Em Andamento","Conclu\xeddo","Cancelado"],description:"Status atual da tarefa"},Prioridade:{required:!1,enum:["Baixa","M\xe9dia","Alta","Cr\xedtica"],description:"Prioridade da tarefa"}}}[a]||{},r=[];for(let a of e){if(!Array.isArray(a.data))continue;if(0===a.data.length){r.push("Planilha '".concat(a.name,"' est\xe1 vazia"));continue}let e=Array.isArray(a.data[0])?a.data[0]:Object.keys(a.data[0]||{});for(let s of Object.keys(t).filter(e=>t[e].required))e.includes(s)||r.push("Coluna obrigat\xf3ria '".concat(s,"' n\xe3o encontrada na planilha '").concat(a.name,"'"));let o=a.data.slice(1);for(let a=0;a<o.length;a++){let n=o[a],l=a+2;for(let[a,o]of Object.entries(t)){let t=Array.isArray(n)?n[e.indexOf(a)]:n[a];if(o.required&&(null==t||""===t)){r.push("Linha ".concat(l,": Campo obrigat\xf3rio '").concat(a,"' est\xe1 vazio"));continue}if(null!=t&&""!==t){var s;"number"===o.type&&isNaN(Number(t))&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ser um n\xfamero v\xe1lido")),"email"!==o.type||(s=String(t),/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))||r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ser um email v\xe1lido")),"date"===o.type&&!function(e){if(!e)return!1;if(e instanceof Date)return!isNaN(e.getTime());let a=String(e);if(isNaN(new Date(a).getTime())){let e=a.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);if(e){let[,a,t,r]=e;if(a&&t&&r)return!isNaN(new Date(parseInt(r),parseInt(t)-1,parseInt(a)).getTime())}return!1}return!0}(t)&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ser uma data v\xe1lida")),"url"===o.type&&!function(e){try{return new URL(e),!0}catch(e){return!1}}(String(t))&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ser uma URL v\xe1lida")),o.enum&&!o.enum.includes(t)&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ser um dos valores: ").concat(o.enum.join(", "))),void 0!==o.min&&Number(t)<o.min&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ser maior ou igual a ").concat(o.min)),void 0!==o.max&&Number(t)>o.max&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ser menor ou igual a ").concat(o.max)),void 0!==o.minLength&&String(t).length<o.minLength&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ter pelo menos ").concat(o.minLength," caracteres")),void 0!==o.maxLength&&String(t).length>o.maxLength&&r.push("Linha ".concat(l,": Campo '").concat(a,"' deve ter no m\xe1ximo ").concat(o.maxLength," caracteres")),o.pattern&&!new RegExp(o.pattern).test(String(t))&&r.push("Linha ".concat(l,": Campo '").concat(a,"' n\xe3o atende ao padr\xe3o esperado"))}}}}if(r.length>0)throw Error("Erros de valida\xe7\xe3o encontrados:\n".concat(r.join("\n")))}async function eH(e,a){let t={"financial-expenses":{Valor:{type:"currency"},Data:{type:"date"}},"sales-data":{"Valor Unit\xe1rio":{type:"currency"},Total:{type:"currency"},Data:{type:"date"}},"employee-data":{Nome:{type:"uppercase"},Email:{type:"lowercase"},"Data Admiss\xe3o":{type:"date"}}}[a]||{};for(let a of e)Array.isArray(a.data)&&(a.data=a.data.map(e=>{let a={...e};for(let[e,r]of Object.entries(t))if(void 0!==a[e])switch(r.type){case"currency":a[e]=function(e){let a=Number(e);return isNaN(a)?String(e):new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(a)}(a[e]);break;case"date":a[e]=function(e){try{let a=new Date(e);if(isNaN(a.getTime()))return String(e);return a.toLocaleDateString("pt-BR")}catch(a){return String(e)}}(a[e]);break;case"uppercase":a[e]=String(a[e]).toUpperCase();break;case"lowercase":a[e]=String(a[e]).toLowerCase()}return a}))}var eJ=t(24241),eW=t(54662),eQ=t(69324),eY=t(22468);let eX=j.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,o.jsx)(eQ.fC,{ref:a,className:(0,T.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...r,children:(0,o.jsx)(eQ.z$,{className:(0,T.cn)("flex items-center justify-center text-current"),children:(0,o.jsx)(eY.Z,{className:"h-4 w-4"})})})});eX.displayName=eQ.fC.displayName;var eK=t(2128),e$=t(77209),e0=t(70402),e1=t(86864),e2=t(87992);let e4=[{id:"xlsx",name:"Excel (.xlsx)",icon:(0,o.jsx)(l.Z,{className:"h-4 w-4"})},{id:"csv",name:"CSV (.csv)",icon:(0,o.jsx)(l.Z,{className:"h-4 w-4"})},{id:"json",name:"JSON (.json)",icon:(0,o.jsx)(l.Z,{className:"h-4 w-4"})},{id:"pdf",name:"PDF (.pdf)",icon:(0,o.jsx)(l.Z,{className:"h-4 w-4"})}];function e5(e){let{open:a,onOpenChange:t,workbookId:r,onExport:s}=e,[n,l]=(0,j.useState)(["xlsx"]),[i,c]=(0,j.useState)("immediate"),[d,u]=(0,j.useState)(""),[m,p]=(0,j.useState)(""),[h,f]=(0,j.useState)(),[g,x]=(0,j.useState)(!1),[v,y]=(0,j.useState)(!1),[b,w]=(0,j.useState)(!0),[N,A]=(0,j.useState)("{{workbook}}_{{date}}_{{format}}"),E=e=>{l(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},C=n.length>0&&("immediate"===i||d&&m);return(0,o.jsx)(eW.Vq,{open:a,onOpenChange:t,children:(0,o.jsxs)(eW.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,o.jsx)(eW.fK,{children:(0,o.jsxs)(eW.$N,{className:"flex items-center gap-2",children:[(0,o.jsx)(K.Z,{className:"h-5 w-5"}),"Export em Lote - Configura\xe7\xf5es Avan\xe7adas"]})}),(0,o.jsxs)(e1.mQ,{defaultValue:"formats",className:"space-y-6",children:[(0,o.jsxs)(e1.dr,{className:"grid w-full grid-cols-4",children:[(0,o.jsx)(e1.SP,{value:"formats",children:"Formatos"}),(0,o.jsx)(e1.SP,{value:"schedule",children:"Agendamento"}),(0,o.jsx)(e1.SP,{value:"options",children:"Op\xe7\xf5es"}),(0,o.jsx)(e1.SP,{value:"preview",children:"Preview"})]}),(0,o.jsx)(e1.nU,{value:"formats",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsx)(G.ll,{className:"text-lg",children:"Selecionar Formatos de Export"})}),(0,o.jsxs)(G.aY,{children:[(0,o.jsx)("div",{className:"grid grid-cols-2 gap-4",children:e4.map(e=>(0,o.jsxs)("div",{className:"flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ".concat(n.includes(e.id)?"border-primary bg-primary/5":"border-border hover:bg-muted/50"),onClick:()=>E(e.id),children:[(0,o.jsx)(eX,{checked:n.includes(e.id),onCheckedChange:()=>E(e.id)}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,o.jsx)("span",{className:"font-medium",children:e.name})]})]},e.id))}),0===n.length&&(0,o.jsx)(e2.bZ,{className:"mt-4",children:(0,o.jsx)(e2.X,{children:"Selecione pelo menos um formato para continuar."})})]})]})}),(0,o.jsx)(e1.nU,{value:"schedule",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsx)(G.ll,{className:"text-lg",children:"Configurar Agendamento"})}),(0,o.jsxs)(G.aY,{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(e0._,{children:"Tipo de Execu\xe7\xe3o"}),(0,o.jsxs)(eK.Ph,{value:i,onValueChange:e=>c(e),children:[(0,o.jsx)(eK.i4,{children:(0,o.jsx)(eK.ki,{})}),(0,o.jsxs)(eK.Bw,{children:[(0,o.jsx)(eK.Ql,{value:"immediate",children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(Y.Z,{className:"h-4 w-4"}),"Executar Agora"]})}),(0,o.jsx)(eK.Ql,{value:"scheduled",children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(eJ.Z,{className:"h-4 w-4"}),"Agendar Execu\xe7\xe3o"]})})]})]})]}),"scheduled"===i&&(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{htmlFor:"date",children:"Data"}),(0,o.jsx)(e$.I,{id:"date",type:"date",value:d,onChange:e=>u(e.target.value),min:new Date().toISOString().split("T")[0]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{htmlFor:"time",children:"Hor\xe1rio"}),(0,o.jsx)(e$.I,{id:"time",type:"time",value:m,onChange:e=>p(e.target.value)})]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{children:"Recorr\xeancia (Opcional)"}),(0,o.jsxs)(eK.Ph,{value:h||"",onValueChange:e=>f(e),children:[(0,o.jsx)(eK.i4,{children:(0,o.jsx)(eK.ki,{placeholder:"Selecionar recorr\xeancia"})}),(0,o.jsxs)(eK.Bw,{children:[(0,o.jsx)(eK.Ql,{value:"",children:"Sem recorr\xeancia"}),(0,o.jsx)(eK.Ql,{value:"daily",children:"Di\xe1rio"}),(0,o.jsx)(eK.Ql,{value:"weekly",children:"Semanal"}),(0,o.jsx)(eK.Ql,{value:"monthly",children:"Mensal"})]})]})]})]})]})]})}),(0,o.jsx)(e1.nU,{value:"options",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsx)(G.ll,{className:"text-lg",children:"Op\xe7\xf5es de Export"})}),(0,o.jsxs)(G.aY,{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"compression",checked:g,onCheckedChange:e=>x(!0===e)}),(0,o.jsx)(e0._,{htmlFor:"compression",children:"Compactar arquivos em ZIP"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"splitBySheets",checked:v,onCheckedChange:e=>y(!0===e)}),(0,o.jsx)(e0._,{htmlFor:"splitBySheets",children:"Separar por planilhas"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"includeMetadata",checked:b,onCheckedChange:e=>w(!0===e)}),(0,o.jsx)(e0._,{htmlFor:"includeMetadata",children:"Incluir metadados"})]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{htmlFor:"naming",children:"Padr\xe3o de Nomenclatura"}),(0,o.jsx)(e$.I,{id:"naming",value:N,onChange:e=>A(e.target.value),placeholder:"{{workbook}}_{{date}}_{{format}}"}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Vari\xe1veis dispon\xedveis: ","{workbook}",", ","{date}",", ","{time}",", ","{format}",", ","{sheet}"]})]})]})]})}),(0,o.jsx)(e1.nU,{value:"preview",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsx)(G.ll,{className:"text-lg",children:"Preview da Configura\xe7\xe3o"})}),(0,o.jsx)(G.aY,{className:"space-y-4",children:(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)(e0._,{className:"text-sm font-medium",children:"Formatos Selecionados:"}),(0,o.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:n.map(e=>{let a=e4.find(a=>a.id===e);return(0,o.jsx)(M.C,{variant:"secondary",children:null==a?void 0:a.name},e)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(e0._,{className:"text-sm font-medium",children:"Agendamento:"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"immediate"===i?"Executar imediatamente":"Agendado para ".concat(d," \xe0s ").concat(m).concat(h?" (".concat(h,")"):"")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(e0._,{className:"text-sm font-medium",children:"Op\xe7\xf5es:"}),(0,o.jsxs)("ul",{className:"text-sm text-muted-foreground mt-1 space-y-1",children:[g&&(0,o.jsx)("li",{children:"• Compacta\xe7\xe3o habilitada"}),v&&(0,o.jsx)("li",{children:"• Separar por planilhas"}),b&&(0,o.jsx)("li",{children:"• Incluir metadados"}),(0,o.jsxs)("li",{children:["• Padr\xe3o de nome: ",N]})]})]})]})})]})})]}),(0,o.jsxs)(eW.cN,{children:[(0,o.jsx)(k.Button,{variant:"outline",onClick:()=>t(!1),children:"Cancelar"}),(0,o.jsx)(k.Button,{onClick:()=>{s({formats:n,schedule:"scheduled"===i?{type:"scheduled",datetime:d&&m?new Date("".concat(d,"T").concat(m)):void 0,recurring:h}:{type:"immediate"},compression:g,splitBySheets:v,includeMetadata:b,customNaming:N})},disabled:!C,children:"immediate"===i?"Exportar Agora":"Agendar Export"})]})]})})}var e3=t(24501),e6=t(74453);function e8(e){let{open:a,onOpenChange:t,workbookId:r,workbookName:s,sheets:n,onExport:i}=e,[c,d]=(0,j.useState)("xlsx"),[u,m]=(0,j.useState)(!0),[p,h]=(0,j.useState)(!0),[f,g]=(0,j.useState)(!0),[x,v]=(0,j.useState)(""),[y,b]=(0,j.useState)([50]),[w,N]=(0,j.useState)("portrait"),[A,E]=(0,j.useState)("A4"),[C,S]=(0,j.useState)([]),[O,R]=(0,j.useState)("1"),[F,T]=(0,j.useState)(""),[I,L]=(0,j.useState)("#3b82f6"),[_,z]=(0,j.useState)(!0),[M,V]=(0,j.useState)([12]),Z=n.length>0&&n[0]&&Array.isArray(n[0].data)&&n[0].data.length>0?Object.keys(n[0].data[0]):[],P=e=>{S(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])};return(0,o.jsx)(eW.Vq,{open:a,onOpenChange:t,children:(0,o.jsxs)(eW.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,o.jsx)(eW.fK,{children:(0,o.jsxs)(eW.$N,{className:"flex items-center gap-2",children:[(0,o.jsx)(X.Z,{className:"h-5 w-5"}),"Op\xe7\xf5es Avan\xe7adas de Export - ",s]})}),(0,o.jsxs)(e1.mQ,{defaultValue:"general",className:"space-y-6",children:[(0,o.jsxs)(e1.dr,{className:"grid w-full grid-cols-4",children:[(0,o.jsx)(e1.SP,{value:"general",children:"Geral"}),(0,o.jsx)(e1.SP,{value:"filters",children:"Filtros"}),(0,o.jsx)(e1.SP,{value:"styling",children:"Estilo"}),(0,o.jsx)(e1.SP,{value:"security",children:"Seguran\xe7a"})]}),(0,o.jsx)(e1.nU,{value:"general",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsxs)(G.ll,{className:"text-lg flex items-center gap-2",children:[(0,o.jsx)(l.Z,{className:"h-5 w-5"}),"Configura\xe7\xf5es Gerais"]})}),(0,o.jsxs)(G.aY,{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{children:"Formato de Export"}),(0,o.jsxs)(eK.Ph,{value:c,onValueChange:e=>d(e),children:[(0,o.jsx)(eK.i4,{children:(0,o.jsx)(eK.ki,{})}),(0,o.jsxs)(eK.Bw,{children:[(0,o.jsx)(eK.Ql,{value:"xlsx",children:"Excel (.xlsx)"}),(0,o.jsx)(eK.Ql,{value:"csv",children:"CSV (.csv)"}),(0,o.jsx)(eK.Ql,{value:"pdf",children:"PDF (.pdf)"})]})]})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"headers",checked:u,onCheckedChange:e=>m(!0===e)}),(0,o.jsx)(e0._,{htmlFor:"headers",children:"Incluir cabe\xe7alhos"})]}),"xlsx"===c&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"formulas",checked:p,onCheckedChange:e=>h(!0===e)}),(0,o.jsx)(e0._,{htmlFor:"formulas",children:"Incluir f\xf3rmulas"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"formatting",checked:f,onCheckedChange:e=>g(!0===e)}),(0,o.jsx)(e0._,{htmlFor:"formatting",children:"Incluir formata\xe7\xe3o"})]})]})]}),"pdf"===c&&(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{children:"Orienta\xe7\xe3o"}),(0,o.jsxs)(eK.Ph,{value:w,onValueChange:e=>N(e),children:[(0,o.jsx)(eK.i4,{children:(0,o.jsx)(eK.ki,{})}),(0,o.jsxs)(eK.Bw,{children:[(0,o.jsx)(eK.Ql,{value:"portrait",children:"Retrato"}),(0,o.jsx)(eK.Ql,{value:"landscape",children:"Paisagem"})]})]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{children:"Tamanho da P\xe1gina"}),(0,o.jsxs)(eK.Ph,{value:A,onValueChange:e=>E(e),children:[(0,o.jsx)(eK.i4,{children:(0,o.jsx)(eK.ki,{})}),(0,o.jsxs)(eK.Bw,{children:[(0,o.jsx)(eK.Ql,{value:"A4",children:"A4"}),(0,o.jsx)(eK.Ql,{value:"A3",children:"A3"}),(0,o.jsx)(eK.Ql,{value:"Letter",children:"Letter"})]})]})]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)(e0._,{children:["N\xedvel de Compress\xe3o: ",y[0]||50,"%"]}),(0,o.jsx)("div",{className:"w-full",children:(0,o.jsx)("input",{type:"range",value:y[0]||50,onChange:e=>b([parseInt(e.target.value)]),max:100,min:0,step:10,className:"w-full"})})]})]})]})}),(0,o.jsx)(e1.nU,{value:"filters",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsxs)(G.ll,{className:"text-lg flex items-center gap-2",children:[(0,o.jsx)(D.Z,{className:"h-5 w-5"}),"Filtros de Dados"]})}),(0,o.jsxs)(G.aY,{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{children:"Colunas a Exportar"}),(0,o.jsx)("div",{className:"grid grid-cols-3 gap-2 max-h-40 overflow-y-auto",children:Z.map(e=>(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"col-".concat(e),checked:C.includes(e),onCheckedChange:()=>P(e)}),(0,o.jsx)(e0._,{htmlFor:"col-".concat(e),className:"text-sm",children:e})]},e))}),0===C.length&&(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Nenhuma coluna selecionada = todas as colunas"})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{htmlFor:"rowStart",children:"Linha Inicial"}),(0,o.jsx)(e$.I,{id:"rowStart",type:"number",value:O,onChange:e=>R(e.target.value),placeholder:"1",min:"1"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{htmlFor:"rowEnd",children:"Linha Final"}),(0,o.jsx)(e$.I,{id:"rowEnd",type:"number",value:F,onChange:e=>T(e.target.value),placeholder:"Todas",min:"1"})]})]})]})]})}),(0,o.jsx)(e1.nU,{value:"styling",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsxs)(G.ll,{className:"text-lg flex items-center gap-2",children:[(0,o.jsx)(e3.Z,{className:"h-5 w-5"}),"Personaliza\xe7\xe3o de Estilo"]})}),(0,o.jsxs)(G.aY,{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{htmlFor:"headerColor",children:"Cor do Cabe\xe7alho"}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(e$.I,{id:"headerColor",type:"color",value:I,onChange:e=>L(e.target.value),className:"w-16 h-10"}),(0,o.jsx)(e$.I,{value:I,onChange:e=>L(e.target.value),placeholder:"#3b82f6"})]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eX,{id:"alternateRows",checked:_,onCheckedChange:e=>z(!0===e)}),(0,o.jsx)(e0._,{htmlFor:"alternateRows",children:"Linhas alternadas"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)(e0._,{children:["Tamanho da Fonte: ",M[0]||12,"px"]}),(0,o.jsx)("div",{className:"w-full",children:(0,o.jsx)("input",{type:"range",value:M[0]||12,onChange:e=>V([parseInt(e.target.value)]),max:24,min:8,step:1,className:"w-full"})})]})]})]})}),(0,o.jsx)(e1.nU,{value:"security",className:"space-y-4",children:(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsxs)(G.ll,{className:"text-lg flex items-center gap-2",children:[(0,o.jsx)(e6.Z,{className:"h-5 w-5"}),"Configura\xe7\xf5es de Seguran\xe7a"]})}),(0,o.jsxs)(G.aY,{className:"space-y-4",children:["xlsx"===c&&(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(e0._,{htmlFor:"password",children:"Senha de Prote\xe7\xe3o (Opcional)"}),(0,o.jsx)(e$.I,{id:"password",type:"password",value:x,onChange:e=>v(e.target.value),placeholder:"Digite uma senha para proteger o arquivo"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Deixe em branco para n\xe3o proteger o arquivo"})]}),"xlsx"!==c&&(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Prote\xe7\xe3o por senha dispon\xedvel apenas para arquivos Excel (.xlsx)"})]})]})})]}),(0,o.jsxs)(eW.cN,{children:[(0,o.jsx)(k.Button,{variant:"outline",onClick:()=>t(!1),children:"Cancelar"}),(0,o.jsx)(k.Button,{onClick:()=>{i({format:c,includeHeaders:u,includeFormulas:p,includeFormatting:f,password:x||void 0,compression:y[0]||50,pageOrientation:"pdf"===c?w:void 0,pageSize:"pdf"===c?A:void 0,filters:C.length>0||O||F?{columns:C.length>0?C:Z,rows:{start:parseInt(O)||1,end:parseInt(F)||999999}}:void 0,customStyles:{headerColor:I,alternateRows:_,fontSize:M[0]||12}})},children:"Exportar com Op\xe7\xf5es"})]})]})})}function e9(e){let{workbookId:a,workbookName:t,sheets:r,variant:s="outline",size:n="sm",enableAdvancedExport:l=!1,allowBatchExport:i=!1}=e,{exportExcel:c,isLoading:d}=eq(),[u,m]=(0,j.useState)(!1),[p,h]=(0,j.useState)(!1),f=async()=>{await c(r,t,"xlsx",{trackAnalytics:!0,workbookId:a})},g=async()=>{await c(r,t,"csv",{trackAnalytics:!0,workbookId:a})};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)($.h_,{children:[(0,o.jsx)($.$F,{asChild:!0,children:(0,o.jsx)(k.Button,{variant:s,size:n,disabled:!r||0===r.length||d,className:"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2",children:d?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Exportando..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(Y.Z,{className:"h-4 w-4"}),"Exportar"]})})}),(0,o.jsxs)($.AW,{align:"end",children:[(0,o.jsxs)($.Xi,{onClick:()=>f(),children:[(0,o.jsx)(Y.Z,{className:"h-4 w-4 mr-2"}),(0,o.jsx)("span",{children:"Exportar como Excel (.xlsx)"})]}),(0,o.jsxs)($.Xi,{onClick:()=>g(),children:[(0,o.jsx)(Y.Z,{className:"h-4 w-4 mr-2"}),(0,o.jsx)("span",{children:"Exportar como CSV (.csv)"})]}),l&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)($.VD,{}),(0,o.jsxs)($.Xi,{onClick:()=>m(!0),children:[(0,o.jsx)(X.Z,{className:"h-4 w-4 mr-2"}),(0,o.jsx)("span",{children:"Op\xe7\xf5es Avan\xe7adas"})]})]}),i&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)($.VD,{}),(0,o.jsxs)($.Xi,{onClick:()=>h(!0),children:[(0,o.jsx)(K.Z,{className:"h-4 w-4 mr-2"}),(0,o.jsx)("span",{children:"Export em Lote"})]})]})]})]}),l&&(0,o.jsx)(e8,{open:u,onOpenChange:m,workbookId:a,workbookName:t,sheets:r,onExport:e=>{m(!1)}}),i&&(0,o.jsx)(e5,{open:p,onOpenChange:h,workbookId:a,onExport:e=>{h(!1)}})]})}var e7=t(56160),ae=t(74740),aa=t(11240),at=t(57226),ar=t(30998);class as{subscribeToWorkbook(e,a){let t="workbook:".concat(e);if(this.channels.has(t))return this.channels.get(t);let r=ek.channel(t).on("postgres_changes",{event:"*",schema:"public",table:"Workbook",filter:"id=eq.".concat(e)},e=>{a.onWorkbookChange&&a.onWorkbookChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Sheet",filter:"workbookId=eq.".concat(e)},e=>{a.onSheetChange&&a.onSheetChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Cell",filter:"sheetId=in.(".concat(e,")")},t=>{if(a.onCellChange&&t.new){let r=t.new;a.onCellChange({workbookId:e,sheetId:r.sheetId,cellAddress:r.address,value:r.value,userId:r.updatedBy||"unknown",timestamp:r.updatedAt||new Date().toISOString()})}}).subscribe();return this.channels.set(t,r),r}subscribeToUserPresence(e,a,t){let r="presence:".concat(e);if(this.presenceChannels.has(r))return this.presenceChannels.get(r);let s=ek.channel(r,{config:{presence:{key:a.id}}}).on("presence",{event:"sync"},()=>{Object.entries(s.presenceState()).forEach(a=>{let[r,s]=a,o=s[0],n=o.cursor;t({userId:r,userName:o.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...n&&{cursor:n}})})}).on("presence",{event:"join"},a=>{let{key:r,newPresences:s}=a,o=s[0],n=o.cursor;t({userId:r,userName:o.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...n&&{cursor:n}})}).on("presence",{event:"leave"},a=>{let{key:r,leftPresences:s}=a,o=s[0],n=o.cursor;t({userId:r,userName:o.name,workbookId:e,isOnline:!1,lastSeen:new Date().toISOString(),...n&&{cursor:n}})}).subscribe(async e=>{"SUBSCRIBED"===e&&await s.track({name:a.name,joinedAt:new Date().toISOString()})});return this.presenceChannels.set(r,s),s}async updateUserCursor(e,a){let t=this.presenceChannels.get("presence:".concat(e));t&&await t.track({cursor:a,lastActivity:new Date().toISOString()})}async broadcastCellChange(e,a){let t=this.channels.get("workbook:".concat(e));t&&await t.send({type:"broadcast",event:"cell_change",payload:{...a,timestamp:new Date().toISOString()}})}unsubscribeFromWorkbook(e){let a="workbook:".concat(e),t=this.channels.get(a);t&&(ek.removeChannel(t),this.channels.delete(a))}unsubscribeFromPresence(e){let a="presence:".concat(e),t=this.presenceChannels.get(a);t&&(ek.removeChannel(t),this.presenceChannels.delete(a))}unsubscribeAll(){this.channels.forEach(e=>{ek.removeChannel(e)}),this.channels.clear(),this.presenceChannels.forEach(e=>{ek.removeChannel(e)}),this.presenceChannels.clear()}getConnectionStatus(){return{connected:ek.realtime.isConnected(),activeChannels:this.channels.size,presenceChannels:this.presenceChannels.size}}async reconnectAll(){for(let[,e]of this.channels)e.subscribe();for(let[,e]of this.presenceChannels)e.subscribe()}constructor(){this.channels=new Map,this.presenceChannels=new Map}}let ao=new as;function an(e){let{data:a}=(0,ar.useSession)(),[t,r]=(0,j.useState)(!1),[s,o]=(0,j.useState)(new Map),[n,l]=(0,j.useState)([]),i=(0,j.useRef)(null),c=(0,j.useCallback)(e=>{window.dispatchEvent(new CustomEvent("workbook-changed",{detail:e}))},[]),d=(0,j.useCallback)(e=>{window.dispatchEvent(new CustomEvent("sheet-changed",{detail:e}))},[]),u=(0,j.useCallback)(e=>{l(a=>[e,...a.slice(0,49)]),window.dispatchEvent(new CustomEvent("cell-changed",{detail:e}))},[]),m=(0,j.useCallback)(e=>{o(a=>{let t=new Map(a);if(e.isOnline)t.set(e.userId,{userId:e.userId,userName:e.userName,isOnline:!0,lastSeen:e.lastSeen,cursor:e.cursor});else{let a=t.get(e.userId);a&&t.set(e.userId,{...a,isOnline:!1,lastSeen:e.lastSeen})}return t}),window.dispatchEvent(new CustomEvent("user-presence-changed",{detail:e}))},[]),p=(0,j.useCallback)(async(t,r)=>{if(e&&(null==a?void 0:a.user))try{await ao.updateUserCursor(e,{sheetId:t,cellAddress:r})}catch(e){console.error("Erro ao atualizar cursor:",e)}},[e,a]),h=(0,j.useCallback)(async(t,r,s)=>{if(e&&(null==a?void 0:a.user))try{await ao.broadcastCellChange(e,{workbookId:e,sheetId:t,cellAddress:r,value:s,userId:a.user.id||a.user.email||"unknown"})}catch(e){console.error("Erro ao enviar mudan\xe7a de c\xe9lula:",e)}},[e,a]);(0,j.useEffect)(()=>{if(!e||!(null==a?void 0:a.user)){i.current&&(ao.unsubscribeFromWorkbook(i.current),ao.unsubscribeFromPresence(i.current),i.current=null,r(!1),o(new Map));return}if(i.current!==e){i.current&&(ao.unsubscribeFromWorkbook(i.current),ao.unsubscribeFromPresence(i.current));try{ao.subscribeToWorkbook(e,{onWorkbookChange:c,onSheetChange:d,onCellChange:u}),ao.subscribeToUserPresence(e,{id:a.user.id||a.user.email||"unknown",name:a.user.name||a.user.email||"Usu\xe1rio"},m),i.current=e,r(!0)}catch(e){console.error("Erro ao conectar ao Real-time:",e),r(!1)}return()=>{i.current&&(ao.unsubscribeFromWorkbook(i.current),ao.unsubscribeFromPresence(i.current),i.current=null,r(!1),o(new Map))}}},[e,a,c,d,u,m]);let f=(0,j.useCallback)(()=>ao.getConnectionStatus(),[]),g=(0,j.useCallback)(async()=>{try{await ao.reconnectAll(),r(!0)}catch(e){console.error("Erro ao reconectar:",e),r(!1)}},[]);return{isConnected:t,onlineUsers:Array.from(s.values()),recentChanges:n,updateCursor:p,broadcastCellChange:h,getConnectionStatus:f,reconnect:g,isUserOnline:e=>{var a;return(null===(a=s.get(e))||void 0===a?void 0:a.isOnline)||!1},getUserCursor:e=>{var a;return null===(a=s.get(e))||void 0===a?void 0:a.cursor},getOnlineCount:()=>Array.from(s.values()).filter(e=>e.isOnline).length}}function al(e){let{workbookId:a,className:t=""}=e,{onlineUsers:r,isConnected:s,onlineCount:n}=function(e){let{onlineUsers:a,isConnected:t,getOnlineCount:r}=an(e);return{onlineUsers:a,isConnected:t,onlineCount:r()}}(a);return a?(0,o.jsxs)("div",{className:"flex items-center space-x-2 ".concat(t),children:[(0,o.jsx)(R.pn,{children:(0,o.jsxs)(R.u,{children:[(0,o.jsx)(R.aJ,{asChild:!0,children:(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[s?(0,o.jsx)(e7.Z,{className:"h-4 w-4 text-green-500"}):(0,o.jsx)(ae.Z,{className:"h-4 w-4 text-red-500"}),(0,o.jsx)(M.C,{variant:s?"default":"destructive",className:"text-xs",children:s?"Conectado":"Desconectado"})]})}),(0,o.jsx)(R._v,{children:(0,o.jsx)("p",{children:s?"Conectado ao Real-time":"Desconectado do Real-time"})})]})}),n>0&&(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)(aa.Z,{className:"h-4 w-4 text-gray-500"}),(0,o.jsxs)("span",{className:"text-sm text-gray-600",children:[n," online"]})]}),(0,o.jsx)("div",{className:"flex -space-x-2",children:r.filter(e=>e.isOnline).slice(0,5).map(e=>(0,o.jsx)(R.pn,{children:(0,o.jsxs)(R.u,{children:[(0,o.jsx)(R.aJ,{asChild:!0,children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsxs)(at.qE,{className:"h-8 w-8 border-2 border-white",children:[(0,o.jsx)(at.F$,{src:"https://api.dicebear.com/7.x/initials/svg?seed=".concat(e.userName),alt:e.userName}),(0,o.jsx)(at.Q5,{className:"text-xs",children:e.userName.substring(0,2).toUpperCase()})]}),(0,o.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-white"})]})}),(0,o.jsx)(R._v,{children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("p",{className:"font-medium",children:e.userName}),(0,o.jsx)("p",{className:"text-xs text-gray-500",children:"Online agora"}),e.cursor&&(0,o.jsxs)("p",{className:"text-xs text-blue-500",children:["Editando: ",e.cursor.cellAddress]})]})})]})},e.userId))}),n>5&&(0,o.jsx)(R.pn,{children:(0,o.jsxs)(R.u,{children:[(0,o.jsx)(R.aJ,{asChild:!0,children:(0,o.jsxs)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 border-2 border-white text-xs font-medium text-gray-600",children:["+",n-5]})}),(0,o.jsx)(R._v,{children:(0,o.jsxs)("p",{children:["Mais ",n-5," usu\xe1rios online"]})})]})})]}):null}var ai=t(76669);let ac=ai.fC;ai.xz;let ad=ai.h_,au=j.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,o.jsx)(ai.aV,{className:(0,T.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:a})});au.displayName=ai.aV.displayName;let am=j.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,o.jsxs)(ad,{children:[(0,o.jsx)(au,{}),(0,o.jsx)(ai.VY,{ref:a,className:(0,T.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...r})]})});am.displayName=ai.VY.displayName;let ap=e=>{let{className:a,...t}=e;return(0,o.jsx)("div",{className:(0,T.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};ap.displayName="AlertDialogHeader";let ah=e=>{let{className:a,...t}=e;return(0,o.jsx)("div",{className:(0,T.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};ah.displayName="AlertDialogFooter";let af=j.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,o.jsx)(ai.Dx,{ref:a,className:(0,T.cn)("text-lg font-semibold",t),...r})});af.displayName=ai.Dx.displayName;let ag=j.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,o.jsx)(ai.dk,{ref:a,className:(0,T.cn)("text-sm text-muted-foreground",t),...r})});ag.displayName=ai.dk.displayName,j.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,o.jsx)(ai.aU,{ref:a,className:(0,T.cn)((0,k.d)(),t),...r})}).displayName=ai.aU.displayName;let ax=j.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,o.jsx)(ai.$j,{ref:a,className:(0,T.cn)((0,k.d)({variant:"outline"}),"mt-2 sm:mt-0",t),...r})});ax.displayName=ai.$j.displayName;var av=t(188),ay=t(58184),ab=t(39127),aw=t(38711),aN=t(29338);let aj=[{id:"financial-expenses",name:"Controle de Despesas",description:"Template para importar dados de despesas e receitas",category:"financial",icon:(0,o.jsx)(ab.Z,{className:"h-6 w-6"}),columns:["Data","Descri\xe7\xe3o","Categoria","Valor","Tipo"],validationRules:{Data:{required:!0,type:"date"},Valor:{required:!0,type:"number"},Tipo:{required:!0,enum:["Receita","Despesa"]}},isPopular:!0},{id:"sales-data",name:"Dados de Vendas",description:"Template para importar relat\xf3rios de vendas",category:"sales",icon:(0,o.jsx)(aw.Z,{className:"h-6 w-6"}),columns:["Data","Produto","Cliente","Quantidade","Valor Unit\xe1rio","Total"],validationRules:{Data:{required:!0,type:"date"},Quantidade:{required:!0,type:"number",min:0},"Valor Unit\xe1rio":{required:!0,type:"number",min:0}},isPopular:!0},{id:"inventory-control",name:"Controle de Estoque",description:"Template para importar dados de invent\xe1rio",category:"inventory",icon:(0,o.jsx)(K.Z,{className:"h-6 w-6"}),columns:["C\xf3digo","Produto","Categoria","Quantidade","Pre\xe7o","Fornecedor"],validationRules:{Código:{required:!0,type:"string"},Quantidade:{required:!0,type:"number",min:0},Preço:{required:!0,type:"number",min:0}}},{id:"employee-data",name:"Dados de Funcion\xe1rios",description:"Template para importar informa\xe7\xf5es de RH",category:"hr",icon:(0,o.jsx)(aa.Z,{className:"h-6 w-6"}),columns:["Nome","Email","Cargo","Departamento","Sal\xe1rio","Data Admiss\xe3o"],validationRules:{Nome:{required:!0,type:"string"},Email:{required:!0,type:"email"},"Data Admiss\xe3o":{required:!0,type:"date"}}},{id:"budget-planning",name:"Planejamento Or\xe7ament\xe1rio",description:"Template para importar dados de or\xe7amento",category:"financial",icon:(0,o.jsx)(I.Z,{className:"h-6 w-6"}),columns:["Categoria","Subcategoria","Or\xe7ado","Realizado","Varia\xe7\xe3o"],validationRules:{Categoria:{required:!0,type:"string"},Orçado:{required:!0,type:"number"},Realizado:{required:!0,type:"number"}},isNew:!0},{id:"custom-generic",name:"Template Gen\xe9rico",description:"Template flex\xedvel para qualquer tipo de dados",category:"general",icon:(0,o.jsx)(l.Z,{className:"h-6 w-6"}),columns:[],validationRules:{}}],aA={financial:"Financeiro",sales:"Vendas",inventory:"Estoque",hr:"Recursos Humanos",general:"Geral"};function aE(e){let{onTemplateSelect:a,onSkip:t}=e,[r,s]=(0,j.useState)(""),[n,i]=(0,j.useState)("all"),c=aj.filter(e=>{let a=e.name.toLowerCase().includes(r.toLowerCase())||e.description.toLowerCase().includes(r.toLowerCase()),t="all"===n||e.category===n;return a&&t}),d=aj.filter(e=>e.isPopular);return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(E.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,o.jsx)(e$.I,{placeholder:"Buscar templates...",value:r,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,o.jsxs)(e1.mQ,{value:n,onValueChange:i,children:[(0,o.jsxs)(e1.dr,{className:"grid w-full grid-cols-6",children:[(0,o.jsx)(e1.SP,{value:"all",children:"Todos"}),(0,o.jsx)(e1.SP,{value:"financial",children:"Financeiro"}),(0,o.jsx)(e1.SP,{value:"sales",children:"Vendas"}),(0,o.jsx)(e1.SP,{value:"inventory",children:"Estoque"}),(0,o.jsx)(e1.SP,{value:"hr",children:"RH"}),(0,o.jsx)(e1.SP,{value:"general",children:"Geral"})]}),(0,o.jsxs)(e1.nU,{value:n,className:"space-y-4",children:["all"===n&&d.length>0&&(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[(0,o.jsx)(aN.Z,{className:"h-5 w-5 text-yellow-500"}),"Templates Populares"]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:d.map(e=>(0,o.jsx)(aC,{template:e,onSelect:()=>a(e.id)},e.id))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"all"===n?"Todos os Templates":aA[n]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:c.map(e=>(0,o.jsx)(aC,{template:e,onSelect:()=>a(e.id)},e.id))})]}),0===c.length&&(0,o.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,o.jsx)(l.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,o.jsx)("p",{children:"Nenhum template encontrado"})]})]})]}),(0,o.jsxs)("div",{className:"flex justify-between pt-4 border-t",children:[(0,o.jsx)(k.Button,{variant:"outline",onClick:t,children:"Pular Template"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground self-center",children:"Selecione um template ou pule para importa\xe7\xe3o manual"})]})]})}function aC(e){let{template:a,onSelect:t}=e;return(0,o.jsxs)(G.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:t,children:[(0,o.jsx)(G.Ol,{className:"pb-3",children:(0,o.jsx)("div",{className:"flex items-start justify-between",children:(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)("div",{className:"p-2 bg-primary/10 rounded-lg",children:a.icon}),(0,o.jsxs)("div",{children:[(0,o.jsxs)(G.ll,{className:"text-base flex items-center gap-2",children:[a.name,a.isPopular&&(0,o.jsx)(M.C,{variant:"secondary",className:"text-xs",children:"Popular"}),a.isNew&&(0,o.jsx)(M.C,{variant:"default",className:"text-xs",children:"Novo"})]}),(0,o.jsx)(G.SZ,{className:"text-sm",children:a.description})]})]})})}),(0,o.jsx)(G.aY,{className:"pt-0",children:(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Colunas esperadas:"}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.columns.length>0?a.columns.slice(0,4).map((e,a)=>(0,o.jsx)(M.C,{variant:"outline",className:"text-xs",children:e},a)):(0,o.jsx)(M.C,{variant:"outline",className:"text-xs",children:"Flex\xedvel"}),a.columns.length>4&&(0,o.jsxs)(M.C,{variant:"outline",className:"text-xs",children:["+",a.columns.length-4," mais"]})]})]})})]})}var aS=t(15862);let ak={"financial-expenses":[{name:"Data",type:"date",sampleValues:[],required:!0},{name:"Descri\xe7\xe3o",type:"string",sampleValues:[],required:!0},{name:"Categoria",type:"string",sampleValues:[]},{name:"Valor",type:"number",sampleValues:[],required:!0},{name:"Tipo",type:"string",sampleValues:[],required:!0}],"sales-data":[{name:"Data",type:"date",sampleValues:[],required:!0},{name:"Produto",type:"string",sampleValues:[],required:!0},{name:"Cliente",type:"string",sampleValues:[]},{name:"Quantidade",type:"number",sampleValues:[],required:!0},{name:"Valor Unit\xe1rio",type:"number",sampleValues:[],required:!0},{name:"Total",type:"number",sampleValues:[]}],"inventory-control":[{name:"C\xf3digo",type:"string",sampleValues:[],required:!0},{name:"Produto",type:"string",sampleValues:[],required:!0},{name:"Categoria",type:"string",sampleValues:[]},{name:"Quantidade",type:"number",sampleValues:[],required:!0},{name:"Pre\xe7o",type:"number",sampleValues:[],required:!0},{name:"Fornecedor",type:"string",sampleValues:[]}]};function aO(e){let{open:a,onOpenChange:t,file:r,template:s,onConfirm:n,onCancel:c}=e,[d,m]=(0,j.useState)([]),[p,h]=(0,j.useState)([]),[f,g]=(0,j.useState)({}),[x,v]=(0,j.useState)(!0),[y,b]=(0,j.useState)(null),[N,A]=(0,j.useState)([]);(0,j.useEffect)(()=>{a&&r&&E()},[a,r]),(0,j.useEffect)(()=>{s&&ak[s]?h(ak[s]):h(d.map(e=>({name:e,type:"string",sampleValues:[]})))},[s,d]);let E=async()=>{v(!0),b(null);try{let e=await eb(r);if(!e||0===e.length)throw Error("Arquivo n\xe3o cont\xe9m dados v\xe1lidos");let a=e[0];if(!a)throw Error("Planilha n\xe3o encontrada");let t=Array.isArray(a.data)?a.data:[];if(0===t.length)throw Error("Planilha est\xe1 vazia");let o=t[0],n=Object.keys(o);if(m(n),A(t.slice(0,5)),s&&ak[s]){let e={};ak[s].forEach(a=>{let t=n.find(e=>e.toLowerCase().includes(a.name.toLowerCase())||a.name.toLowerCase().includes(e.toLowerCase()));t&&(e[t]=a.name)}),g(e)}}catch(e){b(e instanceof Error?e.message:"Erro ao analisar arquivo")}finally{v(!1)}},C=(e,a)=>{g(t=>({...t,[e]:a}))},S=e=>{g(a=>{let t={...a};return Object.keys(t).forEach(a=>{t[a]===e&&delete t[a]}),t})},O=e=>Object.keys(f).find(a=>f[a]===e),R=(()=>{let e=p.filter(e=>e.required);return{isValid:e.filter(e=>O(e.name)).length===e.length,missingRequired:e.filter(e=>!O(e.name)),totalMapped:Object.keys(f).length}})();return x?(0,o.jsx)(eW.Vq,{open:a,onOpenChange:t,children:(0,o.jsx)(eW.cZ,{className:"max-w-4xl",children:(0,o.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,o.jsx)("p",{children:"Analisando arquivo..."})]})})})}):(0,o.jsx)(eW.Vq,{open:a,onOpenChange:t,children:(0,o.jsxs)(eW.cZ,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,o.jsx)(eW.fK,{children:(0,o.jsxs)(eW.$N,{className:"flex items-center gap-2",children:[(0,o.jsx)(l.Z,{className:"h-5 w-5"}),"Mapeamento de Colunas - ",r.name]})}),y&&(0,o.jsxs)(e2.bZ,{variant:"destructive",children:[(0,o.jsx)(w.Z,{className:"h-4 w-4"}),(0,o.jsx)(e2.X,{children:y})]}),!y&&(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)(e2.bZ,{variant:R.isValid?"default":"destructive",children:[(0,o.jsx)(aS.Z,{className:"h-4 w-4"}),(0,o.jsx)(e2.X,{children:R.isValid?"Mapeamento v\xe1lido! ".concat(R.totalMapped," colunas mapeadas."):"Colunas obrigat\xf3rias n\xe3o mapeadas: ".concat(R.missingRequired.map(e=>e.name).join(", "))})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsx)(G.ll,{className:"text-lg",children:"Colunas do Arquivo"})}),(0,o.jsx)(G.aY,{className:"space-y-3",children:d.map(e=>{var a;return(0,o.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"font-medium",children:e}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:(null===(a=N[0])||void 0===a?void 0:a[e])?String(N[0][e]).substring(0,30)+"...":"Vazio"})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[f[e]&&(0,o.jsx)(M.C,{variant:"secondary",children:f[e]}),(0,o.jsx)(i.Z,{className:"h-4 w-4 text-muted-foreground"})]})]},e)})})]}),(0,o.jsxs)(G.Zb,{children:[(0,o.jsx)(G.Ol,{children:(0,o.jsx)(G.ll,{className:"text-lg",children:s?"Colunas do Template":"Colunas de Destino"})}),(0,o.jsx)(G.aY,{className:"space-y-3",children:p.map(e=>{let a=O(e.name);return(0,o.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("p",{className:"font-medium",children:e.name}),e.required&&(0,o.jsx)(M.C,{variant:"destructive",className:"text-xs",children:"Obrigat\xf3rio"}),(0,o.jsx)(M.C,{variant:"outline",className:"text-xs",children:e.type})]}),a&&(0,o.jsx)(k.Button,{variant:"ghost",size:"sm",onClick:()=>S(e.name),children:(0,o.jsx)(u.Z,{className:"h-4 w-4"})})]}),(0,o.jsxs)(eK.Ph,{value:a||"",onValueChange:a=>C(a,e.name),children:[(0,o.jsx)(eK.i4,{children:(0,o.jsx)(eK.ki,{placeholder:"Selecionar coluna do arquivo"})}),(0,o.jsx)(eK.Bw,{children:d.map(e=>(0,o.jsx)(eK.Ql,{value:e,children:e},e))})]})]},e.name)})})]})]})]}),(0,o.jsxs)(eW.cN,{children:[(0,o.jsx)(k.Button,{variant:"outline",onClick:c,children:"Cancelar"}),(0,o.jsx)(k.Button,{onClick:()=>n(f),disabled:!R.isValid,children:"Confirmar Mapeamento"})]})]})})}var aR=t(55430),aF=t(97589),aT=t(40933),aI=t(61617);let aD={"file-analysis":(0,o.jsx)(l.Z,{className:"h-4 w-4"}),"schema-validation":(0,o.jsx)(aS.Z,{className:"h-4 w-4"}),"data-transformation":(0,o.jsx)(aR.Z,{className:"h-4 w-4"}),"data-processing":(0,o.jsx)(aF.Z,{className:"h-4 w-4"}),finalization:(0,o.jsx)(aS.Z,{className:"h-4 w-4"})};function aL(e){let{steps:a,currentStep:t,overallProgress:r,isComplete:s,hasError:n,onCancel:l,showDetails:i=!0}=e,[c,d]=(0,j.useState)(0),[u]=(0,j.useState)(new Date);(0,j.useEffect)(()=>{if(!s&&!n){let e=setInterval(()=>{d(Date.now()-u.getTime())},1e3);return()=>clearInterval(e)}},[s,n,u]);let m=e=>{let a=Math.floor(e/1e3),t=Math.floor(a/60),r=a%60;return t>0?"".concat(t,"m ").concat(r,"s"):"".concat(r,"s")},p=e=>e.startTime&&e.endTime?m(e.endTime.getTime()-e.startTime.getTime()):e.startTime&&"running"===e.status?m(Date.now()-e.startTime.getTime()):null,h=a.filter(e=>"completed"===e.status).length,f=a.filter(e=>"error"===e.status).length;return(0,o.jsxs)(G.Zb,{className:"w-full max-w-2xl",children:[(0,o.jsx)(G.Ol,{children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)(G.ll,{className:"flex items-center gap-2",children:[(0,o.jsx)(aF.Z,{className:"h-5 w-5"}),"Progresso da Importa\xe7\xe3o"]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(M.C,{variant:n?"destructive":s?"default":"secondary",children:n?"Erro":s?"Conclu\xeddo":"Processando"}),(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:m(c)})]})]})}),(0,o.jsxs)(G.aY,{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsx)("span",{children:"Progresso Geral"}),(0,o.jsxs)("span",{children:[Math.round(r),"%"]})]}),(0,o.jsx)(aI.E,{value:r,className:"h-2"}),(0,o.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,o.jsxs)("span",{children:[h,"/",a.length," etapas conclu\xeddas"]}),f>0&&(0,o.jsxs)("span",{className:"text-destructive",children:[f," erro(s)"]})]})]}),(0,o.jsx)("div",{className:"space-y-3",children:a.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-start gap-3 p-3 rounded-lg border transition-colors ".concat("running"===e.status?"bg-blue-50 border-blue-200":"completed"===e.status?"bg-green-50 border-green-200":"error"===e.status?"bg-red-50 border-red-200":"bg-muted/20"),children:[(0,o.jsx)("div",{className:"flex-shrink-0 mt-0.5 ".concat("running"===e.status?"text-blue-600":"completed"===e.status?"text-green-600":"error"===e.status?"text-red-600":"text-muted-foreground"),children:"running"===e.status?(0,o.jsx)("div",{className:"h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin"}):"completed"===e.status?(0,o.jsx)(aS.Z,{className:"h-4 w-4"}):"error"===e.status?(0,o.jsx)(w.Z,{className:"h-4 w-4"}):(0,o.jsx)(aT.Z,{className:"h-4 w-4"})}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[aD[e.id],(0,o.jsx)("h4",{className:"font-medium text-sm",children:e.name})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[p(e)&&(0,o.jsx)("span",{children:p(e)}),"running"===e.status&&(0,o.jsxs)("span",{children:[Math.round(e.progress),"%"]})]})]}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.description}),"running"===e.status&&e.progress>0&&(0,o.jsx)(aI.E,{value:e.progress,className:"h-1 mt-2"}),i&&e.details&&(0,o.jsx)("p",{className:"text-xs text-muted-foreground mt-1 font-mono",children:e.details}),"error"===e.status&&e.error&&(0,o.jsxs)(e2.bZ,{variant:"destructive",className:"mt-2",children:[(0,o.jsx)(w.Z,{className:"h-4 w-4"}),(0,o.jsx)(e2.X,{className:"text-xs",children:e.error})]})]})]},e.id))}),(s||n)&&(0,o.jsx)("div",{className:"pt-4 border-t",children:(0,o.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-2xl font-bold text-green-600",children:h}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Conclu\xeddas"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-2xl font-bold text-red-600",children:f}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Erros"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-2xl font-bold",children:m(c)}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Tempo Total"})]})]})}),!s&&!n&&l&&(0,o.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,o.jsx)("button",{onClick:l,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Cancelar Importa\xe7\xe3o"})})]})]})}function a_(e){let{onUpload:a,workbookId:t,variant:r="default",size:s="sm",maxSize:n,saveToSupabase:i=!1,enableAdvancedImport:c=!1,allowTemplates:d=!1}=e,u=(0,j.useRef)(null),{importExcel:m,isLoading:p}=eq(),{data:h}=(0,ar.useSession)(),[f,g]=(0,j.useState)(!1),[x,v]=(0,j.useState)(!1),[y,b]=(0,j.useState)(!1),[w,N]=(0,j.useState)(null),[E,C]=(0,j.useState)(null),S=function(){let[e,a]=(0,j.useState)([]),[t,r]=(0,j.useState)(),[s,o]=(0,j.useState)(0),n=e.length>0&&e.every(e=>"completed"===e.status),l=e.some(e=>"error"===e.status);return{steps:e,currentStep:t,overallProgress:s,isComplete:n,hasError:l,initializeSteps:e=>{a(e.map(e=>({...e,status:"pending",progress:0}))),o(0)},startStep:(e,t)=>{r(e),a(a=>a.map(a=>a.id===e?{...a,status:"running",startTime:new Date,details:t||void 0}:a))},updateStepProgress:(t,r,s)=>{a(e=>e.map(e=>e.id===t?{...e,progress:r,details:s||void 0}:e));let n=e.length;o((e.filter(e=>"completed"===e.status).length+r/100)/n*100)},completeStep:(e,t)=>{a(a=>a.map(a=>a.id===e?{...a,status:"completed",progress:100,endTime:new Date,details:t||void 0}:a))},errorStep:(e,t)=>{a(a=>a.map(a=>a.id===e?{...a,status:"error",endTime:new Date,error:t}:a))}}}(),O=async e=>{var a;let t=null===(a=e.target.files)||void 0===a?void 0:a[0];t&&(c?(N(t),v(!0)):await R(t),u.current&&(u.current.value=""))},R=async(e,r)=>{c&&(S.initializeSteps([{id:"file-analysis",name:"An\xe1lise do Arquivo",description:"Analisando estrutura e conte\xfado"},{id:"schema-validation",name:"Valida\xe7\xe3o de Schema",description:"Validando dados conforme template"},{id:"data-transformation",name:"Transforma\xe7\xe3o",description:"Aplicando transforma\xe7\xf5es nos dados"},{id:"data-processing",name:"Processamento",description:"Processando e organizando dados"},{id:"finalization",name:"Finaliza\xe7\xe3o",description:"Concluindo importa\xe7\xe3o"}]),b(!0));try{if(i&&t&&(null==h?void 0:h.user)){c&&S.startStep("data-processing","Salvando no Supabase Storage..."),A.toast.loading("Salvando arquivo no Supabase...",{id:"supabase-upload"});let a=await eT.uploadExcelFile(e,h.user.id||h.user.email||"unknown",t,{fileName:e.name,upsert:!0});A.toast.success("Arquivo salvo no Supabase!",{id:"supabase-upload",description:"Tamanho: ".concat(Math.round(a.size/1024),"KB")}),c&&S.updateStepProgress("data-processing",30,"Arquivo salvo no storage")}await m(e,{onSuccess:e=>{r&&e.sheets&&(e.sheets=e.sheets.map(e=>({...e,data:F(e.data,r)}))),c&&(S.completeStep("finalization","Importa\xe7\xe3o conclu\xedda com sucesso"),setTimeout(()=>b(!1),2e3)),a(e)},...n?{maxSize:n}:{},trackAnalytics:!0,template:E,validateSchema:c,transformData:c,...c?{onProgress:e=>{e<=10?S.startStep("file-analysis","Carregando arquivo..."):e<=50?S.updateStepProgress("file-analysis",(e-10)*2.5,"Analisando dados..."):e<=70?(S.completeStep("file-analysis"),S.startStep("schema-validation","Validando schema..."),S.updateStepProgress("schema-validation",(e-50)*5,"Verificando estrutura...")):e<=90?(S.completeStep("schema-validation"),S.startStep("data-transformation","Transformando dados..."),S.updateStepProgress("data-transformation",(e-70)*5,"Aplicando transforma\xe7\xf5es...")):(S.completeStep("data-transformation"),S.startStep("finalization","Finalizando..."))}}:{}})}catch(e){if(console.error("Erro no upload:",e),c){let a=S.currentStep||"file-analysis";S.errorStep(a,e instanceof Error?e.message:"Erro desconhecido")}A.toast.error("Erro ao processar arquivo",{description:e instanceof Error?e.message:"Erro desconhecido"})}},F=(e,a)=>e&&Array.isArray(e)?e.map(e=>{let t={};return Object.entries(a).forEach(a=>{let[r,s]=a;void 0!==e[r]&&(t[s]=e[r])}),{...e,...t}}):e;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("input",{type:"file",ref:u,onChange:O,accept:".xlsx,.xls,.csv",style:{display:"none"}}),(0,o.jsx)(k.Button,{variant:r,size:s,onClick:()=>{if(c&&d)g(!0);else{var e;null===(e=u.current)||void 0===e||e.click()}},disabled:p,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2",children:p?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Carregando..."]}):(0,o.jsxs)(o.Fragment,{children:[c?(0,o.jsx)(X.Z,{className:"h-4 w-4 mr-2"}):(0,o.jsx)(ay.Z,{className:"h-4 w-4 mr-2"}),c?"Importa\xe7\xe3o Avan\xe7ada":"Importar Excel"]})}),d&&(0,o.jsx)(eW.Vq,{open:f,onOpenChange:g,children:(0,o.jsxs)(eW.cZ,{className:"max-w-4xl",children:[(0,o.jsx)(eW.fK,{children:(0,o.jsxs)(eW.$N,{className:"flex items-center gap-2",children:[(0,o.jsx)(l.Z,{className:"h-5 w-5"}),"Selecionar Template de Importa\xe7\xe3o"]})}),(0,o.jsx)(aE,{onTemplateSelect:e=>{var a;C(e),g(!1),null===(a=u.current)||void 0===a||a.click()},onSkip:()=>{var e;C(null),g(!1),null===(e=u.current)||void 0===e||e.click()}})]})}),c&&w&&(0,o.jsx)(aO,{open:x,onOpenChange:v,file:w,template:E,onConfirm:e=>{v(!1),R(w,e),N(null)},onCancel:()=>{v(!1),N(null)}}),c&&(0,o.jsx)(eW.Vq,{open:y,onOpenChange:b,children:(0,o.jsxs)(eW.cZ,{className:"max-w-2xl",children:[(0,o.jsx)(eW.fK,{children:(0,o.jsx)(eW.$N,{children:"Progresso da Importa\xe7\xe3o"})}),(0,o.jsx)(aL,{steps:S.steps,currentStep:S.currentStep||void 0,overallProgress:S.overallProgress,isComplete:S.isComplete,hasError:S.hasError,onCancel:()=>{b(!1)}})]})})]})}var az=t(38472),aM=t(49465);(r=s||(s={})).IDLE="idle",r.PENDING="pending",r.COMPLETED="completed",r.FAILED="failed";let aV=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[a,t]=(0,j.useState)(e.initialMessages||[]),[r,s]=(0,j.useState)(!1),[o,n]=(0,j.useState)(null),[l,i]=(0,j.useState)("idle"),[c,d]=(0,j.useState)(null),u=(0,j.useRef)(null);(0,j.useEffect)(()=>{o&&a.length>0&&n(null)},[a,o]);let m=e.useMock||!1,p=(0,j.useCallback)(async e=>new Promise(a=>{setTimeout(()=>{a(function(e){let a=e.toLowerCase(),t=[{keywords:["m\xe9dia","coluna"],response:'{\n        "operations": [\n          {\n            "type": "FORMULA",\n            "data": {\n              "formula": "=M\xc9DIA(B:B)",\n              "range": "C1"\n            }\n          }\n        ],\n        "explanation": "Calculando a m\xe9dia da coluna B",\n        "interpretation": "Voc\xea solicitou o c\xe1lculo da m\xe9dia dos valores na coluna B"\n      }'},{keywords:["gr\xe1fico","barras"],response:'{\n        "operations": [\n          {\n            "type": "CHART",\n            "data": {\n              "type": "bar",\n              "title": "Gr\xe1fico de Barras",\n              "labels": "A1:A10",\n              "datasets": ["B1:B10"]\n            }\n          }\n        ],\n        "explanation": "Criando um gr\xe1fico de barras com dados das colunas A e B",\n        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de um gr\xe1fico de barras usando os dados existentes"\n      }'},{keywords:["tabela","criar"],response:'{\n        "operations": [\n          {\n            "type": "CELL_UPDATE",\n            "data": {\n              "updates": [\n                { "cell": "A1", "value": "Produto" },\n                { "cell": "B1", "value": "Valor" },\n                { "cell": "C1", "value": "Quantidade" },\n                { "cell": "A2", "value": "Produto 1" },\n                { "cell": "B2", "value": 100 },\n                { "cell": "C2", "value": 10 }\n              ]\n            }\n          }\n        ],\n        "explanation": "Criando uma tabela com 3 colunas: Produto, Valor e Quantidade",\n        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de uma nova tabela para registro de produtos"\n      }'}].filter(e=>e.keywords.some(e=>a.includes(e)));return t.length>0&&t[0]?t[0].response:'{\n    "operations": [\n      {\n        "type": "CELL_UPDATE",\n        "data": {\n          "updates": [\n            { "cell": "A1", "value": "Exemplo" },\n            { "cell": "B1", "value": 100 }\n          ]\n        }\n      }\n    ],\n    "explanation": "Realizando uma opera\xe7\xe3o exemplo baseada no seu comando",\n    "interpretation": "Seu comando foi processado como uma solicita\xe7\xe3o de exemplo"\n  }'}(e))},1500)}),[]);(0,j.useCallback)(async a=>{try{let t;if(!a||""===a.trim())return null;if(m)t=await p(a);else try{t=(await az.Z.post("/api/ai/chat",{message:a,userId:e.workbookId||"anonymous",context:{mode:"preview",excelContext:{activeSheet:"Atual"}},preserveContext:!1})).data}catch(e){return console.error("Erro na comunica\xe7\xe3o com Gemini durante interpreta\xe7\xe3o:",e),null}try{let e=JSON.parse(t);if(e.interpretation)return{interpretation:e.interpretation,confidence:e.confidence||0,commandId:(0,T.x0)(),_commandId:(0,T.x0)()}}catch(e){console.error("Erro ao parsear resposta de interpreta\xe7\xe3o:",e)}return null}catch(e){return console.error("Erro ao interpretar comando:",e),null}},[e.workbookId,m,p]);let h=(0,j.useCallback)(async r=>{if(!r.trim())return;let o={id:"user-".concat(Date.now()),content:r,role:"user",timestamp:new Date};t(e=>[...e,o]),s(!0),n(null);try{let r=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...a,o].map(e=>({role:e.role,content:e.content})),modelName:e.modelName,systemPrompt:e.systemPrompt})});if(!r.ok)throw Error("Error: ".concat(r.statusText));let s=await r.json(),n={id:"assistant-".concat(Date.now()),content:s.response,role:"assistant",timestamp:new Date};t(e=>[...e,n])}catch(t){let a=t instanceof Error?t:Error("Unknown error");n(a),e.onError&&e.onError(a)}finally{s(!1)}},[a,e]),f=(0,j.useCallback)(async r=>{try{let o;s(!0);let n={id:"user-".concat(Date.now()),content:r,role:"user",timestamp:new Date};if(t(a=>{let t=[...a,n],r=e.maxHistorySize||20;return t.length>r?t.slice(-r):t}),m)o=await p(r);else try{let t=e.maxHistorySize||20,s=a.slice(-Math.min(t,10)).map(e=>({role:e.role,content:e.content})),n=e.workbookId;o=(await az.Z.post("/api/ai/chat",{message:r,userId:n||"anonymous",context:n?{excelContext:{activeSheet:"Atual"},responseStructure:{preferJson:!0}}:{},preserveContext:s.length>0})).data}catch(a){console.error("Erro na comunica\xe7\xe3o direta com Gemini, tentando API:",a),o=(await az.Z.post("/api/chat",{message:r,workbookId:e.workbookId})).data.response}let l={id:"assistant-".concat(Date.now()),content:o,role:"assistant",timestamp:new Date};t(a=>{let t=[...a,l],r=e.maxHistorySize||20;return t.length>r?t.slice(-r):t}),e.onMessageReceived&&e.onMessageReceived(o);let c=(0,T.x0)();try{await aM.M.storeFeedback({commandId:c,command:r,successful:!0})}catch(e){console.error("Erro ao armazenar comando para feedback:",e)}if(o){let a={interpretation:o,confidence:1,commandId:(0,T.x0)(),_commandId:(0,T.x0)()};d(a),i("pending"),e.onInterpretation&&e.onInterpretation(a)}return{response:o,commandId:c}}catch(e){throw console.error("Erro ao executar comando:",e),e}finally{s(!1)}},[a,e.workbookId,m,p,e.onMessageReceived,e.maxHistorySize,t,s,aM.M,e.onInterpretation]),g=(0,j.useCallback)(async()=>{if(!c)return null;let{_commandId:e}=c,a=await f(c.interpretation);return d(null),a},[c,f,d]),x=(0,j.useCallback)(()=>{d(null),i("idle")},[]),v=(0,j.useCallback)(()=>{t([]),d(null),i("idle")},[]);return(0,j.useEffect)(()=>{let e=u.current;return()=>{e&&e.abort()}},[]),{messages:a,isProcessing:r,error:o,sendMessage:h,clearMessages:v,confirmAndExecute:g,cancelCommand:x,pendingInterpretation:c,commandStatus:l}};var aZ=t(56143),aP=t(91116);let aB={VERBOSE_LOGGING:!1},aU=(e,a)=>{try{return new Worker(e,a)}catch(e){return console.warn("Worker n\xe3o p\xf4de ser criado:",e),null}},aq=new Map,aG=0,aH=0,aJ=(0,j.memo)(e=>{let{rowIndex:a,colIndex:t,value:r,isModified:s,readOnly:n,onCellChange:l,header:i}=e,c="".concat(a,"-").concat(t);null!=r&&aq.set(c,String(r));let d=null!=r?r:(()=>{let e=aq.get(c);return e?(aH++,e):(aG++,"")})(),u=(0,j.useCallback)(e=>{let r=e.target.value;aq.set(c,r),l(a,t,r)},[a,t,l,c]),m=s?"bg-blue-50 dark:bg-blue-900/30 transition-colors duration-1000":"",p=(0,j.useMemo)(()=>"table-cell p-1 border border-border ".concat(m),[m]);return(0,o.jsx)("div",{className:p,role:"gridcell","aria-colindex":t+1,"aria-rowindex":a+1,children:(0,o.jsx)("input",{type:"text",value:d,readOnly:n,onChange:u,className:"w-full bg-transparent border-0 focus:ring-1 focus:ring-blue-500 p-1","aria-label":"C\xe9lula ".concat(i).concat(a+1)})})},(e,a)=>e.value===a.value&&e.isModified===a.isModified&&e.readOnly===a.readOnly);aJ.displayName="OptimizedCell";let aW=(0,j.memo)(e=>{let{visibleRows:a,totalRows:t,virtualizer:r}=e;return(0,j.useEffect)(()=>{if(t>1e3){let e=Array.from(aq.keys()),a=new Set,s=Math.max(0,r.range.startIndex-20),o=Math.min(t-1,r.range.endIndex+20);for(let e=s;e<=o;e++)a.add(e);e.forEach(e=>{let t=e.split("-")[0];if(t){let r=parseInt(t,10);a.has(r)||aq.delete(e)}})}},[a,t,r]),null});aW.displayName="VirtualizedRowWrapper";let aQ=(0,j.memo)(e=>{let{rowIndex:a,rowData:t,headers:r,modifiedCellsMap:s,readOnly:n,onCellChange:l,onRemoveRow:i}=e,c=(0,j.useCallback)(()=>{i(a)},[a,i]),d=(0,j.useMemo)(()=>t.map((e,t)=>{let i="".concat(a,"-").concat(t),c=s[i]||!1;return(0,o.jsx)(aJ,{rowIndex:a,colIndex:t,value:e,isModified:c,readOnly:n,onCellChange:l,header:r[t]||""},i)}),[t,a,s,n,l,r]),m=(0,j.useMemo)(()=>n?null:(0,o.jsx)("div",{className:"table-cell w-10 text-center",children:(0,o.jsx)(k.Button,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:c,"aria-label":"Remover linha ".concat(a+1),children:(0,o.jsx)(u.Z,{className:"h-3 w-3"})})}),[n,c,a]);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"table-cell w-10 text-center text-xs text-muted-foreground bg-muted",children:a+1}),d,m]})},(e,a)=>{if(e.readOnly!==a.readOnly||e.rowIndex!==a.rowIndex||e.rowData.length!==a.rowData.length||e.headers.length!==a.headers.length)return!1;if(e.rowData.length<=10){for(let t=0;t<e.rowData.length;t++){if(e.rowData[t]!==a.rowData[t])return!1;let r="".concat(e.rowIndex,"-").concat(t);if(e.modifiedCellsMap[r]!==a.modifiedCellsMap[r])return!1}return!0}{let t=Object.keys(e.modifiedCellsMap).filter(a=>a.startsWith("".concat(e.rowIndex,"-"))&&e.modifiedCellsMap[a]),r=Object.keys(a.modifiedCellsMap).filter(e=>e.startsWith("".concat(a.rowIndex,"-"))&&a.modifiedCellsMap[e]);return!(t.length!==r.length||t.some(e=>!a.modifiedCellsMap[e]))&&JSON.stringify(e.rowData)===JSON.stringify(a.rowData)}});aQ.displayName="OptimizedRow";let aY=(e,a)=>{let t={};return e&&e.length>0?e.forEach(e=>{t["".concat(e.row,"-").concat(e.col)]=!0}):a&&(t["".concat(a.row,"-").concat(a.col)]=!0),!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e4;aq.size>e&&Array.from(aq.keys()).slice(0,1e3).forEach(e=>aq.delete(e))}(),t},aX=[{text:"Crie uma tabela de controle de horas",icon:(0,o.jsx)(l.Z,{className:"h-3 w-3"})},{text:"Adicione valida\xe7\xe3o de dados na coluna B",icon:(0,o.jsx)(i.Z,{className:"h-3 w-3"})},{text:"Gere um gr\xe1fico de barras com os dados",icon:(0,o.jsx)(c.Z,{className:"h-3 w-3"})},{text:"Calcule a m\xe9dia da coluna C",icon:(0,o.jsx)(i.Z,{className:"h-3 w-3"})},{text:"Formate a tabela com cores alternadas",icon:(0,o.jsx)(i.Z,{className:"h-3 w-3"})}],aK=[{title:"Bem-vindo ao Excel Copilot",content:"Este assistente permite criar e editar planilhas atrav\xe9s de comandos em linguagem natural.",target:"header"},{title:"Assistente de IA",content:"Aqui voc\xea pode digitar comandos como 'Crie uma tabela de vendas' ou 'Calcule a m\xe9dia da coluna B'.",target:"ai-assistant"},{title:"Sugest\xf5es R\xe1pidas",content:"Exemplos de comandos que voc\xea pode usar. Clique em um deles para executar.",target:"suggestions"},{title:"Planilha Interativa",content:"Sua planilha ser\xe1 atualizada automaticamente conforme seus comandos. Voc\xea tamb\xe9m pode editar c\xe9lulas manualmente.",target:"spreadsheet"}],a$=(0,j.memo)(e=>{let{command:a,onClick:t}=e;return(0,o.jsxs)(k.Button,{variant:"ghost",className:"h-8 px-2 text-sm justify-start w-full hover:bg-accent",onClick:t,children:[(0,o.jsx)("span",{className:"mr-2",children:a.icon}),(0,o.jsx)("span",{className:"truncate",children:a.text})]})});function a0(e){let{workbookId:a,initialData:r,readOnly:s=!1,onSave:l,initialCommand:c}=e,[E,C]=(0,j.useState)(r||{headers:["A","B","C"],rows:[["","",""],["","",""],["","",""]],charts:[],name:"Nova Planilha"}),[S,O]=(0,j.useState)([]),[R,F]=(0,j.useState)(-1),[T,I]=(0,j.useState)(!1),[D,L]=(0,j.useState)(!1),[_,z]=(0,j.useState)(!1),[M,Z]=(0,j.useState)(!1),[P,U]=(0,j.useState)(null),[q,G]=(0,j.useState)(!1),[J,W]=(0,j.useState)(!1),[Y,X]=(0,j.useState)(!1),[K,$]=(0,j.useState)(0),[ee,ea]=(0,j.useState)(!1),et=(0,j.useRef)(null),er=(0,j.useRef)([]),[es,eo]=(0,j.useState)(null),[en,el]=(0,j.useState)(null),[ec,ed]=(0,j.useState)(!1),[eu,em]=(0,j.useState)(null),[ep,eh]=(0,j.useState)([]),ef=(0,j.useRef)(null),eg=(0,j.useCallback)((e,a)=>{let t=setTimeout(()=>{e(),er.current=er.current.filter(e=>e!==t)},a);return er.current.push(t),t},[]),ex=(0,j.useCallback)(e=>{if(S.length>0&&JSON.stringify(S[S.length-1])===JSON.stringify(e))return;let a=S.slice(0,R+1).slice(-19);O([...a,JSON.parse(JSON.stringify(e))]),F(a.length)},[S,R]),{processExcelCommand:ev,isProcessing:ey,lastModifiedCells:eb}=function(){let{onDataChange:e,onAddHistory:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[r,s]=(0,j.useState)(!1),[o,n]=(0,j.useState)([]),[l,i]=(0,j.useState)([]),c=(0,j.useRef)(null),{executeOperation:d,isProcessing:u,cancelAllOperations:m}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=(0,j.useRef)(null),[r,s]=(0,j.useState)(!1),o=(0,j.useRef)(new Map);return(0,j.useEffect)(()=>{try{if(a.current=aU(new t.U(t(69037)),{type:"module"}),!a.current){aB.VERBOSE_LOGGING;return}aB.VERBOSE_LOGGING,function r(){try{a.current&&a.current.terminate(),a.current=new Worker(t.tu(new URL(t.p+t.u(38),t.b)),{type:void 0}),a.current.onmessage=a=>{let{result:t,error:r,requestId:n}=a.data,l=o.current.get(n);if(l){var i,c;if(o.current.delete(n),0===o.current.size&&s(!1),r){let a=Error(r);l.reject(a),null===(i=e.onError)||void 0===i||i.call(e,a,n)}else l.resolve(t),null===(c=e.onSuccess)||void 0===c||c.call(e,t,n)}},a.current.onerror=a=>{console.error("Erro no worker Excel:",a),o.current.forEach((a,t)=>{var r;let s=Error("Erro fatal no worker Excel");a.reject(s),null===(r=e.onError)||void 0===r||r.call(e,s,t)}),o.current.clear(),s(!1),r()}}catch(e){console.error("Erro ao reinicializar worker Excel:",e)}}()}catch(e){console.warn("Workers n\xe3o suportados ou bloqueados pela CSP, usando fallback:",e),a.current=null}return()=>{a.current&&(a.current.terminate(),a.current=null)}},[e]),{executeOperation:(0,j.useCallback)(async(e,t)=>{if(!a.current)try{s(!0);let a={updatedData:t,resultSummary:"Opera\xe7\xe3o ".concat(e.type," executada (fallback)"),modifiedCells:[]};return s(!1),a}catch(e){throw s(!1),e}let r=(0,aP.x0)();return s(!0),new Promise((s,n)=>{o.current.set(r,{operation:e,resolve:s,reject:n}),a.current.postMessage({operation:e,sheetData:t,requestId:r,operationType:e.type})})},[]),isProcessing:r,cancelAllOperations:(0,j.useCallback)(()=>{o.current.forEach((e,a)=>{e.reject(Error("Opera\xe7\xe3o cancelada"))}),o.current.clear(),s(!1)},[])}}({onSuccess:e=>{e.modifiedCells&&(n(e.modifiedCells),p())},onError:e=>{ei.logger.error("Erro no worker Excel:",e)}});(0,j.useEffect)(()=>()=>{c.current&&clearTimeout(c.current),m()},[m]);let p=(0,j.useCallback)(()=>{c.current&&clearTimeout(c.current),c.current=setTimeout(()=>{n([]),c.current=null},3e3)},[]),h=(0,j.useCallback)(e=>e.length>2||e.some(e=>{let a=e.type?String(e.type).toUpperCase():void 0;return"FILTER"===a||"SORT"===a||"PIVOT_TABLE"===a||"CHART"===a||"ADVANCED_VISUALIZATION"===a}),[]);return{processExcelCommand:(0,j.useCallback)(async(t,o)=>{if(!t||!o)return null;if(r||u)return A.toast.info("Aguarde a conclus\xe3o da opera\xe7\xe3o anterior",{duration:2e3}),null;s(!0);try{a&&a(structuredClone(o));try{throw Error("IA n\xe3o dispon\xedvel no cliente")}catch(e){(0,aZ.KE)("Erro no novo processor, tentando fallback:",e)}try{let a=await ew(t);if(!a.success||!(a.operations.length>0))return A.toast.info("Nenhuma opera\xe7\xe3o Excel",{description:a.message||"N\xe3o foi poss\xedvel extrair opera\xe7\xf5es Excel deste comando.",duration:4e3}),null;{let t=await eN(o,a.operations),r={updatedData:t.updatedData,resultSummary:Array.isArray(t.resultSummary)?t.resultSummary:[String(t.resultSummary)],modifiedCells:t.modifiedCells};return i(r.resultSummary),r.modifiedCells&&(n(r.modifiedCells),p()),e&&e(r.updatedData),A.toast.success("Opera\xe7\xf5es executadas",{description:r.resultSummary.join("; "),duration:3e3}),r.updatedData}}catch(e){(0,aZ.H)("Erro no parser de comandos:",e)}return A.toast.error("N\xe3o foi poss\xedvel executar o comando",{description:"Tente reformular seu comando ou use um exemplo da lista de sugest\xf5es.",duration:4e3}),null}catch(e){return(0,aZ.H)("Erro ao processar comando Excel:",e),A.toast.error("Erro ao processar comando",{description:e instanceof Error?e.message:"Ocorreu um erro desconhecido.",duration:4e3}),null}finally{s(!1)}},[r,u,e,a,p,d,h]),isProcessing:r||u,lastModifiedCells:o,lastOperationSummary:l}}({onDataChange:C,onAddHistory:ex}),{isConnected:ej,updateCursor:eA,broadcastCellChange:eE}=an(a);(0,j.useCallback)(e=>ev(e,E),[ev,E]);let eC=(0,j.useCallback)(e=>{eo(e.interpretation),el({id:e.commandId||e._commandId||"",command:e.interpretation})},[]),eS=(0,j.useCallback)(e=>{e&&Array.isArray(e)&&0!==e.length&&(ex(E),C(t=>{let r={...t};r.headers=Array.isArray(t.headers)?[...t.headers]:[],r.rows=Array.isArray(t.rows)?[...t.rows]:[];let s=[];return e.forEach(e=>{if(e&&"object"==typeof e){if("cell_update"===e.type&&"number"==typeof e.row&&"number"==typeof e.col){if(Array.isArray(r.rows[e.row])||(r.rows[e.row]=Array(r.headers.length).fill("")),e.row>=0&&e.col>=0&&Array.isArray(r.headers)&&e.col<r.headers.length&&Array.isArray(r.rows)&&void 0!==r.rows[e.row]&&Array.isArray(r.rows[e.row]))try{r.rows[e.row]&&"number"==typeof e.col&&(r.rows[e.row][e.col]=e.value,s.push({row:e.row,col:e.col}))}catch(t){ei.logger.error("SpreadsheetEditor: Erro ao atualizar c\xe9lula",t,{row:e.row,col:e.col,operation:e.type,workbookId:a})}}else if("add_row"===e.type){let e=Array(r.headers.length).fill("");r.rows.push(e)}else"add_column"===e.type&&Array.isArray(r.headers)&&Array.isArray(r.rows)&&(r.headers.push(e.name||"Coluna ".concat(r.headers.length+1)),r.rows.forEach((e,a)=>{Array.isArray(e)?r.rows[a]=[...e,""]:r.rows[a]=Array(r.headers.length).fill("")}))}}),s.length>0&&U(s[0]||null),r}))},[E,ex,U]),{sendMessage:ek,isProcessing:eO,confirmAndExecute:eR,cancelCommand:eF,pendingInterpretation:eT,messages:eI,error:eD,clearMessages:eL,commandStatus:e_}=aV({workbookId:a,onMessageReceived:e=>{if(e)try{let a=JSON.parse(e);a.operations&&eS(a.operations)}catch(t){ei.logger.error("SpreadsheetEditor: Erro ao processar resposta da IA",t,{content:null==e?void 0:e.substring(0,100),workbookId:a})}},onInterpretation:eC});(0,j.useEffect)(()=>{ef.current={sendMessage:ek,isProcessing:eO,confirmAndExecute:eR,cancelCommand:eF,pendingInterpretation:eT,messages:eI,error:eD,clearMessages:eL,commandStatus:e_}},[ek,eO,eR,eF,eT,eI,eD,eL,e_]);let[ez,eM]=(0,j.useState)(null),[eV,eZ]=(0,j.useState)(!1),eP=(0,N.useRouter)();(0,j.useEffect)(()=>{let e=()=>{z(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,j.useEffect)(()=>()=>{er.current.forEach(e=>clearTimeout(e)),er.current=[]},[]);let eB=(0,j.useCallback)(()=>{R>0&&(F(R-1),C(JSON.parse(JSON.stringify(S[R-1]))),A.toast.info("A\xe7\xe3o desfeita"))},[S,R]),eU=(0,j.useCallback)(()=>{R<S.length-1&&(F(R+1),C(JSON.parse(JSON.stringify(S[R+1]))),A.toast.info("A\xe7\xe3o refeita"))},[S,R]);(0,j.useEffect)(()=>{r&&(C(r),O([JSON.parse(JSON.stringify(r))]),F(0))},[r]);let eq=(0,j.useCallback)(async()=>{if(!s)try{if(I(!0),l){await l(E),A.toast.success("Planilha salva com sucesso");return}if(!(await fetch("/api/workbooks/".concat(a,"/sheets"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:E.name||"Sem nome",data:JSON.stringify(E)})})).ok)throw Error("Erro ao salvar planilha");A.toast.success("Planilha salva com sucesso")}catch(e){ei.logger.error("SpreadsheetEditor: Erro ao salvar planilha",e,{workbookId:a,spreadsheetName:E.name,readOnly:s}),A.toast.error("Erro ao salvar planilha")}finally{I(!1)}},[a,E,l,s]),eG=(0,j.useCallback)(e=>{e.trim()&&ek(e)},[ek]),eH=(0,j.useMemo)(()=>aY(eb,P),[eb,P]),eJ=(0,j.useCallback)(async(e,a,t)=>{if(!s&&"number"==typeof e&&"number"==typeof a&&!(e<0)&&!(a<0)&&(ex(E),C(r=>{if(!r||!Array.isArray(r.headers))return r;let s={...r};return s.rows=Array.isArray(r.rows)?[...r.rows]:[],Array.isArray(s.rows[e])?s.rows[e]=[...s.rows[e]]:s.rows[e]=Array(s.headers.length).fill(""),a<s.headers.length&&(s.rows[e][a]=t),s}),U({row:e,col:a}),ej))try{let r="".concat(String.fromCharCode(65+a)).concat(e+1);await eE("sheet1",r,t),await eA("sheet1",r)}catch(e){console.error("Erro ao enviar mudan\xe7a via Real-time:",e)}},[s,ex,E,U,ej,eE,eA]),eW=()=>{s||(ex(E),C(e=>{let a;let t=e.headers.length>0?e.headers[e.headers.length-1]:null;a=t&&/^[A-Z]$/.test(t)?String.fromCharCode((t.charCodeAt(0)||64)+1):"Coluna ".concat(e.headers.length+1);let r={...e};return r.headers=[...e.headers,a],r.rows=Array.isArray(e.rows)?e.rows.map(e=>Array.isArray(e)?[...e,""]:Array(r.headers.length).fill("")):[],r}),A.toast.success("Coluna adicionada"))},eQ=(0,j.useCallback)(()=>{if(s)return;ex(E);let e=Array(E.headers.length).fill("");C(a=>{let t={...a};return t.rows=Array.isArray(a.rows)?[...a.rows,e]:[e],t}),A.toast.success("Linha adicionada")},[s,E,ex]),eY=e=>{if(s)return;ex(E);let a=[...E.rows];a.splice(e,1),C({...E,rows:a})},eX=e=>{if(s)return;ex(E);let a=[...E.headers];a.splice(e,1);let t=E.rows.map(a=>{let t=[...a];return t.splice(e,1),t});C({...E,headers:a,rows:t})};(0,j.useCallback)(e=>{if(ey||s){A.toast.info("Aguarde",{description:"Espere o comando atual terminar antes de enviar outro.",duration:2e3});return}ek(e),A.toast.success("Comando enviado",{description:'Executando: "'.concat(e,'"'),duration:2e3}),D&&L(!1)},[ey,s,ek,D]),(0,j.useCallback)(()=>{L(!0)},[]),(0,j.useCallback)(e=>{if(T||ey){A.toast.info("Aguarde",{description:"Concluindo opera\xe7\xf5es atuais antes de navegar...",duration:2e3});return}S.length>1&&S[R]!==r?confirm("H\xe1 altera\xe7\xf5es n\xe3o salvas. Deseja sair mesmo assim?")&&(window.location.href=e):window.location.href=e},[S,R,r,T,ey]),(0,j.useCallback)(()=>{A.toast.info("Conectando com Excel Desktop",{description:"Iniciando conex\xe3o com o aplicativo Excel...",duration:3e3}),desktopBridge.connect().then(e=>{e?A.toast.success("Excel conectado",{description:"Sincroniza\xe7\xe3o de dados ativada entre o navegador e Excel desktop",duration:3e3}):A.toast.error("Falha na conex\xe3o",{description:"N\xe3o foi poss\xedvel conectar ao Excel. Verifique se o Excel Copilot Desktop est\xe1 instalado e em execu\xe7\xe3o.",duration:4e3})}).catch(e=>{console.error("Erro na conex\xe3o:",e),A.toast.error("Erro na conex\xe3o",{description:"Ocorreu um erro ao tentar conectar ao Excel.",duration:4e3})})},[desktopBridge]),(0,j.useEffect)(()=>{localStorage.getItem("excel_copilot_visited")||(ea(!0),X(!0),localStorage.setItem("excel_copilot_visited","true"))},[]);let eK=(0,j.useCallback)(()=>{W(!0),eg(()=>{W(!1)},3e3)},[eg]);(0,j.useEffect)(()=>{let e=et.current;return e&&(e.addEventListener("input",eK),e.addEventListener("click",eK)),()=>{e&&(e.removeEventListener("input",eK),e.removeEventListener("click",eK))}},[eg]),(0,j.useCallback)(()=>{K<aK.length-1?$(K+1):X(!1)},[K]),(0,j.useCallback)(()=>{X(!1)},[]);let[e$,e0]=(0,j.useState)(!1),[e1,e2]=(0,j.useState)(!1),e4=(0,j.useCallback)(()=>[{key:"s",description:"Salvar planilha",action:eq,modifiers:{ctrl:!0}},{key:"z",description:"Desfazer \xfaltima a\xe7\xe3o",action:eB,modifiers:{ctrl:!0}},{key:"y",description:"Refazer \xfaltima a\xe7\xe3o",action:eU,modifiers:{ctrl:!0}},{key:"+",description:"Adicionar linha",action:eQ,modifiers:{ctrl:!0,shift:!0}},{key:"=",description:"Adicionar coluna",action:eW,modifiers:{ctrl:!0,shift:!0}},{key:"k",description:"Abrir paleta de comandos",action:()=>L(!0),modifiers:{ctrl:!0}},{key:"/",description:"Focar no chat assistente",action:()=>{var e;return null===(e=document.getElementById("chat-input"))||void 0===e?void 0:e.focus()},modifiers:{ctrl:!0}},{key:"f",description:"Alternar modo tela cheia",action:()=>e2(!e1),modifiers:{ctrl:!0,shift:!0}},{key:"Escape",description:"Fechar di\xe1logos/pain\xe9is abertos",action:()=>{L(!1),e0(!1),e1&&e2(!1)}}],[eB,eU,eQ,eW,e1]),e5=(0,j.useCallback)(e=>{if("INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&!e.target.isContentEditable)for(let s of e4()){var a,t,r;if(e.key.toLowerCase()===s.key.toLowerCase()&&(!(null===(a=s.modifiers)||void 0===a?void 0:a.ctrl)||e.ctrlKey)&&(!(null===(t=s.modifiers)||void 0===t?void 0:t.alt)||e.altKey)&&(!(null===(r=s.modifiers)||void 0===r?void 0:r.shift)||e.shiftKey)){e.preventDefault(),s.action();return}}},[e4]);(0,j.useEffect)(()=>{let e=document.documentElement;e1?e.requestFullscreen&&e.requestFullscreen():document.fullscreenElement&&document.exitFullscreen&&document.exitFullscreen()},[e1]),(0,j.useEffect)(()=>(window.addEventListener("keydown",e5),()=>{window.removeEventListener("keydown",e5)}),[e5]);let[e3,e6]=(0,j.useState)(!0),[e8,e7]=(0,j.useState)(""),ae=e=>{e7(e),e6(""===e.trim())};(0,j.useEffect)(()=>{if(c&&!ey){let e=setTimeout(()=>{ek&&ek(c)},1e3);return()=>clearTimeout(e)}},[c,ek,ey]),(0,j.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/user/api-usage");if(e.ok){let a=await e.json();eM({used:a.apiCallsUsed,limit:a.apiCallsLimit}),"free"===a.plan&&a.apiCallsUsed/a.apiCallsLimit>=.8&&eZ(!0)}}catch(e){console.error("Erro ao buscar informa\xe7\xf5es de uso:",e)}};e(),ek.length%5==0&&ek.length>0&&e()},[ek.length]);let aa=()=>{eP.push("/pricing")},at=()=>{eP.push("/api/checkout/trial")},ar=(0,j.useMemo)(()=>aX.map((e,a)=>(0,o.jsx)(a$,{command:e,onClick:()=>eG(e.text)},a)),[eG]),as=(0,j.useRef)(null),ao=(0,j.useRef)(null),ai=(0,n.MG)({count:E.rows.length,getScrollElement:()=>as.current,estimateSize:()=>36,overscan:10}),ad=(0,j.useMemo)(()=>(0,o.jsx)(aW,{visibleRows:ai.getVirtualItems().length,totalRows:E.rows.length,virtualizer:ai}),[ai,E.rows.length]),au=(0,j.useCallback)(async()=>{if(ef.current&&en){eo(null);try{await ef.current.confirmAndExecute()&&(em(en),ed(!0),setTimeout(()=>{ec&&ed(!1)},5e3))}catch(e){console.error("Erro ao executar comando:",e),A.toast.error("Erro ao executar o comando")}finally{el(null)}}},[en,ef]),ay=(0,j.useCallback)(()=>{ef.current&&(ef.current.cancelCommand(),eo(null),el(null))},[ef]),ab=(0,j.useCallback)(async e=>{try{await aM.M.storeFeedback(e),ed(!1),em(null)}catch(e){console.error("Erro ao enviar feedback:",e),A.toast.error("N\xe3o foi poss\xedvel enviar seu feedback")}},[]),aw=(0,j.useCallback)(()=>{ed(!1),em(null)},[]);return(0,o.jsxs)("div",{className:"flex flex-col h-full w-full relative",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center border-b p-2 bg-background/80 backdrop-blur-sm",children:[(0,o.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,o.jsx)(av.MD,{variant:"ghost",size:"icon",onClick:eB,disabled:R<=0||s,title:"Desfazer (Ctrl+Z)",children:(0,o.jsx)(m.Z,{className:"h-4 w-4"})}),(0,o.jsx)(av.MD,{variant:"ghost",size:"icon",onClick:eU,disabled:R>=S.length-1||s,title:"Refazer (Ctrl+Y)",children:(0,o.jsx)(p.Z,{className:"h-4 w-4"})}),(0,o.jsx)("span",{className:"w-px h-4 bg-border mx-1"}),(0,o.jsx)(av.MD,{variant:"ghost",size:"icon",onClick:eq,disabled:T||s,title:"Salvar (Ctrl+S)",children:T?(0,o.jsx)(h.Z,{className:"h-4 w-4 animate-spin"}):(0,o.jsx)(f.Z,{className:"h-4 w-4"})}),(0,o.jsxs)("div",{className:"hidden md:flex gap-1 ml-2",children:[(0,o.jsx)(e9,{workbookId:a,workbookName:E.name,sheets:[{name:E.name,data:E}]}),(0,o.jsx)(a_,{workbookId:a,saveToSupabase:!0,onUpload:e=>{e&&e.sheets&&e.sheets.length>0&&(ex(E),C(e.sheets[0].data))}})]})]}),(0,o.jsxs)("div",{className:"items-center gap-2 hidden md:flex",children:[(0,o.jsx)(al,{workbookId:a,className:"mr-2"}),(0,o.jsxs)(k.Button,{variant:"ghost",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>G(!q),children:[q?(0,o.jsx)(g.Z,{className:"h-3 w-3"}):(0,o.jsx)(x.Z,{className:"h-3 w-3"}),q?"Mostrar":"Ocultar"," AI"]}),(0,o.jsxs)(k.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>L(!0),children:[(0,o.jsx)(v.Z,{className:"h-3 w-3"}),"Comandos"]}),(0,o.jsxs)(k.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>{},children:[(0,o.jsx)(y.Z,{className:"h-3 w-3"}),"Tela Cheia"]})]}),(0,o.jsx)("div",{className:"flex md:hidden",children:(0,o.jsxs)(k.Button,{variant:"outline",size:"sm",className:"h-8",onClick:()=>Z(!0),children:[(0,o.jsx)(b.Z,{className:"h-4 w-4 mr-2"}),"AI Chat"]})})]}),(0,o.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,o.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[ad,(0,o.jsx)("div",{ref:as,className:"overflow-auto border border-border rounded-md",style:{height:"400px",width:"100%"},children:(0,o.jsxs)("div",{className:"table w-full relative",children:[(0,o.jsx)("div",{ref:ao,className:"table-header-group sticky top-0 bg-background z-10",children:(0,o.jsxs)("div",{className:"table-row",children:[(0,o.jsx)("div",{className:"table-cell w-10 text-center text-xs font-medium bg-muted",children:"#"}),E.headers.map((e,a)=>(0,o.jsxs)("div",{className:"table-cell p-2 font-medium bg-muted",children:[e,!s&&(0,o.jsx)(av.Kk,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 ml-1",actionId:"column-".concat(a),onAction:()=>eX(a),"aria-label":"Remover coluna ".concat(e),children:(0,o.jsx)(u.Z,{className:"h-3 w-3"})})]},a)),(0,o.jsx)("div",{className:"table-cell w-10 bg-muted"})]})}),(0,o.jsx)("div",{className:"table-row-group relative",style:{height:"".concat(ai.getTotalSize(),"px")},children:ai.getVirtualItems().map(e=>{let a=E.rows[e.index]||[];return(0,o.jsx)("div",{className:"table-row",style:{position:"absolute",top:0,left:0,width:"100%",height:"".concat(e.size,"px"),transform:"translateY(".concat(e.start,"px)")},children:(0,o.jsx)(aQ,{rowIndex:e.index,rowData:a,headers:E.headers,modifiedCellsMap:eH,readOnly:s,onCellChange:eJ,onRemoveRow:eY})},e.index)})})]})}),eb&&eb.length>0&&(0,o.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-3 py-1.5 rounded-full text-sm font-medium shadow-lg animate-in fade-in slide-in-from-bottom-5 duration-300",children:1===eb.length?"C\xe9lula atualizada":"".concat(eb.length," c\xe9lulas atualizadas")})]}),(0,o.jsx)("div",{className:"\n            h-full border-l overflow-hidden transition-all duration-300 ease-in-out\n            ".concat(q?"w-0 opacity-0":"w-80 opacity-100","\n            hidden md:block\n          "),"data-tutorial-target":"ai-assistant",children:(0,o.jsxs)("div",{className:"flex flex-col h-full",children:[(0,o.jsxs)("div",{className:"p-3 border-b flex items-center justify-between",children:[(0,o.jsxs)("h3",{className:"font-semibold text-sm flex items-center",children:[(0,o.jsx)(d.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),(0,o.jsx)(k.Button,{variant:"ghost",size:"icon",onClick:()=>G(!0),children:(0,o.jsx)(x.Z,{className:"h-4 w-4"})})]}),(0,o.jsx)(V.x,{className:"flex-1 p-3",children:0===eI.length?(0,o.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[(0,o.jsx)(d.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),(0,o.jsx)("h3",{className:"font-medium mb-1",children:"Excel Assistente"}),(0,o.jsx)("p",{className:"text-sm max-w-xs mx-auto",children:"Utilize linguagem natural para manipular sua planilha. Digite comandos como:"}),(0,o.jsx)("div",{className:"mt-4 space-y-2 text-xs",children:ar})]}):(0,o.jsx)("div",{className:"space-y-4",children:eI.map((e,a)=>(0,o.jsxs)("div",{className:"\n                        p-3 rounded-lg text-sm\n                        ".concat("user"===e.role?"bg-primary/10 border border-primary/20":"bg-muted","\n                      "),children:[(0,o.jsx)("div",{className:"text-xs font-medium mb-1 text-muted-foreground",children:"user"===e.role?"Voc\xea":"Excel Copilot"}),(0,o.jsx)("div",{className:"whitespace-pre-wrap",children:e.content})]},a))})}),(0,o.jsx)("div",{className:"p-3 border-t",children:(0,o.jsx)(B,{onSendMessage:ek,isLoading:ey,placeholder:"Digite um comando...",onChange:ae,showExamples:!1})})]})})]}),q&&!_&&(0,o.jsx)(av.MD,{variant:"default",size:"sm",className:"fixed right-4 bottom-4 shadow-lg rounded-full h-10 w-10 p-0",onClick:()=>G(!1),children:(0,o.jsx)(d.Z,{className:"h-4 w-4"})}),_&&(0,o.jsxs)("div",{className:"\n      fixed inset-0 bg-background/95 backdrop-blur-sm z-50 flex flex-col\n      ".concat(M?"translate-y-0":"translate-y-full","\n      transition-transform duration-300 ease-in-out\n    "),children:[(0,o.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,o.jsxs)("h3",{className:"font-semibold flex items-center",children:[(0,o.jsx)(d.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),(0,o.jsx)(k.Button,{variant:"ghost",size:"icon",onClick:()=>Z(!1),children:(0,o.jsx)(u.Z,{className:"h-5 w-5"})})]}),(0,o.jsx)(V.x,{className:"flex-1 p-4",children:0===eI.length?(0,o.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[(0,o.jsx)(d.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),(0,o.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Excel Copilot"}),(0,o.jsx)("p",{className:"text-sm max-w-sm mx-auto",children:"Envie comandos em linguagem natural para manipular sua planilha"})]}):(0,o.jsx)("div",{className:"space-y-4",children:eI.map((e,a)=>(0,o.jsx)("div",{className:"\n                  p-3 rounded-lg max-w-[85%]\n                  ".concat("user"===e.role?"bg-primary text-primary-foreground ml-auto":"bg-muted text-foreground border","\n                "),children:e.content},a))})}),(0,o.jsx)("div",{className:"p-4 border-t",children:(0,o.jsx)(B,{onSendMessage:ek,isLoading:ey,placeholder:"Digite um comando...",showExamples:0===eI.length})})]}),(0,o.jsx)(ac,{open:eV,onOpenChange:eZ,children:(0,o.jsxs)(am,{className:"max-w-md",children:[(0,o.jsxs)(ap,{children:[(0,o.jsxs)(af,{className:"flex items-center gap-2",children:[(0,o.jsx)(w.Z,{className:"h-5 w-5 text-amber-500"}),"Voc\xea est\xe1 chegando ao limite"]}),(0,o.jsxs)(ag,{className:"text-base",children:["Voc\xea j\xe1 utilizou ",(null==ez?void 0:ez.used)||0," de ",(null==ez?void 0:ez.limit)||50," comandos dispon\xedveis no seu plano. Para continuar utilizando todos os recursos do Excel Copilot, escolha uma op\xe7\xe3o abaixo:"]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 gap-4 my-4",children:[(0,o.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>at(),children:[(0,o.jsxs)("h3",{className:"font-semibold flex items-center",children:[(0,o.jsx)(d.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Experimente o Plano Pro Gr\xe1tis por 7 dias"]}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Acesso total a todos os recursos sem limita\xe7\xf5es durante 7 dias. Ser\xe1 necess\xe1rio informar um cart\xe3o, mas voc\xea pode cancelar a qualquer momento."})]}),(0,o.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>aa(),children:[(0,o.jsxs)("h3",{className:"font-semibold flex items-center",children:[(0,o.jsx)(i.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Fazer Upgrade para o Plano Pro"]}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"R$20/m\xeas ou R$200/ano. Acesso ilimitado a todos os recursos premium."})]})]}),(0,o.jsx)(ah,{children:(0,o.jsx)(ax,{children:"Continuar no Plano Free"})})]})}),ez&&ez.used/ez.limit>=.8&&ez.used/ez.limit<1&&(0,o.jsxs)("div",{className:"bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800/50 border px-4 py-2 flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(w.Z,{className:"h-4 w-4 text-amber-500"}),(0,o.jsxs)("span",{className:"text-sm text-amber-800 dark:text-amber-300",children:["Voc\xea utilizou ",Math.round(ez.used/ez.limit*100),"% do seu limite mensal de comandos."]})]}),(0,o.jsx)(k.Button,{variant:"ghost",size:"sm",onClick:()=>eZ(!0),children:"Fazer Upgrade"})]}),ez&&ez.used>=ez.limit&&(0,o.jsxs)("div",{className:"bg-destructive/10 border-destructive/30 border px-4 py-2 flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(w.Z,{className:"h-4 w-4 text-destructive"}),(0,o.jsx)("span",{className:"text-sm text-destructive",children:"Voc\xea atingiu 100% do seu limite mensal de comandos."})]}),(0,o.jsx)(k.Button,{variant:"outline",size:"sm",onClick:()=>eZ(!0),children:"Fazer Upgrade Agora"})]}),(0,o.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 p-4 border-t dark:border-gray-800",children:[(0,o.jsxs)("div",{className:"flex-1",children:[es&&(0,o.jsx)(Q,{command:(null==en?void 0:en.command)||"",interpretation:es,isLoading:ey,onExecute:au,onCancel:ay}),ec&&eu&&(0,o.jsx)(H,{commandId:eu.id,command:eu.command,onDismiss:aw,onFeedbackSubmit:ab}),(0,o.jsx)(B,{onSendMessage:ek,isLoading:ey,disabled:ey||s,autoFocus:!0,onChange:ae})]}),(0,o.jsx)("div",{className:"w-full lg:w-64 space-y-2"})]})]})}a$.displayName="MemoizedQuickCommandButton"},49465:function(e,a,t){"use strict";t.d(a,{M:function(){return n}});var r=t(18473),s=t(25566);class o{static getInstance(){return o.instance||(o.instance=new o),o.instance}async storeFeedback(e){let a={...e,timestamp:new Date().toISOString()};if(this.feedbackItems.unshift(a),this.feedbackItems.length>this.MAX_STORED_ITEMS&&(this.feedbackItems=this.feedbackItems.slice(0,this.MAX_STORED_ITEMS)),this.saveToStorage(),"true"===s.env.NEXT_PUBLIC_ENABLE_FEEDBACK_API)try{await fetch("/api/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}catch(e){r.logger.error("Erro ao enviar feedback para API:",e)}}getAnalytics(){let e=this.feedbackItems.length;if(0===e)return{totalCommands:0,successRate:0,commonIssues:[],commandPatterns:[]};let a=this.feedbackItems.filter(e=>e.successful).length,t=this.feedbackItems.filter(e=>!e.successful&&e.feedbackText),r=new Map;t.forEach(e=>{var a;let t=(null===(a=e.feedbackText)||void 0===a?void 0:a.toLowerCase())||"",s=!1;for(let e of[{word:"entend",issue:"N\xe3o entendeu o comando"},{word:"error",issue:"Erro na execu\xe7\xe3o"},{word:"lent",issue:"Performance lenta"},{word:"format",issue:"Problemas de formata\xe7\xe3o"},{word:"gr\xe1fico",issue:"Problemas com gr\xe1ficos"},{word:"tabela",issue:"Problemas com tabelas"},{word:"f\xf3rmula",issue:"Problemas com f\xf3rmulas"}])t.includes(e.word)&&(r.set(e.issue,(r.get(e.issue)||0)+1),s=!0);!s&&t.length>0&&r.set("Outros problemas",(r.get("Outros problemas")||0)+1)});let s=Array.from(r.entries()).map(e=>{let[a,t]=e;return{issue:a,count:t}}).sort((e,a)=>a.count-e.count),o=new Map;return this.feedbackItems.forEach(e=>{let a=e.command.toLowerCase();for(let t of[{words:["cri","tabela"],pattern:"Criar tabela"},{words:["gr\xe1fico","chart"],pattern:"Criar gr\xe1fico"},{words:["calcul","m\xe9dia","soma"],pattern:"C\xe1lculos"},{words:["format","cor","estilo"],pattern:"Formata\xe7\xe3o"},{words:["filtr","ordem"],pattern:"Filtro/Ordena\xe7\xe3o"}])if(t.words.some(e=>a.includes(e))){let a=o.get(t.pattern)||{success:0,total:0};o.set(t.pattern,{success:a.success+(e.successful?1:0),total:a.total+1});break}}),{totalCommands:e,successRate:a/e*100,commonIssues:s,commandPatterns:Array.from(o.entries()).map(e=>{let[a,t]=e;return{pattern:a,successRate:t.success/t.total*100,count:t.total}}).sort((e,a)=>a.count-e.count)}}saveToStorage(){if(this.feedbackStorage)try{this.feedbackStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.feedbackItems))}catch(e){r.logger.error("Erro ao salvar feedback no localStorage:",e)}}loadFromStorage(){if(this.feedbackStorage)try{let e=this.feedbackStorage.getItem(this.STORAGE_KEY);e&&(this.feedbackItems=JSON.parse(e))}catch(e){r.logger.error("Erro ao carregar feedback do localStorage:",e),this.feedbackItems=[]}}constructor(){this.feedbackItems=[],this.feedbackStorage=null,this.STORAGE_KEY="excel_copilot_feedback",this.MAX_STORED_ITEMS=100,this.feedbackStorage=window.localStorage,this.loadFromStorage()}}let n=o.getInstance()},80585:function(e,a,t){"use strict";var r,s;t.r(a),t.d(a,{DEFAULT_EXCEL_SYSTEM_PROMPT:function(){return n},ExcelAIProcessor:function(){return o},GeminiErrorType:function(){return r},createExcelAIProcessor:function(){return l},getGeminiServiceAPI:function(){return i}}),t(18473);class o{async processQuery(e){try{let a=this.preprocessQuery(e),t=this.buildPrompt(a),r=await i(),s=await r.sendMessage(t,{context:JSON.stringify(this.context),useMock:!this.useRealAI});return this.parseAIResponse(s)}catch(e){return console.error("Erro ao processar query:",e),{operations:[],error:"Erro ao processar: ".concat(e instanceof Error?e.message:String(e)),success:!1,message:"Falha ao processar query com IA"}}}preprocessQuery(e){return e.replace(/\bform\./g,"f\xf3rmula").replace(/\bcol\./g,"coluna").replace(/\btab\./g,"tabela").replace(/\bgraf\./g,"gr\xe1fico").replace(/\bcel\./g,"c\xe9lula").replace(/\bfunc\./g,"fun\xe7\xe3o").replace(/\bop\./g,"opera\xe7\xe3o").replace(/\bval\./g,"valor").replace(/\bmed\./g,"m\xe9dia")}buildPrompt(e){var a;return'\n    Analise o seguinte comando para Excel e retorne as opera\xe7\xf5es necess\xe1rias em formato JSON:\n    \n    Comando: "'.concat(e,'"\n    \n    Contexto da planilha:\n    - Planilha ativa: ').concat(this.context.activeSheet,"\n    - Sele\xe7\xe3o atual: ").concat(this.context.selection,"\n    - Cabe\xe7alhos: ").concat((null===(a=this.context.headers)||void 0===a?void 0:a.join(", "))||"N/A",'\n    \n    Retorne APENAS um objeto JSON com a seguinte estrutura:\n    {\n      "operations": [\n        {\n          "type": "TIPO_OPERACAO", // FORMULA, CHART, TABLE, FORMAT, etc.\n          "data": { ... }, // Dados espec\xedficos da opera\xe7\xe3o\n          "description": "Descri\xe7\xe3o" // Opcional, descri\xe7\xe3o da opera\xe7\xe3o\n        }\n      ],\n      "explanation": "Explica\xe7\xe3o do que foi feito" // Opcional\n    }\n    ')}parseAIResponse(e){try{let a=e.match(/\{[\s\S]*\}/);if(a){let e=a[0],t=JSON.parse(e);if(!t.operations||!Array.isArray(t.operations))throw Error("Formato de resposta inv\xe1lido: operations n\xe3o \xe9 um array");return t}return{operations:[{type:"TABLE",data:{rawResponse:e},description:"Resposta em texto: ".concat(e.substring(0,100),"...")}],explanation:"A resposta n\xe3o p\xf4de ser processada como JSON",success:!0,message:"Processamento parcial realizado"}}catch(a){return console.error("Erro ao analisar resposta da IA:",a),{operations:[{type:"TABLE",data:{error:!0},description:'Processando: "'.concat(e.substring(0,100),'..."')}],explanation:"Erro ao processar JSON da resposta",error:String(a),success:!1,message:"Falha ao analisar resposta da IA"}}}constructor(e={},a=!0){this.context={activeSheet:e.activeSheet||"Sheet1",headers:e.headers||[],selection:e.selection||"A1",recentOperations:e.recentOperations||[]},this.useRealAI=a}}let n='\nVoc\xea \xe9 um assistente especializado em Excel, capaz de ajudar a realizar opera\xe7\xf5es com planilhas.\nSua fun\xe7\xe3o \xe9 interpretar comandos em linguagem natural e convert\xea-los em opera\xe7\xf5es Excel espec\xedficas.\n\n# DIRETRIZES IMPORTANTES\n1. Sempre responda de forma estruturada usando o formato JSON abaixo\n2. Para cada comando, identifique as opera\xe7\xf5es necess\xe1rias e forne\xe7a par\xe2metros precisos\n3. Em caso de ambiguidade, escolha a interpreta\xe7\xe3o mais prov\xe1vel baseada no contexto\n4. Se n\xe3o conseguir interpretar o comando, forne\xe7a uma resposta de erro amig\xe1vel\n5. NUNCA invente dados ou colunas que n\xe3o existam no contexto atual\n\n# FORMATO DE RESPOSTA\n{\n  "operations": [\n    {\n      "type": "TIPO_DA_OPERA\xc7\xc3O",\n      "data": { ... par\xe2metros espec\xedficos da opera\xe7\xe3o ... }\n    }\n  ],\n  "explanation": "Breve explica\xe7\xe3o do que ser\xe1 feito",\n  "interpretation": "Como voc\xea entendeu o comando do usu\xe1rio"\n}\n\n# TIPOS DE OPERA\xc7\xd5ES DISPON\xcdVEIS\n\n## F\xd3RMULAS\n- FORMULA: Aplicar f\xf3rmulas em c\xe9lulas\n  {\n    "type": "FORMULA",\n    "data": {\n      "formula": "=SOMA(A1:A10)", \n      "range": "B1" | ["B1", "B2"] | "B1:B10"\n    }\n  }\n\n## DADOS\n- FILTER: Filtrar dados\n  {\n    "type": "FILTER",\n    "data": {\n      "column": "A" | 1,\n      "condition": ">" | "<" | "=" | "contains" | "between",\n      "value": 100 | "texto" | [10, 20]\n    }\n  }\n- SORT: Ordenar dados\n  {\n    "type": "SORT",\n    "data": {\n      "column": "A" | 1,\n      "direction": "asc" | "desc"\n    }\n  }\n\n## VISUALIZA\xc7\xd5ES\n- CHART: Criar ou modificar gr\xe1ficos\n  {\n    "type": "CHART",\n    "data": {\n      "type": "bar" | "line" | "pie" | "scatter" | "area",\n      "title": "T\xedtulo do gr\xe1fico",\n      "labels": "A1:A10", // Eixo X ou categorias\n      "datasets": ["B1:B10", "C1:C10"], // S\xe9ries de dados\n      "options": { ... op\xe7\xf5es adicionais ... }\n    }\n  }\n\n## FORMATA\xc7\xc3O\n- CONDITIONAL_FORMAT: Formata\xe7\xe3o condicional\n  {\n    "type": "CONDITIONAL_FORMAT",\n    "data": {\n      "range": "A1:B10",\n      "rule": "greater" | "less" | "equal" | "between" | "text" | "date",\n      "value": 100 | [10, 20] | "texto",\n      "format": {\n        "background": "#F5F5F5",\n        "textColor": "#FF0000",\n        "bold": true | false,\n        "italic": true | false\n      }\n    }\n  }\n\n## TABELAS\n- PIVOT_TABLE: Criar tabelas din\xe2micas\n  {\n    "type": "PIVOT_TABLE",\n    "data": {\n      "source": "A1:D10",\n      "rows": ["A"], // Campos para linhas\n      "columns": ["B"], // Campos para colunas\n      "values": [{ "field": "C", "function": "sum" }], // Campos para valores\n      "filters": [{ "field": "D", "value": "X" }] // Filtros opcionais\n    }\n  }\n\n## C\xc9LULAS\n- CELL_UPDATE: Atualizar c\xe9lulas individuais\n  {\n    "type": "CELL_UPDATE",\n    "data": {\n      "updates": [\n        { "cell": "A1", "value": 100 },\n        { "cell": "B1", "value": "Texto" }\n      ]\n    }\n  }\n\n## AN\xc1LISE\n- DATA_ANALYSIS: An\xe1lise estat\xedstica\n  {\n    "type": "DATA_ANALYSIS",\n    "data": {\n      "type": "statistics" | "correlation" | "regression",\n      "range": "A1:B10",\n      "options": { ... op\xe7\xf5es espec\xedficas ... }\n    }\n  }\n\n# EXEMPLOS DE COMANDOS E RESPOSTAS\n\n## Exemplo 1: "Calcule a m\xe9dia da coluna B"\n{\n  "operations": [\n    {\n      "type": "FORMULA",\n      "data": {\n        "formula": "=M\xc9DIA(B:B)",\n        "range": "C1"\n      }\n    }\n  ],\n  "explanation": "Calculando a m\xe9dia da coluna B e colocando o resultado na c\xe9lula C1",\n  "interpretation": "Voc\xea deseja calcular a m\xe9dia de todos os valores num\xe9ricos na coluna B"\n}\n\n## Exemplo 2: "Crie uma tabela de vendas por m\xeas"\n{\n  "operations": [\n    {\n      "type": "CELL_UPDATE",\n      "data": {\n        "updates": [\n          { "cell": "A1", "value": "M\xeas" },\n          { "cell": "B1", "value": "Vendas" },\n          { "cell": "A2", "value": "Janeiro" },\n          { "cell": "A3", "value": "Fevereiro" },\n          { "cell": "A4", "value": "Mar\xe7o" },\n          { "cell": "B2", "value": 0 },\n          { "cell": "B3", "value": 0 },\n          { "cell": "B4", "value": 0 }\n        ]\n      }\n    }\n  ],\n  "explanation": "Criando uma tabela de vendas por m\xeas com layout b\xe1sico",\n  "interpretation": "Voc\xea deseja criar uma nova tabela para registrar vendas mensais"\n}\n\n## Exemplo 3: "Gere um gr\xe1fico de barras com os dados da coluna A e B"\n{\n  "operations": [\n    {\n      "type": "CHART",\n      "data": {\n        "type": "bar",\n        "title": "Gr\xe1fico de Barras A vs B",\n        "labels": "A1:A10",\n        "datasets": ["B1:B10"],\n        "options": {\n          "legend": true,\n          "horizontalBar": false\n        }\n      }\n    }\n  ],\n  "explanation": "Criando um gr\xe1fico de barras usando dados das colunas A e B",\n  "interpretation": "Voc\xea deseja visualizar os dados das colunas A e B em um gr\xe1fico de barras"\n}\n\n# CONTEXTO ATUAL DA PLANILHA\n{contextInfo}\n';function l(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new o({},e)}async function i(){return{async sendMessage(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,...a})});if(!t.ok)throw Error("API Error: ".concat(t.statusText));return(await t.json()).response}}}(s=r||(r={})).NETWORK_ERROR="NETWORK_ERROR",s.API_ERROR="API_ERROR",s.RATE_LIMIT="RATE_LIMIT",s.INVALID_REQUEST="INVALID_REQUEST",s.AUTHENTICATION_ERROR="AUTHENTICATION_ERROR",s.UNKNOWN="UNKNOWN",s.API_UNAVAILABLE="API_UNAVAILABLE"},15589:function(e,a,t){"use strict";t.d(a,{OK:function(){return s},z6:function(){return r}});let r={radius:{sm:"rounded",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl",full:"rounded-full"},width:{none:"border-0",thin:"border",medium:"border-2",thick:"border-4"},card:"border rounded-lg",input:"border rounded-md",button:"rounded-md"},s={transition:{fast:"transition-all duration-150 ease-in-out",medium:"transition-all duration-300 ease-in-out",slow:"transition-all duration-500 ease-in-out"},hover:{scale:"hover:scale-105",brightness:"hover:brightness-110",opacity:"hover:opacity-90"},spin:"animate-spin",pulse:"animate-pulse",loading:"animate-pulse",shake:"animate-[shake_0.82s_cubic-bezier(.36,.07,.19,.97)_both]"},o="bg-primary text-primary-foreground",n="hover:bg-primary/90",l="bg-primary/10 text-primary",i="bg-secondary text-secondary-foreground",c="hover:bg-secondary/90",d="bg-destructive text-destructive-foreground",u="hover:bg-destructive/90",m="bg-destructive/10 text-destructive";"".concat(r.button," font-medium ").concat(s.transition.fast),"".concat(o," ").concat(n),"".concat(i," ").concat(c),"".concat(d," ").concat(u),"".concat(r.input," ").concat("px-4 py-2"," w-full ").concat("focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"),"".concat(m," ").concat(r.radius.md," ").concat("p-2"),"".concat("bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"," ").concat(r.radius.md," ").concat("p-2"),"".concat("bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"," ").concat(r.radius.md," ").concat("p-2"),"".concat(l," ").concat(r.radius.md," ").concat("p-2"),"".concat("p-2"," ").concat(r.radius.md," ").concat(s.transition.fast," hover:bg-accent hover:text-accent-foreground"),"".concat("p-2"," ").concat(r.radius.md," bg-accent text-accent-foreground")},58743:function(e,a,t){"use strict";t.d(a,{Nt:function(){return l},Xf:function(){return o},tt:function(){return i}}),t(13537);var r=t(62848),s=t(25566);let o={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"};o.FREE,o.PRO_MONTHLY,o.PRO_ANNUAL;let n=s.env.STRIPE_SECRET_KEY||"";function l(e){switch(e){case o.FREE:return"Gr\xe1tis";case o.PRO_MONTHLY:return"Pro Mensal";case o.PRO_ANNUAL:return"Pro Anual";default:return"Desconhecido"}}n&&new r.Z(n,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}});let i=()=>"pk_live_51RGJ6nRrKLXtzZkMtpujgPAZR4MmRmQQrImSNrq6vdCLe6gfWulXfJDaDl1K2u3DKeKUegsXvzceFVi8xwnwroic00ER63lsVr"},56143:function(e,a,t){"use strict";t.d(a,{H:function(){return o},KC:function(){return s},KE:function(){return n}});var r=t(18473);function s(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{let a=JSON.stringify(e);return Error(a)}catch(e){return Error("Unknown error")}}}function o(e,a,t){let o=s(a);r.logger.error(e,o)}function n(e,a,t){let s=null==a?void 0:"object"==typeof a?a:{value:a},o=t?{...t,...s}:s;r.logger.warn(e,o)}},64451:function(e,a,t){"use strict";var r,s,o,n,l,i,c,d,u,m;t.d(a,{ox:function(){return r}}),(i=r||(r={})).FORMULA="FORMULA",i.FILTER="FILTER",i.SORT="SORT",i.FORMAT="FORMAT",i.CHART="CHART",i.CELL_UPDATE="CELL_UPDATE",i.COLUMN_OPERATION="COLUMN_OPERATION",i.ROW_OPERATION="ROW_OPERATION",i.TABLE="TABLE",i.DATA_TRANSFORMATION="DATA_TRANSFORMATION",i.PIVOT_TABLE="PIVOT_TABLE",i.CONDITIONAL_FORMAT="CONDITIONAL_FORMAT",i.ADVANCED_CHART="ADVANCED_CHART",i.ADVANCED_VISUALIZATION="ADVANCED_VISUALIZATION",i.RANGE_UPDATE="RANGE_UPDATE",i.CELL_MERGE="CELL_MERGE",i.CELL_SPLIT="CELL_SPLIT",i.NAMED_RANGE="NAMED_RANGE",i.VALIDATION="VALIDATION",i.FREEZE_PANES="FREEZE_PANES",i.SHEET_OPERATION="SHEET_OPERATION",i.ANALYSIS="ANALYSIS",i.GENERIC="GENERIC",(c=s||(s={})).LINE="LINE",c.BAR="BAR",c.COLUMN="COLUMN",c.AREA="AREA",c.SCATTER="SCATTER",c.PIE="PIE",(d=o||(o={})).EQUALS="equals",d.NOT_EQUALS="notEquals",d.GREATER_THAN="greaterThan",d.LESS_THAN="lessThan",d.GREATER_THAN_OR_EQUAL="greaterThanOrEqual",d.LESS_THAN_OR_EQUAL="lessThanOrEqual",d.CONTAINS="contains",d.NOT_CONTAINS="notContains",d.BEGINS_WITH="beginsWith",d.ENDS_WITH="endsWith",d.BETWEEN="between",(u=n||(n={})).DISCONNECTED="disconnected",u.CONNECTING="connecting",u.CONNECTED="connected",u.ERROR="error",(m=l||(l={})).FORMULA_ERROR="FORMULA_ERROR",m.REFERENCE_ERROR="REFERENCE_ERROR",m.VALUE_ERROR="VALUE_ERROR",m.NAME_ERROR="NAME_ERROR",m.RANGE_ERROR="RANGE_ERROR",m.SYNTAX_ERROR="SYNTAX_ERROR",m.DATA_VALIDATION_ERROR="DATA_VALIDATION_ERROR",m.FORMAT_ERROR="FORMAT_ERROR",m.OPERATION_NOT_SUPPORTED="OPERATION_NOT_SUPPORTED",m.UNKNOWN_ERROR="UNKNOWN_ERROR"}}]);