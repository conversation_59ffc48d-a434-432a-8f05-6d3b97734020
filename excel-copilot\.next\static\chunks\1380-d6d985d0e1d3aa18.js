"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1380],{6600:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},97589:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},75733:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4436:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},4086:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},85302:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},92699:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},59061:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},8400:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},38296:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(81066).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},15167:function(e,t,n){n.d(t,{f:function(){return m}});var r=n(2265);n(54887);var a=n(1584),o=n(57437),i=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,c=function(e,t){let n={...t};for(let r in t){let a=e[r],o=t[r];/^on[A-Z]/.test(r)?a&&o?n[r]=(...e)=>{let t=o(...e);return a(...e),t}:a&&(n[r]=a):"style"===r?n[r]={...a,...o}:"className"===r&&(n[r]=[a,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,a.F)(t,l):l),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:a,...i}=e,c=r.Children.toArray(a),s=c.find(l);if(s){let e=s.props.children,a=c.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,o.jsx)(t,{...i,ref:n,children:a})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:a,...i}=e,l=a?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l,{...i,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),s="horizontal",d=["horizontal","vertical"],u=r.forwardRef((e,t)=>{let{decorative:n,orientation:r=s,...a}=e,i=d.includes(r)?r:s;return(0,o.jsx)(c.div,{"data-orientation":i,...n?{role:"none"}:{"aria-orientation":"vertical"===i?i:void 0,role:"separator"},...a,ref:t})});u.displayName="Separator";var m=u},9646:function(e,t,n){n.d(t,{bU:function(){return S},fC:function(){return x}});var r=n(2265),a=n(78149),o=n(1584),i=n(98324),l=n(91715),c=n(47250),s=n(75238),d=n(25171),u=n(57437),m="Switch",[h,f]=(0,i.b)(m),[p,y]=h(m),k=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:i,checked:c,defaultChecked:s,required:h,disabled:f,value:y="on",onCheckedChange:k,form:v,...w}=e,[x,S]=r.useState(null),E=(0,o.e)(t,e=>S(e)),C=r.useRef(!1),M=!x||v||!!x.closest("form"),[Z,T]=(0,l.T)({prop:c,defaultProp:null!=s&&s,onChange:k,caller:m});return(0,u.jsxs)(p,{scope:n,checked:Z,disabled:f,children:[(0,u.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":Z,"aria-required":h,"data-state":g(Z),"data-disabled":f?"":void 0,disabled:f,value:y,...w,ref:E,onClick:(0,a.M)(e.onClick,e=>{T(e=>!e),M&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),M&&(0,u.jsx)(b,{control:x,bubbles:!C.current,name:i,value:y,checked:Z,required:h,disabled:f,form:v,style:{transform:"translateX(-100%)"}})]})});k.displayName=m;var v="SwitchThumb",w=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,a=y(v,n);return(0,u.jsx)(d.WV.span,{"data-state":g(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});w.displayName=v;var b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,control:a,checked:i,bubbles:l=!0,...d}=e,m=r.useRef(null),h=(0,o.e)(m,t),f=(0,c.D)(i),p=(0,s.t)(a);return r.useEffect(()=>{let e=m.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==i&&t){let n=new Event("click",{bubbles:l});t.call(e,i),e.dispatchEvent(n)}},[f,i,l]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...d,tabIndex:-1,ref:h,style:{...d.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function g(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var x=k,S=w},79512:function(e,t,n){n.d(t,{F:function(){return d},ThemeProvider:function(){return u}});var r=n(2265),a=(e,t,n,r,a,o,i,l)=>{let c=document.documentElement,s=["light","dark"];function d(t){(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&o?a.map(e=>o[e]||e):a;n?(c.classList.remove(...r),c.classList.add(o&&o[t]?o[t]:t)):c.setAttribute(e,t)}),l&&s.includes(t)&&(c.style.colorScheme=t)}if(r)d(r);else try{let e=localStorage.getItem(t)||n,r=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(r)}catch(e){}},o=["light","dark"],i="(prefers-color-scheme: dark)",l="undefined"==typeof window,c=r.createContext(void 0),s={setTheme:e=>{},themes:[]},d=()=>{var e;return null!=(e=r.useContext(c))?e:s},u=e=>r.useContext(c)?r.createElement(r.Fragment,null,e.children):r.createElement(h,{...e}),m=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:a=!0,enableColorScheme:l=!0,storageKey:s="theme",themes:d=m,defaultTheme:u=a?"system":"light",attribute:h="data-theme",value:v,children:w,nonce:b,scriptProps:g}=e,[x,S]=r.useState(()=>p(s,u)),[E,C]=r.useState(()=>"system"===x?k():x),M=v?Object.values(v):d,Z=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&a&&(t=k());let r=v?v[t]:t,i=n?y(b):null,c=document.documentElement,s=e=>{"class"===e?(c.classList.remove(...M),r&&c.classList.add(r)):e.startsWith("data-")&&(r?c.setAttribute(e,r):c.removeAttribute(e))};if(Array.isArray(h)?h.forEach(s):s(h),l){let e=o.includes(u)?u:null,n=o.includes(t)?t:e;c.style.colorScheme=n}null==i||i()},[b]),T=r.useCallback(e=>{let t="function"==typeof e?e(x):e;S(t);try{localStorage.setItem(s,t)}catch(e){}},[x]),j=r.useCallback(e=>{C(k(e)),"system"===x&&a&&!t&&Z("system")},[x,t]);r.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(j),j(e),()=>e.removeListener(j)},[j]),r.useEffect(()=>{let e=e=>{e.key===s&&(e.newValue?S(e.newValue):T(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),r.useEffect(()=>{Z(null!=t?t:x)},[t,x]);let P=r.useMemo(()=>({theme:x,setTheme:T,forcedTheme:t,resolvedTheme:"system"===x?E:x,themes:a?[...d,"system"]:d,systemTheme:a?E:void 0}),[x,T,t,E,a,d]);return r.createElement(c.Provider,{value:P},r.createElement(f,{forcedTheme:t,storageKey:s,attribute:h,enableSystem:a,enableColorScheme:l,defaultTheme:u,value:v,themes:d,nonce:b,scriptProps:g}),w)},f=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:o,enableSystem:i,enableColorScheme:l,defaultTheme:c,value:s,themes:d,nonce:u,scriptProps:m}=e,h=JSON.stringify([o,n,c,t,d,s,i,l]).slice(1,-1);return r.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?u:"",dangerouslySetInnerHTML:{__html:"(".concat(a.toString(),")(").concat(h,")")}})}),p=(e,t)=>{let n;if(!l){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},y=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},k=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")}}]);