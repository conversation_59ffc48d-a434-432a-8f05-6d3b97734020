(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4178],{80043:function(e,s,r){Promise.resolve().then(r.bind(r,58037))},58037:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return F}});var a=r(57437),t=r(2265),n=r(30998),i=r(16463),l=r(39343),o=r(31014),d=r(59772),c=r(27776),u=r(74109),m=r(20500),f=r(59061),x=r(38711),p=r(52022),h=r(24241),g=r(4086),j=r(24258),v=r(89733),b=r(48185),N=r(77209),y=r(70402),w=r(79055),k=r(29973),C=r(86864),R=r(80233),Z=r(58184),z=r(74697),E=r(57226),P=r(54662);function S(e){let{currentImage:s,userName:r,onImageUpdate:n,className:i}=e,[l,o]=(0,t.useState)(!1),[d,m]=(0,t.useState)(!1),[f,x]=(0,t.useState)(s||""),[p,h]=(0,t.useState)(null),g=(0,t.useRef)(null),j=e=>e?e.split(" ").map(e=>e[0]).join("").toUpperCase().substring(0,2):"?",b=e=>["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type)?!(e.size>5242880)||(c.toast.error("Arquivo muito grande. M\xe1ximo 5MB."),!1):(c.toast.error("Formato n\xe3o suportado. Use JPEG, PNG ou WebP."),!1),w=async e=>{if(b(e)){m(!0);try{let s=new FileReader;s.onload=e=>{var s;h(null===(s=e.target)||void 0===s?void 0:s.result)},s.readAsDataURL(e),await new Promise(e=>setTimeout(e,2e3));let a="https://api.dicebear.com/7.x/initials/svg?seed=".concat(encodeURIComponent(r||"User"),"&backgroundColor=random");x(a),c.toast.success("Avatar atualizado com sucesso!"),n(a),o(!1)}catch(e){c.toast.error("Erro ao fazer upload da imagem")}finally{m(!1),h(null)}}},k=async()=>{if(!f.trim()){c.toast.error("Digite uma URL v\xe1lida");return}try{let e=new Image;e.onload=()=>{n(f),c.toast.success("Avatar atualizado com sucesso!"),o(!1)},e.onerror=()=>{c.toast.error("URL de imagem inv\xe1lida")},e.src=f}catch(e){c.toast.error("Erro ao validar imagem")}};return(0,a.jsxs)(P.Vq,{open:l,onOpenChange:o,children:[(0,a.jsx)(P.hg,{asChild:!0,children:(0,a.jsxs)("div",{className:"relative group cursor-pointer ".concat(i),children:[(0,a.jsxs)(E.qE,{className:"h-20 w-20",children:[(0,a.jsx)(E.F$,{src:s||"",alt:r||"Usu\xe1rio"}),(0,a.jsx)(E.Q5,{className:"text-lg",children:j(r)})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(R.Z,{className:"h-6 w-6 text-white"})})]})}),(0,a.jsxs)(P.cZ,{className:"sm:max-w-md",children:[(0,a.jsxs)(P.fK,{children:[(0,a.jsx)(P.$N,{children:"Atualizar Avatar"}),(0,a.jsx)(P.Be,{children:"Fa\xe7a upload de uma nova imagem ou cole uma URL"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(E.qE,{className:"h-24 w-24",children:[(0,a.jsx)(E.F$,{src:p||s||"",alt:r||"Usu\xe1rio"}),(0,a.jsx)(E.Q5,{className:"text-xl",children:j(r)})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y._,{children:"Fazer Upload"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(v.Button,{variant:"outline",onClick:()=>{var e;return null===(e=g.current)||void 0===e?void 0:e.click()},disabled:d,className:"flex-1",children:[d?(0,a.jsx)(u.Z,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(Z.Z,{className:"h-4 w-4 mr-2"}),d?"Enviando...":"Escolher Arquivo"]}),(0,a.jsx)("input",{ref:g,type:"file",accept:"image/*",onChange:e=>{var s;let r=null===(s=e.target.files)||void 0===s?void 0:s[0];r&&w(r)},className:"hidden"})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"JPEG, PNG ou WebP. M\xe1ximo 5MB."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y._,{htmlFor:"imageUrl",children:"URL da Imagem"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(N.I,{id:"imageUrl",value:f,onChange:e=>x(e.target.value),placeholder:"https://exemplo.com/imagem.jpg",className:"flex-1"}),(0,a.jsx)(v.Button,{variant:"outline",onClick:k,disabled:!f.trim(),children:"Aplicar"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)(v.Button,{variant:"outline",onClick:()=>{x(""),n(""),c.toast.success("Avatar removido"),o(!1)},className:"text-red-600 hover:text-red-700",children:[(0,a.jsx)(z.Z,{className:"h-4 w-4 mr-2"}),"Remover"]}),(0,a.jsx)(v.Button,{variant:"outline",onClick:()=>o(!1),children:"Cancelar"})]})]})]})]})}let U=d.z.object({name:d.z.string().min(1,"Nome \xe9 obrigat\xf3rio").max(100,"Nome muito longo"),email:d.z.string().email("Email inv\xe1lido"),image:d.z.string().url("URL de imagem inv\xe1lida").optional().or(d.z.literal(""))});function F(){let{data:e,status:s}=(0,n.useSession)(),r=(0,i.useRouter)(),[d,R]=(0,t.useState)(null),[Z,z]=(0,t.useState)(!0),[E,P]=(0,t.useState)(!1),{register:F,handleSubmit:A,setValue:V,formState:{errors:D,isDirty:I}}=(0,l.cI)({resolver:(0,o.F)(U)});(0,t.useEffect)(()=>{"unauthenticated"===s&&r.push("/auth/signin")},[s,r]),(0,t.useEffect)(()=>{let s=async()=>{if(null==e?void 0:e.user)try{let e=await fetch("/api/user/profile"),s=await e.json();e.ok?(R(s.profile),V("name",s.profile.name||""),V("email",s.profile.email||""),V("image",s.profile.image||"")):c.toast.error("Erro ao carregar perfil: "+s.error)}catch(e){c.toast.error("Erro ao carregar perfil")}finally{z(!1)}};e&&s()},[e,V]);let B=async e=>{P(!0);try{let s=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await s.json();s.ok?(c.toast.success("Perfil atualizado com sucesso!"),d&&R({...d,...r.profile})):c.toast.error("Erro ao atualizar perfil: "+r.error)}catch(e){c.toast.error("Erro ao atualizar perfil")}finally{P(!1)}},Y=async e=>{try{let s=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({image:e})}),r=await s.json();s.ok&&d?(R({...d,image:e}),V("image",e)):c.toast.error("Erro ao atualizar avatar: "+r.error)}catch(e){c.toast.error("Erro ao atualizar avatar")}},_=e=>e?new Date(e).toLocaleDateString("pt-BR"):"Nunca";return"loading"===s||Z?(0,a.jsx)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(u.Z,{className:"h-8 w-8 animate-spin"})})}):e&&d?(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,a.jsx)(S,{currentImage:d.image,userName:d.name,onImageUpdate:Y}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:d.name||"Usu\xe1rio"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:d.email}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[d.subscription&&(0,a.jsx)(w.C,{variant:"secondary",children:d.subscription.plan.toUpperCase()}),d.emailVerified&&(0,a.jsxs)(w.C,{variant:"outline",className:"text-green-600",children:[(0,a.jsx)(m.Z,{className:"h-3 w-3 mr-1"}),"Verificado"]})]})]})]}),(0,a.jsxs)(C.mQ,{defaultValue:"profile",className:"space-y-6",children:[(0,a.jsxs)(C.dr,{children:[(0,a.jsx)(C.SP,{value:"profile",children:"Perfil"}),(0,a.jsx)(C.SP,{value:"stats",children:"Estat\xedsticas"}),(0,a.jsx)(C.SP,{value:"preferences",children:"Prefer\xeancias"})]}),(0,a.jsx)(C.nU,{value:"profile",children:(0,a.jsxs)(b.Zb,{children:[(0,a.jsxs)(b.Ol,{children:[(0,a.jsx)(b.ll,{children:"Informa\xe7\xf5es do Perfil"}),(0,a.jsx)(b.SZ,{children:"Atualize suas informa\xe7\xf5es pessoais"})]}),(0,a.jsx)(b.aY,{children:(0,a.jsxs)("form",{onSubmit:A(B),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y._,{htmlFor:"name",children:"Nome"}),(0,a.jsx)(N.I,{id:"name",...F("name"),placeholder:"Seu nome completo"}),D.name&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:D.name.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y._,{htmlFor:"email",children:"Email"}),(0,a.jsx)(N.I,{id:"email",type:"email",...F("email"),placeholder:"<EMAIL>"}),D.email&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:D.email.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y._,{htmlFor:"image",children:"URL da Imagem"}),(0,a.jsx)(N.I,{id:"image",...F("image"),placeholder:"https://exemplo.com/sua-foto.jpg"}),D.image&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:D.image.message})]}),(0,a.jsxs)(v.Button,{type:"submit",disabled:!I||E,className:"w-full sm:w-auto",children:[E?(0,a.jsx)(u.Z,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Salvar Altera\xe7\xf5es"]})]})})]})}),(0,a.jsx)(C.nU,{value:"stats",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(b.Zb,{children:[(0,a.jsxs)(b.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ll,{className:"text-sm font-medium",children:"Planilhas Criadas"}),(0,a.jsx)(x.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(b.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:d.stats.workbooksCount})})]}),(0,a.jsxs)(b.Zb,{children:[(0,a.jsxs)(b.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ll,{className:"text-sm font-medium",children:"Total de Logins"}),(0,a.jsx)(p.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(b.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:d.stats.loginCount})})]}),(0,a.jsxs)(b.Zb,{children:[(0,a.jsxs)(b.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ll,{className:"text-sm font-medium",children:"Membro Desde"}),(0,a.jsx)(h.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(b.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:_(d.stats.memberSince)})})]}),(0,a.jsxs)(b.Zb,{children:[(0,a.jsxs)(b.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ll,{className:"text-sm font-medium",children:"\xdaltimo Login"}),(0,a.jsx)(g.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(b.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:_(d.stats.lastLogin)})})]})]})}),(0,a.jsx)(C.nU,{value:"preferences",children:(0,a.jsxs)(b.Zb,{children:[(0,a.jsxs)(b.Ol,{children:[(0,a.jsx)(b.ll,{children:"Prefer\xeancias"}),(0,a.jsx)(b.SZ,{children:"Configure suas prefer\xeancias de uso"})]}),(0,a.jsx)(b.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Tema"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Atual: ",d.preferences.theme]})]}),(0,a.jsx)(k.Z,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Idioma"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Atual: ",d.preferences.language]})]}),(0,a.jsx)(k.Z,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Notifica\xe7\xf5es"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,a.jsxs)("p",{children:["Email: ",d.preferences.notifications.email?"Ativado":"Desativado"]}),(0,a.jsxs)("p",{children:["Push: ",d.preferences.notifications.push?"Ativado":"Desativado"]}),(0,a.jsxs)("p",{children:["Marketing: ",d.preferences.notifications.marketing?"Ativado":"Desativado"]})]})]}),(0,a.jsx)(k.Z,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Privacidade"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,a.jsxs)("p",{children:["Perfil Vis\xedvel: ",d.preferences.privacy.profileVisible?"Sim":"N\xe3o"]}),(0,a.jsxs)("p",{children:["Compartilhar Dados de Uso: ",d.preferences.privacy.shareUsageData?"Sim":"N\xe3o"]})]})]}),(0,a.jsxs)(v.Button,{variant:"outline",className:"w-full sm:w-auto",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 mr-2"}),"Configurar Prefer\xeancias"]})]})})]})})]})]}):null}},57226:function(e,s,r){"use strict";r.d(s,{F$:function(){return o},Q5:function(){return d},qE:function(){return l}});var a=r(57437),t=r(81464),n=r(2265),i=r(49354);let l=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.fC,{ref:s,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...n})});l.displayName=t.fC.displayName;let o=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.Ee,{ref:s,className:(0,i.cn)("aspect-square h-full w-full",r),...n})});o.displayName=t.Ee.displayName;let d=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.NY,{ref:s,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",r),...n})});d.displayName=t.NY.displayName},79055:function(e,s,r){"use strict";r.d(s,{C:function(){return l}});var a=r(57437),t=r(13027);r(2265);var n=r(49354);let i=(0,t.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:r,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:r}),s),...t})}},89733:function(e,s,r){"use strict";r.d(s,{Button:function(){return u},d:function(){return c}});var a=r(57437),t=r(71538),n=r(13027),i=r(847),l=r(2265),o=r(18043),d=r(49354);let c=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),u=l.forwardRef((e,s)=>{let{className:r,variant:n,size:l,rounded:u,cssFeedback:m,asChild:f=!1,animated:x=!1,icon:p,iconPosition:h="left",children:g,...j}=e,v=f?t.g7:"button",b=(0,a.jsxs)("span",{className:"inline-flex items-center justify-center",children:[p&&"left"===h&&(0,a.jsx)("span",{className:"mr-2",children:p}),g,p&&"right"===h&&(0,a.jsx)("span",{className:"ml-2",children:p})]});if(x){let e={whileTap:{scale:.97},whileHover:["link","ghost"].includes(n)?void 0:{y:-2},transition:{duration:.67*o.zn,ease:o.d}},t=(0,d.cn)(c({variant:n,size:l,rounded:u,cssFeedback:"none",className:r})),m={...j,className:t,...e};return(0,a.jsx)(i.E.button,{ref:s,...m,children:b})}return(0,a.jsx)(v,{className:(0,d.cn)(c({variant:n,size:l,rounded:u,cssFeedback:m,className:r})),ref:s,...j,children:b})});u.displayName="Button"},48185:function(e,s,r){"use strict";r.d(s,{Ol:function(){return d},SZ:function(){return u},Zb:function(){return o},aY:function(){return m},eW:function(){return f},ll:function(){return c}});var a=r(57437),t=r(847),n=r(2265),i=r(18043),l=r(49354);let o=(0,n.forwardRef)((e,s)=>{let{className:r,children:n,hoverable:o=!1,variant:d="default",noPadding:c=!1,animated:u=!1,...m}=e,f=(0,l.cn)("rounded-xl border shadow-sm",{"p-6":!c,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":o&&!u,"border-border bg-card":"default"===d,"border-border/50 bg-transparent":"outline"===d,"bg-card/90 backdrop-blur-md border-border/50":"glass"===d,"bg-gradient-primary text-primary-foreground border-none":"gradient"===d},r);return u?(0,a.jsx)(t.E.div,{ref:s,className:f,...(0,i.Ph)("card"),whileHover:o?i.q.hover:void 0,whileTap:o?i.q.tap:void 0,...m,children:n}):(0,a.jsx)("div",{ref:s,className:f,...m,children:n})});o.displayName="Card";let d=(0,n.forwardRef)((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("mb-4 flex flex-col space-y-1.5",r),...t})});d.displayName="CardHeader";let c=(0,n.forwardRef)((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-xl font-semibold leading-none tracking-tight",r),...t})});c.displayName="CardTitle";let u=(0,n.forwardRef)((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",r),...t})});u.displayName="CardDescription";let m=(0,n.forwardRef)((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("card-content",r),...t})});m.displayName="CardContent";let f=(0,n.forwardRef)((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center pt-4 mt-auto",r),...t})});f.displayName="CardFooter"},54662:function(e,s,r){"use strict";r.d(s,{$N:function(){return p},Be:function(){return h},Vq:function(){return o},cN:function(){return x},cZ:function(){return m},fK:function(){return f},hg:function(){return d}});var a=r(57437),t=r(13304),n=r(74697),i=r(2265),l=r(49354);let o=t.fC,d=t.xz,c=t.h_;t.x8;let u=i.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.aV,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n})});u.displayName=t.aV.displayName;let m=i.forwardRef((e,s)=>{let{className:r,children:i,...o}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(t.VY,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...o,children:[i,(0,a.jsxs)(t.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=t.VY.displayName;let f=e=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...r})};f.displayName="DialogHeader";let x=e=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...r})};x.displayName="DialogFooter";let p=i.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.Dx,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",r),...n})});p.displayName=t.Dx.displayName;let h=i.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.dk,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",r),...n})});h.displayName=t.dk.displayName},6432:function(e,s,r){"use strict";r.d(s,{RM:function(){return l},aF:function(){return o}});var a=r(2265),t=r(49354);let n={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},i={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"md",r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3?arguments[3]:void 0;return(0,t.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n[e],i[s],r&&"min-h-[80px] resize-vertical",a)}function o(e,s){return s?a.createElement("div",{className:s},e):e}},77209:function(e,s,r){"use strict";r.d(s,{I:function(){return i}});var a=r(57437),t=r(2265),n=r(6432);let i=t.forwardRef((e,s)=>{let{className:r,type:t,wrapperClassName:i,variant:l="default",fieldSize:o="md",inputSize:d,...c}=e,u=(0,a.jsx)("input",{type:t,className:(0,n.RM)(l,d||o,!1,r),ref:s,...c});return(0,n.aF)(u,i)});i.displayName="Input",s.Z=i},70402:function(e,s,r){"use strict";r.d(s,{_:function(){return d}});var a=r(57437),t=r(38364),n=r(13027),i=r(2265),l=r(49354);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.f,{ref:s,className:(0,l.cn)(o(),r),...n})});d.displayName=t.f.displayName},29973:function(e,s,r){"use strict";r.d(s,{Z:function(){return l}});var a=r(57437),t=r(15167),n=r(2265),i=r(49354);let l=n.forwardRef((e,s)=>{let{className:r,orientation:n="horizontal",decorative:l=!0,...o}=e;return(0,a.jsx)(t.f,{ref:s,decorative:l,orientation:n,className:(0,i.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",r),...o})});l.displayName=t.f.displayName},86864:function(e,s,r){"use strict";r.d(s,{SP:function(){return d},dr:function(){return o},mQ:function(){return l},nU:function(){return c}});var a=r(57437),t=r(62447),n=r(2265),i=r(49354);let l=t.fC,o=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.aV,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...n})});o.displayName=t.aV.displayName;let d=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.xz,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...n})});d.displayName=t.xz.displayName;let c=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,a.jsx)(t.VY,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...n})});c.displayName=t.VY.displayName}},function(e){e.O(0,[7142,8638,7776,5660,3526,4974,8101,298,8194,2971,7023,1744],function(){return e(e.s=80043)}),_N_E=e.O()}]);