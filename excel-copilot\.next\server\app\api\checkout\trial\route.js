"use strict";(()=>{var e={};e.id=3803,e.ids=[3803],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},23678:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>R,requestAsyncStorage:()=>h,routeModule:()=>f,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g});var n={};t.r(n),t.d(n,{GET:()=>m,dynamic:()=>p});var o=t(49303),a=t(88716),i=t(60670),s=t(87070),c=t(45609),l=t(43895),u=t(79381),d=t(63841);let p="force-dynamic";async function m(e){try{if(!u.Ag)return s.NextResponse.redirect(new URL("/dashboard?error=stripe-not-configured",e.url));let r=await (0,c.getServerSession)();if(!r?.user)return s.NextResponse.redirect(new URL("/auth/signin",e.url));let t=r.user.id,n=await d.prisma.subscription.findMany({where:{userId:t},orderBy:{createdAt:"desc"}}),o=await d.prisma.user.findUnique({where:{id:t}});if(!o)return s.NextResponse.redirect(new URL("/dashboard?error=user-not-found",e.url));if(n.some(e=>e.plan!==u.Xf.FREE&&"canceled"!==e.status))return s.NextResponse.redirect(new URL("/pricing?error=trial-already-used",e.url));let a=n[0]?.stripeCustomerId;if(!a){let e={metadata:{userId:o.id}};o.email&&(e.email=o.email),o.name&&(e.name=o.name),a=(await u.Ag.customers.create(e)).id}let i=(0,u.Al)(u.Xf.PRO_MONTHLY),l=await u.Ag.checkout.sessions.create({customer:a,line_items:[{price:i,quantity:1}],mode:"subscription",success_url:`${e.headers.get("origin")}/dashboard?checkout=success&trial=true`,cancel_url:`${e.headers.get("origin")}/dashboard?checkout=cancelled`,allow_promotion_codes:!0,billing_address_collection:"auto",metadata:{userId:o.id,plan:u.Xf.PRO_MONTHLY,isTrial:"true"},payment_method_types:["card"],locale:"pt-BR",subscription_data:{trial_period_days:7,metadata:{userId:o.id,plan:u.Xf.PRO_MONTHLY,isTrial:"true"}}});return s.NextResponse.redirect(l.url)}catch(r){return l.kg.error("[TRIAL_CHECKOUT_ERROR]",r),s.NextResponse.redirect(new URL("/dashboard?error=checkout-failed",e.url))}}let f=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/checkout/trial/route",pathname:"/api/checkout/trial",filename:"route",bundlePath:"app/api/checkout/trial/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\checkout\\trial\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:v}=f,x="/api/checkout/trial/route";function R(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}},43895:(e,r,t)=>{let n;t.d(r,{kg:()=>u});var o=t(99557),a=t.n(o);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(n=>{r.includes(n)||(t[n]=e[n])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let l={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=l.production;n=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),n=a()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,r)=>{n.trace(r||{},e)},debug:(e,r)=>{n.debug(r||{},e)},info:(e,r)=>{n.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=s(r);n.warn(t,e)}else n.warn(c(r)||{},e)},error:(e,r,t)=>{let{normalizedError:o,extractedMetadata:a}=s(r),i={...t||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};n.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:o,extractedMetadata:a}=s(r),i={...t||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};n.fatal(i,e)},createChild:e=>{let r=n.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:n}=s(t);r.warn(n,e)}else r.warn(c(t)||{},e)},error:(e,t,n)=>{let{normalizedError:o,extractedMetadata:a}=s(t),i={...n||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.error(i,e)},fatal:(e,t,n)=>{let{normalizedError:o,extractedMetadata:a}=s(t),i={...n||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},79381:(e,r,t)=>{t.d(r,{cb:()=>g,Xf:()=>h,Al:()=>R,DI:()=>w,Ag:()=>x});var n,o="https://js.stripe.com",a="".concat(o,"/").concat("basil","/stripe.js"),i=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,s=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,c=function(){for(var e=document.querySelectorAll('script[src^="'.concat(o,'"]')),r=0;r<e.length;r++){var t,n=e[r];if(t=n.src,i.test(t)||s.test(t))return n}return null},l=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",t=document.createElement("script");t.src="".concat(a).concat(r);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(t),t},u=null,d=null,p=null;Promise.resolve().then(function(){return n||(n=(null!==u?u:(u=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,n=c();n?n&&null!==p&&null!==d&&(n.removeEventListener("load",p),n.removeEventListener("error",d),null===(t=n.parentNode)||void 0===t||t.removeChild(n),n=l(null)):n=l(null),p=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},d=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",p),n.addEventListener("error",d)}catch(e){r(e);return}})).catch(function(e){return u=null,Promise.reject(e)})).catch(function(e){return n=null,Promise.reject(e)}))}).catch(function(e){console.warn(e)});var m=t(31059);let f={PRO_MONTHLY:"price_1RJeZYRrKLXtzZkMf1SS2CRR",PRO_ANNUAL:"price_1RJecORrKLXtzZkMy1RSRpMV"},h={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"},g={[h.FREE]:50,[h.PRO_MONTHLY]:500,[h.PRO_ANNUAL]:1e3},v=process.env.STRIPE_SECRET_KEY||"",x=v?new m.Z(v,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}}):null;function R(e){switch(e){case h.PRO_MONTHLY:return f.PRO_MONTHLY;case h.PRO_ANNUAL:return f.PRO_ANNUAL;default:return f.PRO_MONTHLY}}function w(e){switch(e){case"active":case"trialing":return"active";case"canceled":case"unpaid":case"incomplete_expired":return"canceled";case"past_due":return"past_due";case"incomplete":return"incomplete";default:return"unknown"}}},63841:(e,r,t)=>{t.d(r,{P:()=>c,prisma:()=>s});var n=t(53524);let o={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],s=global.prisma||new n.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function c(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function l(){try{await s.$disconnect(),o.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){o.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{a.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),a.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&o.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),o.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{l()})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,5972,9557,330,5609,1059],()=>t(23678));module.exports=n})();