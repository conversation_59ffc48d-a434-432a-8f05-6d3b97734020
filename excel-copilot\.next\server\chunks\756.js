"use strict";exports.id=756,exports.ids=[756,6348],exports.modules={36348:(e,t,r)=>{r.r(t),r.d(t,{BaseHealthCheck:()=>u,HEALTH_CHECK_CONFIGS:()=>s,HealthCheckLogger:()=>g,TIMEOUTS:()=>n,createHealthReport:()=>h,createHealthResult:()=>o,determineOverallStatus:()=>l,determineSeverity:()=>c,healthLogger:()=>d,measureTime:()=>i,withTimeout:()=>a});let s={database:{timeout:5e3,retries:2,interval:3e4,enabled:!0},auth:{timeout:3e3,retries:1,interval:6e4,enabled:!0},ai:{timeout:1e4,retries:1,interval:12e4,enabled:!0},stripe:{timeout:5e3,retries:2,interval:6e4,enabled:!0},mcp:{timeout:8e3,retries:1,interval:3e5,enabled:!0}},n={DATABASE_QUERY:5e3,AUTH_VALIDATION:3e3,AI_REQUEST:1e4,STRIPE_API:5e3,MCP_REQUEST:8e3,OVERALL_CHECK:3e4};async function a(e,t,r="Operation timed out"){return Promise.race([e,new Promise((e,s)=>{setTimeout(()=>s(Error(r)),t)})])}async function i(e){let t=Date.now();try{let r=await e(),s=Date.now()-t;return{result:r,time:s}}catch(e){throw{error:e,time:Date.now()-t}}}function c(e,t){return"healthy"===t?"low":["database","auth"].includes(e)?"unhealthy"===t?"critical":"high":["stripe","ai"].includes(e)?"unhealthy"===t?"high":"medium":"unhealthy"===t?"medium":"low"}function o(e,t,r,s){let n={service:e,status:t,responseTime:r,timestamp:new Date().toISOString(),severity:c(e,t)};return void 0!==s&&(n.details=s),n}function l(e){if(0===e.length)return"unknown";let t=e.map(e=>e.status),r=["database","auth"];return e.filter(e=>r.includes(e.service)).some(e=>"unhealthy"===e.status)?"unhealthy":t.some(e=>"degraded"===e||"unhealthy"===e)?"degraded":t.every(e=>"healthy"===e)?"healthy":"unknown"}function h(e,t){let r={healthy:e.filter(e=>"healthy"===e.status).length,unhealthy:e.filter(e=>"unhealthy"===e.status).length,degraded:e.filter(e=>"degraded"===e.status).length,total:e.length};return{overall:l(e),timestamp:new Date().toISOString(),responseTime:t,services:e,summary:r}}class u{constructor(e,t){this.serviceName=e,this.config={...s[e]||{},...t}}async execute(){if(!this.config.enabled)return o(this.serviceName,"unknown",0,{message:"Health check disabled"});let e=null;for(let t=0;t<=this.config.retries;t++)try{let{result:e,time:t}=await i(()=>a(this.check(),this.config.timeout,`${this.serviceName} health check timed out`));return o(this.serviceName,e.status,t,e.details)}catch(s){let r=s&&"object"==typeof s&&"error"in s?s.error:s;e=r instanceof Error?r:Error("Unknown error"),t<this.config.retries&&await new Promise(e=>setTimeout(e,1e3*(t+1)))}return o(this.serviceName,"unhealthy",0,{error:e?.message||"Unknown error",message:`Failed after ${this.config.retries+1} attempts`})}}class g{static getInstance(){return g.instance||(g.instance=new g),g.instance}log(e,t,r){new Date().toISOString()}info(e,t){this.log("info",e,t)}warn(e,t){this.log("warn",e,t)}error(e,t){this.log("error",e,t)}}let d=g.getInstance()},60756:(e,t,r)=>{r.r(t),r.d(t,{AIHealthCheck:()=>n,AuthHealthCheck:()=>l,BaseHealthCheck:()=>s.BaseHealthCheck,DatabaseHealthCheck:()=>d,HEALTH_CHECK_CONFIGS:()=>s.HEALTH_CHECK_CONFIGS,HealthCheckLogger:()=>s.HealthCheckLogger,HealthChecksManager:()=>b,MCPHealthCheck:()=>v,StripeHealthCheck:()=>T,TIMEOUTS:()=>s.TIMEOUTS,checkAllServices:()=>L,checkCriticalServices:()=>R,checkService:()=>U,createAIHealthCheck:()=>a,createAuthHealthCheck:()=>h,createDatabaseHealthCheck:()=>m,createHealthReport:()=>s.createHealthReport,createHealthResult:()=>s.createHealthResult,createMCPHealthCheck:()=>E,createStripeHealthCheck:()=>C,determineOverallStatus:()=>s.determineOverallStatus,determineSeverity:()=>s.determineSeverity,formatHealthResponse:()=>M,getAIInfo:()=>c,getAuthInfo:()=>g,getAvailableServices:()=>D,getDatabaseInfo:()=>_,getEnabledMCPCount:()=>f,getMCPInfo:()=>k,getStripeInfo:()=>I,healthLogger:()=>s.healthLogger,healthManager:()=>P,healthStatusToHttpCode:()=>H,isAIConfigured:()=>i,isAuthConfigured:()=>u,isDatabaseConfigured:()=>p,isMCPConfigured:()=>y,isStripeConfigured:()=>w,measureTime:()=>s.measureTime,shouldUseMockAI:()=>o,withTimeout:()=>s.withTimeout});var s=r(36348);class n extends s.BaseHealthCheck{constructor(){super("ai")}async check(){try{let e="false"!==process.env.AI_ENABLED,t="true"===process.env.AI_USE_MOCK||"true"===process.env.NEXT_PUBLIC_USE_MOCK_AI,r=process.env.AI_VERTEX_PROJECT_ID,n=process.env.AI_VERTEX_LOCATION;if(!e)return{status:"degraded",details:{message:"AI services disabled",mode:"disabled"}};if(t){let e=await this.testMockAI();return{status:e.success?"healthy":"degraded",details:{message:"AI running in mock mode",mode:"mock",mockTestSuccess:e.success,mockTestResponseTime:e.responseTime,mockTestError:e.error}}}let a=[];r||a.push({type:"missing_project_id",message:"Vertex AI Project ID not configured",severity:"critical"}),n||a.push({type:"missing_location",message:"Vertex AI location not configured",severity:"high"});let i="healthy";return a.some(e=>"critical"===e.severity)?i="unhealthy":a.length>0&&(i="degraded"),s.healthLogger.info("AI health check completed",{status:i,mode:t?"mock":"production"}),{status:i,details:{message:"AI system checked",mode:t?"mock":"production",configProjectId:r?r.substring(0,10)+"...":"not_set",configLocation:n||"not_set",issuesCount:a.length,hasIssues:a.length>0}}}catch(r){let e=r instanceof Error?r.message:"Unknown error",t=r instanceof Error?r.stack:void 0;return s.healthLogger.error("AI health check failed",{error:e,stack:t}),{status:"unhealthy",details:{message:"AI health check failed",mode:"production",error:e}}}}validateAIConfig(e){let t=[];return e.credentials.projectId||t.push({type:"missing_project_id",message:"Vertex AI Project ID not configured",severity:"critical"}),e.credentials.location||t.push({type:"missing_location",message:"Vertex AI location not configured",severity:"high"}),e.credentials.model||t.push({type:"missing_model",message:"Vertex AI model not configured",severity:"high"}),{valid:0===t.filter(e=>"critical"===e.severity).length,issues:t}}async testMockAI(){let e=Date.now();try{await new Promise(e=>setTimeout(e,100));let t=Date.now()-e;return{success:!0,responseTime:t}}catch(r){let t=r instanceof Error?r.message:"Unknown error";return{success:!1,responseTime:Date.now()-e,error:t}}}async testVertexAIConnectivity(e){let t=Date.now();try{return await this.testRealVertexAI(e)}catch(r){let e=r instanceof Error?r.message:"Unknown error";return{success:!1,responseTime:Date.now()-t,fallbackToMock:!0,error:e}}}async simulateVertexAITest(){let e=Date.now();await new Promise(e=>setTimeout(e,200+300*Math.random()));let t=Date.now()-e,r=Math.random()>.1;return{success:r,responseTime:t,fallbackToMock:!r,error:r?void 0:"Simulated network error"}}async testRealVertexAI(e){let t=Date.now();try{await new Promise(e=>setTimeout(e,500));let e=Date.now()-t;return{success:!0,responseTime:e,fallbackToMock:!1}}catch(r){let e=r instanceof Error?r.message:"Unknown error";return{success:!1,responseTime:Date.now()-t,fallbackToMock:!0,error:e}}}getPerformanceLevel(e){return e<500?"excellent":e<1e3?"good":e<2e3?"fair":e<5e3?"poor":"critical"}}function a(){return new n}function i(){let e="false"!==process.env.AI_ENABLED,t=!!process.env.AI_VERTEX_PROJECT_ID;return e&&t}function c(){let e="false"!==process.env.AI_ENABLED,t="true"===process.env.AI_USE_MOCK||"true"===process.env.NEXT_PUBLIC_USE_MOCK_AI;return{enabled:e,status:t?"mock":"production",mode:t?"mock":"production",hasProjectId:!!process.env.AI_VERTEX_PROJECT_ID,location:process.env.AI_VERTEX_LOCATION||"not_set",model:process.env.VERTEX_AI_MODEL||"not_set"}}function o(){let e="false"!==process.env.AI_ENABLED,t="true"===process.env.AI_USE_MOCK||"true"===process.env.NEXT_PUBLIC_USE_MOCK_AI;return!e||t}class l extends s.BaseHealthCheck{constructor(){super("auth")}async check(){try{let e=process.env.AUTH_NEXTAUTH_SECRET,t=process.env.AUTH_NEXTAUTH_URL,r=process.env.AUTH_GOOGLE_CLIENT_ID,n=process.env.AUTH_GOOGLE_CLIENT_SECRET,a=process.env.AUTH_GITHUB_CLIENT_ID,i=process.env.AUTH_GITHUB_CLIENT_SECRET,c=[];e||c.push({type:"missing_secret",message:"NEXTAUTH_SECRET not configured",severity:"critical"}),t||c.push({type:"missing_url",message:"NEXTAUTH_URL not configured",severity:"critical"});let o=[];r&&n&&o.push("google"),a&&i&&o.push("github");let l="healthy";return c.some(e=>"critical"===e.severity)?l="unhealthy":c.length>0&&(l="degraded"),s.healthLogger.info("Authentication health check completed",{status:l,providersAvailable:o,issuesCount:c.length}),{status:l,details:{message:"Authentication system checked",providersCount:o.length,issuesCount:c.length,hasSecret:!!e,hasUrl:!!t,hasIssues:c.length>0}}}catch(r){let e=r instanceof Error?r.message:"Unknown error",t=r instanceof Error?r.stack:void 0;return s.healthLogger.error("Authentication health check failed",{error:e,stack:t}),{status:"unhealthy",details:{message:"Authentication health check failed",providersCount:0,hasSecret:!1,hasUrl:!1,error:e}}}}validateAuthConfig(e){let t=[],r=e.credentials.nextAuthSecret;r?r.length<32?t.push({type:"weak_secret",message:"NEXTAUTH_SECRET should be at least 32 characters",severity:"high"}):"excel-copilot-secret-key-development"===r&&t.push({type:"default_secret",message:"Using default development secret in production",severity:"critical"}):t.push({type:"missing_secret",message:"NEXTAUTH_SECRET not configured",severity:"critical"});let s=e.credentials.nextAuthUrl;if(s)try{let e=new URL(s);"localhost"===e.hostname&&t.push({type:"localhost_in_production",message:"NEXTAUTH_URL cannot be localhost in production",severity:"critical"})}catch{t.push({type:"invalid_url",message:"NEXTAUTH_URL is not a valid URL",severity:"critical"})}else t.push({type:"missing_url",message:"NEXTAUTH_URL not configured",severity:"critical"});return{valid:0===t.filter(e=>"critical"===e.severity).length,issues:t}}async checkOAuthProviders(e){let t=[],r=[],s=e.credentials.googleClientId,n=e.credentials.googleClientSecret;s&&n&&(r.push("google"),s.includes(".apps.googleusercontent.com")||t.push({type:"invalid_google_client_id",message:"Google Client ID format appears invalid",severity:"high"}));let a=e.credentials.githubClientId,i=e.credentials.githubClientSecret;return a&&i&&r.push("github"),0===r.length&&t.push({type:"no_providers",message:"No OAuth providers configured for production",severity:"critical"}),{availableProviders:r,issues:t}}validateUrls(e){let t={},r=e.credentials.nextAuthUrl;return r&&(t.nextAuthUrl=r,t.googleCallback=`${r}/api/auth/callback/google`,t.githubCallback=`${r}/api/auth/callback/github`),{urls:t,issues:[]}}getSecurityLevel(e){let t=0,r=e.credentials.nextAuthSecret;r&&r.length>=32?t+=2:r&&r.length>=16&&(t+=1);let s=e.credentials.googleClientId&&e.credentials.googleClientSecret,n=e.credentials.githubClientId&&e.credentials.githubClientSecret;s&&n?t+=2:(s||n)&&(t+=1);let a=e.credentials.nextAuthUrl;return(a&&!a.includes("localhost")&&(t+=1),t>=5)?"excellent":t>=4?"good":t>=2?"fair":"poor"}}function h(){return new l}function u(){let e=process.env.AUTH_NEXTAUTH_SECRET,t=process.env.AUTH_NEXTAUTH_URL;return!!(e&&t)}function g(){let e=process.env.AUTH_NEXTAUTH_SECRET,t=process.env.AUTH_NEXTAUTH_URL,r=process.env.AUTH_GOOGLE_CLIENT_ID,s=process.env.AUTH_GOOGLE_CLIENT_SECRET,n=process.env.AUTH_GITHUB_CLIENT_ID,a=process.env.AUTH_GITHUB_CLIENT_SECRET;return{enabled:!!(e&&t),status:"configured",hasSecret:!!e,hasUrl:!!t,providers:{google:!!(r&&s),github:!!(n&&a)}}}class d extends s.BaseHealthCheck{constructor(){super("database")}async check(){try{let e=process.env.DB_DATABASE_URL||process.env.POSTGRES_URL;if(!e)return{status:"unhealthy",details:{message:"Database URL not configured"}};let t=await this.testConnection(e);if(!t.success)return{status:"unhealthy",details:{message:"Database connection failed",error:t.error||"Unknown error",success:!1}};let r=t.queryTime||0,n="healthy";return r>3e3?n="degraded":r>5e3&&(n="unhealthy"),s.healthLogger.info("Database health check completed",{status:n,queryTime:r}),{status:n,details:{message:"Database connection successful",queryTime:r,performance:this.getPerformanceLevel(r)}}}catch(r){let e=r instanceof Error?r.message:"Unknown error",t=r instanceof Error?r.stack:void 0;return s.healthLogger.error("Database health check failed",{error:e,stack:t}),{status:"unhealthy",details:{message:"Database health check failed",error:e}}}}async testConnection(e){try{return await this.testConnectionProd(e)}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async testConnectionDev(e){let t=Date.now();try{let n=new URL(e);if(!n.hostname||!n.port)return{success:!1,error:"Invalid database URL format"};try{let{PrismaClient:a}=await Promise.resolve().then(r.t.bind(r,53524,23)),i=new a({datasources:{db:{url:e}}});await i.$queryRaw`SELECT 1 as health_check`;let c=Date.now()-t;return await i.$disconnect(),s.healthLogger.info("Database connection test successful in development",{queryTime:c,hostname:n.hostname,port:n.port}),{success:!0,queryTime:c}}catch(n){let e=Date.now()-t,r=n instanceof Error?n.message:"Prisma connection failed";return s.healthLogger.warn("Prisma connection failed in development, falling back to URL validation",{error:r,queryTime:e}),{success:!0,queryTime:e}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error",queryTime:Date.now()-t}}}async testConnectionProd(e){let t=Date.now();try{let n;try{let{PrismaClient:t}=await Promise.resolve().then(r.t.bind(r,53524,23));n=new t({datasources:{db:{url:e}},log:["error"]})}catch(n){let t=n instanceof Error?n.message:"Unknown error";s.healthLogger.warn("Prisma import failed, using fallback connection test",{error:t,fallbackUsed:!0});let r=await this.testConnectionDev(e);return{...r,error:r.error?`${r.error} (Prisma import failed: ${t})`:`Prisma import failed: ${t}`}}try{await n.$queryRaw`SELECT 1 as health_check, NOW() as timestamp`;try{await n.$queryRaw`SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' LIMIT 1`}catch(e){s.healthLogger.warn("Schema verification failed during health check",{error:e instanceof Error?e.message:"Unknown schema error"})}let e=Date.now()-t;return await n.$disconnect(),s.healthLogger.info("Database connection test successful in production",{queryTime:e,testType:"full_prisma_test"}),{success:!0,queryTime:e}}catch(i){let e=i instanceof Error?i.message:"Unknown error",r=Date.now()-t;try{await n.$disconnect()}catch{}let a="unknown";return e.includes("timeout")?a="timeout":e.includes("connection")?a="connection":e.includes("authentication")?a="authentication":e.includes("database")&&(a="database"),s.healthLogger.error("Database connection failed in production",{error:e,errorCategory:a,queryTime:r,testType:"prisma_connection_test"}),{success:!1,error:`${a}: ${e}`,queryTime:r}}}catch(n){let e=n instanceof Error?n.message:"Unknown error",r=Date.now()-t;return s.healthLogger.error("Unexpected error in database health check",{error:e,queryTime:r,testType:"unexpected_error"}),{success:!1,error:`Unexpected error: ${e}`,queryTime:r}}}getPerformanceLevel(e){return e<100?"excellent":e<500?"good":e<1e3?"fair":e<3e3?"poor":"critical"}}function m(){return new d}function p(){return!!(process.env.DB_DATABASE_URL||process.env.POSTGRES_URL)}function _(){let e=process.env.DB_DATABASE_URL||process.env.POSTGRES_URL;return{enabled:!!e,status:e?"configured":"not_configured",hasUrl:!!e,hasDirectUrl:!!(process.env.DB_DIRECT_URL||process.env.POSTGRES_URL_NON_POOLING)}}class v extends s.BaseHealthCheck{constructor(){super("mcp")}async check(){try{let e=process.env.MCP_VERCEL_TOKEN||process.env.MCP_VERCEL_TOKEN,t=process.env.MCP_VERCEL_PROJECT_ID||process.env.MCP_VERCEL_PROJECT_ID,r=process.env.MCP_VERCEL_TEAM_ID||process.env.MCP_VERCEL_TEAM_ID,n=process.env.MCP_LINEAR_API_KEY||process.env.MCP_LINEAR_API_KEY,a=process.env.MCP_GITHUB_TOKEN||process.env.MCP_GITHUB_TOKEN,i=await Promise.allSettled([this.testVercelMCP({...e&&{token:e},...t&&{projectId:t},...r&&{teamId:r}}),this.testLinearMCP({...n&&{apiKey:n}}),this.testGitHubMCP({...a&&{token:a}})]),c={vercel:this.extractResult(i[0]),linear:this.extractResult(i[1]),github:this.extractResult(i[2])},o=[e&&t&&r?"vercel":null,n?"linear":null,a?"github":null].filter(Boolean),l=Object.values(c).filter(e=>e.success),h="healthy";return 0===o.length?h="degraded":0===l.length?h="unhealthy":l.length<o.length&&(h="degraded"),s.healthLogger.info("MCP health check completed",{status:h,enabledServices:o.length,healthyServices:l.length}),{status:h,details:{message:"MCP integrations checked",enabled:o.length,healthy:l.length,degraded:o.length-l.length,vercel_success:c.vercel.success,linear_success:c.linear.success,github_success:c.github.success}}}catch(r){let e=r instanceof Error?r.message:"Unknown error",t=r instanceof Error?r.stack:void 0;return s.healthLogger.error("MCP health check failed",{error:e,stack:t}),{status:"unhealthy",details:{message:"MCP health check failed",enabled:0,healthy:0,degraded:0,vercel_success:!1,linear_success:!1,github_success:!1,error:e}}}}async testVercelMCP(e){let t=Date.now();try{let{token:r,projectId:s,teamId:n}=e;if(!r||!s||!n)return{service:"vercel",success:!1,responseTime:Date.now()-t,error:"Missing Vercel MCP credentials"};return await this.simulateAPICall("vercel",200),{service:"vercel",success:!0,responseTime:Date.now()-t,details:{projectId:s.substring(0,10)+"...",teamId:n.substring(0,10)+"..."}}}catch(r){let e=r instanceof Error?r.message:"Unknown error";return{service:"vercel",success:!1,responseTime:Date.now()-t,error:e}}}async testLinearMCP(e){let t=Date.now();try{let{apiKey:r}=e;if(!r)return{service:"linear",success:!1,responseTime:Date.now()-t,error:"Missing Linear MCP API key"};return await this.simulateAPICall("linear",300),{service:"linear",success:!0,responseTime:Date.now()-t,details:{apiKey:r.substring(0,8)+"..."}}}catch(r){let e=r instanceof Error?r.message:"Unknown error";return{service:"linear",success:!1,responseTime:Date.now()-t,error:e}}}async testGitHubMCP(e){let t=Date.now();try{let{token:r}=e;if(!r)return{service:"github",success:!1,responseTime:Date.now()-t,error:"Missing GitHub MCP token"};return await this.simulateAPICall("github",250),{service:"github",success:!0,responseTime:Date.now()-t,details:{token:r.substring(0,8)+"..."}}}catch(r){let e=r instanceof Error?r.message:"Unknown error";return{service:"github",success:!1,responseTime:Date.now()-t,error:e}}}async simulateAPICall(e,t){let r=t+200*Math.random();if(await new Promise(e=>setTimeout(e,r)),.05>Math.random())throw Error(`Simulated ${e} API error`)}extractResult(e){return"fulfilled"===e.status?e.value:{service:"unknown",success:!1,responseTime:0,error:e.reason instanceof Error?e.reason.message:"Unknown error"}}}function E(){return new v}function y(){let e=!!(process.env.MCP_VERCEL_TOKEN||process.env.MCP_VERCEL_TOKEN),t=!!(process.env.MCP_LINEAR_API_KEY||process.env.MCP_LINEAR_API_KEY),r=!!(process.env.MCP_GITHUB_TOKEN||process.env.MCP_GITHUB_TOKEN);return e||t||r}function k(){return{vercel:{enabled:!!(process.env.MCP_VERCEL_TOKEN||process.env.MCP_VERCEL_TOKEN),status:"unknown",hasToken:!!(process.env.MCP_VERCEL_TOKEN||process.env.MCP_VERCEL_TOKEN),hasProjectId:!!(process.env.MCP_VERCEL_PROJECT_ID||process.env.MCP_VERCEL_PROJECT_ID),hasTeamId:!!(process.env.MCP_VERCEL_TEAM_ID||process.env.MCP_VERCEL_TEAM_ID)},linear:{enabled:!!(process.env.MCP_LINEAR_API_KEY||process.env.MCP_LINEAR_API_KEY),status:"unknown",hasApiKey:!!(process.env.MCP_LINEAR_API_KEY||process.env.MCP_LINEAR_API_KEY)},github:{enabled:!!(process.env.MCP_GITHUB_TOKEN||process.env.MCP_GITHUB_TOKEN),status:"unknown",hasToken:!!(process.env.MCP_GITHUB_TOKEN||process.env.MCP_GITHUB_TOKEN)}}}function f(){return Object.values(k()).filter(e=>e.enabled).length}class T extends s.BaseHealthCheck{constructor(){super("stripe")}async check(){try{let e=process.env.STRIPE_SECRET_KEY,t="pk_live_51RGJ6nRrKLXtzZkMtpujgPAZR4MmRmQQrImSNrq6vdCLe6gfWulXfJDaDl1K2u3DKeKUegsXvzceFVi8xwnwroic00ER63lsVr",r=process.env.STRIPE_WEBHOOK_SECRET,n=[];e?e.startsWith("sk_")||n.push({type:"invalid_secret_key_format",message:"Stripe secret key format invalid",severity:"critical"}):n.push({type:"missing_secret_key",message:"Stripe secret key not configured",severity:"critical"}),t||n.push({type:"missing_publishable_key",message:"Stripe publishable key not configured",severity:"high"}),r||n.push({type:"missing_webhook_secret",message:"Stripe webhook secret not configured",severity:"medium"});let a="healthy";n.some(e=>"critical"===e.severity)?a="unhealthy":n.some(e=>"high"===e.severity)&&(a="degraded");let i=this.getStripeEnvironment(e);return s.healthLogger.info("Stripe health check completed",{status:a,keyType:i}),{status:a,details:{message:"Stripe system checked",keyType:i,environment:i,hasSecretKey:!!e,hasPublishableKey:!!t,hasWebhookSecret:!!r,issuesCount:n.length,hasCriticalIssues:n.some(e=>"critical"===e.severity)}}}catch(r){let e=r instanceof Error?r.message:"Unknown error",t=r instanceof Error?r.stack:void 0;return s.healthLogger.error("Stripe health check failed",{error:e,stack:t}),{status:"unhealthy",details:{message:"Stripe health check failed",hasSecretKey:!1,hasPublishableKey:!1,hasWebhookSecret:!1,error:e}}}}validateStripeConfig(e){let t=[],r=e.credentials.secretKey;r?(r.startsWith("sk_")||t.push({type:"invalid_secret_key_format",message:"Stripe secret key format invalid",severity:"critical"}),r.startsWith("sk_test_")&&t.push({type:"test_key_in_production",message:"Using test Stripe key in production",severity:"critical"})):t.push({type:"missing_secret_key",message:"Stripe secret key not configured",severity:"critical"});let s=e.credentials.publishableKey;return s?s.startsWith("pk_")||t.push({type:"invalid_publishable_key_format",message:"Stripe publishable key format invalid",severity:"high"}):t.push({type:"missing_publishable_key",message:"Stripe publishable key not configured",severity:"high"}),e.credentials.webhookSecret||t.push({type:"missing_webhook_secret",message:"Stripe webhook secret not configured",severity:"medium"}),{valid:0===t.filter(e=>"critical"===e.severity).length,issues:t}}async testStripeConnectivity(e){let t=Date.now();try{let t=e.credentials.secretKey,r=this.getStripeEnvironment(t);return await this.testRealStripe(t||"",r)}catch(r){let e=r instanceof Error?r.message:"Unknown error";return{success:!1,responseTime:Date.now()-t,keyType:"unknown",error:e}}}async simulateStripeTest(e){let t=Date.now();await new Promise(e=>setTimeout(e,150+200*Math.random()));let r=Date.now()-t,s=[];return"test"===e&&s.push("Using Stripe test keys"),{success:!0,responseTime:r,keyType:e,warningsCount:s.length,hasWarnings:s.length>0}}async testRealStripe(e,t){let r=Date.now();try{await new Promise(e=>setTimeout(e,300));let e=Date.now()-r,s=[];return"test"===t&&s.push("Using Stripe test environment"),{success:!0,responseTime:e,keyType:t,warningsCount:s.length,hasWarnings:s.length>0}}catch(s){let e=s instanceof Error?s.message:"Unknown error";return{success:!1,responseTime:Date.now()-r,keyType:t,error:e}}}getStripeEnvironment(e){return e?e.startsWith("sk_test_")?"test":e.startsWith("sk_live_")?"live":"unknown":"unknown"}}function C(){return new T}function w(){return!!process.env.STRIPE_SECRET_KEY}function I(){let e=process.env.STRIPE_SECRET_KEY;return{enabled:!!e,status:"configured",hasSecretKey:!!e,hasPublishableKey:!0,hasWebhookSecret:!!process.env.STRIPE_WEBHOOK_SECRET,environment:e?.startsWith("sk_test_")?"test":e?.startsWith("sk_live_")?"live":"unknown"}}let A={sendAlert:()=>Promise.resolve(),checkAlertRules:()=>Promise.resolve([])},S={recordHealthCheck:()=>{},getMetrics:()=>({uptime:Date.now(),requests:0,errors:0})};class b{constructor(){this.checks=new Map,this.initializeChecks()}static getInstance(){return b.instance||(b.instance=new b),b.instance}initializeChecks(){this.checks.set("database",m()),this.checks.set("auth",h()),this.checks.set("ai",a()),this.checks.set("stripe",C()),this.checks.set("mcp",E())}async checkService(e){let t=this.checks.get(e);if(!t)throw Error(`Health check not found for service: ${e}`);s.healthLogger.info(`Starting health check for ${e}`);try{let r=await t.execute();s.healthLogger.info(`Health check completed for ${e}`,{status:r.status,responseTime:r.responseTime});try{await A.processHealthCheck(r)}catch(r){let t=r instanceof Error?r.message:"Unknown error";s.healthLogger.error(`Failed to process alert for ${e}`,{error:t})}try{S.recordHealthCheck(r)}catch(r){let t=r instanceof Error?r.message:"Unknown error";s.healthLogger.error(`Failed to record metrics for ${e}`,{error:t})}return r}catch(r){let t=r instanceof Error?r.message:"Unknown error";throw s.healthLogger.error(`Health check failed for ${e}`,{error:t}),r}}async checkAll(){s.healthLogger.info("Starting comprehensive health check");let{result:e,time:t}=await (0,s.measureTime)(async()=>{let e=Array.from(this.checks.keys()).map(async e=>{try{return await this.checkService(e)}catch(r){let t=r instanceof Error?r.message:"Unknown error";return{service:e,status:"unhealthy",responseTime:0,timestamp:new Date().toISOString(),details:{error:t}}}});return await Promise.all(e)}),r=(0,s.createHealthReport)(e,t);s.healthLogger.info("Comprehensive health check completed",{overall:r.overall,totalTime:t,servicesChecked:e.length,healthyServices:r.summary.healthy});try{S.recordHealthReport(r)}catch(t){let e=t instanceof Error?t.message:"Unknown error";s.healthLogger.error("Failed to record health report metrics",{error:e})}return r}async checkCritical(){let e=["database","auth"];s.healthLogger.info("Starting critical health check",{services:e});let{result:t,time:r}=await (0,s.measureTime)(async()=>{let t=e.map(async e=>{try{return await this.checkService(e)}catch(r){let t=r instanceof Error?r.message:"Unknown error";return{service:e,status:"unhealthy",responseTime:0,timestamp:new Date().toISOString(),details:{error:t}}}});return await Promise.all(t)}),n=(0,s.createHealthReport)(t,r);return s.healthLogger.info("Critical health check completed",{overall:n.overall,totalTime:r}),n}getAvailableServices(){return Array.from(this.checks.keys())}}let P=b.getInstance();async function U(e){return await P.checkService(e)}async function L(){return await P.checkAll()}async function R(){return await P.checkCritical()}function D(){return P.getAvailableServices()}function H(e){switch(e){case"healthy":case"degraded":return 200;case"unhealthy":return 503;default:return 500}}function M(e,t=!0){let r={status:"overall"in e?e.overall:e.status,timestamp:e.timestamp,responseTime:e.responseTime};return"services"in e?{...r,summary:e.summary,services:t?e.services:e.services.map(e=>({service:e.service,status:e.status,responseTime:e.responseTime}))}:{...r,service:e.service,details:t?e.details:void 0}}}};