(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},340:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),a(38256),a(65675),a(12523);var r=a(23191),s=a(88716),n=a(37922),i=a.n(n),o=a(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let d=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,38256)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\page.tsx"],u="/dashboard/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10223:(e,t,a)=>{Promise.resolve().then(a.bind(a,55787))},87888:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},76464:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},95396:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},37358:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},11890:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48998:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},43810:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},7027:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},41137:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},88307:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},1572:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},63685:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},24061:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},94019:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},92481:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return v}});let r=a(91174),s=a(58374),n=a(10326),i=s._(a(17577)),o=r._(a(60962)),l=r._(a(60815)),d=a(23078),c=a(35248),u=a(31206);a(576);let m=a(50131),h=r._(a(86820)),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1};function x(e,t,a,r,s,n,i){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&s(!0),null==a?void 0:a.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,s=!1;a.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>s,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{s=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function f(e){let[t,a]=i.version.split(".",2),r=parseInt(t,10),s=parseInt(a,10);return r>18||18===r&&s>=3?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let g=(0,i.forwardRef)((e,t)=>{let{src:a,srcSet:r,sizes:s,height:o,width:l,decoding:d,className:c,style:u,fetchPriority:m,placeholder:h,loading:p,unoptimized:g,fill:j,onLoadRef:v,onLoadingCompleteRef:b,setBlurComplete:y,setShowAltText:w,sizesInput:k,onLoad:N,onError:C,...M}=e;return(0,n.jsx)("img",{...M,...f(m),loading:p,width:l,height:o,decoding:d,"data-nimg":j?"fill":"1",className:c,style:u,sizes:s,srcSet:r,src:a,ref:(0,i.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(C&&(e.src=e.src),e.complete&&x(e,h,v,b,y,g,k))},[a,h,v,b,y,C,g,k,t]),onLoad:e=>{x(e.currentTarget,h,v,b,y,g,k)},onError:e=>{w(!0),"empty"!==h&&y(!0),C&&C(e)}})});function j(e){let{isAppRouter:t,imgAttributes:a}=e,r={as:"image",imageSrcSet:a.srcSet,imageSizes:a.sizes,crossOrigin:a.crossOrigin,referrerPolicy:a.referrerPolicy,...f(a.fetchPriority)};return t&&o.default.preload?(o.default.preload(a.src,r),null):(0,n.jsx)(l.default,{children:(0,n.jsx)("link",{rel:"preload",href:a.srcSet?void 0:a.src,...r},"__nimg-"+a.src+a.srcSet+a.sizes)})}let v=(0,i.forwardRef)((e,t)=>{let a=(0,i.useContext)(m.RouterContext),r=(0,i.useContext)(u.ImageConfigContext),s=(0,i.useMemo)(()=>{let e=p||r||c.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),a=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:a}},[r]),{onLoad:o,onLoadingComplete:l}=e,x=(0,i.useRef)(o);(0,i.useEffect)(()=>{x.current=o},[o]);let f=(0,i.useRef)(l);(0,i.useEffect)(()=>{f.current=l},[l]);let[v,b]=(0,i.useState)(!1),[y,w]=(0,i.useState)(!1),{props:k,meta:N}=(0,d.getImgProps)(e,{defaultLoader:h.default,imgConf:s,blurComplete:v,showAltText:y});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(g,{...k,unoptimized:N.unoptimized,placeholder:N.placeholder,fill:N.fill,onLoadRef:x,onLoadingCompleteRef:f,setBlurComplete:b,setShowAltText:w,sizesInput:e.sizes,ref:t}),N.priority?(0,n.jsx)(j,{isAppRouter:!a,imgAttributes:k}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23484:(e,t,a)=>{"use strict";e.exports=a(81616).vendored.contexts.AmpContext},81157:(e,t,a)=>{"use strict";e.exports=a(81616).vendored.contexts.HeadManagerContext},31206:(e,t,a)=>{"use strict";e.exports=a(81616).vendored.contexts.ImageConfigContext},98710:(e,t)=>{"use strict";function a(e){let{ampFirst:t=!1,hybrid:a=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||a&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return a}})},23078:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),a(576);let r=a(20380),s=a(35248);function n(e){return void 0!==e.default}function i(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var a;let o,l,d,{src:c,sizes:u,unoptimized:m=!1,priority:h=!1,loading:p,className:x,quality:f,width:g,height:j,fill:v=!1,style:b,overrideSrc:y,onLoad:w,onLoadingComplete:k,placeholder:N="empty",blurDataURL:C,fetchPriority:M,layout:P,objectFit:S,objectPosition:A,lazyBoundary:D,lazyRoot:E,...Z}=e,{imgConf:W,showAltText:_,blurComplete:z,defaultLoader:O}=t,R=W||s.imageConfigDefault;if("allSizes"in R)o=R;else{let e=[...R.deviceSizes,...R.imageSizes].sort((e,t)=>e-t),t=R.deviceSizes.sort((e,t)=>e-t);o={...R,allSizes:e,deviceSizes:t}}if(void 0===O)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let T=Z.loader||O;delete Z.loader,delete Z.srcSet;let B="__next_img_default"in T;if(B){if("custom"===o.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=T;T=t=>{let{config:a,...r}=t;return e(r)}}if(P){"fill"===P&&(v=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[P];e&&(b={...b,...e});let t={responsive:"100vw",fill:"100vw"}[P];t&&!u&&(u=t)}let F="",q=i(g),I=i(j);if("object"==typeof(a=c)&&(n(a)||void 0!==a.src)){let e=n(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(l=e.blurWidth,d=e.blurHeight,C=C||e.blurDataURL,F=e.src,!v){if(q||I){if(q&&!I){let t=q/e.width;I=Math.round(e.height*t)}else if(!q&&I){let t=I/e.height;q=Math.round(e.width*t)}}else q=e.width,I=e.height}}let V=!h&&("lazy"===p||void 0===p);(!(c="string"==typeof c?c:F)||c.startsWith("data:")||c.startsWith("blob:"))&&(m=!0,V=!1),o.unoptimized&&(m=!0),B&&c.endsWith(".svg")&&!o.dangerouslyAllowSVG&&(m=!0),h&&(M="high");let X=i(f),L=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:S,objectPosition:A}:{},_?{}:{color:"transparent"},b),H=z||"empty"===N?null:"blur"===N?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:q,heightInt:I,blurWidth:l,blurHeight:d,blurDataURL:C||"",objectFit:L.objectFit})+'")':'url("'+N+'")',$=H?{backgroundSize:L.objectFit||"cover",backgroundPosition:L.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},U=function(e){let{config:t,src:a,unoptimized:r,width:s,quality:n,sizes:i,loader:o}=e;if(r)return{src:a,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,a){let{deviceSizes:r,allSizes:s}=e;if(a){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(a);r)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,i),c=l.length-1;return{sizes:i||"w"!==d?i:"100vw",srcSet:l.map((e,r)=>o({config:t,src:a,quality:n,width:e})+" "+("w"===d?e:r+1)+d).join(", "),src:o({config:t,src:a,quality:n,width:l[c]})}}({config:o,src:c,unoptimized:m,width:q,quality:X,sizes:u,loader:T});return{props:{...Z,loading:V?"lazy":p,fetchPriority:M,width:q,height:I,decoding:"async",className:x,style:{...L,...$},sizes:U.sizes,srcSet:U.srcSet,src:y||U.src},meta:{unoptimized:m,priority:h,placeholder:N,fill:v}}}},60815:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return x},defaultHead:function(){return u}});let r=a(91174),s=a(58374),n=a(10326),i=s._(a(17577)),o=r._(a(78003)),l=a(23484),d=a(81157),c=a(98710);function u(e){void 0===e&&(e=!1);let t=[(0,n.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,n.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function m(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}a(576);let h=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:a}=t;return e.reduce(m,[]).reverse().concat(u(a).reverse()).filter(function(){let e=new Set,t=new Set,a=new Set,r={};return s=>{let n=!0,i=!1;if(s.key&&"number"!=typeof s.key&&s.key.indexOf("$")>0){i=!0;let t=s.key.slice(s.key.indexOf("$")+1);e.has(t)?n=!1:e.add(t)}switch(s.type){case"title":case"base":t.has(s.type)?n=!1:t.add(s.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(s.props.hasOwnProperty(t)){if("charSet"===t)a.has(t)?n=!1:a.add(t);else{let e=s.props[t],a=r[t]||new Set;("name"!==t||!i)&&a.has(e)?n=!1:(a.add(e),r[t]=a)}}}}return n}}()).reverse().map((e,t)=>{let r=e.key||t;if(!a&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:r})})}let x=function(e){let{children:t}=e,a=(0,i.useContext)(l.AmpStateContext),r=(0,i.useContext)(d.HeadManagerContext);return(0,n.jsx)(o.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,c.isInAmpMode)(a),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20380:(e,t)=>{"use strict";function a(e){let{widthInt:t,heightInt:a,blurWidth:r,blurHeight:s,blurDataURL:n,objectFit:i}=e,o=r?40*r:t,l=s?40*s:a,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return a}})},35248:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{VALID_LOADERS:function(){return a},imageConfigDefault:function(){return r}});let a=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},69029:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return l},getImageProps:function(){return o}});let r=a(91174),s=a(23078),n=a(92481),i=r._(a(86820));function o(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let l=n.Image},86820:(e,t)=>{"use strict";function a(e){let{config:t,src:a,width:r,quality:s}=e;return t.path+"?url="+encodeURIComponent(a)+"&w="+r+"&q="+(s||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),a.__next_img_default=!0;let r=a},78003:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let r=a(17577),s=()=>{},n=()=>{};function i(e){var t;let{headManager:a,reduceComponentsToState:i}=e;function o(){if(a&&a.mountedInstances){let t=r.Children.toArray(Array.from(a.mountedInstances).filter(Boolean));a.updateHead(i(t,e))}}return null==a||null==(t=a.mountedInstances)||t.add(e.children),o(),s(()=>{var t;return null==a||null==(t=a.mountedInstances)||t.add(e.children),()=>{var t;null==a||null==(t=a.mountedInstances)||t.delete(e.children)}}),s(()=>(a&&(a._pendingUpdate=o),()=>{a&&(a._pendingUpdate=o)})),n(()=>(a&&a._pendingUpdate&&(a._pendingUpdate(),a._pendingUpdate=null),()=>{a&&a._pendingUpdate&&(a._pendingUpdate(),a._pendingUpdate=null)})),null}},55787:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>eV});var r=a(10326);let s=Symbol.for("constructDateFrom");function n(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&s in e?e[s](t):e instanceof Date?new e.constructor(t):new Date(t)}let i={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function o(e){return (t={})=>{let a=t.width?String(t.width):e.defaultWidth;return e.formats[a]||e.formats[e.defaultWidth]}}let l={date:o({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:o({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:o({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},d={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function c(e){return(t,a)=>{let r;if("formatting"===(a?.context?String(a.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,s=a?.width?String(a.width):t;r=e.formattingValues[s]||e.formattingValues[t]}else{let t=e.defaultWidth,s=a?.width?String(a.width):e.defaultWidth;r=e.values[s]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function u(e){return(t,a={})=>{let r;let s=a.width,n=s&&e.matchPatterns[s]||e.matchPatterns[e.defaultMatchWidth],i=t.match(n);if(!i)return null;let o=i[0],l=s&&e.parsePatterns[s]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(let a=0;a<e.length;a++)if(t(e[a]))return a}(l,e=>e.test(o)):function(e,t){for(let a in e)if(Object.prototype.hasOwnProperty.call(e,a)&&t(e[a]))return a}(l,e=>e.test(o));return r=e.valueCallback?e.valueCallback(d):d,{value:r=a.valueCallback?a.valueCallback(r):r,rest:t.slice(o.length)}}}function m(e){return(t,a={})=>{let r=t.match(e.matchPattern);if(!r)return null;let s=r[0],n=t.match(e.parsePattern);if(!n)return null;let i=e.valueCallback?e.valueCallback(n[0]):n[0];return{value:i=a.valueCallback?a.valueCallback(i):i,rest:t.slice(s.length)}}}let h={code:"en-US",formatDistance:(e,t,a)=>{let r;let s=i[e];return(r="string"==typeof s?s:1===t?s.one:s.other.replace("{{count}}",t.toString()),a?.addSuffix)?a.comparison&&a.comparison>0?"in "+r:r+" ago":r},formatLong:l,formatRelative:(e,t,a,r)=>d[e],localize:{ordinalNumber:(e,t)=>{let a=Number(e),r=a%100;if(r>20||r<10)switch(r%10){case 1:return a+"st";case 2:return a+"nd";case 3:return a+"rd"}return a+"th"},era:c({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:c({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:c({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:c({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:c({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:m({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:u({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:u({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:u({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:u({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:u({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},p={};function x(e,t){return n(t||e,e)}function f(e){let t=x(e),a=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return a.setUTCFullYear(t.getFullYear()),+e-+a}function g(e,...t){let a=n.bind(null,e||t.find(e=>"object"==typeof e));return t.map(a)}function j(e,t){let a=+x(e)-+x(t);return a<0?-1:a>0?1:a}function v(e,t){return function(e,t,a){var r,s,n,i;let o;let l=a?.locale??p.locale??h,d=j(e,t);if(isNaN(d))throw RangeError("Invalid time value");let c=Object.assign({},a,{addSuffix:a?.addSuffix,comparison:d}),[u,m]=g(a?.in,...d>0?[t,e]:[e,t]),v=(r=m,s=u,(i=void 0,e=>{let t=(i?Math[i]:Math.trunc)(e);return 0===t?0:t})((+x(r)-+x(s))/1e3)),b=Math.round((v-(f(m)-f(u))/1e3)/60);if(b<2){if(a?.includeSeconds){if(v<5)return l.formatDistance("lessThanXSeconds",5,c);if(v<10)return l.formatDistance("lessThanXSeconds",10,c);if(v<20)return l.formatDistance("lessThanXSeconds",20,c);if(v<40)return l.formatDistance("halfAMinute",0,c);else if(v<60)return l.formatDistance("lessThanXMinutes",1,c);else return l.formatDistance("xMinutes",1,c)}return 0===b?l.formatDistance("lessThanXMinutes",1,c):l.formatDistance("xMinutes",b,c)}if(b<45)return l.formatDistance("xMinutes",b,c);if(b<90)return l.formatDistance("aboutXHours",1,c);if(b<1440)return l.formatDistance("aboutXHours",Math.round(b/60),c);if(b<2520)return l.formatDistance("xDays",1,c);if(b<43200)return l.formatDistance("xDays",Math.round(b/1440),c);if(b<86400)return o=Math.round(b/43200),l.formatDistance("aboutXMonths",o,c);if((o=function(e,t,a){let[r,s,n]=g(void 0,e,e,t),i=j(s,n),o=Math.abs(function(e,t,a){let[r,s]=g(void 0,e,t);return 12*(r.getFullYear()-s.getFullYear())+(r.getMonth()-s.getMonth())}(s,n));if(o<1)return 0;1===s.getMonth()&&s.getDate()>27&&s.setDate(30),s.setMonth(s.getMonth()-i*o);let l=j(s,n)===-i;(function(e,t){let a=x(e,void 0);return+function(e,t){let a=x(e,t?.in);return a.setHours(23,59,59,999),a}(a,void 0)==+function(e,t){let a=x(e,t?.in),r=a.getMonth();return a.setFullYear(a.getFullYear(),r+1,0),a.setHours(23,59,59,999),a}(a,void 0)})(r)&&1===o&&1===j(r,n)&&(l=!1);let d=i*(o-+l);return 0===d?0:d}(m,u))<12)return l.formatDistance("xMonths",Math.round(b/43200),c);{let e=o%12,t=Math.trunc(o/12);return e<3?l.formatDistance("aboutXYears",t,c):e<9?l.formatDistance("overXYears",t,c):l.formatDistance("almostXYears",t+1,c)}}(e,n(e,Date.now()),t)}let b={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 m\xeas",other:"cerca de {{count}} meses"},xMonths:{one:"1 m\xeas",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},y={date:o({formats:{full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:o({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:o({formats:{full:"{{date}} '\xe0s' {{time}}",long:"{{date}} '\xe0s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},w={lastWeek:e=>{let t=e.getDay();return"'"+(0===t||6===t?"\xfaltimo":"\xfaltima")+"' eeee '\xe0s' p"},yesterday:"'ontem \xe0s' p",today:"'hoje \xe0s' p",tomorrow:"'amanh\xe3 \xe0s' p",nextWeek:"eeee '\xe0s' p",other:"P"},k={code:"pt-BR",formatDistance:(e,t,a)=>{let r;let s=b[e];return(r="string"==typeof s?s:1===t?s.one:s.other.replace("{{count}}",String(t)),a?.addSuffix)?a.comparison&&a.comparison>0?"em "+r:"h\xe1 "+r:r},formatLong:y,formatRelative:(e,t,a,r)=>{let s=w[e];return"function"==typeof s?s(t):s},localize:{ordinalNumber:(e,t)=>{let a=Number(e);return t?.unit==="week"?a+"\xaa":a+"\xba"},era:c({values:{narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},defaultWidth:"wide"}),quarter:c({values:{narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xba trimestre","2\xba trimestre","3\xba trimestre","4\xba trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:c({values:{narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","mar\xe7o","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},defaultWidth:"wide"}),day:c({values:{narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","ter\xe7a","quarta","quinta","sexta","s\xe1bado"],wide:["domingo","segunda-feira","ter\xe7a-feira","quarta-feira","quinta-feira","sexta-feira","s\xe1bado"]},defaultWidth:"wide"}),dayPeriod:c({values:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:m({matchPattern:/^(\d+)[ºªo]?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:u({matchPatterns:{narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},defaultParseWidth:"any"}),quarter:u({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:u({matchPatterns:{narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},defaultParseWidth:"any"}),day:u({matchPatterns:{narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},defaultMatchWidth:"wide",parsePatterns:{short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},defaultParseWidth:"any"}),dayPeriod:u({matchPatterns:{narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};var N=a(99102),C=a(95396),M=a(11890),P=a(39183),S=a(75290),A=a(1572),D=a(88307),E=a(94019),Z=a(48998),W=a(37358),_=a(69029),z=a.n(_),O=a(35047),R=a(77109),T=a(17577),B=a(85999),F=a(76557);let q=(0,F.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var I=a(63685),V=a(43810),X=a(24061),L=a(65842),H=a(76464),$=a(72257),U=a(91664),Y=a(29752),Q=a(51223);function J({className:e,onCreateWorkbook:t,onImportFile:a,recentWorkbooks:s=[]}){let n=(0,O.useRouter)(),[i,o]=(0,T.useState)(!1),[l,d]=(0,T.useState)(!1),c=async()=>{if(t){t();return}try{o(!0),n.push("/dashboard?create=true")}catch{B.toast.error("Erro ao criar planilha")}finally{o(!1)}},u=async()=>{if(a){a();return}try{d(!0);let e=document.createElement("input");e.type="file",e.accept=".xlsx,.xls,.csv",e.onchange=async e=>{let t=e.target.files?.[0];t&&B.toast.success(`Arquivo ${t.name} selecionado para importa\xe7\xe3o`)},e.click()}catch{B.toast.error("Erro ao importar arquivo")}finally{d(!1)}},m=async()=>{if(0===s.length){B.toast.error("Nenhuma planilha recente encontrada");return}let e=s[0];if(!e){B.toast.error("Nenhuma planilha recente encontrada");return}try{B.toast.success(`Duplicando planilha "${e.name}"`)}catch{B.toast.error("Erro ao duplicar planilha")}},h=[{id:"create",title:"Nova Planilha",description:"Criar planilha em branco",icon:r.jsx(q,{className:"h-5 w-5"}),action:c,disabled:i},{id:"ai-create",title:"Criar com IA",description:"Usar assistente inteligente",icon:r.jsx(A.Z,{className:"h-5 w-5"}),action:()=>{n.push("/dashboard?create=true&ai=true")},badge:"IA"},{id:"import",title:"Importar Arquivo",description:"Excel, CSV ou outros formatos",icon:r.jsx(I.Z,{className:"h-5 w-5"}),action:u,variant:"outline",disabled:l},{id:"duplicate",title:"Duplicar Recente",description:"Copiar \xfaltima planilha editada",icon:r.jsx(V.Z,{className:"h-5 w-5"}),action:m,variant:"outline",disabled:0===s.length}],p=[{id:"templates",title:"Templates",description:"Modelos prontos",icon:r.jsx(N.Z,{className:"h-4 w-4"}),action:()=>{n.push("/dashboard?templates=true")},variant:"secondary"},{id:"collaborate",title:"Colaborar",description:"Planilhas compartilhadas",icon:r.jsx(X.Z,{className:"h-4 w-4"}),action:()=>{n.push("/dashboard?tab=shared")},variant:"secondary"}];return(0,r.jsxs)(Y.Zb,{className:(0,Q.cn)("",e),children:[(0,r.jsxs)(Y.Ol,{children:[r.jsx(Y.ll,{className:"text-lg",children:"A\xe7\xf5es R\xe1pidas"}),r.jsx(Y.SZ,{children:"Comece rapidamente com suas planilhas"})]}),(0,r.jsxs)(Y.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:h.map(e=>(0,r.jsxs)(U.Button,{variant:e.variant||"default",className:(0,Q.cn)("h-auto p-4 flex flex-col items-start space-y-2 relative",e.disabled&&"opacity-50 cursor-not-allowed"),onClick:e.action,disabled:e.disabled,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.icon,r.jsx("span",{className:"font-medium",children:e.title})]}),e.badge&&r.jsx("span",{className:"text-xs bg-primary/20 text-primary px-2 py-1 rounded-full",children:e.badge})]}),r.jsx("p",{className:"text-xs text-left opacity-80",children:e.description})]},e.id))}),r.jsx("div",{className:"flex flex-wrap gap-2",children:p.map(e=>(0,r.jsxs)(U.Button,{variant:e.variant||"secondary",size:"sm",className:"flex items-center space-x-2",onClick:e.action,disabled:e.disabled,children:[e.icon,r.jsx("span",{children:e.title})]},e.id))})]})]})}function G({className:e}){let t=(0,O.useRouter)(),a=[{id:"financial",name:"Controle Financeiro",icon:r.jsx(L.Z,{className:"h-4 w-4"}),color:"text-green-600"},{id:"dashboard",name:"Dashboard",icon:r.jsx(H.Z,{className:"h-4 w-4"}),color:"text-blue-600"},{id:"calculator",name:"Calculadora",icon:r.jsx($.Z,{className:"h-4 w-4"}),color:"text-purple-600"}],s=e=>{t.push(`/dashboard?template=${e}`),B.toast.success("Carregando template...")};return(0,r.jsxs)(Y.Zb,{className:(0,Q.cn)("",e),children:[(0,r.jsxs)(Y.Ol,{children:[r.jsx(Y.ll,{className:"text-base",children:"Templates Populares"}),r.jsx(Y.SZ,{children:"Comece com modelos prontos"})]}),(0,r.jsxs)(Y.aY,{children:[r.jsx("div",{className:"space-y-2",children:a.map(e=>(0,r.jsxs)(U.Button,{variant:"ghost",className:"w-full justify-start h-auto p-3",onClick:()=>s(e.id),children:[r.jsx("div",{className:(0,Q.cn)("mr-3",e.color),children:e.icon}),r.jsx("span",{className:"text-sm",children:e.name})]},e.id))}),r.jsx(U.Button,{variant:"outline",size:"sm",className:"w-full mt-3",onClick:()=>t.push("/dashboard?templates=true"),children:"Ver Todos os Templates"})]})]})}function K({recentWorkbooks:e=[],className:t}){let a=(0,O.useRouter)(),s=e=>{a.push(`/workbook/${e}`)};return 0===e.length?null:(0,r.jsxs)(Y.Zb,{className:(0,Q.cn)("",t),children:[(0,r.jsxs)(Y.Ol,{children:[r.jsx(Y.ll,{className:"text-base",children:"Acesso R\xe1pido"}),r.jsx(Y.SZ,{children:"Suas planilhas mais recentes"})]}),(0,r.jsxs)(Y.aY,{children:[r.jsx("div",{className:"space-y-2",children:e.slice(0,3).map(e=>(0,r.jsxs)(U.Button,{variant:"ghost",className:"w-full justify-start h-auto p-3",onClick:()=>s(e.id),children:[r.jsx(N.Z,{className:"h-4 w-4 mr-3 text-primary"}),(0,r.jsxs)("div",{className:"flex-1 text-left",children:[r.jsx("p",{className:"text-sm font-medium truncate",children:e.name}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Editado ",new Date(e.updatedAt).toLocaleDateString("pt-BR")]})]})]},e.id))}),e.length>3&&(0,r.jsxs)(U.Button,{variant:"outline",size:"sm",className:"w-full mt-3",onClick:()=>a.push("/dashboard?tab=recent"),children:["Ver Todas (",e.length,")"]})]})]})}let ee=(0,F.Z)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]]),et=(0,F.Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var ea=a(7027),er=a(90434),es=a(38443),en=a(10143),ei=a(38227);let eo={workbook_created:{icon:N.Z,color:"text-green-600",bgColor:"bg-green-50",label:"Cria\xe7\xe3o"},workbook_edited:{icon:ee,color:"text-blue-600",bgColor:"bg-blue-50",label:"Edi\xe7\xe3o"},ai_command:{icon:A.Z,color:"text-purple-600",bgColor:"bg-purple-50",label:"IA"},collaboration:{icon:X.Z,color:"text-orange-600",bgColor:"bg-orange-50",label:"Colabora\xe7\xe3o"}};function el({activity:e}){let t=eo[e.type],a=t.icon,s=v(new Date(e.timestamp),{addSuffix:!0,locale:k}),n=e.metadata?.workbookId,i=e.metadata?.hasAI;return(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors group",children:[r.jsx("div",{className:(0,Q.cn)("flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",t.bgColor),children:r.jsx(a,{className:(0,Q.cn)("h-4 w-4",t.color)})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("p",{className:"text-sm font-medium text-foreground",children:e.title}),r.jsx(es.C,{variant:"outline",className:"text-xs",children:t.label}),i&&r.jsx(es.C,{variant:"secondary",className:"text-xs bg-purple-100 text-purple-700",children:"IA"})]}),(0,r.jsxs)(en.h_,{children:[r.jsx(en.$F,{asChild:!0,children:r.jsx(U.Button,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:r.jsx(et,{className:"h-3 w-3"})})}),(0,r.jsxs)(en.AW,{align:"end",children:[n&&r.jsx(en.Xi,{asChild:!0,children:(0,r.jsxs)(er.default,{href:`/workbook/${n}`,children:[r.jsx(ea.Z,{className:"h-3 w-3 mr-2"}),"Abrir Planilha"]})}),r.jsx(en.Xi,{children:"Ver Detalhes"})]})]})]}),r.jsx("p",{className:"text-sm text-muted-foreground mt-1 truncate",children:e.description}),e.metadata&&(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-muted-foreground",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[r.jsx(Z.Z,{className:"h-3 w-3"}),r.jsx("span",{children:s})]}),e.metadata.sharedBy&&(0,r.jsxs)("span",{children:["por ",e.metadata.sharedBy]}),e.metadata.sharedWith&&(0,r.jsxs)("span",{children:["com ",e.metadata.sharedWith]})]})]})]})}function ed(){return(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3",children:[r.jsx(ei.O,{className:"w-8 h-8 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(ei.O,{className:"h-4 w-32"}),r.jsx(ei.O,{className:"h-4 w-12"})]}),r.jsx(ei.O,{className:"h-3 w-48"}),r.jsx(ei.O,{className:"h-3 w-24"})]})]})}function ec({activities:e,isLoading:t,className:a,maxItems:s=10}){let n=e.slice(0,s);return(0,r.jsxs)(Y.Zb,{className:(0,Q.cn)("",a),children:[r.jsx(Y.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(Y.ll,{className:"text-lg",children:"Atividade Recente"}),r.jsx(Y.SZ,{children:"Suas \xfaltimas a\xe7\xf5es no Excel Copilot"})]}),!t&&e.length>s&&(0,r.jsxs)(U.Button,{variant:"outline",size:"sm",children:["Ver Todas (",e.length,")"]})]})}),r.jsx(Y.aY,{className:"p-0",children:t?r.jsx("div",{className:"space-y-1",children:Array.from({length:5}).map((e,t)=>r.jsx(ed,{},t))}):n.length>0?r.jsx("div",{className:"space-y-1",children:n.map(e=>r.jsx(el,{activity:e},e.id))}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[r.jsx(Z.Z,{className:"h-8 w-8 text-muted-foreground mb-2"}),r.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Nenhuma atividade recente"}),r.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Suas a\xe7\xf5es aparecer\xe3o aqui conforme voc\xea usa o Excel Copilot"})]})})]})}var eu=a(87888),em=a(88378);let eh=(0,F.Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),ep=(0,F.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var ex=a(6709);function ef({title:e,description:t,icon:a,action:s,className:n}){return(0,r.jsxs)("div",{className:(0,Q.cn)("flex flex-col items-center justify-center p-8 text-center","bg-gray-50 dark:bg-gray-900 border border-dashed rounded-lg","min-h-[220px]",n),children:[a&&r.jsx("div",{className:"text-gray-400 mb-4",children:a}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-200",children:e}),t&&r.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400 max-w-md",children:t}),s&&r.jsx("div",{className:"mt-5",children:s})]})}var eg=a(50258);let ej=T.forwardRef(({className:e,...t},a)=>r.jsx("table",{ref:a,className:(0,Q.cn)("w-full caption-bottom text-sm",e),...t}));ej.displayName="Table";let ev=T.forwardRef(({className:e,...t},a)=>r.jsx("thead",{ref:a,className:(0,Q.cn)("[&_tr]:border-b",e),...t}));ev.displayName="TableHeader";let eb=T.forwardRef(({className:e,...t},a)=>r.jsx("tbody",{ref:a,className:(0,Q.cn)("[&_tr:last-child]:border-0",e),...t}));eb.displayName="TableBody",T.forwardRef(({className:e,...t},a)=>r.jsx("tfoot",{ref:a,className:(0,Q.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let ey=T.forwardRef(({className:e,...t},a)=>r.jsx("tr",{ref:a,className:(0,Q.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));ey.displayName="TableRow";let ew=T.forwardRef(({className:e,...t},a)=>r.jsx("th",{ref:a,className:(0,Q.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));ew.displayName="TableHead";let ek=T.forwardRef(({className:e,...t},a)=>r.jsx("td",{ref:a,className:(0,Q.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));ek.displayName="TableCell";let eN=T.forwardRef(({className:e,...t},a)=>r.jsx("caption",{ref:a,className:(0,Q.cn)("mt-4 text-sm text-muted-foreground",e),...t}));eN.displayName="TableCaption";var eC=a(51641);function eM({searchQuery:e="",filters:t={},onFiltersChange:a}){let s=(0,O.useRouter)(),{data:n}=(0,R.useSession)(),[i,o]=(0,T.useState)([]),[l,d]=(0,T.useState)(!0),[c,u]=(0,T.useState)(null),{fetchWithCSRF:m}=(0,ex.useFetchWithCSRF)(),[h,p]=(0,T.useState)(!1),[x,f]=(0,T.useState)({current:0,total:1,limit:10,hasMore:!1}),g=e=>v(e,{addSuffix:!0,locale:k}),j=(0,T.useCallback)(async(a=0)=>{try{let r;if(d(!0),!n?.user){p(!0),o([]),d(!1);return}let s=0,i=!1;for(;s<3&&!i;)try{s++;let n=new URLSearchParams({...e&&{search:e},...t.sortBy&&{sortBy:t.sortBy},...t.sortOrder&&{sortOrder:t.sortOrder},...t.dateRange&&"all"!==t.dateRange&&{dateRange:t.dateRange},...t.minSheets&&{minSheets:t.minSheets.toString()},...t.maxSheets&&{maxSheets:t.maxSheets.toString()},page:a.toString(),limit:x.limit.toString()}),o=await m(`/api/workbooks?${n.toString()}`,{headers:{"Content-Type":"application/json"}});if(o.ok)i=!0,r=await o.json();else{let e="";try{let t=await o.json();e=t.details||t.error||""}catch{}if(401===o.status)throw Error(`N\xe3o autorizado: ${e}`);if(s<3)await new Promise(e=>setTimeout(e,1e3*s));else throw Error(`API retornou c\xf3digo ${o.status}: ${e}`)}}catch(e){if(e instanceof Error&&e.message?.includes("N\xe3o autorizado")){B.toast.error("Voc\xea precisa estar autenticado para acessar suas planilhas"),p(!0),o([]),d(!1);return}if(s<3)await new Promise(e=>setTimeout(e,1e3*s));else throw e}if(!i)throw Error("M\xe1ximo de tentativas atingido ao carregar planilhas");eC.logger.debug("WorkbooksTable: Processando workbooks",{responseDataKeys:Object.keys(r||{}),hasData:!!r?.data,hasWorkbooks:!!r?.data?.workbooks||!!r?.workbooks});let l=r?.data?.workbooks||r?.workbooks||[];if(eC.logger.debug("WorkbooksTable: Array de workbooks extra\xeddo",{count:l.length,isArray:Array.isArray(l)}),r?.data?.pagination||r?.pagination){let e=r?.data?.pagination||r?.pagination;f({current:e.page,total:e.totalPages,limit:e.limit,hasMore:e.hasMore})}let c=l.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));o(c),p(!1)}catch(t){eC.logger.error("WorkbooksTable: Erro ao carregar planilhas",t,{userId:n?.user?.id,searchQuery:e,attempts:"max_attempts_reached"}),t instanceof Error?t.message.includes("401")||t.message.includes("unauthorized")?B.toast.error("Sess\xe3o expirada. Fa\xe7a login novamente."):t.message.includes("network")||t.message.includes("fetch")?B.toast.error("Erro de conex\xe3o. Verificando conectividade..."):t.message.includes("timeout")?B.toast.error("Tempo limite excedido. Tente novamente."):B.toast.error(`Erro ao carregar planilhas: ${t.message}`):B.toast.error("Erro desconhecido ao carregar planilhas. Tente novamente mais tarde."),p(!0),o([])}finally{eC.logger.debug("WorkbooksTable: Finalizando carregamento",{workbooksCount:i.length,hasError:h}),d(!1)}},[m,n?.user?.id,e,t,x.limit]),b=()=>{p(!1),d(!0),j()},y=e=>{s.push(`/workbook/${e}`)},w=async e=>{try{let t=await m(`/api/workbooks/${e}/duplicate`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Erro ao duplicar planilha");let a=await t.json(),r={...a.workbook,createdAt:new Date(a.workbook.createdAt),updatedAt:new Date(a.workbook.updatedAt),sheets:Array.isArray(a.workbook.sheets)?a.workbook.sheets:a.workbook.sheetsCount||0};o([...i,r]),B.toast.success("Planilha duplicada com sucesso!")}catch(t){eC.logger.error("WorkbooksTable: Erro ao duplicar workbook",t,{workbookId:e,userId:n?.user?.id}),B.toast.error("N\xe3o foi poss\xedvel duplicar a planilha")}},C=async e=>{u(e);try{if(!(await m(`/api/workbooks/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao excluir planilha");o(i.filter(t=>t.id!==e)),B.toast.success("Planilha exclu\xedda com sucesso!")}catch(t){eC.logger.error("WorkbooksTable: Erro ao excluir workbook",t,{workbookId:e,userId:n?.user?.id}),B.toast.error("N\xe3o foi poss\xedvel excluir a planilha")}finally{u(null)}};return l?r.jsx("div",{className:"w-full py-10 flex justify-center",children:r.jsx("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"})}):h?r.jsx("div",{className:"flex flex-col items-center justify-center h-64 text-center",children:r.jsx(ef,{icon:r.jsx(eu.Z,{className:"h-12 w-12 text-destructive"}),title:"Erro ao carregar planilhas",description:"N\xe3o foi poss\xedvel carregar suas planilhas. Isso pode ser um problema tempor\xe1rio.",action:r.jsx(U.Button,{onClick:()=>b(),children:"Tentar Novamente"})})}):0===i.length?r.jsx("div",{className:"flex flex-col items-center justify-center h-64 text-center",children:r.jsx(ef,{icon:r.jsx(N.Z,{className:"h-12 w-12"}),title:"Nenhuma planilha encontrada",description:e?`N\xe3o encontramos planilhas com "${e}". Tente outro termo de busca.`:"Voc\xea ainda n\xe3o criou nenhuma planilha. Comece criando sua primeira planilha agora.",action:e?void 0:(0,r.jsxs)(eg.MD,{onClick:()=>s.push("/dashboard?create=true"),children:[r.jsx(q,{className:"h-4 w-4 mr-2"}),"Criar Nova Planilha"]})})}):(0,r.jsxs)("div",{className:"w-full space-y-4",children:[(0,r.jsxs)(ej,{children:[r.jsx(eN,{children:"Lista de suas planilhas Excel"}),r.jsx(ev,{children:(0,r.jsxs)(ey,{children:[(0,r.jsxs)(ew,{className:"w-[40%]",children:["Nome","name"===t.sortBy&&r.jsx("span",{className:"ml-1 text-xs",children:"asc"===t.sortOrder?"↑":"↓"})]}),(0,r.jsxs)(ew,{children:["Folhas","sheets"===t.sortBy&&r.jsx("span",{className:"ml-1 text-xs",children:"asc"===t.sortOrder?"↑":"↓"})]}),(0,r.jsxs)(ew,{children:["Criado","createdAt"===t.sortBy&&r.jsx("span",{className:"ml-1 text-xs",children:"asc"===t.sortOrder?"↑":"↓"})]}),(0,r.jsxs)(ew,{children:["Modificado","updatedAt"===t.sortBy&&r.jsx("span",{className:"ml-1 text-xs",children:"asc"===t.sortOrder?"↑":"↓"})]}),r.jsx(ew,{className:"text-right",children:"A\xe7\xf5es"})]})}),r.jsx(eb,{children:i.map(e=>(0,r.jsxs)(ey,{className:"cursor-pointer hover:bg-muted/50",children:[(0,r.jsxs)(ek,{className:"font-medium flex items-center gap-2",onClick:()=>y(e.id),children:[r.jsx(N.Z,{className:"h-4 w-4 text-primary"}),e.name]}),r.jsx(ek,{onClick:()=>y(e.id),children:(0,r.jsxs)(es.C,{variant:"outline",className:"text-xs",children:["number"==typeof e.sheets?e.sheets:e.sheets.length," ",("number"==typeof e.sheets?e.sheets:e.sheets.length)>1?"folhas":"folha"]})}),r.jsx(ek,{onClick:()=>y(e.id),children:(0,r.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[r.jsx(W.Z,{className:"h-3 w-3 mr-1"}),g(e.createdAt)]})}),r.jsx(ek,{onClick:()=>y(e.id),children:(0,r.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[r.jsx(Z.Z,{className:"h-3 w-3 mr-1"}),g(e.updatedAt)]})}),r.jsx(ek,{className:"text-right",children:(0,r.jsxs)(en.h_,{children:[r.jsx(en.$F,{asChild:!0,children:r.jsx(eg.Kk,{variant:"ghost",size:"icon",actionId:e.id,onAction:()=>{},children:r.jsx(em.Z,{className:"h-4 w-4"})})}),(0,r.jsxs)(en.AW,{align:"end",children:[r.jsx(en.Ju,{children:"Op\xe7\xf5es"}),r.jsx(en.VD,{}),(0,r.jsxs)(en.Xi,{onClick:()=>y(e.id),children:[r.jsx(eh,{className:"h-4 w-4 mr-2"}),"Editar"]}),(0,r.jsxs)(en.Xi,{onClick:()=>w(e.id),children:[r.jsx(V.Z,{className:"h-4 w-4 mr-2"}),"Duplicar"]}),r.jsx(en.VD,{}),r.jsx(en.Xi,{className:"text-destructive focus:text-destructive",onClick:()=>C(e.id),disabled:c===e.id,children:c===e.id?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"h-4 w-4 mr-2 rounded-full border-2 border-destructive/20 border-t-destructive animate-spin"}),"Excluindo..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(ep,{className:"h-4 w-4 mr-2"}),"Excluir"]})})]})]})})]},e.id))})]}),x.total>1&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["P\xe1gina ",x.current+1," de ",x.total]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(U.Button,{variant:"outline",size:"sm",onClick:()=>{if(x.current>0){let e=x.current-1;f(t=>({...t,current:e})),j(e)}},disabled:0===x.current,children:"Anterior"}),r.jsx(U.Button,{variant:"outline",size:"sm",onClick:()=>{if(x.hasMore){let e=x.current+1;f(t=>({...t,current:e})),j(e)}},disabled:!x.hasMore,children:"Pr\xf3xima"})]})]})]})}var eP=a(41137),eS=a(941);let eA=(0,F.Z)("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]),eD=(0,F.Z)("ArrowDownWideNarrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]]),eE=(0,F.Z)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var eZ=a(41190),eW=a(44794),e_=a(29280),ez=a(51027);function eO({filters:e,onFiltersChange:t,onReset:a}){let[s,n]=(0,T.useState)(!1),i=(a,r)=>{t({...e,[a]:r})},o=Object.entries(e).filter(([e,t])=>("sortBy"!==e||"updatedAt"!==t)&&("sortOrder"!==e||"desc"!==t)&&("dateRange"!==e||"all"!==t)&&null!=t&&""!==t).length;return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(ez.J2,{open:s,onOpenChange:n,children:[r.jsx(ez.xo,{asChild:!0,children:(0,r.jsxs)(U.Button,{variant:"outline",size:"sm",className:"relative",children:[r.jsx(eP.Z,{className:"h-4 w-4 mr-2"}),"Filtros",o>0&&r.jsx(es.C,{variant:"secondary",className:"ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs",children:o}),r.jsx(eS.Z,{className:"h-3 w-3 ml-1"})]})}),r.jsx(ez.yk,{className:"w-80",align:"start",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h4",{className:"font-medium",children:"Filtros Avan\xe7ados"}),o>0&&(0,r.jsxs)(U.Button,{variant:"ghost",size:"sm",onClick:()=>{a(),n(!1)},children:[r.jsx(E.Z,{className:"h-3 w-3 mr-1"}),"Limpar"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(eW._,{className:"text-sm font-medium",children:"Ordenar por"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(e_.Ph,{value:e.sortBy||"updatedAt",onValueChange:e=>i("sortBy",e),children:[r.jsx(e_.i4,{className:"flex-1",children:r.jsx(e_.ki,{})}),(0,r.jsxs)(e_.Bw,{children:[r.jsx(e_.Ql,{value:"name",children:"Nome"}),r.jsx(e_.Ql,{value:"createdAt",children:"Data de Cria\xe7\xe3o"}),r.jsx(e_.Ql,{value:"updatedAt",children:"\xdaltima Modifica\xe7\xe3o"}),r.jsx(e_.Ql,{value:"sheets",children:"N\xfamero de Folhas"})]})]}),r.jsx(U.Button,{variant:"outline",size:"sm",onClick:()=>i("sortOrder","asc"===e.sortOrder?"desc":"asc"),children:"asc"===e.sortOrder?r.jsx(eA,{className:"h-4 w-4"}):r.jsx(eD,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(eW._,{className:"text-sm font-medium flex items-center gap-1",children:[r.jsx(W.Z,{className:"h-3 w-3"}),"Per\xedodo"]}),(0,r.jsxs)(e_.Ph,{value:e.dateRange||"all",onValueChange:e=>i("dateRange",e),children:[r.jsx(e_.i4,{children:r.jsx(e_.ki,{})}),(0,r.jsxs)(e_.Bw,{children:[r.jsx(e_.Ql,{value:"all",children:"Todos os per\xedodos"}),r.jsx(e_.Ql,{value:"today",children:"Hoje"}),r.jsx(e_.Ql,{value:"week",children:"\xdaltima semana"}),r.jsx(e_.Ql,{value:"month",children:"\xdaltimo m\xeas"}),r.jsx(e_.Ql,{value:"year",children:"\xdaltimo ano"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(eW._,{className:"text-sm font-medium flex items-center gap-1",children:[r.jsx(eE,{className:"h-3 w-3"}),"N\xfamero de Folhas"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx(eW._,{className:"text-xs text-muted-foreground",children:"M\xednimo"}),r.jsx(eZ.I,{type:"number",placeholder:"0",min:"0",value:e.minSheets||"",onChange:e=>i("minSheets",e.target.value?parseInt(e.target.value):void 0)})]}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx(eW._,{className:"text-xs text-muted-foreground",children:"M\xe1ximo"}),r.jsx(eZ.I,{type:"number",placeholder:"∞",min:"0",value:e.maxSheets||"",onChange:e=>i("maxSheets",e.target.value?parseInt(e.target.value):void 0)})]})]})]}),r.jsx("div",{className:"flex gap-2 pt-2",children:r.jsx(U.Button,{size:"sm",className:"flex-1",onClick:()=>n(!1),children:"Aplicar Filtros"})})]})})]}),o>0&&(0,r.jsxs)("div",{className:"flex gap-1 flex-wrap",children:[e.sortBy&&"updatedAt"!==e.sortBy&&(0,r.jsxs)(es.C,{variant:"secondary",className:"text-xs",children:["Ordenar: ","name"===e.sortBy?"Nome":"createdAt"===e.sortBy?"Cria\xe7\xe3o":"sheets"===e.sortBy?"Folhas":"Modifica\xe7\xe3o",r.jsx(U.Button,{variant:"ghost",size:"sm",className:"h-3 w-3 p-0 ml-1",onClick:()=>i("sortBy","updatedAt"),children:r.jsx(E.Z,{className:"h-2 w-2"})})]}),e.dateRange&&"all"!==e.dateRange&&(0,r.jsxs)(es.C,{variant:"secondary",className:"text-xs",children:["today"===e.dateRange?"Hoje":"week"===e.dateRange?"Semana":"month"===e.dateRange?"M\xeas":"Ano",r.jsx(U.Button,{variant:"ghost",size:"sm",className:"h-3 w-3 p-0 ml-1",onClick:()=>i("dateRange","all"),children:r.jsx(E.Z,{className:"h-2 w-2"})})]}),(e.minSheets||e.maxSheets)&&(0,r.jsxs)(es.C,{variant:"secondary",className:"text-xs",children:["Folhas: ",e.minSheets||0,"-",e.maxSheets||"∞",r.jsx(U.Button,{variant:"ghost",size:"sm",className:"h-3 w-3 p-0 ml-1",onClick:()=>{i("minSheets",void 0),i("maxSheets",void 0)},children:r.jsx(E.Z,{className:"h-2 w-2"})})]})]})]})}var eR=a(50384);function eT({workbookId:e,onComplete:t,allowDuplicate:a=!0,allowDelete:s=!0,buttonVariant:n="ghost",buttonSize:i="icon",onlyEdit:o=!1}){let l=(0,O.useRouter)(),{fetchWithCSRF:d}=(0,ex.useFetchWithCSRF)(),[c,u]=(0,T.useState)(!1),m=a=>{a&&a.stopPropagation(),l.push(`/workbook/${e}`),t&&t()},h=async a=>{a&&a.stopPropagation();try{if(!(await d(`/api/workbooks/${e}/duplicate`,{method:"POST",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao duplicar planilha");B.toast.success("Planilha duplicada com sucesso!"),t&&t()}catch(e){console.error("Erro ao duplicar workbook:",e),B.toast.error("N\xe3o foi poss\xedvel duplicar a planilha")}},p=async a=>{a&&a.stopPropagation();try{if(u(!0),!(await d(`/api/workbooks/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao excluir planilha");B.toast.success("Planilha exclu\xedda com sucesso!"),t&&t()}catch(e){console.error("Erro ao excluir workbook:",e),B.toast.error("N\xe3o foi poss\xedvel excluir a planilha")}finally{u(!1)}};return o?(0,r.jsxs)(U.Button,{variant:n,size:i,onClick:m,"aria-label":"Editar planilha",children:[r.jsx(eh,{className:"h-4 w-4 mr-2"}),"Editar"]}):(0,r.jsxs)(en.h_,{children:[r.jsx(en.$F,{asChild:!0,onClick:e=>e.stopPropagation(),children:r.jsx(U.Button,{variant:n,size:i,"aria-label":"Op\xe7\xf5es da planilha",children:r.jsx(em.Z,{className:"h-4 w-4"})})}),(0,r.jsxs)(en.AW,{align:"end",children:[r.jsx(en.Ju,{children:"Op\xe7\xf5es"}),r.jsx(en.VD,{}),(0,r.jsxs)(en.Xi,{onClick:e=>m(e),children:[r.jsx(eh,{className:"h-4 w-4 mr-2"}),"Editar"]}),a&&(0,r.jsxs)(en.Xi,{onClick:e=>h(e),children:[r.jsx(V.Z,{className:"h-4 w-4 mr-2"}),"Duplicar"]}),s&&(0,r.jsxs)(r.Fragment,{children:[r.jsx(en.VD,{}),(0,r.jsxs)(en.Xi,{className:"text-destructive focus:text-destructive",onClick:e=>p(e),disabled:c,children:[r.jsx(ep,{className:"h-4 w-4 mr-2"}),c?"Excluindo...":"Excluir"]})]})]})]})}var eB=a(75982),eF=a(82015);let eq={headers:["A","B","C","D","E"],rows:[["","","","",""],["","","","",""],["","","","",""],["","","","",""],["","","","",""]],charts:[],name:"Nova Planilha"},eI=[{title:"Planilha de Finan\xe7as",description:"Crie uma planilha para controle de gastos mensais com categorias",icon:r.jsx(N.Z,{className:"h-6 w-6 text-green-500"}),command:"Criar planilha de controle financeiro pessoal com categorias de gastos e receitas e balan\xe7o mensal"},{title:"Controle de Tarefas",description:"Organize suas tarefas com datas, status e prioridades",icon:r.jsx(N.Z,{className:"h-6 w-6 text-blue-500"}),command:"Criar planilha de gerenciamento de tarefas com datas, status, respons\xe1veis e prioridades"},{title:"Dashboard de Vendas",description:"Visualize dados de vendas com gr\xe1ficos e tabelas din\xe2micas",icon:r.jsx(C.Z,{className:"h-6 w-6 text-indigo-500"}),command:"Criar dashboard de vendas com tabela de dados, gr\xe1fico de barras para vendas mensais e gr\xe1fico de pizza para produtos"}];function eV(){let e=(0,O.useRouter)(),{data:t,status:a}=(0,R.useSession)(),[s,n]=(0,T.useState)(""),[i,o]=(0,T.useState)(!1),[l,d]=(0,T.useState)(""),[c,u]=(0,T.useState)(""),[m,h]=(0,T.useState)(""),[p,x]=(0,T.useState)(!1);(0,O.useSearchParams)();let{csrfToken:f}=(0,ex.useCSRF)(),[g,j]=(0,T.useState)([]),[b,y]=(0,T.useState)([]),[w,C]=(0,T.useState)(!1),[_,F]=(0,T.useState)(null),[q,I]=(0,T.useState)({}),V=(0,T.useRef)({}),[X,L]=(0,T.useState)({current:0,total:1,limit:10,hasMore:!1}),[H,$]=(0,T.useState)(""),[Q,ee]=(0,T.useState)({sortBy:"updatedAt",sortOrder:"desc",dateRange:"all"}),et=()=>{o(!0)},ea=async()=>{try{if(x(!0),!l&&!m){B.toast.error("Por favor, forne\xe7a um nome ou um comando para a planilha"),x(!1);return}let t=await fetch("/api/workbooks",{method:"POST",headers:{"Content-Type":"application/json",...f?{"x-csrf-token":f}:{}},body:JSON.stringify({name:l||"Nova Planilha",description:c,aiCommand:m||null,initialData:eq})});if(!t.ok)throw Error("Erro ao criar planilha");let a=await t.json();B.toast.success("Planilha criada com sucesso"),m?e.push(`/workbook/${a.workbook.id}?command=${encodeURIComponent(m)}`):e.push(`/workbook/${a.workbook.id}`)}catch{B.toast.error("N\xe3o foi poss\xedvel criar a planilha"),x(!1)}},er=e=>{h(e),o(!0)},es=(0,T.useCallback)(async(e=0)=>{try{if(!t?.user)return;V.current.recentWorkbooks&&V.current.recentWorkbooks.abort();let a=new AbortController;if(V.current.recentWorkbooks=a,!f)throw Error("Token CSRF n\xe3o dispon\xedvel");let r=await fetch(`/api/workbooks/recent?page=${e}&limit=${X.limit}`,{headers:{"Content-Type":"application/json",...f?{"x-csrf-token":f}:{}},signal:a.signal});if(delete V.current.recentWorkbooks,!r.ok){let e=await r.json();I(t=>({...t,recentWorkbooks:e.details||e.error||"Erro ao carregar planilhas recentes"}));return}let s=await r.json();s.pagination&&L({current:s.pagination.page,total:s.pagination.totalPages,limit:s.pagination.limit,hasMore:s.pagination.hasMore});let n=s.workbooks.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),lastAccessedAt:new Date(e.lastAccessedAt||e.updatedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));j(n),I(e=>({...e,recentWorkbooks:""}))}catch(t){if(t instanceof DOMException&&"AbortError"===t.name)return;console.error("Erro ao carregar planilhas recentes:",t);let e=t instanceof Error?`Falha ao carregar planilhas: ${t.message}`:"Erro ao carregar planilhas recentes";B.toast.error(e),I(t=>({...t,recentWorkbooks:e}))}},[t,f,X.limit]),en=()=>{X.hasMore&&es(X.current+1)},ei=()=>{X.current>0&&es(X.current-1)},eo=(0,T.useCallback)(async()=>{try{if(!t?.user)return;C(!0),V.current.sharedWorkbooks&&V.current.sharedWorkbooks.abort();let e=new AbortController;V.current.sharedWorkbooks=e;let a=await fetch("/api/workbooks/shared",{headers:{"Content-Type":"application/json",...f?{"x-csrf-token":f}:{}},signal:e.signal});if(delete V.current.sharedWorkbooks,!a.ok){let e=await a.json();I(t=>({...t,sharedWorkbooks:e.details||e.error||"Erro ao carregar planilhas compartilhadas"}));return}let r=(await a.json()).workbooks.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),sharedAt:new Date(e.sharedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));y(r),I(e=>({...e,sharedWorkbooks:""}))}catch(t){if(t instanceof DOMException&&"AbortError"===t.name)return;console.error("Erro ao carregar planilhas compartilhadas:",t);let e=t instanceof Error?`Falha ao carregar planilhas compartilhadas: ${t.message}`:"Erro ao carregar planilhas compartilhadas";B.toast.error(e),I(t=>({...t,sharedWorkbooks:e}))}finally{C(!1)}},[t,f]),el=e=>v(e,{addSuffix:!0,locale:k}),ed=t=>{e.push(`/workbook/${t}`)};return"loading"===a?r.jsx("div",{className:"h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[r.jsx(S.Z,{className:"h-10 w-10 animate-spin text-primary"}),r.jsx("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):"unauthenticated"===a?null:i?(0,r.jsxs)("main",{className:"container mx-auto py-6 px-4 max-w-3xl",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Criar Nova Planilha"}),r.jsx(U.Button,{variant:"ghost",onClick:()=>o(!1),children:"Voltar"})]}),(0,r.jsxs)(Y.Zb,{className:"mb-6",children:[(0,r.jsxs)(Y.Ol,{children:[r.jsx(Y.ll,{className:"text-xl",children:"Informa\xe7\xf5es B\xe1sicas"}),r.jsx(Y.SZ,{children:"Defina os detalhes da sua planilha"})]}),(0,r.jsxs)(Y.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(eW._,{htmlFor:"name",children:"Nome da Planilha"}),r.jsx(eZ.Z,{id:"name",placeholder:"Ex: Controle Financeiro 2023",value:l,onChange:e=>d(e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(eW._,{htmlFor:"description",children:"Descri\xe7\xe3o (opcional)"}),r.jsx(eF.Z,{id:"description",placeholder:"Descreva o prop\xf3sito desta planilha...",rows:2,value:c,onChange:e=>u(e.target.value)})]})]})]}),(0,r.jsxs)(Y.Zb,{className:"mb-6",children:[(0,r.jsxs)(Y.Ol,{children:[(0,r.jsxs)(Y.ll,{className:"text-xl flex items-center gap-2",children:[r.jsx(A.Z,{className:"h-5 w-5 text-primary"}),"Assistente IA"]}),r.jsx(Y.SZ,{children:"Descreva o que voc\xea deseja criar e deixe a IA fazer o trabalho"})]}),r.jsx(Y.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(eW._,{htmlFor:"prompt",children:"Comando para IA (opcional)"}),r.jsx(eF.Z,{id:"prompt",placeholder:"Ex: Crie uma planilha de controle financeiro com categorias de gastos e gr\xe1ficos...",rows:3,value:m,onChange:e=>h(e.target.value)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Descreva em linguagem natural o que voc\xea quer criar. Quanto mais detalhes, melhor ser\xe1 o resultado."})]})}),(0,r.jsxs)(Y.eW,{className:"flex justify-between items-center",children:[r.jsx(U.Button,{variant:"outline",onClick:()=>h(""),disabled:!m,children:"Limpar"}),r.jsx(U.Button,{onClick:()=>ea(),disabled:p,className:"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",children:p?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Criando..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(A.Z,{className:"mr-2 h-4 w-4"}),"Criar Nova Planilha"]})})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"Dica: voc\xea tamb\xe9m pode usar templates pr\xe9-definidos"}),r.jsx(eB.WorkbookTemplatesServer,{})]})]}):(0,r.jsxs)("main",{className:"container mx-auto py-6 px-4 max-w-7xl",children:[r.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-6 mb-8 shadow-sm",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gradient bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400",children:[s,", ",t?.user?.name?.split(" ")[0]||"Bem-vindo","!"]}),r.jsx("p",{className:"text-muted-foreground mt-2 max-w-xl",children:"Pronto para turbinar suas planilhas? Use o Excel Copilot com IA para criar, editar e analisar dados de forma inteligente."})]}),r.jsx("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(U.Button,{size:"lg",onClick:()=>et(),className:"mt-4 md:mt-0 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-all duration-300 hover:shadow-lg",children:[r.jsx(A.Z,{className:"w-5 h-5 mr-2"}),"Criar Nova Planilha"]})})]})}),(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h2",{className:"text-xl font-medium mb-4",children:"Criar Rapidamente"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:eI.map((e,t)=>(0,r.jsxs)(Y.Zb,{className:"cursor-pointer hover:shadow-md transition-all border-2 hover:border-primary/20",onClick:()=>er(e.command),children:[r.jsx(Y.Ol,{className:"pb-2",children:(0,r.jsxs)(Y.ll,{className:"text-lg flex items-center gap-2",children:[e.icon,e.title]})}),r.jsx(Y.aY,{children:r.jsx(Y.SZ,{children:e.description})})]},t))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx(ec,{activities:[],isLoading:!1,maxItems:8})}),(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx(J,{onCreateWorkbook:()=>o(!0),recentWorkbooks:g}),r.jsx(G,{}),r.jsx(K,{recentWorkbooks:g})]})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"mb-6 space-y-4",children:(0,r.jsxs)("div",{className:"flex gap-4 items-start",children:[(0,r.jsxs)("div",{className:"relative flex-1 max-w-md",children:[r.jsx(D.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),r.jsx(eZ.Z,{placeholder:"Buscar planilhas...",value:H,onChange:e=>$(e.target.value),className:"pl-10 pr-10"}),H&&r.jsx(U.Button,{variant:"ghost",size:"sm",onClick:()=>$(""),className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",children:r.jsx(E.Z,{className:"h-3 w-3"})})]}),r.jsx(eO,{filters:Q,onFiltersChange:ee,onReset:()=>ee({sortBy:"updatedAt",sortOrder:"desc",dateRange:"all"})})]})}),(0,r.jsxs)(eR.mQ,{defaultValue:"all",className:"w-full",children:[r.jsx("div",{className:"flex justify-between items-center mb-4",children:(0,r.jsxs)(eR.dr,{children:[r.jsx(eR.SP,{value:"all",children:"Todas as Planilhas"}),r.jsx(eR.SP,{value:"recent",children:"Recentes"}),r.jsx(eR.SP,{value:"shared",children:"Compartilhadas"})]})}),r.jsx(eR.nU,{value:"all",className:"mt-0",children:r.jsx(eM,{searchQuery:H,filters:Q,onFiltersChange:ee})}),(0,r.jsxs)(eR.nU,{value:"recent",className:"mt-0",children:[q.recentWorkbooks?r.jsx("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4",children:(0,r.jsxs)("div",{className:"flex",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Erro ao carregar planilhas recentes"}),r.jsx("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:r.jsx("p",{children:q.recentWorkbooks})}),r.jsx("div",{className:"mt-4",children:r.jsx(U.Button,{size:"sm",variant:"outline",onClick:()=>es(X.current),className:"text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40",children:"Tentar novamente"})})]})]})}):null,g.length>0?(0,r.jsxs)("div",{className:"border rounded-md",children:[(0,r.jsxs)(ej,{children:[r.jsx(ev,{children:(0,r.jsxs)(ey,{children:[r.jsx(ew,{children:"Nome"}),r.jsx(ew,{children:"\xdaltimo acesso"}),r.jsx(ew,{className:"text-right",children:"A\xe7\xf5es"})]})}),r.jsx(eb,{children:g.map(e=>(0,r.jsxs)(ey,{className:"cursor-pointer hover:bg-muted/40",onClick:()=>ed(e.id),children:[(0,r.jsxs)(ek,{className:"font-medium flex items-center gap-2",children:[r.jsx(N.Z,{className:"h-4 w-4 text-blue-500"}),e.name]}),r.jsx(ek,{className:"text-muted-foreground",children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(Z.Z,{className:"h-3 w-3"}),el(e.lastAccessedAt||e.updatedAt)]})}),r.jsx(ek,{className:"text-right",children:r.jsx(eT,{workbookId:e.id,onComplete:es,buttonVariant:"ghost",buttonSize:"icon"})})]},e.id))})]}),r.jsx(()=>(0,r.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["P\xe1gina ",X.current+1," de ",Math.max(1,X.total)]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(U.Button,{variant:"outline",size:"sm",onClick:ei,disabled:X.current<=0,children:[r.jsx(M.Z,{className:"h-4 w-4 mr-1"})," Anterior"]}),(0,r.jsxs)(U.Button,{variant:"outline",size:"sm",onClick:en,disabled:!X.hasMore,children:["Pr\xf3xima ",r.jsx(P.Z,{className:"h-4 w-4 ml-1"})]})]})]}),{})]}):r.jsx(ef,{icon:r.jsx(N.Z,{className:"h-10 w-10"}),title:"Nenhuma planilha recente",description:"As planilhas que voc\xea acessou recentemente aparecer\xe3o aqui."})]}),(0,r.jsxs)(eR.nU,{value:"shared",className:"mt-0",children:[q.sharedWorkbooks?r.jsx("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4",children:(0,r.jsxs)("div",{className:"flex",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Erro ao carregar planilhas compartilhadas"}),r.jsx("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:r.jsx("p",{children:q.sharedWorkbooks})}),r.jsx("div",{className:"mt-4",children:r.jsx(U.Button,{size:"sm",variant:"outline",onClick:eo,className:"text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40",children:"Tentar novamente"})})]})]})}):null,w?r.jsx("div",{className:"w-full py-10 flex justify-center",children:r.jsx("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"})}):b.length>0?r.jsx("div",{className:"border rounded-md",children:(0,r.jsxs)(ej,{children:[r.jsx(ev,{children:(0,r.jsxs)(ey,{children:[r.jsx(ew,{children:"Nome"}),r.jsx(ew,{children:"Compartilhado por"}),r.jsx(ew,{children:"Data de compartilhamento"}),r.jsx(ew,{className:"text-right",children:"A\xe7\xf5es"})]})}),r.jsx(eb,{children:b.map(e=>(0,r.jsxs)(ey,{className:"cursor-pointer hover:bg-muted/40",onClick:()=>ed(e.id),children:[(0,r.jsxs)(ek,{className:"font-medium flex items-center gap-2",children:[r.jsx(N.Z,{className:"h-4 w-4 text-blue-500"}),e.name]}),r.jsx(ek,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.sharedBy.image?r.jsx(z(),{src:e.sharedBy.image,alt:e.sharedBy.name||"Usu\xe1rio",width:24,height:24,className:"h-6 w-6 rounded-full"}):r.jsx("div",{className:"h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs",children:e.sharedBy.name?.charAt(0)||"?"}),r.jsx("span",{children:e.sharedBy.name||e.sharedBy.email})]})}),r.jsx(ek,{className:"text-muted-foreground",children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(W.Z,{className:"h-3 w-3"}),el(e.sharedAt)]})}),r.jsx(ek,{className:"text-right",children:r.jsx(eT,{workbookId:e.id,onComplete:eo,onlyEdit:!0,buttonVariant:"ghost",buttonSize:"sm",allowDelete:!1,allowDuplicate:!1})})]},e.id))})]})}):r.jsx(ef,{icon:r.jsx(N.Z,{className:"h-10 w-10"}),title:"Nenhuma planilha compartilhada",description:"As planilhas compartilhadas com voc\xea aparecer\xe3o aqui."})]})]})]})]})}},82015:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var r=a(10326),s=a(17577),n=a(62734);let i=s.forwardRef(({className:e,wrapperClassName:t,variant:a="default",fieldSize:s="md",textareaSize:i,...o},l)=>{let d=r.jsx("textarea",{className:(0,n.RM)(a,i||s,!0,e),ref:l,...o});return(0,n.aF)(d,t)});i.displayName="Textarea";let o=i},38256:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var r=a(68570);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\dashboard\page.tsx`),{__esModule:n,$$typeof:i}=s;s.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\dashboard\page.tsx#default`)},74964:(e,t,a)=>{"use strict";a.d(t,{VY:()=>L,fC:()=>I,h_:()=>X,xz:()=>V});var r=a(17577),s=a(82561),n=a(48051),i=a(93095),o=a(825),l=a(80699),d=a(10441),c=a(88957),u=a(17103),m=a(83078),h=a(9815),p=a(45226),x=a(34214),f=a(52067),g=a(35664),j=a(58260),v=a(10326),b="Popover",[y,w]=(0,i.b)(b,[u.D7]),k=(0,u.D7)(),[N,C]=y(b),M=e=>{let{__scopePopover:t,children:a,open:s,defaultOpen:n,onOpenChange:i,modal:o=!1}=e,l=k(t),d=r.useRef(null),[m,h]=r.useState(!1),[p,x]=(0,f.T)({prop:s,defaultProp:n??!1,onChange:i,caller:b});return(0,v.jsx)(u.fC,{...l,children:(0,v.jsx)(N,{scope:t,contentId:(0,c.M)(),triggerRef:d,open:p,onOpenChange:x,onOpenToggle:r.useCallback(()=>x(e=>!e),[x]),hasCustomAnchor:m,onCustomAnchorAdd:r.useCallback(()=>h(!0),[]),onCustomAnchorRemove:r.useCallback(()=>h(!1),[]),modal:o,children:a})})};M.displayName=b;var P="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:a,...s}=e,n=C(P,a),i=k(a),{onCustomAnchorAdd:o,onCustomAnchorRemove:l}=n;return r.useEffect(()=>(o(),()=>l()),[o,l]),(0,v.jsx)(u.ee,{...i,...s,ref:t})}).displayName=P;var S="PopoverTrigger",A=r.forwardRef((e,t)=>{let{__scopePopover:a,...r}=e,i=C(S,a),o=k(a),l=(0,n.e)(t,i.triggerRef),d=(0,v.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":q(i.open),...r,ref:l,onClick:(0,s.M)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?d:(0,v.jsx)(u.ee,{asChild:!0,...o,children:d})});A.displayName=S;var D="PopoverPortal",[E,Z]=y(D,{forceMount:void 0}),W=e=>{let{__scopePopover:t,forceMount:a,children:r,container:s}=e,n=C(D,t);return(0,v.jsx)(E,{scope:t,forceMount:a,children:(0,v.jsx)(h.z,{present:a||n.open,children:(0,v.jsx)(m.h,{asChild:!0,container:s,children:r})})})};W.displayName=D;var _="PopoverContent",z=r.forwardRef((e,t)=>{let a=Z(_,e.__scopePopover),{forceMount:r=a.forceMount,...s}=e,n=C(_,e.__scopePopover);return(0,v.jsx)(h.z,{present:r||n.open,children:n.modal?(0,v.jsx)(R,{...s,ref:t}):(0,v.jsx)(T,{...s,ref:t})})});z.displayName=_;var O=(0,x.Z8)("PopoverContent.RemoveScroll"),R=r.forwardRef((e,t)=>{let a=C(_,e.__scopePopover),i=r.useRef(null),o=(0,n.e)(t,i),l=r.useRef(!1);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Ry)(e)},[]),(0,v.jsx)(j.Z,{as:O,allowPinchZoom:!0,children:(0,v.jsx)(B,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||a.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey,r=2===t.button||a;l.current=r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,s.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),T=r.forwardRef((e,t)=>{let a=C(_,e.__scopePopover),s=r.useRef(!1),n=r.useRef(!1);return(0,v.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(s.current||a.triggerRef.current?.focus(),t.preventDefault()),s.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(s.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let r=t.target;a.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopePopover:a,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:n,disableOutsidePointerEvents:i,onEscapeKeyDown:c,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:p,...x}=e,f=C(_,a),g=k(a);return(0,l.EW)(),(0,v.jsx)(d.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:n,children:(0,v.jsx)(o.XB,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:p,onEscapeKeyDown:c,onPointerDownOutside:m,onFocusOutside:h,onDismiss:()=>f.onOpenChange(!1),children:(0,v.jsx)(u.VY,{"data-state":q(f.open),role:"dialog",id:f.contentId,...g,...x,ref:t,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),F="PopoverClose";function q(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:a,...r}=e,n=C(F,a);return(0,v.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,s.M)(e.onClick,()=>n.onOpenChange(!1))})}).displayName=F,r.forwardRef((e,t)=>{let{__scopePopover:a,...r}=e,s=k(a);return(0,v.jsx)(u.Eh,{...s,...r,ref:t})}).displayName="PopoverArrow";var I=M,V=A,X=W,L=z}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,9557,7410,86,605,5999,5108,2972,4433,736,9361,611],()=>a(340));module.exports=r})();