(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7090],{35496:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0});let{Decimal:r,objectEnumValues:n,makeStrictEnum:s,Public:o,getRuntime:a,skip:u}=i(80736),c={};t.Prisma=c,t.$Enums={},c.prismaVersion={client:"5.22.0",engine:"605197351a3c8bdd595af2d2a9bc3025bca48ea2"},c.PrismaClientKnownRequestError=()=>{let e=a().prettyName;throw Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.<PERSON>rismaClientUnknownRequestError=()=>{let e=a().prettyName;throw Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientRustPanicError=()=>{let e=a().prettyName;throw Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientInitializationError=()=>{let e=a().prettyName;throw Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientValidationError=()=>{let e=a().prettyName;throw Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.NotFoundError=()=>{let e=a().prettyName;throw Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.Decimal=r,c.sql=()=>{let e=a().prettyName;throw Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.empty=()=>{let e=a().prettyName;throw Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.join=()=>{let e=a().prettyName;throw Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.raw=()=>{let e=a().prettyName;throw Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.validator=o.validator,c.getExtensionContext=()=>{let e=a().prettyName;throw Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.defineExtension=()=>{let e=a().prettyName;throw Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.DbNull=n.instances.DbNull,c.JsonNull=n.instances.JsonNull,c.AnyNull=n.instances.AnyNull,c.NullTypes={DbNull:n.classes.DbNull,JsonNull:n.classes.JsonNull,AnyNull:n.classes.AnyNull},t.Prisma.TransactionIsolationLevel=s({ReadUncommitted:"ReadUncommitted",ReadCommitted:"ReadCommitted",RepeatableRead:"RepeatableRead",Serializable:"Serializable"}),t.Prisma.AccountScalarFieldEnum={id:"id",userId:"userId",type:"type",provider:"provider",providerAccountId:"providerAccountId",refresh_token:"refresh_token",access_token:"access_token",expires_at:"expires_at",token_type:"token_type",scope:"scope",id_token:"id_token",session_state:"session_state"},t.Prisma.SessionScalarFieldEnum={id:"id",sessionToken:"sessionToken",userId:"userId",expires:"expires"},t.Prisma.UserScalarFieldEnum={id:"id",name:"name",email:"email",emailVerified:"emailVerified",image:"image",createdAt:"createdAt",updatedAt:"updatedAt",lastIpAddress:"lastIpAddress",lastLoginAt:"lastLoginAt",loginCount:"loginCount",userAgent:"userAgent",isSuspicious:"isSuspicious",isBanned:"isBanned",banReason:"banReason",banDate:"banDate"},t.Prisma.VerificationTokenScalarFieldEnum={identifier:"identifier",token:"token",expires:"expires"},t.Prisma.WorkbookScalarFieldEnum={id:"id",name:"name",description:"description",userId:"userId",isPublic:"isPublic",createdAt:"createdAt",updatedAt:"updatedAt",lastAccessedAt:"lastAccessedAt"},t.Prisma.WorkbookShareScalarFieldEnum={id:"id",workbookId:"workbookId",sharedByUserId:"sharedByUserId",sharedWithUserId:"sharedWithUserId",permissionLevel:"permissionLevel",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.SheetScalarFieldEnum={id:"id",name:"name",workbookId:"workbookId",data:"data",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.ChatHistoryScalarFieldEnum={id:"id",userId:"userId",message:"message",response:"response",workbookId:"workbookId",createdAt:"createdAt"},t.Prisma.SubscriptionScalarFieldEnum={id:"id",userId:"userId",stripeCustomerId:"stripeCustomerId",stripeSubscriptionId:"stripeSubscriptionId",stripePriceId:"stripePriceId",status:"status",plan:"plan",cancelAtPeriodEnd:"cancelAtPeriodEnd",currentPeriodStart:"currentPeriodStart",currentPeriodEnd:"currentPeriodEnd",apiCallsLimit:"apiCallsLimit",apiCallsUsed:"apiCallsUsed",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.PaymentScalarFieldEnum={id:"id",amount:"amount",currency:"currency",stripePaymentId:"stripePaymentId",stripeInvoiceId:"stripeInvoiceId",status:"status",subscriptionId:"subscriptionId",metadata:"metadata",createdAt:"createdAt"},t.Prisma.ApiUsageScalarFieldEnum={id:"id",userId:"userId",count:"count",endpoint:"endpoint",workbookId:"workbookId",billable:"billable",createdAt:"createdAt"},t.Prisma.SecurityLogScalarFieldEnum={id:"id",userId:"userId",eventType:"eventType",details:"details",timestamp:"timestamp"},t.Prisma.UserActionLogScalarFieldEnum={id:"id",userId:"userId",action:"action",details:"details",timestamp:"timestamp"},t.Prisma.CommandFeedbackScalarFieldEnum={id:"id",commandId:"commandId",command:"command",successful:"successful",feedbackText:"feedbackText",timestamp:"timestamp"},t.Prisma.AiMetricsScalarFieldEnum={id:"id",totalCommands:"totalCommands",successfulCommands:"successfulCommands",failedCommands:"failedCommands",lastUpdated:"lastUpdated"},t.Prisma.WebVitalScalarFieldEnum={id:"id",name:"name",value:"value",rating:"rating",delta:"delta",metricId:"metricId",navigationType:"navigationType",url:"url",userAgent:"userAgent",timestamp:"timestamp",createdAt:"createdAt"},t.Prisma.TemplateScalarFieldEnum={id:"id",name:"name",title:"title",description:"description",icon:"icon",isPublic:"isPublic",isActive:"isActive",isFeatured:"isFeatured",isNew:"isNew",popularity:"popularity",usageCount:"usageCount",data:"data",createdBy:"createdBy",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.TemplateCategoryScalarFieldEnum={id:"id",name:"name",slug:"slug",description:"description",icon:"icon",color:"color",isActive:"isActive",sortOrder:"sortOrder",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.TemplateReviewScalarFieldEnum={id:"id",templateId:"templateId",userId:"userId",rating:"rating",comment:"comment",isHelpful:"isHelpful",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.TemplateUsageScalarFieldEnum={id:"id",templateId:"templateId",userId:"userId",workbookId:"workbookId",createdAt:"createdAt"},t.Prisma.SortOrder={asc:"asc",desc:"desc"},t.Prisma.QueryMode={default:"default",insensitive:"insensitive"},t.Prisma.NullsOrder={first:"first",last:"last"},t.Prisma.ModelName={Account:"Account",Session:"Session",User:"User",VerificationToken:"VerificationToken",Workbook:"Workbook",WorkbookShare:"WorkbookShare",Sheet:"Sheet",ChatHistory:"ChatHistory",Subscription:"Subscription",Payment:"Payment",ApiUsage:"ApiUsage",SecurityLog:"SecurityLog",UserActionLog:"UserActionLog",CommandFeedback:"CommandFeedback",AiMetrics:"AiMetrics",WebVital:"WebVital",Template:"Template",TemplateCategory:"TemplateCategory",TemplateReview:"TemplateReview",TemplateUsage:"TemplateUsage"};class d{constructor(){return new Proxy(this,{get(e,t){let i=a();throw Error((i.isEdge?`PrismaClient is not configured to run in ${i.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`:"PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `"+i.prettyName+"`).")+`
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`)}})}}t.PrismaClient=d,Object.assign(t,c)},17090:function(e,t,i){let r=i(35496);e.exports=r},80736:function(e){"use strict";var t=Object.defineProperty,i=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,s=(e,i)=>{for(var r in i)t(e,r,{get:i[r],enumerable:!0})},o={};s(o,{Decimal:()=>e0,Public:()=>a,getRuntime:()=>y,makeStrictEnum:()=>v,objectEnumValues:()=>g}),e.exports=((e,s,o,a)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let u of r(s))n.call(e,u)||u===o||t(e,u,{get:()=>s[u],enumerable:!(a=i(s,u))||a.enumerable});return e})(t({},"__esModule",{value:!0}),o);var a={};function u(...e){return e=>e}s(a,{validator:()=>u});var c=Symbol(),d=new WeakMap,l=class{constructor(e){e===c?d.set(this,"Prisma.".concat(this._getName())):d.set(this,"new Prisma.".concat(this._getNamespace(),".").concat(this._getName(),"()"))}_getName(){return this.constructor.name}toString(){return d.get(this)}},h=class extends l{_getNamespace(){return"NullTypes"}},p=class extends h{};w(p,"DbNull");var f=class extends h{};w(f,"JsonNull");var m=class extends h{};w(m,"AnyNull");var g={classes:{DbNull:p,JsonNull:f,AnyNull:m},instances:{DbNull:new p(c),JsonNull:new f(c),AnyNull:new m(c)}};function w(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var b=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function v(e){return new Proxy(e,{get(e,t){if(t in e)return e[t];if(!b.has(t))throw TypeError("Invalid enum value: ".concat(String(t)))}})}var N={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function y(){var e,t,i;let r="object"==typeof Netlify?"netlify":"string"==typeof EdgeRuntime?"edge-light":(null==(e=globalThis.navigator)?void 0:e.userAgent)==="Cloudflare-Workers"?"workerd":globalThis.Deno?"deno":globalThis.__lagon__?"lagon":(null==(i=null==(t=globalThis.process)?void 0:t.release)?void 0:i.name)==="node"?"node":globalThis.Bun?"bun":globalThis.fastly?"fastly":"unknown";return{id:r,prettyName:N[r]||r,isEdge:["workerd","deno","netlify","edge-light"].includes(r)}}var E,A,x="0123456789abcdef",P="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",I="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",k={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},S=!0,C="[DecimalError] ",F=C+"Invalid argument: ",T=C+"Precision limit exceeded",M=C+"crypto unavailable",_="[object Decimal]",O=Math.floor,R=Math.pow,U=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,D=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,q=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,L=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Z=P.length-1,$=I.length-1,j={toStringTag:_};function V(e){var t,i,r,n=e.length-1,s="",o=e[0];if(n>0){for(s+=o,t=1;t<n;t++)(i=7-(r=e[t]+"").length)&&(s+=ee(i)),s+=r;(i=7-(r=(o=e[t])+"").length)&&(s+=ee(i))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}function H(e,t,i){if(e!==~~e||e<t||e>i)throw Error(F+e)}function W(e,t,i,r){var n,s,o,a;for(s=e[0];s>=10;s/=10)--t;return--t<0?(t+=7,n=0):(n=Math.ceil((t+1)/7),t%=7),s=R(10,7-t),a=e[n]%s|0,null==r?t<3?(0==t?a=a/100|0:1==t&&(a=a/10|0),o=i<4&&99999==a||i>3&&49999==a||5e4==a||0==a):o=(i<4&&a+1==s||i>3&&a+1==s/2)&&(e[n+1]/s/100|0)==R(10,t-2)-1||(a==s/2||0==a)&&(e[n+1]/s/100|0)==0:t<4?(0==t?a=a/1e3|0:1==t?a=a/100|0:2==t&&(a=a/10|0),o=(r||i<4)&&9999==a||!r&&i>3&&4999==a):o=((r||i<4)&&a+1==s||!r&&i>3&&a+1==s/2)&&(e[n+1]/s/1e3|0)==R(10,t-3)-1,o}function B(e,t,i){for(var r,n,s=[0],o=0,a=e.length;o<a;){for(n=s.length;n--;)s[n]*=t;for(s[0]+=x.indexOf(e.charAt(o++)),r=0;r<s.length;r++)s[r]>i-1&&(void 0===s[r+1]&&(s[r+1]=0),s[r+1]+=s[r]/i|0,s[r]%=i)}return s.reverse()}j.absoluteValue=j.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),z(e)},j.ceil=function(){return z(new this.constructor(this),this.e+1,2)},j.clampedTo=j.clamp=function(e,t){var i=this.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(F+t);return 0>this.cmp(e)?e:this.cmp(t)>0?t:new i(this)},j.comparedTo=j.cmp=function(e){var t,i,r,n,s=this.d,o=(e=new this.constructor(e)).d,a=this.s,u=e.s;if(!s||!o)return a&&u?a!==u?a:s===o?0:!s^a<0?1:-1:NaN;if(!s[0]||!o[0])return s[0]?a:o[0]?-u:0;if(a!==u)return a;if(this.e!==e.e)return this.e>e.e^a<0?1:-1;for(r=s.length,n=o.length,t=0,i=r<n?r:n;t<i;++t)if(s[t]!==o[t])return s[t]>o[t]^a<0?1:-1;return r===n?0:r>n^a<0?1:-1},j.cosine=j.cos=function(){var e,t,i=this,r=i.constructor;return i.d?i.d[0]?(e=r.precision,t=r.rounding,r.precision=e+Math.max(i.e,i.sd())+7,r.rounding=1,i=function(e,t){var i,r,n;if(t.isZero())return t;(r=t.d.length)<32?n=(1/ec(4,i=Math.ceil(r/3))).toString():(i=16,n="2.3283064365386962890625e-10"),e.precision+=i,t=eu(e,1,t.times(n),new e(1));for(var s=i;s--;){var o=t.times(t);t=o.times(o).minus(o).times(8).plus(1)}return e.precision-=i,t}(r,ed(r,i)),r.precision=e,r.rounding=t,z(2==A||3==A?i.neg():i,e,t,!0)):new r(1):new r(NaN)},j.cubeRoot=j.cbrt=function(){var e,t,i,r,n,s,o,a,u,c,d=this.constructor;if(!this.isFinite()||this.isZero())return new d(this);for(S=!1,(s=this.s*R(this.s*this,1/3))&&Math.abs(s)!=1/0?r=new d(s.toString()):(i=V(this.d),(s=((e=this.e)-i.length+1)%3)&&(i+=1==s||-2==s?"0":"00"),s=R(i,1/3),e=O((e+1)/3)-(e%3==(e<0?-1:2)),(r=new d(i=s==1/0?"5e"+e:(i=s.toExponential()).slice(0,i.indexOf("e")+1)+e)).s=this.s),o=(e=d.precision)+3;;)if(r=J((c=(u=(a=r).times(a).times(a)).plus(this)).plus(this).times(a),c.plus(u),o+2,1),V(a.d).slice(0,o)===(i=V(r.d)).slice(0,o)){if("9999"!=(i=i.slice(o-3,o+1))&&(n||"4999"!=i)){+i&&(+i.slice(1)||"5"!=i.charAt(0))||(z(r,e+1,1),t=!r.times(r).times(r).eq(this));break}if(!n&&(z(a,e+1,0),a.times(a).times(a).eq(this))){r=a;break}o+=4,n=1}return S=!0,z(r,e,d.rounding,t)},j.decimalPlaces=j.dp=function(){var e,t=this.d,i=NaN;if(t){if(i=((e=t.length-1)-O(this.e/7))*7,e=t[e])for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i},j.dividedBy=j.div=function(e){return J(this,new this.constructor(e))},j.dividedToIntegerBy=j.divToInt=function(e){var t=this.constructor;return z(J(this,new t(e),0,1,1),t.precision,t.rounding)},j.equals=j.eq=function(e){return 0===this.cmp(e)},j.floor=function(){return z(new this.constructor(this),this.e+1,3)},j.greaterThan=j.gt=function(e){return this.cmp(e)>0},j.greaterThanOrEqualTo=j.gte=function(e){var t=this.cmp(e);return 1==t||0===t},j.hyperbolicCosine=j.cosh=function(){var e,t,i,r,n,s=this,o=s.constructor,a=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return a;i=o.precision,r=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,(n=s.d.length)<32?t=(1/ec(4,e=Math.ceil(n/3))).toString():(e=16,t="2.3283064365386962890625e-10"),s=eu(o,1,s.times(t),new o(1),!0);for(var u,c=e,d=new o(8);c--;)u=s.times(s),s=a.minus(u.times(d.minus(u.times(d))));return z(s,o.precision=i,o.rounding=r,!0)},j.hyperbolicSine=j.sinh=function(){var e,t,i,r,n=this,s=n.constructor;if(!n.isFinite()||n.isZero())return new s(n);if(t=s.precision,i=s.rounding,s.precision=t+Math.max(n.e,n.sd())+4,s.rounding=1,(r=n.d.length)<3)n=eu(s,2,n,n,!0);else{e=(e=1.4*Math.sqrt(r))>16?16:0|e,n=eu(s,2,n=n.times(1/ec(5,e)),n,!0);for(var o,a=new s(5),u=new s(16),c=new s(20);e--;)o=n.times(n),n=n.times(a.plus(o.times(u.times(o).plus(c))))}return s.precision=t,s.rounding=i,z(n,t,i,!0)},j.hyperbolicTangent=j.tanh=function(){var e,t,i=this.constructor;return this.isFinite()?this.isZero()?new i(this):(e=i.precision,t=i.rounding,i.precision=e+7,i.rounding=1,J(this.sinh(),this.cosh(),i.precision=e,i.rounding=t)):new i(this.s)},j.inverseCosine=j.acos=function(){var e,t=this,i=t.constructor,r=t.abs().cmp(1),n=i.precision,s=i.rounding;return -1!==r?0===r?t.isNeg()?X(i,n,s):new i(0):new i(NaN):t.isZero()?X(i,n+4,s).times(.5):(i.precision=n+6,i.rounding=1,t=t.asin(),e=X(i,n+4,s).times(.5),i.precision=n,i.rounding=s,e.minus(t))},j.inverseHyperbolicCosine=j.acosh=function(){var e,t,i=this,r=i.constructor;return i.lte(1)?new r(i.eq(1)?0:NaN):i.isFinite()?(e=r.precision,t=r.rounding,r.precision=e+Math.max(Math.abs(i.e),i.sd())+4,r.rounding=1,S=!1,i=i.times(i).minus(1).sqrt().plus(i),S=!0,r.precision=e,r.rounding=t,i.ln()):new r(i)},j.inverseHyperbolicSine=j.asinh=function(){var e,t,i=this,r=i.constructor;return!i.isFinite()||i.isZero()?new r(i):(e=r.precision,t=r.rounding,r.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,r.rounding=1,S=!1,i=i.times(i).plus(1).sqrt().plus(i),S=!0,r.precision=e,r.rounding=t,i.ln())},j.inverseHyperbolicTangent=j.atanh=function(){var e,t,i,r,n=this,s=n.constructor;return n.isFinite()?n.e>=0?new s(n.abs().eq(1)?n.s/0:n.isZero()?n:NaN):(e=s.precision,t=s.rounding,Math.max(r=n.sd(),e)<-(2*n.e)-1?z(new s(n),e,t,!0):(s.precision=i=r-n.e,n=J(n.plus(1),new s(1).minus(n),i+e,1),s.precision=e+4,s.rounding=1,n=n.ln(),s.precision=e,s.rounding=t,n.times(.5))):new s(NaN)},j.inverseSine=j.asin=function(){var e,t,i,r,n=this,s=n.constructor;return n.isZero()?new s(n):(t=n.abs().cmp(1),i=s.precision,r=s.rounding,-1!==t?0===t?((e=X(s,i+4,r).times(.5)).s=n.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,n=n.div(new s(1).minus(n.times(n)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=r,n.times(2)))},j.inverseTangent=j.atan=function(){var e,t,i,r,n,s,o,a,u,c=this,d=c.constructor,l=d.precision,h=d.rounding;if(c.isFinite()){if(c.isZero())return new d(c);if(c.abs().eq(1)&&l+4<=$)return(o=X(d,l+4,h).times(.25)).s=c.s,o}else{if(!c.s)return new d(NaN);if(l+4<=$)return(o=X(d,l+4,h).times(.5)).s=c.s,o}for(d.precision=a=l+10,d.rounding=1,e=i=Math.min(28,a/7+2|0);e;--e)c=c.div(c.times(c).plus(1).sqrt().plus(1));for(S=!1,t=Math.ceil(a/7),r=1,u=c.times(c),o=new d(c),n=c;-1!==e;)if(n=n.times(u),s=o.minus(n.div(r+=2)),n=n.times(u),void 0!==(o=s.plus(n.div(r+=2))).d[t])for(e=t;o.d[e]===s.d[e]&&e--;);return i&&(o=o.times(2<<i-1)),S=!0,z(o,d.precision=l,d.rounding=h,!0)},j.isFinite=function(){return!!this.d},j.isInteger=j.isInt=function(){return!!this.d&&O(this.e/7)>this.d.length-2},j.isNaN=function(){return!this.s},j.isNegative=j.isNeg=function(){return this.s<0},j.isPositive=j.isPos=function(){return this.s>0},j.isZero=function(){return!!this.d&&0===this.d[0]},j.lessThan=j.lt=function(e){return 0>this.cmp(e)},j.lessThanOrEqualTo=j.lte=function(e){return 1>this.cmp(e)},j.logarithm=j.log=function(e){var t,i,r,n,s,o,a,u=this.constructor,c=u.precision,d=u.rounding;if(null==e)e=new u(10),t=!0;else{if(i=(e=new u(e)).d,e.s<0||!i||!i[0]||e.eq(1))return new u(NaN);t=e.eq(10)}if(i=this.d,this.s<0||!i||!i[0]||this.eq(1))return new u(i&&!i[0]?-1/0:1!=this.s?NaN:i?0:1/0);if(t){if(i.length>1)n=!0;else{for(r=i[0];r%10==0;)r/=10;n=1!==r}}if(S=!1,W((a=J(es(this,o=c+5),t?G(u,o+10):es(e,o),o,1)).d,r=c,d))do if(o+=10,a=J(es(this,o),t?G(u,o+10):es(e,o),o,1),!n){+V(a.d).slice(r+1,r+15)+1==1e14&&(a=z(a,c+1,0));break}while(W(a.d,r+=10,d));return S=!0,z(a,c,d)},j.minus=j.sub=function(e){var t,i,r,n,s,o,a,u,c,d,l,h,p=this.constructor;if(e=new p(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new p(e.d||this.s!==e.s?this:NaN):e=new p(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(c=this.d,h=e.d,a=p.precision,u=p.rounding,!c[0]||!h[0]){if(h[0])e.s=-e.s;else{if(!c[0])return new p(3===u?-0:0);e=new p(this)}return S?z(e,a,u):e}if(i=O(e.e/7),d=O(this.e/7),c=c.slice(),s=d-i){for((l=s<0)?(t=c,s=-s,o=h.length):(t=h,i=d,o=c.length),s>(r=Math.max(Math.ceil(a/7),o)+2)&&(s=r,t.length=1),t.reverse(),r=s;r--;)t.push(0);t.reverse()}else{for((l=(r=c.length)<(o=h.length))&&(o=r),r=0;r<o;r++)if(c[r]!=h[r]){l=c[r]<h[r];break}s=0}for(l&&(t=c,c=h,h=t,e.s=-e.s),o=c.length,r=h.length-o;r>0;--r)c[o++]=0;for(r=h.length;r>s;){if(c[--r]<h[r]){for(n=r;n&&0===c[--n];)c[n]=1e7-1;--c[n],c[r]+=1e7}c[r]-=h[r]}for(;0===c[--o];)c.pop();for(;0===c[0];c.shift())--i;return c[0]?(e.d=c,e.e=Q(c,i),S?z(e,a,u):e):new p(3===u?-0:0)},j.modulo=j.mod=function(e){var t,i=this.constructor;return e=new i(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(S=!1,9==i.modulo?(t=J(this,e.abs(),0,3,1),t.s*=e.s):t=J(this,e,0,i.modulo,1),t=t.times(e),S=!0,this.minus(t)):z(new i(this),i.precision,i.rounding):new i(NaN)},j.naturalExponential=j.exp=function(){return en(this)},j.naturalLogarithm=j.ln=function(){return es(this)},j.negated=j.neg=function(){var e=new this.constructor(this);return e.s=-e.s,z(e)},j.plus=j.add=function(e){var t,i,r,n,s,o,a,u,c,d,l=this.constructor;if(e=new l(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new l(e.d||this.s===e.s?this:NaN)):e=new l(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(c=this.d,d=e.d,a=l.precision,u=l.rounding,!c[0]||!d[0])return d[0]||(e=new l(this)),S?z(e,a,u):e;if(s=O(this.e/7),r=O(e.e/7),c=c.slice(),n=s-r){for(n<0?(i=c,n=-n,o=d.length):(i=d,r=s,o=c.length),n>(o=(s=Math.ceil(a/7))>o?s+1:o+1)&&(n=o,i.length=1),i.reverse();n--;)i.push(0);i.reverse()}for((o=c.length)-(n=d.length)<0&&(n=o,i=d,d=c,c=i),t=0;n;)t=(c[--n]=c[n]+d[n]+t)/1e7|0,c[n]%=1e7;for(t&&(c.unshift(t),++r),o=c.length;0==c[--o];)c.pop();return e.d=c,e.e=Q(c,r),S?z(e,a,u):e},j.precision=j.sd=function(e){var t;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(F+e);return this.d?(t=Y(this.d),e&&this.e+1>t&&(t=this.e+1)):t=NaN,t},j.round=function(){var e=this.constructor;return z(new e(this),this.e+1,e.rounding)},j.sine=j.sin=function(){var e,t,i=this,r=i.constructor;return i.isFinite()?i.isZero()?new r(i):(e=r.precision,t=r.rounding,r.precision=e+Math.max(i.e,i.sd())+7,r.rounding=1,i=function(e,t){var i,r=t.d.length;if(r<3)return t.isZero()?t:eu(e,2,t,t);i=(i=1.4*Math.sqrt(r))>16?16:0|i,t=eu(e,2,t=t.times(1/ec(5,i)),t);for(var n,s=new e(5),o=new e(16),a=new e(20);i--;)n=t.times(t),t=t.times(s.plus(n.times(o.times(n).minus(a))));return t}(r,ed(r,i)),r.precision=e,r.rounding=t,z(A>2?i.neg():i,e,t,!0)):new r(NaN)},j.squareRoot=j.sqrt=function(){var e,t,i,r,n,s,o=this.d,a=this.e,u=this.s,c=this.constructor;if(1!==u||!o||!o[0])return new c(!u||u<0&&(!o||o[0])?NaN:o?this:1/0);for(S=!1,0==(u=Math.sqrt(+this))||u==1/0?(((t=V(o)).length+a)%2==0&&(t+="0"),u=Math.sqrt(t),a=O((a+1)/2)-(a<0||a%2),r=new c(t=u==1/0?"5e"+a:(t=u.toExponential()).slice(0,t.indexOf("e")+1)+a)):r=new c(u.toString()),i=(a=c.precision)+3;;)if(r=(s=r).plus(J(this,s,i+2,1)).times(.5),V(s.d).slice(0,i)===(t=V(r.d)).slice(0,i)){if("9999"!=(t=t.slice(i-3,i+1))&&(n||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(z(r,a+1,1),e=!r.times(r).eq(this));break}if(!n&&(z(s,a+1,0),s.times(s).eq(this))){r=s;break}i+=4,n=1}return S=!0,z(r,a,c.rounding,e)},j.tangent=j.tan=function(){var e,t,i=this,r=i.constructor;return i.isFinite()?i.isZero()?new r(i):(e=r.precision,t=r.rounding,r.precision=e+10,r.rounding=1,(i=i.sin()).s=1,i=J(i,new r(1).minus(i.times(i)).sqrt(),e+10,0),r.precision=e,r.rounding=t,z(2==A||4==A?i.neg():i,e,t,!0)):new r(NaN)},j.times=j.mul=function(e){var t,i,r,n,s,o,a,u,c,d=this.constructor,l=this.d,h=(e=new d(e)).d;if(e.s*=this.s,!l||!l[0]||!h||!h[0])return new d(e.s&&(!l||l[0]||h)&&(!h||h[0]||l)?l&&h?0*e.s:e.s/0:NaN);for(i=O(this.e/7)+O(e.e/7),(u=l.length)<(c=h.length)&&(s=l,l=h,h=s,o=u,u=c,c=o),s=[],r=o=u+c;r--;)s.push(0);for(r=c;--r>=0;){for(t=0,n=u+r;n>r;)a=s[n]+h[r]*l[n-r-1]+t,s[n--]=a%1e7|0,t=a/1e7|0;s[n]=(s[n]+t)%1e7|0}for(;!s[--o];)s.pop();return t?++i:s.shift(),e.d=s,e.e=Q(s,i),S?z(e,d.precision,d.rounding):e},j.toBinary=function(e,t){return el(this,2,e,t)},j.toDecimalPlaces=j.toDP=function(e,t){var i=this,r=i.constructor;return i=new r(i),void 0===e?i:(H(e,0,1e9),void 0===t?t=r.rounding:H(t,0,8),z(i,e+i.e+1,t))},j.toExponential=function(e,t){var i,r=this,n=r.constructor;return void 0===e?i=K(r,!0):(H(e,0,1e9),void 0===t?t=n.rounding:H(t,0,8),i=K(r=z(new n(r),e+1,t),!0,e+1)),r.isNeg()&&!r.isZero()?"-"+i:i},j.toFixed=function(e,t){var i,r,n=this.constructor;return void 0===e?i=K(this):(H(e,0,1e9),void 0===t?t=n.rounding:H(t,0,8),i=K(r=z(new n(this),e+this.e+1,t),!1,e+r.e+1)),this.isNeg()&&!this.isZero()?"-"+i:i},j.toFraction=function(e){var t,i,r,n,s,o,a,u,c,d,l,h,p=this.d,f=this.constructor;if(!p)return new f(this);if(c=i=new f(1),r=u=new f(0),o=(s=(t=new f(r)).e=Y(p)-this.e-1)%7,t.d[0]=R(10,o<0?7+o:o),null==e)e=s>0?t:c;else{if(!(a=new f(e)).isInt()||a.lt(c))throw Error(F+a);e=a.gt(t)?s>0?t:c:a}for(S=!1,a=new f(V(p)),d=f.precision,f.precision=s=14*p.length;l=J(a,t,0,1,1),1!=(n=i.plus(l.times(r))).cmp(e);)i=r,r=n,n=c,c=u.plus(l.times(n)),u=n,n=t,t=a.minus(l.times(n)),a=n;return n=J(e.minus(i),r,0,1,1),u=u.plus(n.times(c)),i=i.plus(n.times(r)),u.s=c.s=this.s,h=1>J(c,r,s,1).minus(this).abs().cmp(J(u,i,s,1).minus(this).abs())?[c,r]:[u,i],f.precision=d,S=!0,h},j.toHexadecimal=j.toHex=function(e,t){return el(this,16,e,t)},j.toNearest=function(e,t){var i=this,r=i.constructor;if(i=new r(i),null==e){if(!i.d)return i;e=new r(1),t=r.rounding}else{if(e=new r(e),void 0===t?t=r.rounding:H(t,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(S=!1,i=J(i,e,0,t,1).times(e),S=!0,z(i)):(e.s=i.s,i=e),i},j.toNumber=function(){return+this},j.toOctal=function(e,t){return el(this,8,e,t)},j.toPower=j.pow=function(e){var t,i,r,n,s,o,a=this,u=a.constructor,c=+(e=new u(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new u(R(+a,c));if((a=new u(a)).eq(1))return a;if(r=u.precision,s=u.rounding,e.eq(1))return z(a,r,s);if((t=O(e.e/7))>=e.d.length-1&&(i=c<0?-c:c)<=9007199254740991)return n=et(u,a,i,r),e.s<0?new u(1).div(n):z(n,r,s);if((o=a.s)<0){if(t<e.d.length-1)return new u(NaN);if(1&e.d[t]||(o=1),0==a.e&&1==a.d[0]&&1==a.d.length)return a.s=o,a}return(t=0!=(i=R(+a,c))&&isFinite(i)?new u(i+"").e:O(c*(Math.log("0."+V(a.d))/Math.LN10+a.e+1)))>u.maxE+1||t<u.minE-1?new u(t>0?o/0:0):(S=!1,u.rounding=a.s=1,i=Math.min(12,(t+"").length),(n=en(e.times(es(a,r+i)),r)).d&&W((n=z(n,r+5,1)).d,r,s)&&(t=r+10,+V((n=z(en(e.times(es(a,t+i)),t),t+5,1)).d).slice(r+1,r+15)+1==1e14&&(n=z(n,r+1,0))),n.s=o,S=!0,u.rounding=s,z(n,r,s))},j.toPrecision=function(e,t){var i,r=this,n=r.constructor;return void 0===e?i=K(r,r.e<=n.toExpNeg||r.e>=n.toExpPos):(H(e,1,1e9),void 0===t?t=n.rounding:H(t,0,8),i=K(r=z(new n(r),e,t),e<=r.e||r.e<=n.toExpNeg,e)),r.isNeg()&&!r.isZero()?"-"+i:i},j.toSignificantDigits=j.toSD=function(e,t){var i=this.constructor;return void 0===e?(e=i.precision,t=i.rounding):(H(e,1,1e9),void 0===t?t=i.rounding:H(t,0,8)),z(new i(this),e,t)},j.toString=function(){var e=this.constructor,t=K(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+t:t},j.truncated=j.trunc=function(){return z(new this.constructor(this),this.e+1,1)},j.valueOf=j.toJSON=function(){var e=this.constructor,t=K(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+t:t};var J=function(){function e(e,t,i){var r,n=0,s=e.length;for(e=e.slice();s--;)r=e[s]*t+n,e[s]=r%i|0,n=r/i|0;return n&&e.unshift(n),e}function t(e,t,i,r){var n,s;if(i!=r)s=i>r?1:-1;else for(n=s=0;n<i;n++)if(e[n]!=t[n]){s=e[n]>t[n]?1:-1;break}return s}function i(e,t,i,r){for(var n=0;i--;)e[i]-=n,n=e[i]<t[i]?1:0,e[i]=n*r+e[i]-t[i];for(;!e[0]&&e.length>1;)e.shift()}return function(r,n,s,o,a,u){var c,d,l,h,p,f,m,g,w,b,v,N,y,A,x,P,I,k,S,C,F=r.constructor,T=r.s==n.s?1:-1,M=r.d,_=n.d;if(!M||!M[0]||!_||!_[0])return new F(r.s&&n.s&&(M?!_||M[0]!=_[0]:_)?M&&0==M[0]||!_?0*T:T/0:NaN);for(u?(p=1,d=r.e-n.e):(u=1e7,p=7,d=O(r.e/p)-O(n.e/p)),S=_.length,I=M.length,b=(w=new F(T)).d=[],l=0;_[l]==(M[l]||0);l++);if(_[l]>(M[l]||0)&&d--,null==s?(A=s=F.precision,o=F.rounding):A=a?s+(r.e-n.e)+1:s,A<0)b.push(1),f=!0;else{if(A=A/p+2|0,l=0,1==S){for(h=0,_=_[0],A++;(l<I||h)&&A--;l++)x=h*u+(M[l]||0),b[l]=x/_|0,h=x%_|0;f=h||l<I}else{for((h=u/(_[0]+1)|0)>1&&(_=e(_,h,u),M=e(M,h,u),S=_.length,I=M.length),P=S,N=(v=M.slice(0,S)).length;N<S;)v[N++]=0;(C=_.slice()).unshift(0),k=_[0],_[1]>=u/2&&++k;do h=0,(c=t(_,v,S,N))<0?(y=v[0],S!=N&&(y=y*u+(v[1]||0)),(h=y/k|0)>1?(h>=u&&(h=u-1),g=(m=e(_,h,u)).length,N=v.length,1==(c=t(m,v,g,N))&&(h--,i(m,S<g?C:_,g,u))):(0==h&&(c=h=1),m=_.slice()),(g=m.length)<N&&m.unshift(0),i(v,m,N,u),-1==c&&(N=v.length,(c=t(_,v,S,N))<1&&(h++,i(v,S<N?C:_,N,u))),N=v.length):0===c&&(h++,v=[0]),b[l++]=h,c&&v[0]?v[N++]=M[P]||0:(v=[M[P]],N=1);while((P++<I||void 0!==v[0])&&A--);f=void 0!==v[0]}b[0]||b.shift()}if(1==p)w.e=d,E=f;else{for(l=1,h=b[0];h>=10;h/=10)l++;w.e=l+d*p-1,z(w,a?s+w.e+1:s,o,f)}return w}}();function z(e,t,i,r){var n,s,o,a,u,c,d,l,h,p=e.constructor;e:if(null!=t){if(!(l=e.d))return e;for(n=1,a=l[0];a>=10;a/=10)n++;if((s=t-n)<0)s+=7,o=t,u=(d=l[h=0])/R(10,n-o-1)%10|0;else if((h=Math.ceil((s+1)/7))>=(a=l.length)){if(r){for(;a++<=h;)l.push(0);d=u=0,n=1,s%=7,o=s-7+1}else break e}else{for(d=a=l[h],n=1;a>=10;a/=10)n++;s%=7,u=(o=s-7+n)<0?0:d/R(10,n-o-1)%10|0}if(r=r||t<0||void 0!==l[h+1]||(o<0?d:d%R(10,n-o-1)),c=i<4?(u||r)&&(0==i||i==(e.s<0?3:2)):u>5||5==u&&(4==i||r||6==i&&(s>0?o>0?d/R(10,n-o):0:l[h-1])%10&1||i==(e.s<0?8:7)),t<1||!l[0])return l.length=0,c?(t-=e.e+1,l[0]=R(10,(7-t%7)%7),e.e=-t||0):l[0]=e.e=0,e;if(0==s?(l.length=h,a=1,h--):(l.length=h+1,a=R(10,7-s),l[h]=o>0?(d/R(10,n-o)%R(10,o)|0)*a:0),c)for(;;)if(0==h){for(s=1,o=l[0];o>=10;o/=10)s++;for(o=l[0]+=a,a=1;o>=10;o/=10)a++;s!=a&&(e.e++,1e7==l[0]&&(l[0]=1));break}else{if(l[h]+=a,1e7!=l[h])break;l[h--]=0,a=1}for(s=l.length;0===l[--s];)l.pop()}return S&&(e.e>p.maxE?(e.d=null,e.e=NaN):e.e<p.minE&&(e.e=0,e.d=[0])),e}function K(e,t,i){if(!e.isFinite())return eo(e);var r,n=e.e,s=V(e.d),o=s.length;return t?(i&&(r=i-o)>0?s=s.charAt(0)+"."+s.slice(1)+ee(r):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):n<0?(s="0."+ee(-n-1)+s,i&&(r=i-o)>0&&(s+=ee(r))):n>=o?(s+=ee(n+1-o),i&&(r=i-n-1)>0&&(s=s+"."+ee(r))):((r=n+1)<o&&(s=s.slice(0,r)+"."+s.slice(r)),i&&(r=i-o)>0&&(n+1===o&&(s+="."),s+=ee(r))),s}function Q(e,t){var i=e[0];for(t*=7;i>=10;i/=10)t++;return t}function G(e,t,i){if(t>Z)throw S=!0,i&&(e.precision=i),Error(T);return z(new e(P),t,1,!0)}function X(e,t,i){if(t>$)throw Error(T);return z(new e(I),t,i,!0)}function Y(e){var t=e.length-1,i=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)i--;for(t=e[0];t>=10;t/=10)i++}return i}function ee(e){for(var t="";e--;)t+="0";return t}function et(e,t,i,r){var n,s=new e(1),o=Math.ceil(r/7+4);for(S=!1;;){if(i%2&&eh((s=s.times(t)).d,o)&&(n=!0),0===(i=O(i/2))){i=s.d.length-1,n&&0===s.d[i]&&++s.d[i];break}eh((t=t.times(t)).d,o)}return S=!0,s}function ei(e){return 1&e.d[e.d.length-1]}function er(e,t,i){for(var r,n=new e(t[0]),s=0;++s<t.length;)if((r=new e(t[s])).s)n[i](r)&&(n=r);else{n=r;break}return n}function en(e,t){var i,r,n,s,o,a,u,c=0,d=0,l=0,h=e.constructor,p=h.rounding,f=h.precision;if(!e.d||!e.d[0]||e.e>17)return new h(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(S=!1,u=f):u=t,a=new h(.03125);e.e>-2;)e=e.times(a),l+=5;for(u+=r=Math.log(R(2,l))/Math.LN10*2+5|0,i=s=o=new h(1),h.precision=u;;){if(s=z(s.times(e),u,1),i=i.times(++d),V((a=o.plus(J(s,i,u,1))).d).slice(0,u)===V(o.d).slice(0,u)){for(n=l;n--;)o=z(o.times(o),u,1);if(null!=t)return h.precision=f,o;if(!(c<3&&W(o.d,u-r,p,c)))return z(o,h.precision=f,p,S=!0);h.precision=u+=10,i=s=a=new h(1),d=0,c++}o=a}}function es(e,t){var i,r,n,s,o,a,u,c,d,l,h,p=1,f=e,m=f.d,g=f.constructor,w=g.rounding,b=g.precision;if(f.s<0||!m||!m[0]||!f.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=f.s?NaN:m?0:f);if(null==t?(S=!1,d=b):d=t,g.precision=d+=10,r=(i=V(m)).charAt(0),!(15e14>Math.abs(s=f.e)))return c=G(g,d+2,b).times(s+""),f=es(new g(r+"."+i.slice(1)),d-10).plus(c),g.precision=b,null==t?z(f,b,w,S=!0):f;for(;r<7&&1!=r||1==r&&i.charAt(1)>3;)r=(i=V((f=f.times(e)).d)).charAt(0),p++;for(s=f.e,r>1?(f=new g("0."+i),s++):f=new g(r+"."+i.slice(1)),l=f,u=o=f=J(f.minus(1),f.plus(1),d,1),h=z(f.times(f),d,1),n=3;;){if(o=z(o.times(h),d,1),V((c=u.plus(J(o,new g(n),d,1))).d).slice(0,d)===V(u.d).slice(0,d)){if(u=u.times(2),0!==s&&(u=u.plus(G(g,d+2,b).times(s+""))),u=J(u,new g(p),d,1),null!=t)return g.precision=b,u;if(!W(u.d,d-10,w,a))return z(u,g.precision=b,w,S=!0);g.precision=d+=10,c=o=f=J(l.minus(1),l.plus(1),d,1),h=z(f.times(f),d,1),n=a=1}u=c,n+=2}}function eo(e){return String(e.s*e.s/0)}function ea(e,t){var i,r,n;for((i=t.indexOf("."))>-1&&(t=t.replace(".","")),(r=t.search(/e/i))>0?(i<0&&(i=r),i+=+t.slice(r+1),t=t.substring(0,r)):i<0&&(i=t.length),r=0;48===t.charCodeAt(r);r++);for(n=t.length;48===t.charCodeAt(n-1);--n);if(t=t.slice(r,n)){if(n-=r,e.e=i=i-r-1,e.d=[],r=(i+1)%7,i<0&&(r+=7),r<n){for(r&&e.d.push(+t.slice(0,r)),n-=7;r<n;)e.d.push(+t.slice(r,r+=7));r=7-(t=t.slice(r)).length}else r-=n;for(;r--;)t+="0";e.d.push(+t),S&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function eu(e,t,i,r,n){var s,o,a,u,c=e.precision,d=Math.ceil(c/7);for(S=!1,u=i.times(i),a=new e(r);;){if(o=J(a.times(u),new e(t++*t++),c,1),a=n?r.plus(o):r.minus(o),r=J(o.times(u),new e(t++*t++),c,1),void 0!==(o=a.plus(r)).d[d]){for(s=d;o.d[s]===a.d[s]&&s--;);if(-1==s)break}s=a,a=r,r=o,o=s}return S=!0,o.d.length=d+1,o}function ec(e,t){for(var i=e;--t;)i*=e;return i}function ed(e,t){var i,r=t.s<0,n=X(e,e.precision,1),s=n.times(.5);if((t=t.abs()).lte(s))return A=r?4:1,t;if((i=t.divToInt(n)).isZero())A=r?3:2;else{if((t=t.minus(i.times(n))).lte(s))return A=ei(i)?r?2:3:r?4:1,t;A=ei(i)?r?1:4:r?3:2}return t.minus(n).abs()}function el(e,t,i,r){var n,s,o,a,u,c,d,l,h,p=e.constructor,f=void 0!==i;if(f?(H(i,1,1e9),void 0===r?r=p.rounding:H(r,0,8)):(i=p.precision,r=p.rounding),e.isFinite()){for(o=(d=K(e)).indexOf("."),f?(n=2,16==t?i=4*i-3:8==t&&(i=3*i-2)):n=t,o>=0&&(d=d.replace(".",""),(h=new p(1)).e=d.length-o,h.d=B(K(h),10,n),h.e=h.d.length),s=u=(l=B(d,10,n)).length;0==l[--u];)l.pop();if(l[0]){if(o<0?s--:((e=new p(e)).d=l,e.e=s,l=(e=J(e,h,i,r,0,n)).d,s=e.e,c=E),o=l[i],a=n/2,c=c||void 0!==l[i+1],c=r<4?(void 0!==o||c)&&(0===r||r===(e.s<0?3:2)):o>a||o===a&&(4===r||c||6===r&&1&l[i-1]||r===(e.s<0?8:7)),l.length=i,c)for(;++l[--i]>n-1;)l[i]=0,i||(++s,l.unshift(1));for(u=l.length;!l[u-1];--u);for(o=0,d="";o<u;o++)d+=x.charAt(l[o]);if(f){if(u>1){if(16==t||8==t){for(o=16==t?4:3,--u;u%o;u++)d+="0";for(u=(l=B(d,n,t)).length;!l[u-1];--u);for(o=1,d="1.";o<u;o++)d+=x.charAt(l[o])}else d=d.charAt(0)+"."+d.slice(1)}d=d+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)d="0"+d;d="0."+d}else if(++s>u)for(s-=u;s--;)d+="0";else s<u&&(d=d.slice(0,s)+"."+d.slice(s))}else d=f?"0p+0":"0";d=(16==t?"0x":2==t?"0b":8==t?"0o":"")+d}else d=eo(e);return e.s<0?"-"+d:d}function eh(e,t){if(e.length>t)return e.length=t,!0}function ep(e){return new this(e).abs()}function ef(e){return new this(e).acos()}function em(e){return new this(e).acosh()}function eg(e,t){return new this(e).plus(t)}function ew(e){return new this(e).asin()}function eb(e){return new this(e).asinh()}function ev(e){return new this(e).atan()}function eN(e){return new this(e).atanh()}function ey(e,t){e=new this(e),t=new this(t);var i,r=this.precision,n=this.rounding,s=r+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(i=t.s<0?X(this,r,n):new this(0)).s=e.s:!e.d||t.isZero()?(i=X(this,s,1).times(.5)).s=e.s:t.s<0?(this.precision=s,this.rounding=1,i=this.atan(J(e,t,s,1)),t=X(this,s,1),this.precision=r,this.rounding=n,i=e.s<0?i.minus(t):i.plus(t)):i=this.atan(J(e,t,s,1)):(i=X(this,s,1).times(t.s>0?.25:.75)).s=e.s:i=new this(NaN),i}function eE(e){return new this(e).cbrt()}function eA(e){return z(e=new this(e),e.e+1,2)}function ex(e,t,i){return new this(e).clamp(t,i)}function eP(e){if(!e||"object"!=typeof e)throw Error(C+"Object expected");var t,i,r,n=!0===e.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(t=0;t<s.length;t+=3)if(i=s[t],n&&(this[i]=k[i]),void 0!==(r=e[i])){if(O(r)===r&&r>=s[t+1]&&r<=s[t+2])this[i]=r;else throw Error(F+i+": "+r)}if(i="crypto",n&&(this[i]=k[i]),void 0!==(r=e[i])){if(!0===r||!1===r||0===r||1===r){if(r){if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(M)}else this[i]=!1}else throw Error(F+i+": "+r)}return this}function eI(e){return new this(e).cos()}function ek(e){return new this(e).cosh()}function eS(e,t){return new this(e).div(t)}function eC(e){return new this(e).exp()}function eF(e){return z(e=new this(e),e.e+1,3)}function eT(){var e,t,i=new this(0);for(S=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)i.d&&(i=i.plus(t.times(t)));else{if(t.s)return S=!0,new this(1/0);i=t}return S=!0,i.sqrt()}function eM(e){return e instanceof eY||e&&e.toStringTag===_||!1}function e_(e){return new this(e).ln()}function eO(e,t){return new this(e).log(t)}function eR(e){return new this(e).log(2)}function eU(e){return new this(e).log(10)}function eD(){return er(this,arguments,"lt")}function eq(){return er(this,arguments,"gt")}function eL(e,t){return new this(e).mod(t)}function eZ(e,t){return new this(e).mul(t)}function e$(e,t){return new this(e).pow(t)}function ej(e){var t,i,r,n,s=0,o=new this(1),a=[];if(void 0===e?e=this.precision:H(e,1,1e9),r=Math.ceil(e/7),this.crypto){if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(r));s<r;)(n=t[s])>=429e7?t[s]=crypto.getRandomValues(new Uint32Array(1))[0]:a[s++]=n%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(r*=4);s<r;)(n=t[s]+(t[s+1]<<8)+(t[s+2]<<16)+((127&t[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,s):(a.push(n%1e7),s+=4);s=r/4}else throw Error(M)}else for(;s<r;)a[s++]=1e7*Math.random()|0;for(r=a[--s],e%=7,r&&e&&(n=R(10,7-e),a[s]=(r/n|0)*n);0===a[s];s--)a.pop();if(s<0)i=0,a=[0];else{for(i=-1;0===a[0];i-=7)a.shift();for(r=1,n=a[0];n>=10;n/=10)r++;r<7&&(i-=7-r)}return o.e=i,o.d=a,o}function eV(e){return z(e=new this(e),e.e+1,this.rounding)}function eH(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function eW(e){return new this(e).sin()}function eB(e){return new this(e).sinh()}function eJ(e){return new this(e).sqrt()}function ez(e,t){return new this(e).sub(t)}function eK(){var e=0,t=arguments,i=new this(t[0]);for(S=!1;i.s&&++e<t.length;)i=i.plus(t[e]);return S=!0,z(i,this.precision,this.rounding)}function eQ(e){return new this(e).tan()}function eG(e){return new this(e).tanh()}function eX(e){return z(e=new this(e),e.e+1,1)}j[Symbol.for("nodejs.util.inspect.custom")]=j.toString,j[Symbol.toStringTag]="Decimal";var eY=j.constructor=function e(t){var i,r,n;function s(e){var t,i,r;if(!(this instanceof s))return new s(e);if(this.constructor=s,eM(e)){this.s=e.s,S?!e.d||e.e>s.maxE?(this.e=NaN,this.d=null):e.e<s.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(r=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(t=0,i=e;i>=10;i/=10)t++;S?t>s.maxE?(this.e=NaN,this.d=null):t<s.minE?(this.e=0,this.d=[0]):(this.e=t,this.d=[e]):(this.e=t,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return ea(this,e.toString())}if("string"!==r)throw Error(F+e);return 45===(i=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===i&&(e=e.slice(1)),this.s=1),L.test(e)?ea(this,e):function(e,t){var i,r,n,s,o,a,u,c,d;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),L.test(t))return ea(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(D.test(t))i=16,t=t.toLowerCase();else if(U.test(t))i=2;else if(q.test(t))i=8;else throw Error(F+t);for((s=t.search(/p/i))>0?(u=+t.slice(s+1),t=t.substring(2,s)):t=t.slice(2),o=(s=t.indexOf("."))>=0,r=e.constructor,o&&(s=(a=(t=t.replace(".","")).length)-s,n=et(r,new r(i),s,2*s)),s=d=(c=B(t,i,1e7)).length-1;0===c[s];--s)c.pop();return s<0?new r(0*e.s):(e.e=Q(c,d),e.d=c,S=!1,o&&(e=J(e,n,4*a)),u&&(e=e.times(54>Math.abs(u)?R(2,u):eY.pow(2,u))),S=!0,e)}(this,e)}if(s.prototype=j,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=eP,s.clone=e,s.isDecimal=eM,s.abs=ep,s.acos=ef,s.acosh=em,s.add=eg,s.asin=ew,s.asinh=eb,s.atan=ev,s.atanh=eN,s.atan2=ey,s.cbrt=eE,s.ceil=eA,s.clamp=ex,s.cos=eI,s.cosh=ek,s.div=eS,s.exp=eC,s.floor=eF,s.hypot=eT,s.ln=e_,s.log=eO,s.log10=eU,s.log2=eR,s.max=eD,s.min=eq,s.mod=eL,s.mul=eZ,s.pow=e$,s.random=ej,s.round=eV,s.sign=eH,s.sin=eW,s.sinh=eB,s.sqrt=eJ,s.sub=ez,s.sum=eK,s.tan=eQ,s.tanh=eG,s.trunc=eX,void 0===t&&(t={}),t&&!0!==t.defaults)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],i=0;i<n.length;)t.hasOwnProperty(r=n[i++])||(t[r]=this[r]);return s.config(t),s}(k);P=new eY(P),I=new eY(I);var e0=eY;/*! Bundled license information:

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.4.3
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/}}]);