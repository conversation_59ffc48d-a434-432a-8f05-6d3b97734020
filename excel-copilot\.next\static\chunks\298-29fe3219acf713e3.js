"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[298],{80233:function(e,t,r){r.d(t,{Z:function(){return a}});let a=(0,r(81066).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},74109:function(e,t,r){r.d(t,{Z:function(){return a}});let a=(0,r(81066).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},4086:function(e,t,r){r.d(t,{Z:function(){return a}});let a=(0,r(81066).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},24258:function(e,t,r){r.d(t,{Z:function(){return a}});let a=(0,r(81066).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},20500:function(e,t,r){r.d(t,{Z:function(){return a}});let a=(0,r(81066).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},52022:function(e,t,r){r.d(t,{Z:function(){return a}});let a=(0,r(81066).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},16463:function(e,t,r){var a=r(71169);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},31014:function(e,t,r){r.d(t,{F:function(){return u}});var a=r(39343);let i=(e,t,r)=>{if(e&&"reportValidity"in e){let i=(0,a.U2)(r,t);e.setCustomValidity(i&&i.message||""),e.reportValidity()}},n=(e,t)=>{for(let r in t.fields){let a=t.fields[r];a&&a.ref&&"reportValidity"in a.ref?i(a.ref,r,e):a.refs&&a.refs.forEach(t=>i(t,r,e))}},s=(e,t)=>{t.shouldUseNativeValidation&&n(e,t);let r={};for(let i in e){let n=(0,a.U2)(t.fields,i),s=Object.assign(e[i]||{},{ref:n&&n.ref});if(l(t.names||Object.keys(e),i)){let e=Object.assign({},(0,a.U2)(r,i));(0,a.t8)(e,"root",s),(0,a.t8)(r,i,e)}else(0,a.t8)(r,i,s)}return r},l=(e,t)=>e.some(e=>e.startsWith(t+"."));var o=function(e,t){for(var r={};e.length;){var i=e[0],n=i.code,s=i.message,l=i.path.join(".");if(!r[l]){if("unionErrors"in i){var o=i.unionErrors[0].errors[0];r[l]={message:o.message,type:o.code}}else r[l]={message:s,type:n}}if("unionErrors"in i&&i.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var u=r[l].types,d=u&&u[i.code];r[l]=(0,a.KN)(l,t,r,n,d?[].concat(d,i.message):i.message)}e.shift()}return r},u=function(e,t,r){return void 0===r&&(r={}),function(a,i,l){try{return Promise.resolve(function(i,s){try{var o=Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](a,t)).then(function(e){return l.shouldUseNativeValidation&&n({},l),{errors:{},values:r.raw?a:e}})}catch(e){return s(e)}return o&&o.then?o.then(void 0,s):o}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:s(o(e.errors,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)};throw e}))}catch(e){return Promise.reject(e)}}}},53938:function(e,t,r){r.d(t,{I0:function(){return p},XB:function(){return c},fC:function(){return h}});var a,i=r(2265),n=r(78149),s=r(25171),l=r(1584),o=r(75137),u=r(57437),d="dismissableLayer.update",f=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),c=i.forwardRef((e,t)=>{var r,c;let{disableOutsidePointerEvents:y=!1,onEscapeKeyDown:h,onPointerDownOutside:p,onFocusOutside:b,onInteractOutside:g,onDismiss:V,...w}=e,_=i.useContext(f),[x,F]=i.useState(null),A=null!==(c=null==x?void 0:x.ownerDocument)&&void 0!==c?c:null===(r=globalThis)||void 0===r?void 0:r.document,[,E]=i.useState({}),k=(0,l.e)(t,e=>F(e)),D=Array.from(_.layers),[S]=[..._.layersWithOutsidePointerEventsDisabled].slice(-1),C=D.indexOf(S),O=x?D.indexOf(x):-1,L=_.layersWithOutsidePointerEventsDisabled.size>0,j=O>=C,P=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,a=(0,o.W)(e),n=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let t=function(){m("dismissableLayer.pointerDownOutside",a,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",s.current),s.current=t,r.addEventListener("click",s.current,{once:!0})):t()}else r.removeEventListener("click",s.current);n.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",s.current)}},[r,a]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[..._.branches].some(e=>e.contains(t));!j||r||(null==p||p(e),null==g||g(e),e.defaultPrevented||null==V||V())},A),R=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,a=(0,o.W)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",a,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,a]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[..._.branches].some(e=>e.contains(t))||(null==b||b(e),null==g||g(e),e.defaultPrevented||null==V||V())},A);return!function(e,t=globalThis?.document){let r=(0,o.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{O!==_.layers.size-1||(null==h||h(e),!e.defaultPrevented&&V&&(e.preventDefault(),V()))},A),i.useEffect(()=>{if(x)return y&&(0===_.layersWithOutsidePointerEventsDisabled.size&&(a=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),_.layersWithOutsidePointerEventsDisabled.add(x)),_.layers.add(x),v(),()=>{y&&1===_.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=a)}},[x,A,y,_]),i.useEffect(()=>()=>{x&&(_.layers.delete(x),_.layersWithOutsidePointerEventsDisabled.delete(x),v())},[x,_]),i.useEffect(()=>{let e=()=>E({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,u.jsx)(s.WV.div,{...w,ref:k,style:{pointerEvents:L?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,n.M)(e.onFocusCapture,R.onFocusCapture),onBlurCapture:(0,n.M)(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:(0,n.M)(e.onPointerDownCapture,P.onPointerDownCapture)})});c.displayName="DismissableLayer";var y=i.forwardRef((e,t)=>{let r=i.useContext(f),a=i.useRef(null),n=(0,l.e)(t,a);return i.useEffect(()=>{let e=a.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(s.WV.div,{...e,ref:n})});function v(){let e=new CustomEvent(d);document.dispatchEvent(e)}function m(e,t,r,a){let{discrete:i}=a,n=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&n.addEventListener(e,t,{once:!0}),i?(0,s.jH)(n,l):n.dispatchEvent(l)}y.displayName="DismissableLayerBranch";var h=c,p=y},38364:function(e,t,r){r.d(t,{f:function(){return l}});var a=r(2265),i=r(25171),n=r(57437),s=a.forwardRef((e,t)=>(0,n.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=s},7715:function(e,t,r){r.d(t,{h:function(){return o}});var a=r(2265),i=r(54887),n=r(25171),s=r(1336),l=r(57437),o=a.forwardRef((e,t)=>{var r,o;let{container:u,...d}=e,[f,c]=a.useState(!1);(0,s.b)(()=>c(!0),[]);let y=u||f&&(null===(o=globalThis)||void 0===o?void 0:null===(r=o.document)||void 0===r?void 0:r.body);return y?i.createPortal((0,l.jsx)(n.WV.div,{...d,ref:t}),y):null});o.displayName="Portal"},15167:function(e,t,r){r.d(t,{f:function(){return c}});var a=r(2265);r(54887);var i=r(1584),n=r(57437),s=Symbol("radix.slottable");function l(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){let e,s;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let a in t){let i=e[a],n=t[a];/^on[A-Z]/.test(a)?i&&n?r[a]=(...e)=>{let t=n(...e);return i(...e),t}:i&&(r[a]=i):"style"===a?r[a]={...i,...n}:"className"===a&&(r[a]=[i,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(o.ref=t?(0,i.F)(t,l):l),a.cloneElement(r,o)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:i,...s}=e,o=a.Children.toArray(i),u=o.find(l);if(u){let e=u.props.children,i=o.map(t=>t!==u?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...s,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,i):null})}return(0,n.jsx)(t,{...s,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),s=a.forwardRef((e,a)=>{let{asChild:i,...s}=e,l=i?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(l,{...s,ref:a})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{}),u="horizontal",d=["horizontal","vertical"],f=a.forwardRef((e,t)=>{let{decorative:r,orientation:a=u,...i}=e,s=d.includes(a)?a:u;return(0,n.jsx)(o.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...i,ref:t})});f.displayName="Separator";var c=f},62447:function(e,t,r){r.d(t,{VY:function(){return O},aV:function(){return S},fC:function(){return D},xz:function(){return C}});var a=r(2265),i=r(78149),n=r(98324),s=r(53398),l=r(31383),o=r(25171),u=r(87513),d=r(91715),f=r(53201),c=r(57437),y="Tabs",[v,m]=(0,n.b)(y,[s.Pc]),h=(0,s.Pc)(),[p,b]=v(y),g=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:i,defaultValue:n,orientation:s="horizontal",dir:l,activationMode:v="automatic",...m}=e,h=(0,u.gm)(l),[b,g]=(0,d.T)({prop:a,onChange:i,defaultProp:null!=n?n:"",caller:y});return(0,c.jsx)(p,{scope:r,baseId:(0,f.M)(),value:b,onValueChange:g,orientation:s,dir:h,activationMode:v,children:(0,c.jsx)(o.WV.div,{dir:h,"data-orientation":s,...m,ref:t})})});g.displayName=y;var V="TabsList",w=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...i}=e,n=b(V,r),l=h(r);return(0,c.jsx)(s.fC,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:a,children:(0,c.jsx)(o.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});w.displayName=V;var _="TabsTrigger",x=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...l}=e,u=b(_,r),d=h(r),f=E(u.baseId,a),y=k(u.baseId,a),v=a===u.value;return(0,c.jsx)(s.ck,{asChild:!0,...d,focusable:!n,active:v,children:(0,c.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":y,"data-state":v?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:f,...l,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(a)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(a)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==u.activationMode;v||n||!e||u.onValueChange(a)})})})});x.displayName=_;var F="TabsContent",A=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:n,children:s,...u}=e,d=b(F,r),f=E(d.baseId,i),y=k(d.baseId,i),v=i===d.value,m=a.useRef(v);return a.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,c.jsx)(l.z,{present:n||v,children:r=>{let{present:a}=r;return(0,c.jsx)(o.WV.div,{"data-state":v?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":f,hidden:!a,id:y,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:a&&s})}})});function E(e,t){return"".concat(e,"-trigger-").concat(t)}function k(e,t){return"".concat(e,"-content-").concat(t)}A.displayName=F;var D=g,S=w,C=x,O=A},39343:function(e,t,r){r.d(t,{KN:function(){return C},U2:function(){return p},cI:function(){return eg},t8:function(){return w}});var a=r(2265),i=e=>"checkbox"===e.type,n=e=>e instanceof Date,s=e=>null==e;let l=e=>"object"==typeof e;var o=e=>!s(e)&&!Array.isArray(e)&&l(e)&&!n(e),u=e=>o(e)&&e.target?i(e.target)?e.target.checked:e.target.value:e,d=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,f=(e,t)=>e.has(d(t)),c=e=>{let t=e.constructor&&e.constructor.prototype;return o(t)&&t.hasOwnProperty("isPrototypeOf")},y="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function v(e){let t;let r=Array.isArray(e),a="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(y&&(e instanceof Blob||a))&&(r||o(e))))return e;else if(t=r?[]:{},r||c(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=v(e[r]));else t=e;return t}var m=e=>Array.isArray(e)?e.filter(Boolean):[],h=e=>void 0===e,p=(e,t,r)=>{if(!t||!o(e))return r;let a=m(t.split(/[,[\].]+?/)).reduce((e,t)=>s(e)?e:e[t],e);return h(a)||a===e?h(e[t])?r:e[t]:a},b=e=>"boolean"==typeof e,g=e=>/^\w*$/.test(e),V=e=>m(e.replace(/["|']|\]/g,"").split(/\.|\[/)),w=(e,t,r)=>{let a=-1,i=g(t)?[t]:V(t),n=i.length,s=n-1;for(;++a<n;){let t=i[a],n=r;if(a!==s){let r=e[t];n=o(r)||Array.isArray(r)?r:isNaN(+i[a+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=n,e=e[t]}};let _={BLUR:"blur",FOCUS_OUT:"focusout"},x={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},F={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};a.createContext(null);var A=(e,t,r,a=!0)=>{let i={defaultValues:t._defaultValues};for(let n in e)Object.defineProperty(i,n,{get:()=>(t._proxyFormState[n]!==x.all&&(t._proxyFormState[n]=!a||x.all),r&&(r[n]=!0),e[n])});return i},E=e=>s(e)||!l(e);function k(e,t){if(E(e)||E(t))return e===t;if(n(e)&&n(t))return e.getTime()===t.getTime();let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let i of r){let r=e[i];if(!a.includes(i))return!1;if("ref"!==i){let e=t[i];if(n(r)&&n(e)||o(r)&&o(e)||Array.isArray(r)&&Array.isArray(e)?!k(r,e):r!==e)return!1}}return!0}var D=e=>"string"==typeof e,S=(e,t,r,a,i)=>D(e)?(a&&t.watch.add(e),p(r,e,i)):Array.isArray(e)?e.map(e=>(a&&t.watch.add(e),p(r,e))):(a&&(t.watchAll=!0),r),C=(e,t,r,a,i)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[a]:i||!0}}:{},O=e=>Array.isArray(e)?e:[e],L=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},j=e=>o(e)&&!Object.keys(e).length,P=e=>"file"===e.type,R=e=>"function"==typeof e,T=e=>{if(!y)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},M=e=>"select-multiple"===e.type,N=e=>"radio"===e.type,U=e=>N(e)||i(e),W=e=>T(e)&&e.isConnected;function B(e,t){let r=Array.isArray(t)?t:g(t)?[t]:V(t),a=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,a=0;for(;a<r;)e=h(e)?a++:e[t[a++]];return e}(e,r),i=r.length-1,n=r[i];return a&&delete a[n],0!==i&&(o(a)&&j(a)||Array.isArray(a)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!h(e[t]))return!1;return!0}(a))&&B(e,r.slice(0,-1)),e}var I=e=>{for(let t in e)if(R(e[t]))return!0;return!1};function z(e,t={}){let r=Array.isArray(e);if(o(e)||r)for(let r in e)Array.isArray(e[r])||o(e[r])&&!I(e[r])?(t[r]=Array.isArray(e[r])?[]:{},z(e[r],t[r])):s(e[r])||(t[r]=!0);return t}var Z=(e,t)=>(function e(t,r,a){let i=Array.isArray(t);if(o(t)||i)for(let i in t)Array.isArray(t[i])||o(t[i])&&!I(t[i])?h(r)||E(a[i])?a[i]=Array.isArray(t[i])?z(t[i],[]):{...z(t[i])}:e(t[i],s(r)?{}:r[i],a[i]):a[i]=!k(t[i],r[i]);return a})(e,t,z(t));let q={value:!1,isValid:!1},$={value:!0,isValid:!0};var H=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!h(e[0].attributes.value)?h(e[0].value)||""===e[0].value?$:{value:e[0].value,isValid:!0}:$:q}return q},K=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:a})=>h(e)?e:t?""===e?NaN:e?+e:e:r&&D(e)?new Date(e):a?a(e):e;let X={isValid:!1,value:null};var Y=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,X):X;function G(e){let t=e.ref;return P(t)?t.files:N(t)?Y(e.refs).value:M(t)?[...t.selectedOptions].map(({value:e})=>e):i(t)?H(e.refs).value:K(h(t.value)?e.ref.value:t.value,e)}var J=(e,t,r,a)=>{let i={};for(let r of e){let e=p(t,r);e&&w(i,r,e._f)}return{criteriaMode:r,names:[...e],fields:i,shouldUseNativeValidation:a}},Q=e=>e instanceof RegExp,ee=e=>h(e)?e:Q(e)?e.source:o(e)?Q(e.value)?e.value.source:e.value:e,et=e=>({isOnSubmit:!e||e===x.onSubmit,isOnBlur:e===x.onBlur,isOnChange:e===x.onChange,isOnAll:e===x.all,isOnTouch:e===x.onTouched});let er="AsyncFunction";var ea=e=>!!e&&!!e.validate&&!!(R(e.validate)&&e.validate.constructor.name===er||o(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===er)),ei=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),en=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let es=(e,t,r,a)=>{for(let i of r||Object.keys(e)){let r=p(e,i);if(r){let{_f:e,...n}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],i)&&!a||e.ref&&t(e.ref,e.name)&&!a)return!0;if(es(n,t))break}else if(o(n)&&es(n,t))break}}};function el(e,t,r){let a=p(e,r);if(a||g(r))return{error:a,name:r};let i=r.split(".");for(;i.length;){let a=i.join("."),n=p(t,a),s=p(e,a);if(n&&!Array.isArray(n)&&r!==a)break;if(s&&s.type)return{name:a,error:s};i.pop()}return{name:r}}var eo=(e,t,r,a)=>{r(e);let{name:i,...n}=e;return j(n)||Object.keys(n).length>=Object.keys(t).length||Object.keys(n).find(e=>t[e]===(!a||x.all))},eu=(e,t,r)=>!e||!t||e===t||O(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),ed=(e,t,r,a,i)=>!i.isOnAll&&(!r&&i.isOnTouch?!(t||e):(r?a.isOnBlur:i.isOnBlur)?!e:(r?!a.isOnChange:!i.isOnChange)||e),ef=(e,t)=>!m(p(e,t)).length&&B(e,t),ec=(e,t,r)=>{let a=O(p(e,r));return w(a,"root",t[r]),w(e,r,a),e},ey=e=>D(e);function ev(e,t,r="validate"){if(ey(e)||Array.isArray(e)&&e.every(ey)||b(e)&&!e)return{type:r,message:ey(e)?e:"",ref:t}}var em=e=>o(e)&&!Q(e)?e:{value:e,message:""},eh=async(e,t,r,a,n,l)=>{let{ref:u,refs:d,required:f,maxLength:c,minLength:y,min:v,max:m,pattern:g,validate:V,name:w,valueAsNumber:_,mount:x}=e._f,A=p(r,w);if(!x||t.has(w))return{};let E=d?d[0]:u,k=e=>{n&&E.reportValidity&&(E.setCustomValidity(b(e)?"":e||""),E.reportValidity())},S={},O=N(u),L=i(u),M=(_||P(u))&&h(u.value)&&h(A)||T(u)&&""===u.value||""===A||Array.isArray(A)&&!A.length,U=C.bind(null,w,a,S),W=(e,t,r,a=F.maxLength,i=F.minLength)=>{let n=e?t:r;S[w]={type:e?a:i,message:n,ref:u,...U(e?a:i,n)}};if(l?!Array.isArray(A)||!A.length:f&&(!(O||L)&&(M||s(A))||b(A)&&!A||L&&!H(d).isValid||O&&!Y(d).isValid)){let{value:e,message:t}=ey(f)?{value:!!f,message:f}:em(f);if(e&&(S[w]={type:F.required,message:t,ref:E,...U(F.required,t)},!a))return k(t),S}if(!M&&(!s(v)||!s(m))){let e,t;let r=em(m),i=em(v);if(s(A)||isNaN(A)){let a=u.valueAsDate||new Date(A),n=e=>new Date(new Date().toDateString()+" "+e),s="time"==u.type,l="week"==u.type;D(r.value)&&A&&(e=s?n(A)>n(r.value):l?A>r.value:a>new Date(r.value)),D(i.value)&&A&&(t=s?n(A)<n(i.value):l?A<i.value:a<new Date(i.value))}else{let a=u.valueAsNumber||(A?+A:A);s(r.value)||(e=a>r.value),s(i.value)||(t=a<i.value)}if((e||t)&&(W(!!e,r.message,i.message,F.max,F.min),!a))return k(S[w].message),S}if((c||y)&&!M&&(D(A)||l&&Array.isArray(A))){let e=em(c),t=em(y),r=!s(e.value)&&A.length>+e.value,i=!s(t.value)&&A.length<+t.value;if((r||i)&&(W(r,e.message,t.message),!a))return k(S[w].message),S}if(g&&!M&&D(A)){let{value:e,message:t}=em(g);if(Q(e)&&!A.match(e)&&(S[w]={type:F.pattern,message:t,ref:u,...U(F.pattern,t)},!a))return k(t),S}if(V){if(R(V)){let e=ev(await V(A,r),E);if(e&&(S[w]={...e,...U(F.validate,e.message)},!a))return k(e.message),S}else if(o(V)){let e={};for(let t in V){if(!j(e)&&!a)break;let i=ev(await V[t](A,r),E,t);i&&(e={...i,...U(t,i.message)},k(i.message),a&&(S[w]=e))}if(!j(e)&&(S[w]={ref:E,...e},!a))return S}}return k(!0),S};let ep={mode:x.onSubmit,reValidateMode:x.onChange,shouldFocusError:!0},eb="undefined"!=typeof window?a.useLayoutEffect:a.useEffect;function eg(e={}){let t=a.useRef(void 0),r=a.useRef(void 0),[l,d]=a.useState({isDirty:!1,isValidating:!1,isLoading:R(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:R(e.defaultValues)?void 0:e.defaultValues});!t.current&&(t.current={...e.formControl?e.formControl:function(e={}){let t,r={...ep,...e},a={submitCount:0,isDirty:!1,isReady:!1,isLoading:R(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},l={},d=(o(r.defaultValues)||o(r.values))&&v(r.values||r.defaultValues)||{},c=r.shouldUnregister?{}:v(d),g={action:!1,mount:!1,watch:!1},V={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},F=0,A={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},E={...A},C={array:L(),state:L()},N=et(r.mode),I=et(r.reValidateMode),z=r.criteriaMode===x.all,q=e=>t=>{clearTimeout(F),F=setTimeout(e,t)},$=async e=>{if(!r.disabled&&(A.isValid||E.isValid||e)){let e=r.resolver?j((await ey()).errors):await em(l,!0);e!==a.isValid&&C.state.next({isValid:e})}},H=(e,t)=>{!r.disabled&&(A.isValidating||A.validatingFields||E.isValidating||E.validatingFields)&&((e||Array.from(V.mount)).forEach(e=>{e&&(t?w(a.validatingFields,e,t):B(a.validatingFields,e))}),C.state.next({validatingFields:a.validatingFields,isValidating:!j(a.validatingFields)}))},X=(e,t)=>{w(a.errors,e,t),C.state.next({errors:a.errors})},Y=(e,t,r,a)=>{let i=p(l,e);if(i){let n=p(c,e,h(r)?p(d,e):r);h(n)||a&&a.defaultChecked||t?w(c,e,t?n:G(i._f)):eV(e,n),g.mount&&$()}},Q=(e,t,i,n,s)=>{let l=!1,o=!1,u={name:e};if(!r.disabled){if(!i||n){(A.isDirty||E.isDirty)&&(o=a.isDirty,a.isDirty=u.isDirty=eb(),l=o!==u.isDirty);let r=k(p(d,e),t);o=!!p(a.dirtyFields,e),r?B(a.dirtyFields,e):w(a.dirtyFields,e,!0),u.dirtyFields=a.dirtyFields,l=l||(A.dirtyFields||E.dirtyFields)&&!r!==o}if(i){let t=p(a.touchedFields,e);t||(w(a.touchedFields,e,i),u.touchedFields=a.touchedFields,l=l||(A.touchedFields||E.touchedFields)&&t!==i)}l&&s&&C.state.next(u)}return l?u:{}},er=(e,i,n,s)=>{let l=p(a.errors,e),o=(A.isValid||E.isValid)&&b(i)&&a.isValid!==i;if(r.delayError&&n?(t=q(()=>X(e,n)))(r.delayError):(clearTimeout(F),t=null,n?w(a.errors,e,n):B(a.errors,e)),(n?!k(l,n):l)||!j(s)||o){let t={...s,...o&&b(i)?{isValid:i}:{},errors:a.errors,name:e};a={...a,...t},C.state.next(t)}},ey=async e=>{H(e,!0);let t=await r.resolver(c,r.context,J(e||V.mount,l,r.criteriaMode,r.shouldUseNativeValidation));return H(e),t},ev=async e=>{let{errors:t}=await ey(e);if(e)for(let r of e){let e=p(t,r);e?w(a.errors,r,e):B(a.errors,r)}else a.errors=t;return t},em=async(e,t,i={valid:!0})=>{for(let n in e){let s=e[n];if(s){let{_f:e,...l}=s;if(e){let l=V.array.has(e.name),o=s._f&&ea(s._f);o&&A.validatingFields&&H([n],!0);let u=await eh(s,V.disabled,c,z,r.shouldUseNativeValidation&&!t,l);if(o&&A.validatingFields&&H([n]),u[e.name]&&(i.valid=!1,t))break;t||(p(u,e.name)?l?ec(a.errors,u,e.name):w(a.errors,e.name,u[e.name]):B(a.errors,e.name))}j(l)||await em(l,t,i)}}return i.valid},eb=(e,t)=>!r.disabled&&(e&&t&&w(c,e,t),!k(eE(),d)),eg=(e,t,r)=>S(e,V,{...g.mount?c:h(t)?d:D(e)?{[e]:t}:t},r,t),eV=(e,t,r={})=>{let a=p(l,e),n=t;if(a){let r=a._f;r&&(r.disabled||w(c,e,K(t,r)),n=T(r.ref)&&s(t)?"":t,M(r.ref)?[...r.ref.options].forEach(e=>e.selected=n.includes(e.value)):r.refs?i(r.ref)?r.refs.length>1?r.refs.forEach(e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(n)?!!n.find(t=>t===e.value):n===e.value)):r.refs[0]&&(r.refs[0].checked=!!n):r.refs.forEach(e=>e.checked=e.value===n):P(r.ref)?r.ref.value="":(r.ref.value=n,r.ref.type||C.state.next({name:e,values:v(c)})))}(r.shouldDirty||r.shouldTouch)&&Q(e,n,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&eA(e)},ew=(e,t,r)=>{for(let a in t){let i=t[a],s=`${e}.${a}`,u=p(l,s);(V.array.has(e)||o(i)||u&&!u._f)&&!n(i)?ew(s,i,r):eV(s,i,r)}},e_=(e,t,r={})=>{let i=p(l,e),n=V.array.has(e),o=v(t);w(c,e,o),n?(C.array.next({name:e,values:v(c)}),(A.isDirty||A.dirtyFields||E.isDirty||E.dirtyFields)&&r.shouldDirty&&C.state.next({name:e,dirtyFields:Z(d,c),isDirty:eb(e,o)})):!i||i._f||s(o)?eV(e,o,r):ew(e,o,r),en(e,V)&&C.state.next({...a}),C.state.next({name:g.mount?e:void 0,values:v(c)})},ex=async e=>{g.mount=!0;let i=e.target,s=i.name,o=!0,d=p(l,s),f=e=>{o=Number.isNaN(e)||n(e)&&isNaN(e.getTime())||k(e,p(c,s,e))};if(d){let n,y;let m=i.type?G(d._f):u(e),h=e.type===_.BLUR||e.type===_.FOCUS_OUT,b=!ei(d._f)&&!r.resolver&&!p(a.errors,s)&&!d._f.deps||ed(h,p(a.touchedFields,s),a.isSubmitted,I,N),g=en(s,V,h);w(c,s,m),h?(d._f.onBlur&&d._f.onBlur(e),t&&t(0)):d._f.onChange&&d._f.onChange(e);let x=Q(s,m,h),F=!j(x)||g;if(h||C.state.next({name:s,type:e.type,values:v(c)}),b)return(A.isValid||E.isValid)&&("onBlur"===r.mode?h&&$():h||$()),F&&C.state.next({name:s,...g?{}:x});if(!h&&g&&C.state.next({...a}),r.resolver){let{errors:e}=await ey([s]);if(f(m),o){let t=el(a.errors,l,s),r=el(e,l,t.name||s);n=r.error,s=r.name,y=j(e)}}else H([s],!0),n=(await eh(d,V.disabled,c,z,r.shouldUseNativeValidation))[s],H([s]),f(m),o&&(n?y=!1:(A.isValid||E.isValid)&&(y=await em(l,!0)));o&&(d._f.deps&&eA(d._f.deps),er(s,y,n,x))}},eF=(e,t)=>{if(p(a.errors,t)&&e.focus)return e.focus(),1},eA=async(e,t={})=>{let i,n;let s=O(e);if(r.resolver){let t=await ev(h(e)?e:s);i=j(t),n=e?!s.some(e=>p(t,e)):i}else e?((n=(await Promise.all(s.map(async e=>{let t=p(l,e);return await em(t&&t._f?{[e]:t}:t)}))).every(Boolean))||a.isValid)&&$():n=i=await em(l);return C.state.next({...!D(e)||(A.isValid||E.isValid)&&i!==a.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:a.errors}),t.shouldFocus&&!n&&es(l,eF,e?s:V.mount),n},eE=e=>{let t={...g.mount?c:d};return h(e)?t:D(e)?p(t,e):e.map(e=>p(t,e))},ek=(e,t)=>({invalid:!!p((t||a).errors,e),isDirty:!!p((t||a).dirtyFields,e),error:p((t||a).errors,e),isValidating:!!p(a.validatingFields,e),isTouched:!!p((t||a).touchedFields,e)}),eD=(e,t,r)=>{let i=(p(l,e,{_f:{}})._f||{}).ref,{ref:n,message:s,type:o,...u}=p(a.errors,e)||{};w(a.errors,e,{...u,...t,ref:i}),C.state.next({name:e,errors:a.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},eS=e=>C.state.subscribe({next:t=>{eu(e.name,t.name,e.exact)&&eo(t,e.formState||A,eM,e.reRenderRoot)&&e.callback({values:{...c},...a,...t})}}).unsubscribe,eC=(e,t={})=>{for(let i of e?O(e):V.mount)V.mount.delete(i),V.array.delete(i),t.keepValue||(B(l,i),B(c,i)),t.keepError||B(a.errors,i),t.keepDirty||B(a.dirtyFields,i),t.keepTouched||B(a.touchedFields,i),t.keepIsValidating||B(a.validatingFields,i),r.shouldUnregister||t.keepDefaultValue||B(d,i);C.state.next({values:v(c)}),C.state.next({...a,...t.keepDirty?{isDirty:eb()}:{}}),t.keepIsValid||$()},eO=({disabled:e,name:t})=>{(b(e)&&g.mount||e||V.disabled.has(t))&&(e?V.disabled.add(t):V.disabled.delete(t))},eL=(e,t={})=>{let a=p(l,e),i=b(t.disabled)||b(r.disabled);return w(l,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...t}}),V.mount.add(e),a?eO({disabled:b(t.disabled)?t.disabled:r.disabled,name:e}):Y(e,!0,t.value),{...i?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ee(t.min),max:ee(t.max),minLength:ee(t.minLength),maxLength:ee(t.maxLength),pattern:ee(t.pattern)}:{},name:e,onChange:ex,onBlur:ex,ref:i=>{if(i){eL(e,t),a=p(l,e);let r=h(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,n=U(r),s=a._f.refs||[];(n?s.find(e=>e===r):r===a._f.ref)||(w(l,e,{_f:{...a._f,...n?{refs:[...s.filter(W),r,...Array.isArray(p(d,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),Y(e,!1,void 0,r))}else(a=p(l,e,{}))._f&&(a._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(f(V.array,e)&&g.action)&&V.unMount.add(e)}}},ej=()=>r.shouldFocusError&&es(l,eF,V.mount),eP=(e,t)=>async i=>{let n;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let s=v(c);if(C.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await ey();a.errors=e,s=t}else await em(l);if(V.disabled.size)for(let e of V.disabled)w(s,e,void 0);if(B(a.errors,"root"),j(a.errors)){C.state.next({errors:{}});try{await e(s,i)}catch(e){n=e}}else t&&await t({...a.errors},i),ej(),setTimeout(ej);if(C.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:j(a.errors)&&!n,submitCount:a.submitCount+1,errors:a.errors}),n)throw n},eR=(e,t={})=>{let i=e?v(e):d,n=v(i),s=j(e),o=s?d:n;if(t.keepDefaultValues||(d=i),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...V.mount,...Object.keys(Z(d,c))])))p(a.dirtyFields,e)?w(o,e,p(c,e)):e_(e,p(o,e));else{if(y&&h(e))for(let e of V.mount){let t=p(l,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(T(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of V.mount)e_(e,p(o,e))}c=v(o),C.array.next({values:{...o}}),C.state.next({values:{...o}})}V={mount:t.keepDirtyValues?V.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},g.mount=!A.isValid||!!t.keepIsValid||!!t.keepDirtyValues,g.watch=!!r.shouldUnregister,C.state.next({submitCount:t.keepSubmitCount?a.submitCount:0,isDirty:!s&&(t.keepDirty?a.isDirty:!!(t.keepDefaultValues&&!k(e,d))),isSubmitted:!!t.keepIsSubmitted&&a.isSubmitted,dirtyFields:s?{}:t.keepDirtyValues?t.keepDefaultValues&&c?Z(d,c):a.dirtyFields:t.keepDefaultValues&&e?Z(d,e):t.keepDirty?a.dirtyFields:{},touchedFields:t.keepTouched?a.touchedFields:{},errors:t.keepErrors?a.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&a.isSubmitSuccessful,isSubmitting:!1})},eT=(e,t)=>eR(R(e)?e(c):e,t),eM=e=>{a={...a,...e}},eN={control:{register:eL,unregister:eC,getFieldState:ek,handleSubmit:eP,setError:eD,_subscribe:eS,_runSchema:ey,_getWatch:eg,_getDirty:eb,_setValid:$,_setFieldArray:(e,t=[],i,n,s=!0,o=!0)=>{if(n&&i&&!r.disabled){if(g.action=!0,o&&Array.isArray(p(l,e))){let t=i(p(l,e),n.argA,n.argB);s&&w(l,e,t)}if(o&&Array.isArray(p(a.errors,e))){let t=i(p(a.errors,e),n.argA,n.argB);s&&w(a.errors,e,t),ef(a.errors,e)}if((A.touchedFields||E.touchedFields)&&o&&Array.isArray(p(a.touchedFields,e))){let t=i(p(a.touchedFields,e),n.argA,n.argB);s&&w(a.touchedFields,e,t)}(A.dirtyFields||E.dirtyFields)&&(a.dirtyFields=Z(d,c)),C.state.next({name:e,isDirty:eb(e,t),dirtyFields:a.dirtyFields,errors:a.errors,isValid:a.isValid})}else w(c,e,t)},_setDisabledField:eO,_setErrors:e=>{a.errors=e,C.state.next({errors:a.errors,isValid:!1})},_getFieldArray:e=>m(p(g.mount?c:d,e,r.shouldUnregister?p(d,e,[]):[])),_reset:eR,_resetDefaultValues:()=>R(r.defaultValues)&&r.defaultValues().then(e=>{eT(e,r.resetOptions),C.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of V.unMount){let t=p(l,e);t&&(t._f.refs?t._f.refs.every(e=>!W(e)):!W(t._f.ref))&&eC(e)}V.unMount=new Set},_disableForm:e=>{b(e)&&(C.state.next({disabled:e}),es(l,(t,r)=>{let a=p(l,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach(t=>{t.disabled=a._f.disabled||e}))},0,!1))},_subjects:C,_proxyFormState:A,get _fields(){return l},get _formValues(){return c},get _state(){return g},set _state(value){g=value},get _defaultValues(){return d},get _names(){return V},set _names(value){V=value},get _formState(){return a},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(g.mount=!0,E={...E,...e.formState},eS({...e,formState:E})),trigger:eA,register:eL,handleSubmit:eP,watch:(e,t)=>R(e)?C.state.subscribe({next:r=>e(eg(void 0,t),r)}):eg(e,t,!0),setValue:e_,getValues:eE,reset:eT,resetField:(e,t={})=>{p(l,e)&&(h(t.defaultValue)?e_(e,v(p(d,e))):(e_(e,t.defaultValue),w(d,e,v(t.defaultValue))),t.keepTouched||B(a.touchedFields,e),t.keepDirty||(B(a.dirtyFields,e),a.isDirty=t.defaultValue?eb(e,v(p(d,e))):eb()),!t.keepError&&(B(a.errors,e),A.isValid&&$()),C.state.next({...a}))},clearErrors:e=>{e&&O(e).forEach(e=>B(a.errors,e)),C.state.next({errors:e?a.errors:{}})},unregister:eC,setError:eD,setFocus:(e,t={})=>{let r=p(l,e),a=r&&r._f;if(a){let e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&R(e.select)&&e.select())}},getFieldState:ek};return{...eN,formControl:eN}}(e),formState:l},e.formControl&&e.defaultValues&&!R(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));let c=t.current.control;return c._options=e,eb(()=>{let e=c._subscribe({formState:c._proxyFormState,callback:()=>d({...c._formState}),reRenderRoot:!0});return d(e=>({...e,isReady:!0})),c._formState.isReady=!0,e},[c]),a.useEffect(()=>c._disableForm(e.disabled),[c,e.disabled]),a.useEffect(()=>{e.mode&&(c._options.mode=e.mode),e.reValidateMode&&(c._options.reValidateMode=e.reValidateMode),e.errors&&!j(e.errors)&&c._setErrors(e.errors)},[c,e.errors,e.mode,e.reValidateMode]),a.useEffect(()=>{e.shouldUnregister&&c._subjects.state.next({values:c._getWatch()})},[c,e.shouldUnregister]),a.useEffect(()=>{if(c._proxyFormState.isDirty){let e=c._getDirty();e!==l.isDirty&&c._subjects.state.next({isDirty:e})}},[c,l.isDirty]),a.useEffect(()=>{e.values&&!k(e.values,r.current)?(c._reset(e.values,c._options.resetOptions),r.current=e.values,d(e=>({...e}))):c._resetDefaultValues()},[c,e.values]),a.useEffect(()=>{c._state.mount||(c._setValid(),c._state.mount=!0),c._state.watch&&(c._state.watch=!1,c._subjects.state.next({...c._formState})),c._removeUnmounted()}),t.current.formState=A(l,c),t.current}}}]);