(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6758],{38815:function(e,t,r){Promise.resolve().then(r.bind(r,70016))},81066:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var s=r(2265),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(e,t)=>{let r=(0,s.forwardRef)((r,l)=>{let{color:i="currentColor",size:d=24,strokeWidth:o=2,absoluteStrokeWidth:c,children:u,...m}=r;return(0,s.createElement)("svg",{ref:l,...n,width:d,height:d,stroke:i,strokeWidth:c?24*Number(o)/Number(d):o,className:"lucide lucide-".concat(a(e)),...m},[...t.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...(Array.isArray(u)?u:[u])||[]])});return r.displayName="".concat(e),r}},26032:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},15862:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40933:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},56935:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},97589:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},20500:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},59738:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},70016:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return w}});var s=r(57437),n=r(97589),a=r(20500),l=r(81066);let i=(0,l.Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var d=r(56935);let o=(0,l.Z)("Plug",[["path",{d:"M12 22v-5",key:"1ega77"}],["path",{d:"M9 8V2",key:"14iosj"}],["path",{d:"M15 8V2",key:"18g5xt"}],["path",{d:"M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z",key:"osxo6l"}]]);var c=r(15862),u=r(26032),m=r(59738),f=r(40933);let h=(0,l.Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),x=(0,l.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var p=r(2265),v=r(79055),g=r(89733),y=r(48185),b=r(29973);let j={database:n.Z,auth:a.Z,ai:i,stripe:d.Z,mcp:o},N={healthy:"default",degraded:"secondary",unhealthy:"destructive",unknown:"outline"};function w(){let[e,t]=(0,p.useState)(null),[r,n]=(0,p.useState)(!0),[a,l]=(0,p.useState)(null),[i,d]=(0,p.useState)(!0),o=async()=>{try{n(!0);let e=await fetch("/api/health"),r=await e.json();t(r),l(new Date)}catch(e){console.error("Failed to fetch health data:",e)}finally{n(!1)}};(0,p.useEffect)(()=>{o()},[]),(0,p.useEffect)(()=>{if(!i)return;let e=setInterval(o,3e4);return()=>clearInterval(e)},[i]);let w=e=>{switch(e){case"healthy":return(0,s.jsx)(c.Z,{className:"h-4 w-4 text-green-500"});case"degraded":return(0,s.jsx)(u.Z,{className:"h-4 w-4 text-yellow-500"});case"unhealthy":return(0,s.jsx)(m.Z,{className:"h-4 w-4 text-red-500"});default:return(0,s.jsx)(f.Z,{className:"h-4 w-4 text-gray-500"})}},k=e=>{let t=j[e]||h;return(0,s.jsx)(t,{className:"h-4 w-4"})},Z=e=>e<1e3?"".concat(e,"ms"):"".concat((e/1e3).toFixed(2),"s"),C=e=>e<100?"Excelente":e<500?"Bom":e<1e3?"Regular":e<3e3?"Lento":"Cr\xedtico";return(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"\uD83C\uDFE5 Health Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Monitoramento em tempo real da sa\xfade do sistema"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(g.Button,{variant:"outline",size:"sm",onClick:()=>d(!i),children:[i?"Pausar":"Retomar"," Auto-refresh"]}),(0,s.jsxs)(g.Button,{variant:"outline",size:"sm",onClick:o,disabled:r,children:[(0,s.jsx)(x,{className:"h-4 w-4 mr-2 ".concat(r?"animate-spin":"")}),"Atualizar"]})]})]}),e&&(0,s.jsxs)(y.Zb,{children:[(0,s.jsxs)(y.Ol,{children:[(0,s.jsxs)(y.ll,{className:"flex items-center gap-2",children:[w(e.overall),"Status Geral do Sistema"]}),(0,s.jsxs)(y.SZ,{children:["\xdaltima atualiza\xe7\xe3o: ",(null==a?void 0:a.toLocaleString())||"Nunca"]})]}),(0,s.jsx)(y.aY,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.summary.healthy}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Saud\xe1veis"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:e.summary.degraded}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Degradados"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:e.summary.unhealthy}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"N\xe3o Saud\xe1veis"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:Z(e.responseTime)}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Tempo Total"})]})]})})]}),e&&(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.services.map(e=>(0,s.jsxs)(y.Zb,{children:[(0,s.jsx)(y.Ol,{className:"pb-3",children:(0,s.jsxs)(y.ll,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[k(e.service),(0,s.jsx)("span",{className:"capitalize",children:e.service})]}),w(e.status)]})}),(0,s.jsxs)(y.aY,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Status:"}),(0,s.jsx)(v.C,{variant:N[e.status],children:e.status})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Tempo de Resposta:"}),(0,s.jsx)("span",{className:"text-sm font-mono",children:Z(e.responseTime)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Performance:"}),(0,s.jsx)("span",{className:"text-sm",children:C(e.responseTime)})]}),e.details&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.Z,{}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Detalhes:"}),e.details.message&&(0,s.jsx)("div",{className:"text-xs",children:e.details.message}),e.details.mode&&(0,s.jsxs)("div",{className:"text-xs",children:["Modo: ",e.details.mode]}),e.details.providers&&(0,s.jsxs)("div",{className:"text-xs",children:["Providers: ",e.details.providers.join(", ")]})]})]})]})]},e.service))}),r&&!e&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(x,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Carregando dados de sa\xfade..."})]}),!r&&!e&&(0,s.jsx)(y.Zb,{children:(0,s.jsx)(y.aY,{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(m.Z,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Erro ao carregar dados de sa\xfade"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"N\xe3o foi poss\xedvel conectar com os servi\xe7os de health check."}),(0,s.jsx)(g.Button,{onClick:o,children:"Tentar Novamente"})]})})}),(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[(0,s.jsx)("p",{children:"Dashboard de Health Checks • Excel Copilot v1.0.0"}),(0,s.jsx)("p",{children:"Atualiza\xe7\xe3o autom\xe1tica a cada 30 segundos"})]})]})}},79055:function(e,t,r){"use strict";r.d(t,{C:function(){return i}});var s=r(57437),n=r(13027);r(2265);var a=r(49354);let l=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...n}=e;return(0,s.jsx)("div",{className:(0,a.cn)(l({variant:r}),t),...n})}},89733:function(e,t,r){"use strict";r.d(t,{Button:function(){return u},d:function(){return c}});var s=r(57437),n=r(71538),a=r(13027),l=r(847),i=r(2265),d=r(18043),o=r(49354);let c=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),u=i.forwardRef((e,t)=>{let{className:r,variant:a,size:i,rounded:u,cssFeedback:m,asChild:f=!1,animated:h=!1,icon:x,iconPosition:p="left",children:v,...g}=e,y=f?n.g7:"button",b=(0,s.jsxs)("span",{className:"inline-flex items-center justify-center",children:[x&&"left"===p&&(0,s.jsx)("span",{className:"mr-2",children:x}),v,x&&"right"===p&&(0,s.jsx)("span",{className:"ml-2",children:x})]});if(h){let e={whileTap:{scale:.97},whileHover:["link","ghost"].includes(a)?void 0:{y:-2},transition:{duration:.67*d.zn,ease:d.d}},n=(0,o.cn)(c({variant:a,size:i,rounded:u,cssFeedback:"none",className:r})),m={...g,className:n,...e};return(0,s.jsx)(l.E.button,{ref:t,...m,children:b})}return(0,s.jsx)(y,{className:(0,o.cn)(c({variant:a,size:i,rounded:u,cssFeedback:m,className:r})),ref:t,...g,children:b})});u.displayName="Button"},48185:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return u},Zb:function(){return d},aY:function(){return m},eW:function(){return f},ll:function(){return c}});var s=r(57437),n=r(847),a=r(2265),l=r(18043),i=r(49354);let d=(0,a.forwardRef)((e,t)=>{let{className:r,children:a,hoverable:d=!1,variant:o="default",noPadding:c=!1,animated:u=!1,...m}=e,f=(0,i.cn)("rounded-xl border shadow-sm",{"p-6":!c,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":d&&!u,"border-border bg-card":"default"===o,"border-border/50 bg-transparent":"outline"===o,"bg-card/90 backdrop-blur-md border-border/50":"glass"===o,"bg-gradient-primary text-primary-foreground border-none":"gradient"===o},r);return u?(0,s.jsx)(n.E.div,{ref:t,className:f,...(0,l.Ph)("card"),whileHover:d?l.q.hover:void 0,whileTap:d?l.q.tap:void 0,...m,children:a}):(0,s.jsx)("div",{ref:t,className:f,...m,children:a})});d.displayName="Card";let o=(0,a.forwardRef)((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("mb-4 flex flex-col space-y-1.5",r),...n})});o.displayName="CardHeader";let c=(0,a.forwardRef)((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",r),...n})});c.displayName="CardTitle";let u=(0,a.forwardRef)((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...n})});u.displayName="CardDescription";let m=(0,a.forwardRef)((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("card-content",r),...n})});m.displayName="CardContent";let f=(0,a.forwardRef)((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center pt-4 mt-auto",r),...n})});f.displayName="CardFooter"},29973:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var s=r(57437),n=r(15167),a=r(2265),l=r(49354);let i=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:i=!0,...d}=e;return(0,s.jsx)(n.f,{ref:t,decorative:i,orientation:a,className:(0,l.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...d})});i.displayName=n.f.displayName},15167:function(e,t,r){"use strict";r.d(t,{f:function(){return m}});var s=r(2265);r(54887);var n=r(1584),a=r(57437),l=Symbol("radix.slottable");function i(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,d=function(e,t){let r={...t};for(let s in t){let n=e[s],a=t[s];/^on[A-Z]/.test(s)?n&&a?r[s]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...a}:"className"===s&&(r[s]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==s.Fragment&&(d.ref=t?(0,n.F)(t,i):i),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...l}=e,d=s.Children.toArray(n),o=d.find(i);if(o){let e=o.props.children,n=d.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...l,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),l=s.forwardRef((e,s)=>{let{asChild:n,...l}=e,i=n?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...l,ref:s})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),o="horizontal",c=["horizontal","vertical"],u=s.forwardRef((e,t)=>{let{decorative:r,orientation:s=o,...n}=e,l=c.includes(s)?s:o;return(0,a.jsx)(d.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...n,ref:t})});u.displayName="Separator";var m=u},13027:function(e,t,r){"use strict";r.d(t,{j:function(){return l}});var s=r(44839);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=s.W,l=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,d=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],s=null==i?void 0:i[e];if(null===t)return null;let a=n(t)||n(s);return l[e][a]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return a(e,d,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...o}[t]):({...i,...o})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[7142,8638,8194,2971,7023,1744],function(){return e(e.s=38815)}),_N_E=e.O()}]);