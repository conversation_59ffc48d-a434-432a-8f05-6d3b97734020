exports.id=5609,exports.ids=[5609],exports.modules={43183:(e,t)=>{"use strict";/*!
 * cookie
 * Copyright(c) 2012-2014 <PERSON>
 * Copyright(c) 2015 <PERSON>
 * MIT Licensed
 */t.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},o=e.length;if(o<2)return r;var i=t&&t.decode||c,s=0,a=0,d=0;do{if(-1===(a=e.indexOf("=",s)))break;if(-1===(d=e.indexOf(";",s)))d=o;else if(a>d){s=e.lastIndexOf(";",a-1)+1;continue}var p=l(e,s,a),h=u(e,a,p),f=e.slice(p,h);if(!n.call(r,f)){var _=l(e,a+1,d),g=u(e,d,_);34===e.charCodeAt(_)&&34===e.charCodeAt(g-1)&&(_++,g--);var y=e.slice(_,g);r[f]=function(e,t){try{return t(e)}catch(t){return e}}(y,i)}s=d+1}while(s<o);return r},t.serialize=function(e,t,n){var l=n&&n.encode||encodeURIComponent;if("function"!=typeof l)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var u=l(t);if(!i.test(u))throw TypeError("argument val is invalid");var c=e+"="+u;if(!n)return c;if(null!=n.maxAge){var d=Math.floor(n.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");c+="; Max-Age="+d}if(n.domain){if(!s.test(n.domain))throw TypeError("option domain is invalid");c+="; Domain="+n.domain}if(n.path){if(!a.test(n.path))throw TypeError("option path is invalid");c+="; Path="+n.path}if(n.expires){var p=n.expires;if("[object Date]"!==r.call(p)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");c+="; Expires="+p.toUTCString()}if(n.httpOnly&&(c+="; HttpOnly"),n.secure&&(c+="; Secure"),n.partitioned&&(c+="; Partitioned"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var r=Object.prototype.toString,n=Object.prototype.hasOwnProperty,o=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/;function l(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function u(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function c(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},85793:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.UnsupportedStrategy=t.UnknownError=t.OAuthCallbackError=t.MissingSecret=t.MissingAuthorize=t.MissingAdapterMethods=t.MissingAdapter=t.MissingAPIRoute=t.InvalidCallbackUrl=t.AccountNotLinkedError=void 0,t.adapterErrorHandler=function(e,t){if(e)return Object.keys(e).reduce(function(r,n){return r[n]=(0,i.default)(o.default.mark(function r(){var i,s,a,l,u,c=arguments;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:for(r.prev=0,s=Array(i=c.length),a=0;a<i;a++)s[a]=c[a];return t.debug("adapter_".concat(n),{args:s}),l=e[n],r.next=6,l.apply(void 0,s);case 6:return r.abrupt("return",r.sent);case 9:throw r.prev=9,r.t0=r.catch(0),t.error("adapter_error_".concat(n),r.t0),(u=new _(r.t0)).name="".concat(y(n),"Error"),u;case 15:case"end":return r.stop()}},r,null,[[0,9]])})),r},{})},t.capitalize=y,t.eventsErrorHandler=function(e,t){return Object.keys(e).reduce(function(r,n){return r[n]=(0,i.default)(o.default.mark(function r(){var i,s=arguments;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,i=e[n],r.next=4,i.apply(void 0,s);case 4:return r.abrupt("return",r.sent);case 7:r.prev=7,r.t0=r.catch(0),t.error("".concat(g(n),"_EVENT_ERROR"),r.t0);case 10:case"end":return r.stop()}},r,null,[[0,7]])})),r},{})},t.upperSnake=g;var o=n(r(81213)),i=n(r(85577)),s=n(r(33679)),a=n(r(8908)),l=n(r(85925)),u=n(r(64702)),c=n(r(83968)),d=n(r(23006)),p=n(r(16041));function h(e,t,r){return t=(0,c.default)(t),(0,u.default)(e,f()?Reflect.construct(t,r||[],(0,c.default)(e).constructor):t.apply(e,r))}function f(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(f=function(){return!!e})()}var _=t.UnknownError=function(e){function t(e){var r,n;return(0,a.default)(this,t),(n=h(this,t,[null!==(r=null==e?void 0:e.message)&&void 0!==r?r:e])).name="UnknownError",n.code=e.code,e instanceof Error&&(n.stack=e.stack),n}return(0,d.default)(t,e),(0,l.default)(t,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,stack:this.stack}}}])}((0,p.default)(Error));function g(e){return e.replace(/([A-Z])/g,"_$1").toUpperCase()}function y(e){return"".concat(e[0].toUpperCase()).concat(e.slice(1))}t.OAuthCallbackError=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","OAuthCallbackError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.AccountNotLinkedError=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","AccountNotLinkedError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.MissingAPIRoute=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAPIRouteError"),(0,s.default)(e,"code","MISSING_NEXTAUTH_API_ROUTE_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.MissingSecret=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","MissingSecretError"),(0,s.default)(e,"code","NO_SECRET"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.MissingAuthorize=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAuthorizeError"),(0,s.default)(e,"code","CALLBACK_CREDENTIALS_HANDLER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.MissingAdapter=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAdapterError"),(0,s.default)(e,"code","EMAIL_REQUIRES_ADAPTER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.MissingAdapterMethods=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","MissingAdapterMethodsError"),(0,s.default)(e,"code","MISSING_ADAPTER_METHODS_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.UnsupportedStrategy=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","UnsupportedStrategyError"),(0,s.default)(e,"code","CALLBACK_CREDENTIALS_JWT_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_),t.InvalidCallbackUrl=function(e){function t(){var e;(0,a.default)(this,t);for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=h(this,t,[].concat(n)),(0,s.default)(e,"name","InvalidCallbackUrl"),(0,s.default)(e,"code","INVALID_CALLBACK_URL_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(_)},5328:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.AuthHandler=g;var o=h(r(80878)),i=r(35144),s=h(r(83081)),a=n(r(45606)),l=r(15663),u=r(76790),c=r(33656),d=r(43183);function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(p=function(e){return e?r:t})(e)}function h(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=p(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}async function f(e){try{return await e.json()}catch(e){}}async function _(e){var t,r,n,o;if(e instanceof Request){let t=new URL(e.url),s=t.pathname.split("/").slice(3),a=Object.fromEntries(e.headers),l=Object.fromEntries(t.searchParams);return l.nextauth=s,{action:s[0],method:e.method,headers:a,body:await f(e),cookies:(0,d.parse)(null!==(r=e.headers.get("cookie"))&&void 0!==r?r:""),providerId:s[1],error:null!==(n=t.searchParams.get("error"))&&void 0!==n?n:s[1],origin:(0,i.detectOrigin)(null!==(o=a["x-forwarded-host"])&&void 0!==o?o:a.host,a["x-forwarded-proto"]),query:l}}let{headers:s}=e,a=null!==(t=null==s?void 0:s["x-forwarded-host"])&&void 0!==t?t:null==s?void 0:s.host;return e.origin=(0,i.detectOrigin)(a,null==s?void 0:s["x-forwarded-proto"]),e}async function g(e){var t,r,n,i,d,p,h;let{options:f,req:g}=e,y=await _(g);(0,o.setLogger)(f.logger,f.debug);let v=(0,u.assertConfig)({options:f,req:y});if(Array.isArray(v))v.forEach(o.default.warn);else if(v instanceof Error){if(o.default.error(v.code,v),!["signin","signout","error","verify-request"].includes(y.action)||"GET"!==y.method)return{status:500,headers:[{key:"Content-Type",value:"application/json"}],body:{message:"There is a problem with the server configuration. Check the server logs for more information."}};let{pages:e,theme:t}=f,r=(null==e?void 0:e.error)&&(null===(d=y.query)||void 0===d||null===(d=d.callbackUrl)||void 0===d?void 0:d.startsWith(e.error));return!(null!=e&&e.error)||r?(r&&o.default.error("AUTH_ON_ERROR_PAGE_ERROR",Error(`The error page ${null==e?void 0:e.error} should not require authentication`)),(0,a.default)({theme:t}).error({error:"configuration"})):{redirect:`${e.error}?error=Configuration`}}let{action:m,providerId:w,error:b,method:k="GET"}=y,{options:x,cookies:A}=await (0,l.init)({authOptions:f,action:m,providerId:w,origin:y.origin,callbackUrl:null!==(t=null===(r=y.body)||void 0===r?void 0:r.callbackUrl)&&void 0!==t?t:null===(n=y.query)||void 0===n?void 0:n.callbackUrl,csrfToken:null===(i=y.body)||void 0===i?void 0:i.csrfToken,cookies:y.cookies,isPost:"POST"===k}),S=new c.SessionStore(x.cookies.sessionToken,y,x.logger);if("GET"===k){let e=(0,a.default)({...x,query:y.query,cookies:A}),{pages:t}=x;switch(m){case"providers":return await s.providers(x.providers);case"session":{let e=await s.session({options:x,sessionStore:S});return e.cookies&&A.push(...e.cookies),{...e,cookies:A}}case"csrf":return{headers:[{key:"Content-Type",value:"application/json"}],body:{csrfToken:x.csrfToken},cookies:A};case"signin":if(t.signIn){let e=`${t.signIn}${t.signIn.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(x.callbackUrl)}`;return b&&(e=`${e}&error=${encodeURIComponent(b)}`),{redirect:e,cookies:A}}return e.signin();case"signout":if(t.signOut)return{redirect:t.signOut,cookies:A};return e.signout();case"callback":if(x.provider){let e=await s.callback({body:y.body,query:y.query,headers:y.headers,cookies:y.cookies,method:k,options:x,sessionStore:S});return e.cookies&&A.push(...e.cookies),{...e,cookies:A}}break;case"verify-request":if(t.verifyRequest)return{redirect:t.verifyRequest,cookies:A};return e.verifyRequest();case"error":if(["Signin","OAuthSignin","OAuthCallback","OAuthCreateAccount","EmailCreateAccount","Callback","OAuthAccountNotLinked","EmailSignin","CredentialsSignin","SessionRequired"].includes(b))return{redirect:`${x.url}/signin?error=${b}`,cookies:A};if(t.error)return{redirect:`${t.error}${t.error.includes("?")?"&":"?"}error=${b}`,cookies:A};return e.error({error:b})}}else if("POST"===k)switch(m){case"signin":if(x.csrfTokenVerified&&x.provider){let e=await s.signin({query:y.query,body:y.body,options:x});return e.cookies&&A.push(...e.cookies),{...e,cookies:A}}return{redirect:`${x.url}/signin?csrf=true`,cookies:A};case"signout":if(x.csrfTokenVerified){let e=await s.signout({options:x,sessionStore:S});return e.cookies&&A.push(...e.cookies),{...e,cookies:A}}return{redirect:`${x.url}/signout?csrf=true`,cookies:A};case"callback":if(x.provider){if("credentials"===x.provider.type&&!x.csrfTokenVerified)return{redirect:`${x.url}/signin?csrf=true`,cookies:A};let e=await s.callback({body:y.body,query:y.query,headers:y.headers,cookies:y.cookies,method:k,options:x,sessionStore:S});return e.cookies&&A.push(...e.cookies),{...e,cookies:A}}break;case"_log":if(f.logger)try{let{code:e,level:t,...r}=null!==(p=y.body)&&void 0!==p?p:{};o.default[t](e,r)}catch(e){o.default.error("LOGGER_ERROR",e)}return{};case"session":if(x.csrfTokenVerified){let e=await s.session({options:x,sessionStore:S,newSession:null===(h=y.body)||void 0===h?void 0:h.data,isUpdate:!0});return e.cookies&&A.push(...e.cookies),{...e,cookies:A}}return{status:400,body:{},cookies:A}}return{status:400,body:`Error: This action with HTTP ${k} is not supported by NextAuth.js`}}},15663:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.init=y;var o=r(6113),i=n(r(80878)),s=r(85793),a=n(r(82355)),l=r(95627),u=g(r(33656)),c=g(r(20330)),d=r(11515),p=r(26197),h=r(77558),f=n(r(33768));function _(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_=function(e){return e?r:t})(e)}function g(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=_(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}async function y({authOptions:e,providerId:t,action:r,origin:n,cookies:_,callbackUrl:g,csrfToken:y,isPost:v}){var m,w;let b=(0,f.default)(n),k=(0,l.createSecret)({authOptions:e,url:b}),{providers:x,provider:A}=(0,a.default)({providers:e.providers,url:b,providerId:t}),S={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:b,action:r,provider:A,cookies:{...u.defaultCookies(null!==(m=e.useSecureCookies)&&void 0!==m?m:b.base.startsWith("https://")),...e.cookies},secret:k,providers:x,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>{var e;return null!==(e=null===o.randomUUID||void 0===o.randomUUID?void 0:(0,o.randomUUID)())&&void 0!==e?e:(0,o.randomBytes)(32).toString("hex")},...e.session},jwt:{secret:k,maxAge:2592e3,encode:c.encode,decode:c.decode,...e.jwt},events:(0,s.eventsErrorHandler)(null!==(w=e.events)&&void 0!==w?w:{},i.default),adapter:(0,s.adapterErrorHandler)(e.adapter,i.default),callbacks:{...d.defaultCallbacks,...e.callbacks},logger:i.default,callbackUrl:b.origin},E=[],{csrfToken:O,cookie:T,csrfTokenVerified:j}=(0,p.createCSRFToken)({options:S,cookieValue:null==_?void 0:_[S.cookies.csrfToken.name],isPost:v,bodyValue:y});S.csrfToken=O,S.csrfTokenVerified=j,T&&E.push({name:S.cookies.csrfToken.name,value:T,options:S.cookies.csrfToken.options});let{callbackUrl:P,callbackUrlCookie:C}=await (0,h.createCallbackUrl)({options:S,cookieValue:null==_?void 0:_[S.cookies.callbackUrl.name],paramValue:g});return S.callbackUrl=P,C&&E.push({name:S.cookies.callbackUrl.name,value:C,options:S.cookies.callbackUrl.options}),{options:S,cookies:E}}},76790:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.assertConfig=function(e){var t,r,n,u,c,d,p;let h,f,_;let{options:g,req:y}=e,v=[];if(!a&&(y.origin||v.push("NEXTAUTH_URL"),g.secret,g.debug&&v.push("DEBUG_ENABLED")),!g.secret)return new o.MissingSecret("Please define a `secret` in production.");if(!(null!==(t=y.query)&&void 0!==t&&t.nextauth)&&!y.action)return new o.MissingAPIRoute("Cannot find [...nextauth].{js,ts} in `/pages/api/auth`. Make sure the filename is written correctly.");let m=null===(r=y.query)||void 0===r?void 0:r.callbackUrl,w=(0,i.default)(y.origin);if(m&&!l(m,w.base))return new o.InvalidCallbackUrl(`Invalid callback URL. Received: ${m}`);let{callbackUrl:b}=(0,s.defaultCookies)(null!==(n=g.useSecureCookies)&&void 0!==n?n:w.base.startsWith("https://")),k=null===(u=y.cookies)||void 0===u?void 0:u[null!==(c=null===(d=g.cookies)||void 0===d||null===(d=d.callbackUrl)||void 0===d?void 0:d.name)&&void 0!==c?c:b.name];if(k&&!l(k,w.base))return new o.InvalidCallbackUrl(`Invalid callback URL. Received: ${k}`);for(let e of g.providers)"credentials"===e.type?h=!0:"email"===e.type?f=!0:"twitter"===e.id&&"2.0"===e.version&&(_=!0);if(h){let e=(null===(p=g.session)||void 0===p?void 0:p.strategy)==="database",t=!g.providers.some(e=>"credentials"!==e.type);if(e&&t)return new o.UnsupportedStrategy("Signin in with credentials only supported if JWT strategy is enabled");if(g.providers.some(e=>"credentials"===e.type&&!e.authorize))return new o.MissingAuthorize("Must define an authorize() handler to use credentials authentication provider")}if(f){let{adapter:e}=g;if(!e)return new o.MissingAdapter("E-mail login requires an adapter.");let t=["createVerificationToken","useVerificationToken","getUserByEmail"].filter(t=>!e[t]);if(t.length)return new o.MissingAdapterMethods(`Required adapter methods were missing: ${t.join(", ")}`)}return a||(_&&v.push("TWITTER_OAUTH_2_BETA"),a=!0),v};var o=r(85793),i=n(r(33768)),s=r(33656);let a=!1;function l(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch(e){return!1}}},93024:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=i;var n=r(85793),o=r(95627);async function i(e){var t,r,i,s,a,l;let{sessionToken:u,profile:c,account:d,options:p}=e;if(!(null!=d&&d.providerAccountId)||!d.type)throw Error("Missing or invalid provider account");if(!["email","oauth"].includes(d.type))throw Error("Provider not supported");let{adapter:h,jwt:f,events:_,session:{strategy:g,generateSessionToken:y}}=p;if(!h)return{user:c,account:d};let{createUser:v,updateUser:m,getUser:w,getUserByAccount:b,getUserByEmail:k,linkAccount:x,createSession:A,getSessionAndUser:S,deleteSession:E}=h,O=null,T=null,j=!1,P="jwt"===g;if(u){if(P)try{(O=await f.decode({...f,token:u}))&&"sub"in O&&O.sub&&(T=await w(O.sub))}catch(e){}else{let e=await S(u);e&&(O=e.session,T=e.user)}}if("email"===d.type){let e=await k(c.email);if(e)(null===(t=T)||void 0===t?void 0:t.id)!==e.id&&!P&&u&&await E(u),T=await m({id:e.id,emailVerified:new Date}),await (null===(r=_.updateUser)||void 0===r?void 0:r.call(_,{user:T}));else{let{id:e,...t}={...c,emailVerified:new Date};T=await v(t),await (null===(i=_.createUser)||void 0===i?void 0:i.call(_,{user:T})),j=!0}return{session:O=P?{}:await A({sessionToken:await y(),userId:T.id,expires:(0,o.fromDate)(p.session.maxAge)}),user:T,isNewUser:j}}if("oauth"===d.type){let e=await b({providerAccountId:d.providerAccountId,provider:d.provider});if(e){if(T){if(e.id===T.id)return{session:O,user:T,isNewUser:j};throw new n.AccountNotLinkedError("The account is already associated with another user")}return{session:O=P?{}:await A({sessionToken:await y(),userId:e.id,expires:(0,o.fromDate)(p.session.maxAge)}),user:e,isNewUser:j}}{if(T)return await x({...d,userId:T.id}),await (null===(l=_.linkAccount)||void 0===l?void 0:l.call(_,{user:T,account:d,profile:c})),{session:O,user:T,isNewUser:j};let e=c.email?await k(c.email):null;if(e){let t=p.provider;if(null!=t&&t.allowDangerousEmailAccountLinking)T=e;else throw new n.AccountNotLinkedError("Another account already exists with the same e-mail address")}else{let{id:e,...t}={...c,emailVerified:null};T=await v(t)}return await (null===(s=_.createUser)||void 0===s?void 0:s.call(_,{user:T})),await x({...d,userId:T.id}),await (null===(a=_.linkAccount)||void 0===a?void 0:a.call(_,{user:T,account:d,profile:c})),{session:O=P?{}:await A({sessionToken:await y(),userId:T.id,expires:(0,o.fromDate)(p.session.maxAge)}),user:T,isNewUser:!0}}}throw Error("Unsupported account type")}},77558:(e,t)=>{"use strict";async function r({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:o}=e,i=n.origin;return t?i=await o.redirect({url:t,baseUrl:n.origin}):r&&(i=await o.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}Object.defineProperty(t,"__esModule",{value:!0}),t.createCallbackUrl=r},26197:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createCSRFToken=function({options:e,cookieValue:t,isPost:r,bodyValue:o}){if(t){let[i,s]=t.split("|");if(s===(0,n.createHash)("sha256").update(`${i}${e.secret}`).digest("hex"))return{csrfTokenVerified:r&&i===o,csrfToken:i}}let i=(0,n.randomBytes)(32).toString("hex"),s=(0,n.createHash)("sha256").update(`${i}${e.secret}`).digest("hex");return{cookie:`${i}|${s}`,csrfToken:i}};var n=r(6113)},11515:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultCallbacks=void 0,t.defaultCallbacks={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>e,jwt:({token:e})=>e}},5905:(e,t)=>{"use strict";async function r({email:e,adapter:t}){let{getUserByEmail:r}=t;return(e?await r(e):null)||{id:e,email:e,emailVerified:null}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},32093:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=i;var n=r(6113),o=r(95627);async function i(e,t){var r,i,s,a;let{url:l,adapter:u,provider:c,callbackUrl:d,theme:p}=t,h=null!==(r=await (null===(i=c.generateVerificationToken)||void 0===i?void 0:i.call(c)))&&void 0!==r?r:(0,n.randomBytes)(32).toString("hex"),f=new Date(Date.now()+(null!==(s=c.maxAge)&&void 0!==s?s:86400)*1e3),_=new URLSearchParams({callbackUrl:d,token:h,email:e}),g=`${l}/callback/${c.id}?${_}`;return await Promise.all([c.sendVerificationRequest({identifier:e,token:h,expires:f,url:g,provider:c,theme:p}),null===(a=u.createVerificationToken)||void 0===a?void 0:a.call(u,{identifier:e,token:(0,o.hashToken)(h,t),expires:f})]),`${l}/verify-request?${new URLSearchParams({provider:c.id,type:c.type})}`}},75971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=a;var n=r(35282),o=r(78863),i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(5880));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}async function a({options:e,query:t}){var r,s,a;let{logger:l,provider:u}=e,c={};if("string"==typeof u.authorization){let e=Object.fromEntries(new URL(u.authorization).searchParams);c={...c,...e}}else c={...c,...null===(s=u.authorization)||void 0===s?void 0:s.params};if(c={...c,...t},null!==(r=u.version)&&void 0!==r&&r.startsWith("1.")){let t=(0,o.oAuth1Client)(e),r=await t.getOAuthRequestToken(c),n=`${null===(a=u.authorization)||void 0===a?void 0:a.url}?${new URLSearchParams({oauth_token:r.oauth_token,oauth_token_secret:r.oauth_token_secret,...r.params})}`;return o.oAuth1TokenStore.set(r.oauth_token,r.oauth_token_secret),l.debug("GET_AUTHORIZATION_URL",{url:n,provider:u}),{redirect:n}}let d=await (0,n.openidClient)(e),p=c,h=[];await i.state.create(e,h,p),await i.pkce.create(e,h,p),await i.nonce.create(e,h,p);let f=d.authorizationUrl(p);return l.debug("GET_AUTHORIZATION_URL",{url:f,cookies:h,provider:u}),{redirect:f,cookies:h}}},88302:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=u;var n=r(77838),o=r(35282),i=r(78863),s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=l(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(5880)),a=r(85793);function l(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(l=function(e){return e?r:t})(e)}async function u(e){var t,r,l,u,d,p;let{options:h,query:f,body:_,method:g,cookies:y}=e,{logger:v,provider:m}=h,w=null!==(t=null==_?void 0:_.error)&&void 0!==t?t:null==f?void 0:f.error;if(w){let e=Error(w);throw v.error("OAUTH_CALLBACK_HANDLER_ERROR",{error:e,error_description:null==f?void 0:f.error_description,providerId:m.id}),v.debug("OAUTH_CALLBACK_HANDLER_ERROR",{body:_}),e}if(null!==(r=m.version)&&void 0!==r&&r.startsWith("1."))try{let e=await (0,i.oAuth1Client)(h),{oauth_token:t,oauth_verifier:r}=null!=f?f:{},n=await e.getOAuthAccessToken(t,i.oAuth1TokenStore.get(t),r),o=await e.get(m.profileUrl,n.oauth_token,n.oauth_token_secret);return"string"==typeof o&&(o=JSON.parse(o)),{...await c({profile:o,tokens:n,provider:m,logger:v}),cookies:[]}}catch(e){throw v.error("OAUTH_V1_GET_ACCESS_TOKEN_ERROR",e),e}null!=f&&f.oauth_token&&i.oAuth1TokenStore.delete(f.oauth_token);try{let e,t;let r=await (0,o.openidClient)(h),i={},a=[];await s.state.use(y,a,h,i),await s.pkce.use(y,a,h,i),await s.nonce.use(y,a,h,i);let w={...r.callbackParams({url:`http://n?${new URLSearchParams(f)}`,body:_,method:g}),...null===(l=m.token)||void 0===l?void 0:l.params};if(null!==(u=m.token)&&void 0!==u&&u.request){let t=await m.token.request({provider:m,params:w,checks:i,client:r});e=new n.TokenSet(t.tokens)}else e=m.idToken?await r.callback(m.callbackUrl,w,i):await r.oauthCallback(m.callbackUrl,w,i);return Array.isArray(e.scope)&&(e.scope=e.scope.join(" ")),t=null!==(d=m.userinfo)&&void 0!==d&&d.request?await m.userinfo.request({provider:m,tokens:e,client:r}):m.idToken?e.claims():await r.userinfo(e,{params:null===(p=m.userinfo)||void 0===p?void 0:p.params}),{...await c({profile:t,provider:m,tokens:e,logger:v}),cookies:a}}catch(e){throw new a.OAuthCallbackError(e)}}async function c({profile:e,tokens:t,provider:r,logger:n}){try{var o;n.debug("PROFILE_DATA",{OAuthProfile:e});let i=await r.profile(e,t);if(i.email=null===(o=i.email)||void 0===o?void 0:o.toLowerCase(),!i.id)throw TypeError(`Profile id is missing in ${r.name} OAuth profile response`);return{profile:i,account:{provider:r.id,type:r.type,providerAccountId:i.id.toString(),...t},OAuthProfile:e}}catch(t){n.error("OAUTH_PARSE_PROFILE_ERROR",{error:t,OAuthProfile:e})}}},5880:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pkce=t.nonce=t.PKCE_CODE_CHALLENGE_METHOD=void 0,t.signCookie=s,t.state=void 0;var n=r(77838),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var a=o?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(n,s,a):n[s]=e[s]}return n.default=e,r&&r.set(e,n),n}(r(20330));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}async function s(e,t,r,n){let{cookies:i,logger:s}=n;s.debug(`CREATE_${e.toUpperCase()}`,{value:t,maxAge:r});let{name:a}=i[e],l=new Date;return l.setTime(l.getTime()+1e3*r),{name:a,value:await o.encode({...n.jwt,maxAge:r,token:{value:t},salt:a}),options:{...i[e].options,expires:l}}}let a=t.PKCE_CODE_CHALLENGE_METHOD="S256";t.pkce={async create(e,t,r){var o,i;if(!(null!==(o=e.provider)&&void 0!==o&&null!==(o=o.checks)&&void 0!==o&&o.includes("pkce")))return;let l=n.generators.codeVerifier(),u=n.generators.codeChallenge(l);r.code_challenge=u,r.code_challenge_method=a;let c=null!==(i=e.cookies.pkceCodeVerifier.options.maxAge)&&void 0!==i?i:900;t.push(await s("pkceCodeVerifier",l,c,e))},async use(e,t,r,n){var i;if(!(null!==(i=r.provider)&&void 0!==i&&null!==(i=i.checks)&&void 0!==i&&i.includes("pkce")))return;let s=null==e?void 0:e[r.cookies.pkceCodeVerifier.name];if(!s)throw TypeError("PKCE code_verifier cookie was missing.");let{name:a}=r.cookies.pkceCodeVerifier,l=await o.decode({...r.jwt,token:s,salt:a});if(!(null!=l&&l.value))throw TypeError("PKCE code_verifier value could not be parsed.");t.push({name:a,value:"",options:{...r.cookies.pkceCodeVerifier.options,maxAge:0}}),n.code_verifier=l.value}},t.state={async create(e,t,r){var o,i;if(!(null!==(o=e.provider.checks)&&void 0!==o&&o.includes("state")))return;let a=n.generators.state();r.state=a;let l=null!==(i=e.cookies.state.options.maxAge)&&void 0!==i?i:900;t.push(await s("state",a,l,e))},async use(e,t,r,n){var i;if(!(null!==(i=r.provider.checks)&&void 0!==i&&i.includes("state")))return;let s=null==e?void 0:e[r.cookies.state.name];if(!s)throw TypeError("State cookie was missing.");let{name:a}=r.cookies.state,l=await o.decode({...r.jwt,token:s,salt:a});if(!(null!=l&&l.value))throw TypeError("State value could not be parsed.");t.push({name:a,value:"",options:{...r.cookies.state.options,maxAge:0}}),n.state=l.value}},t.nonce={async create(e,t,r){var o,i;if(!(null!==(o=e.provider.checks)&&void 0!==o&&o.includes("nonce")))return;let a=n.generators.nonce();r.nonce=a;let l=null!==(i=e.cookies.nonce.options.maxAge)&&void 0!==i?i:900;t.push(await s("nonce",a,l,e))},async use(e,t,r,n){var i;if(!(null!==(i=r.provider)&&void 0!==i&&null!==(i=i.checks)&&void 0!==i&&i.includes("nonce")))return;let s=null==e?void 0:e[r.cookies.nonce.name];if(!s)throw TypeError("Nonce cookie was missing.");let{name:a}=r.cookies.nonce,l=await o.decode({...r.jwt,token:s,salt:a});if(!(null!=l&&l.value))throw TypeError("Nonce value could not be parsed.");t.push({name:a,value:"",options:{...r.cookies.nonce.options,maxAge:0}}),n.nonce=l.value}}},78863:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.oAuth1Client=function(e){var t,r;let o=e.provider,i=new n.OAuth(o.requestTokenUrl,o.accessTokenUrl,o.clientId,o.clientSecret,null!==(t=o.version)&&void 0!==t?t:"1.0",o.callbackUrl,null!==(r=o.encoding)&&void 0!==r?r:"HMAC-SHA1"),s=i.get.bind(i);i.get=async(...e)=>await new Promise((t,r)=>{s(...e,(e,n)=>{if(e)return r(e);t(n)})});let a=i.getOAuthAccessToken.bind(i);i.getOAuthAccessToken=async(...e)=>await new Promise((t,r)=>{a(...e,(e,n,o)=>{if(e)return r(e);t({oauth_token:n,oauth_token_secret:o})})});let l=i.getOAuthRequestToken.bind(i);return i.getOAuthRequestToken=async(e={})=>await new Promise((t,r)=>{l(e,(e,n,o,i)=>{if(e)return r(e);t({oauth_token:n,oauth_token_secret:o,params:i})})}),i},t.oAuth1TokenStore=void 0;var n=r(66234);t.oAuth1TokenStore=new Map},35282:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.openidClient=o;var n=r(77838);async function o(e){let t;let r=e.provider;if(r.httpOptions&&n.custom.setHttpOptionsDefaults(r.httpOptions),r.wellKnown)t=await n.Issuer.discover(r.wellKnown);else{var o,i,s;t=new n.Issuer({issuer:r.issuer,authorization_endpoint:null===(o=r.authorization)||void 0===o?void 0:o.url,token_endpoint:null===(i=r.token)||void 0===i?void 0:i.url,userinfo_endpoint:null===(s=r.userinfo)||void 0===s?void 0:s.url,jwks_uri:r.jwks_endpoint})}let a=new t.Client({client_id:r.clientId,client_secret:r.clientSecret,redirect_uris:[r.callbackUrl],...r.client},r.jwks);return a[n.custom.clock_tolerance]=10,a}},82355:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,providerId:r}=e,i=e.providers.map(({options:e,...r})=>{var i,s;if("oauth"===r.type){let i=o(r),a=o(e,!0),l=null!==(s=null==a?void 0:a.id)&&void 0!==s?s:r.id;return(0,n.merge)(i,{...a,signinUrl:`${t}/signin/${l}`,callbackUrl:`${t}/callback/${l}`})}let a=null!==(i=null==e?void 0:e.id)&&void 0!==i?i:r.id;return(0,n.merge)(r,{...e,signinUrl:`${t}/signin/${a}`,callbackUrl:`${t}/callback/${a}`})});return{providers:i,provider:i.find(({id:e})=>e===r)}};var n=r(86479);function o(e,t=!1){var r,n,o,i,s;if(!e)return;let a=Object.entries(e).reduce((e,[t,r])=>{if(["authorization","token","userinfo"].includes(t)&&"string"==typeof r){var n;let o=new URL(r);e[t]={url:`${o.origin}${o.pathname}`,params:Object.fromEntries(null!==(n=o.searchParams)&&void 0!==n?n:[])}}else e[t]=r;return e},{});return t||null!==(r=a.version)&&void 0!==r&&r.startsWith("1.")||(a.idToken=!!(null!==(n=null!==(o=a.idToken)&&void 0!==o?o:null===(i=a.wellKnown)||void 0===i?void 0:i.includes("openid-configuration"))&&void 0!==n?n:null===(s=a.authorization)||void 0===s||null===(s=s.params)||void 0===s||null===(s=s.scope)||void 0===s?void 0:s.includes("openid")),a.checks||(a.checks=["state"])),a}},95627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createSecret=function(e){var t;let{authOptions:r,url:o}=e;return null!==(t=r.secret)&&void 0!==t?t:(0,n.createHash)("sha256").update(JSON.stringify({...o,...r})).digest("hex")},t.fromDate=function(e,t=Date.now()){return new Date(t+1e3*e)},t.hashToken=function(e,t){var r;let{provider:o,secret:i}=t;return(0,n.createHash)("sha256").update(`${e}${null!==(r=o.secret)&&void 0!==r?r:i}`).digest("hex")};var n=r(6113)},46283:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let{url:r,error:o="default",theme:i}=e,s=`${r}/signin`,a={default:{status:200,heading:"Error",message:(0,n.h)("p",null,(0,n.h)("a",{className:"site",href:null==r?void 0:r.origin},null==r?void 0:r.host))},configuration:{status:500,heading:"Server error",message:(0,n.h)("div",null,(0,n.h)("p",null,"There is a problem with the server configuration."),(0,n.h)("p",null,"Check the server logs for more information."))},accessdenied:{status:403,heading:"Access Denied",message:(0,n.h)("div",null,(0,n.h)("p",null,"You do not have permission to sign in."),(0,n.h)("p",null,(0,n.h)("a",{className:"button",href:s},"Sign in")))},verification:{status:403,heading:"Unable to sign in",message:(0,n.h)("div",null,(0,n.h)("p",null,"The sign in link is no longer valid."),(0,n.h)("p",null,"It may have been used already or it may have expired.")),signin:(0,n.h)("a",{className:"button",href:s},"Sign in")}},{status:l,heading:u,message:c,signin:d}=null!==(t=a[o.toLowerCase()])&&void 0!==t?t:a.default;return{status:l,html:(0,n.h)("div",{className:"error"},(null==i?void 0:i.brandColor)&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${null==i?void 0:i.brandColor}
        }
      `}}),(0,n.h)("div",{className:"card"},(null==i?void 0:i.logo)&&(0,n.h)("img",{src:i.logo,alt:"Logo",className:"logo"}),(0,n.h)("h1",null,u),(0,n.h)("div",{className:"message"},c),d))}};var n=r(97983)},45606:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,theme:r,query:n,cookies:c}=e;function d({html:e,title:t,status:n}){var i;return{cookies:c,status:n,headers:[{key:"Content-Type",value:"text/html"}],body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${(0,u.default)()}</style><title>${t}</title></head><body class="__next-auth-theme-${null!==(i=null==r?void 0:r.colorScheme)&&void 0!==i?i:"auto"}"><div class="page">${(0,o.default)(e)}</div></body></html>`}}return{signin:t=>d({html:(0,i.default)({csrfToken:e.csrfToken,providers:e.providers,callbackUrl:e.callbackUrl,theme:r,...n,...t}),title:"Sign In"}),signout:n=>d({html:(0,s.default)({csrfToken:e.csrfToken,url:t,theme:r,...n}),title:"Sign Out"}),verifyRequest:e=>d({html:(0,a.default)({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>d({...(0,l.default)({url:t,theme:r,...e}),title:"Error"})}};var o=n(r(23935)),i=n(r(74599)),s=n(r(8465)),a=n(r(52904)),l=n(r(46283)),u=n(r(97764))},74599:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let{csrfToken:r,providers:n,callbackUrl:a,theme:l,email:u,error:c}=e,d=n.filter(e=>"oauth"===e.type||"email"===e.type||"credentials"===e.type&&!!e.credentials);"undefined"!=typeof document&&l.buttonText&&document.documentElement.style.setProperty("--button-text-color",l.buttonText),"undefined"!=typeof document&&l.brandColor&&document.documentElement.style.setProperty("--brand-color",l.brandColor);let p={Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallback:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page.",default:"Unable to sign in."},h=c&&(null!==(t=p[c])&&void 0!==t?t:p.default),f="https://authjs.dev/img/providers";return(0,o.h)("div",{className:"signin"},l.brandColor&&(0,o.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${l.brandColor}
        }
      `}}),l.buttonText&&(0,o.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${l.buttonText}
        }
      `}}),(0,o.h)("div",{className:"card"},l.logo&&(0,o.h)("img",{src:l.logo,alt:"Logo",className:"logo"}),h&&(0,o.h)("div",{className:"error"},(0,o.h)("p",null,h)),d.map((e,t)=>{let n,l,c,p,h,_;if("oauth"===e.type){var g;({bg:n="",text:l="",logo:c="",bgDark:h=n,textDark:_=l,logoDark:p=""}=null!==(g=e.style)&&void 0!==g?g:{}),c=c.startsWith("/")?`${f}${c}`:c,(p=p.startsWith("/")?`${f}${p}`:p||c)||(p=c)}return(0,o.h)("div",{key:e.id,className:"provider"},"oauth"===e.type&&(0,o.h)("form",{action:e.signinUrl,method:"POST"},(0,o.h)("input",{type:"hidden",name:"csrfToken",value:r}),a&&(0,o.h)("input",{type:"hidden",name:"callbackUrl",value:a}),(0,o.h)("button",{type:"submit",className:"button",style:{"--provider-bg":n,"--provider-dark-bg":h,"--provider-color":l,"--provider-dark-color":_,"--provider-bg-hover":s(n,.8),"--provider-dark-bg-hover":s(h,.8)}},c&&(0,o.h)("img",{loading:"lazy",height:24,width:24,id:"provider-logo",src:`${c.startsWith("/")?f:""}${c}`}),p&&(0,o.h)("img",{loading:"lazy",height:24,width:24,id:"provider-logo-dark",src:`${c.startsWith("/")?f:""}${p}`}),(0,o.h)("span",null,"Sign in with ",e.name))),("email"===e.type||"credentials"===e.type)&&t>0&&"email"!==d[t-1].type&&"credentials"!==d[t-1].type&&(0,o.h)("hr",null),"email"===e.type&&(0,o.h)("form",{action:e.signinUrl,method:"POST"},(0,o.h)("input",{type:"hidden",name:"csrfToken",value:r}),(0,o.h)("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`},"Email"),(0,o.h)("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:u,placeholder:"<EMAIL>",required:!0}),(0,o.h)("button",{id:"submitButton",type:"submit"},"Sign in with ",e.name)),"credentials"===e.type&&(0,o.h)("form",{action:e.callbackUrl,method:"POST"},(0,o.h)("input",{type:"hidden",name:"csrfToken",value:r}),Object.keys(e.credentials).map(t=>{var r,n,s;return(0,o.h)("div",{key:`input-group-${e.id}`},(0,o.h)("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`},null!==(r=e.credentials[t].label)&&void 0!==r?r:t),(0,o.h)("input",(0,i.default)({name:t,id:`input-${t}-for-${e.id}-provider`,type:null!==(n=e.credentials[t].type)&&void 0!==n?n:"text",placeholder:null!==(s=e.credentials[t].placeholder)&&void 0!==s?s:""},e.credentials[t])))}),(0,o.h)("button",{type:"submit"},"Sign in with ",e.name)),("email"===e.type||"credentials"===e.type)&&t+1<d.length&&(0,o.h)("hr",null))})))};var o=r(97983),i=n(r(4239));function s(e,t=1){if(!e)return;3===(e=e.replace(/^#/,"")).length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]);let r=parseInt(e,16);return t=Math.min(Math.max(t,0),1),`rgba(${r>>16&255}, ${r>>8&255}, ${255&r}, ${t})`}},8465:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,csrfToken:r,theme:o}=e;return(0,n.h)("div",{className:"signout"},o.brandColor&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${o.brandColor}
        }
      `}}),o.buttonText&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${o.buttonText}
        }
      `}}),(0,n.h)("div",{className:"card"},o.logo&&(0,n.h)("img",{src:o.logo,alt:"Logo",className:"logo"}),(0,n.h)("h1",null,"Signout"),(0,n.h)("p",null,"Are you sure you want to sign out?"),(0,n.h)("form",{action:`${t}/signout`,method:"POST"},(0,n.h)("input",{type:"hidden",name:"csrfToken",value:r}),(0,n.h)("button",{id:"submitButton",type:"submit"},"Sign out"))))};var n=r(97983)},52904:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{url:t,theme:r}=e;return(0,n.h)("div",{className:"verify-request"},r.brandColor&&(0,n.h)("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),(0,n.h)("div",{className:"card"},r.logo&&(0,n.h)("img",{src:r.logo,alt:"Logo",className:"logo"}),(0,n.h)("h1",null,"Check your email"),(0,n.h)("p",null,"A sign in link has been sent to your email address."),(0,n.h)("p",null,(0,n.h)("a",{className:"site",href:t.origin},t.host))))};var n=r(97983)},60368:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var o=n(r(88302)),i=n(r(93024)),s=r(95627),a=n(r(5905));async function l(e){var t,r,n,l,u,c;let{options:d,query:p,body:h,method:f,headers:_,sessionStore:g}=e,{provider:y,adapter:v,url:m,callbackUrl:w,pages:b,jwt:k,events:x,callbacks:A,session:{strategy:S,maxAge:E},logger:O}=d,T=[],j="jwt"===S;if("oauth"===y.type)try{let{profile:n,account:s,OAuthProfile:a,cookies:l}=await (0,o.default)({query:p,body:h,method:f,options:d,cookies:e.cookies});l.length&&T.push(...l);try{if(O.debug("OAUTH_CALLBACK_RESPONSE",{profile:n,account:s,OAuthProfile:a}),!n||!s||!a)return{redirect:`${m}/signin`,cookies:T};let e=n;if(v){let{getUserByAccount:t}=v,r=await t({providerAccountId:s.providerAccountId,provider:y.id});r&&(e=r)}try{let t=await A.signIn({user:e,account:s,profile:a});if(!t)return{redirect:`${m}/error?error=AccessDenied`,cookies:T};if("string"==typeof t)return{redirect:t,cookies:T}}catch(e){return{redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:T}}let{user:o,session:l,isNewUser:u}=await (0,i.default)({sessionToken:g.value,profile:n,account:s,options:d});if(j){let e={name:o.name,email:o.email,picture:o.image,sub:null===(r=o.id)||void 0===r?void 0:r.toString()},t=await A.jwt({token:e,user:o,account:s,profile:a,isNewUser:u,trigger:u?"signUp":"signIn"}),n=await k.encode({...k,token:t}),i=new Date;i.setTime(i.getTime()+1e3*E);let l=g.chunk(n,{expires:i});T.push(...l)}else T.push({name:d.cookies.sessionToken.name,value:l.sessionToken,options:{...d.cookies.sessionToken.options,expires:l.expires}});if(await (null===(t=x.signIn)||void 0===t?void 0:t.call(x,{user:o,account:s,profile:n,isNewUser:u})),u&&b.newUser)return{redirect:`${b.newUser}${b.newUser.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(w)}`,cookies:T};return{redirect:w,cookies:T}}catch(e){if("AccountNotLinkedError"===e.name)return{redirect:`${m}/error?error=OAuthAccountNotLinked`,cookies:T};if("CreateUserError"===e.name)return{redirect:`${m}/error?error=OAuthCreateAccount`,cookies:T};return O.error("OAUTH_CALLBACK_HANDLER_ERROR",e),{redirect:`${m}/error?error=Callback`,cookies:T}}}catch(e){if("OAuthCallbackError"===e.name)return O.error("OAUTH_CALLBACK_ERROR",{error:e,providerId:y.id}),{redirect:`${m}/error?error=OAuthCallback`,cookies:T};return O.error("OAUTH_CALLBACK_ERROR",e),{redirect:`${m}/error?error=Callback`,cookies:T}}else if("email"===y.type)try{let e=null==p?void 0:p.token,t=null==p?void 0:p.email;if(!e)return{redirect:`${m}/error?error=configuration`,cookies:T};let r=await v.useVerificationToken({identifier:t,token:(0,s.hashToken)(e,d)});if(!r||r.expires.valueOf()<Date.now()||t&&r.identifier!==t)return{redirect:`${m}/error?error=Verification`,cookies:T};let o=await (0,a.default)({email:r.identifier,adapter:v}),u={providerAccountId:o.email,type:"email",provider:y.id};try{let e=await A.signIn({user:o,account:u});if(!e)return{redirect:`${m}/error?error=AccessDenied`,cookies:T};if("string"==typeof e)return{redirect:e,cookies:T}}catch(e){return{redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:T}}let{user:c,session:h,isNewUser:f}=await (0,i.default)({sessionToken:g.value,profile:o,account:u,options:d});if(j){let e={name:c.name,email:c.email,picture:c.image,sub:null===(l=c.id)||void 0===l?void 0:l.toString()},t=await A.jwt({token:e,user:c,account:u,isNewUser:f,trigger:f?"signUp":"signIn"}),r=await k.encode({...k,token:t}),n=new Date;n.setTime(n.getTime()+1e3*E);let o=g.chunk(r,{expires:n});T.push(...o)}else T.push({name:d.cookies.sessionToken.name,value:h.sessionToken,options:{...d.cookies.sessionToken.options,expires:h.expires}});if(await (null===(n=x.signIn)||void 0===n?void 0:n.call(x,{user:c,account:u,isNewUser:f})),f&&b.newUser)return{redirect:`${b.newUser}${b.newUser.includes("?")?"&":"?"}callbackUrl=${encodeURIComponent(w)}`,cookies:T};return{redirect:w,cookies:T}}catch(e){if("CreateUserError"===e.name)return{redirect:`${m}/error?error=EmailCreateAccount`,cookies:T};return O.error("CALLBACK_EMAIL_ERROR",e),{redirect:`${m}/error?error=Callback`,cookies:T}}else if("credentials"===y.type&&"POST"===f){let e;try{if(!(e=await y.authorize(h,{query:p,body:h,headers:_,method:f})))return{status:401,redirect:`${m}/error?${new URLSearchParams({error:"CredentialsSignin",provider:y.id})}`,cookies:T}}catch(e){return{status:401,redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:T}}let t={providerAccountId:e.id,type:"credentials",provider:y.id};try{let r=await A.signIn({user:e,account:t,credentials:h});if(!r)return{status:403,redirect:`${m}/error?error=AccessDenied`,cookies:T};if("string"==typeof r)return{redirect:r,cookies:T}}catch(e){return{redirect:`${m}/error?error=${encodeURIComponent(e.message)}`,cookies:T}}let r={name:e.name,email:e.email,picture:e.image,sub:null===(u=e.id)||void 0===u?void 0:u.toString()},n=await A.jwt({token:r,user:e,account:t,isNewUser:!1,trigger:"signIn"}),o=await k.encode({...k,token:n}),i=new Date;i.setTime(i.getTime()+1e3*E);let s=g.chunk(o,{expires:i});return T.push(...s),await (null===(c=x.signIn)||void 0===c?void 0:c.call(x,{user:e,account:t})),{redirect:w,cookies:T}}return{status:500,body:`Error: Callback for provider type ${y.type} not supported`,cookies:T}}},83081:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callback",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"providers",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(t,"session",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"signin",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"signout",{enumerable:!0,get:function(){return s.default}});var o=n(r(60368)),i=n(r(93558)),s=n(r(12551)),a=n(r(63636)),l=n(r(13234))},13234:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return{headers:[{key:"Content-Type",value:"application/json"}],body:e.reduce((e,{id:t,name:r,type:n,signinUrl:o,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:o,callbackUrl:i},e),{})}}},63636:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=o;var n=r(95627);async function o(e){var t,r,o,i,s,a;let{options:l,sessionStore:u,newSession:c,isUpdate:d}=e,{adapter:p,jwt:h,events:f,callbacks:_,logger:g,session:{strategy:y,maxAge:v}}=l,m={body:{},headers:[{key:"Content-Type",value:"application/json"}],cookies:[]},w=u.value;if(!w)return m;if("jwt"===y)try{let e=await h.decode({...h,token:w});if(!e)throw Error("JWT invalid");let o=await _.jwt({token:e,...d&&{trigger:"update"},session:c}),i=(0,n.fromDate)(v),s=await _.session({session:{user:{name:null==e?void 0:e.name,email:null==e?void 0:e.email,image:null==e?void 0:e.picture},expires:i.toISOString()},token:o});m.body=s;let a=await h.encode({...h,token:o,maxAge:l.session.maxAge}),p=u.chunk(a,{expires:i});null===(t=m.cookies)||void 0===t||t.push(...p),await (null===(r=f.session)||void 0===r?void 0:r.call(f,{session:s,token:o}))}catch(e){g.error("JWT_SESSION_ERROR",e),null===(o=m.cookies)||void 0===o||o.push(...u.clean())}else try{let{getSessionAndUser:e,deleteSession:t,updateSession:r}=p,o=await e(w);if(o&&o.session.expires.valueOf()<Date.now()&&(await t(w),o=null),o){let{user:e,session:t}=o,a=l.session.updateAge,u=t.expires.valueOf()-1e3*v+1e3*a,p=(0,n.fromDate)(v);u<=Date.now()&&await r({sessionToken:w,expires:p});let h=await _.session({session:{user:{name:e.name,email:e.email,image:e.image},expires:t.expires.toISOString()},user:e,newSession:c,...d?{trigger:"update"}:{}});m.body=h,null===(i=m.cookies)||void 0===i||i.push({name:l.cookies.sessionToken.name,value:w,options:{...l.cookies.sessionToken.options,expires:p}}),await (null===(s=f.session)||void 0===s?void 0:s.call(f,{session:h}))}else w&&(null===(a=m.cookies)||void 0===a||a.push(...u.clean()))}catch(e){g.error("SESSION_ERROR",e)}return m}},93558:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.default=a;var o=n(r(75971)),i=n(r(32093)),s=n(r(5905));async function a(e){let{options:t,query:r,body:n}=e,{url:a,callbacks:l,logger:u,provider:c}=t;if(!c.type)return{status:500,text:`Error: Type not specified for ${c.name}`};if("oauth"===c.type)try{return await (0,o.default)({options:t,query:r})}catch(e){return u.error("SIGNIN_OAUTH_ERROR",{error:e,providerId:c.id}),{redirect:`${a}/error?error=OAuthSignin`}}else if("email"===c.type){var d;let e=null==n?void 0:n.email;if(!e)return{redirect:`${a}/error?error=EmailSignin`};let r=null!==(d=c.normalizeIdentifier)&&void 0!==d?d:e=>{let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`};try{e=r(null==n?void 0:n.email)}catch(e){return u.error("SIGNIN_EMAIL_ERROR",{error:e,providerId:c.id}),{redirect:`${a}/error?error=EmailSignin`}}let o=await (0,s.default)({email:e,adapter:t.adapter}),p={providerAccountId:e,userId:e,type:"email",provider:c.id};try{let e=await l.signIn({user:o,account:p,email:{verificationRequest:!0}});if(!e)return{redirect:`${a}/error?error=AccessDenied`};if("string"==typeof e)return{redirect:e}}catch(e){return{redirect:`${a}/error?${new URLSearchParams({error:e})}`}}try{return{redirect:await (0,i.default)(e,t)}}catch(e){return u.error("SIGNIN_EMAIL_ERROR",{error:e,providerId:c.id}),{redirect:`${a}/error?error=EmailSignin`}}}return{redirect:`${a}/signin`}}},12551:(e,t)=>{"use strict";async function r(e){var t,r;let{options:n,sessionStore:o}=e,{adapter:i,events:s,jwt:a,callbackUrl:l,logger:u,session:c}=n,d=null==o?void 0:o.value;if(!d)return{redirect:l};if("jwt"===c.strategy)try{let e=await a.decode({...a,token:d});await (null===(t=s.signOut)||void 0===t?void 0:t.call(s,{token:e}))}catch(e){u.error("SIGNOUT_ERROR",e)}else try{let e=await i.deleteSession(d);await (null===(r=s.signOut)||void 0===r?void 0:r.call(s,{session:e}))}catch(e){u.error("SIGNOUT_ERROR",e)}return{redirect:l,cookies:o.clean()}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},97764:e=>{e.exports=function(){return':root{--border-width:1px;--border-radius:0.5rem;--color-error:#c94b4b;--color-info:#157efb;--color-info-hover:#0f6ddb;--color-info-text:#fff}.__next-auth-theme-auto,.__next-auth-theme-light{--color-background:#ececec;--color-background-hover:hsla(0,0%,93%,.8);--color-background-card:#fff;--color-text:#000;--color-primary:#444;--color-control-border:#bbb;--color-button-active-background:#f9f9f9;--color-button-active-border:#aaa;--color-separator:#ccc}.__next-auth-theme-dark{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}@media (prefers-color-scheme:dark){.__next-auth-theme-auto{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}a.button,button{background-color:var(--provider-dark-bg,var(--color-background));color:var(--provider-dark-color,var(--color-primary))}a.button:hover,button:hover{background-color:var(--provider-dark-bg-hover,var(--color-background-hover))!important}#provider-logo{display:none!important}#provider-logo-dark{display:block!important;width:25px}}html{box-sizing:border-box}*,:after,:before{box-sizing:inherit;margin:0;padding:0}body{background-color:var(--color-background);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;margin:0;padding:0}h1{font-weight:400}h1,p{color:var(--color-text);margin-bottom:1.5rem;padding:0 1rem}form{margin:0;padding:0}label{font-weight:500;margin-bottom:.25rem;text-align:left}input[type],label{color:var(--color-text);display:block}input[type]{background:var(--color-background-card);border:var(--border-width) solid var(--color-control-border);border-radius:var(--border-radius);box-sizing:border-box;font-size:1rem;padding:.5rem 1rem;width:100%}input[type]:focus{box-shadow:none}p{font-size:1.1rem;line-height:2rem}a.button{line-height:1rem;text-decoration:none}a.button:link,a.button:visited{background-color:var(--color-background);color:var(--color-primary)}button span{flex-grow:1}a.button,button{align-items:center;background-color:var(--provider-bg);border-color:rgba(0,0,0,.1);border-radius:var(--border-radius);color:var(--provider-color,var(--color-primary));display:flex;font-size:1.1rem;font-weight:500;justify-content:center;min-height:62px;padding:.75rem 1rem;position:relative;transition:all .1s ease-in-out}a.button:hover,button:hover{background-color:var(--provider-bg-hover,var(--color-background-hover));cursor:pointer}a.button:active,button:active{cursor:pointer}a.button #provider-logo,button #provider-logo{display:block;width:25px}a.button #provider-logo-dark,button #provider-logo-dark{display:none}#submitButton{background-color:var(--brand-color,var(--color-info));color:var(--button-text-color,var(--color-info-text));width:100%}#submitButton:hover{background-color:var(--button-hover-bg,var(--color-info-hover))!important}a.site{color:var(--color-primary);font-size:1rem;line-height:2rem;text-decoration:none}a.site:hover{text-decoration:underline}.page{box-sizing:border-box;display:grid;height:100%;margin:0;padding:0;place-items:center;position:absolute;width:100%}.page>div{text-align:center}.error a.button{margin-top:.5rem;padding-left:2rem;padding-right:2rem}.error .message{margin-bottom:1.5rem}.signin input[type=text]{display:block;margin-left:auto;margin-right:auto}.signin hr{border:0;border-top:1px solid var(--color-separator);display:block;margin:2rem auto 1rem;overflow:visible}.signin hr:before{background:var(--color-background-card);color:#888;content:"or";padding:0 .4rem;position:relative;top:-.7rem}.signin .error{background:#f5f5f5;background:var(--color-error);border-radius:.3rem;font-weight:500}.signin .error p{color:var(--color-info-text);font-size:.9rem;line-height:1.2rem;padding:.5rem 1rem;text-align:left}.signin form,.signin>div{display:block}.signin form input[type],.signin>div input[type]{margin-bottom:.5rem}.signin form button,.signin>div button{width:100%}.signin .provider+.provider{margin-top:1rem}.logo{display:inline-block;margin:1.25rem 0;max-height:70px;max-width:150px}.card{background-color:var(--color-background-card);border-radius:2rem;padding:1.25rem 2rem}.card .header{color:var(--color-primary)}.section-header{color:var(--color-text)}@media screen and (min-width:450px){.card{margin:2rem 0;width:368px}}@media screen and (max-width:450px){.card{margin:1rem 0;width:343px}}'}},45609:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.getServerSession=a,t.unstable_getServerSession=l;var n=r(5328),o=r(56138);async function i(e,t,r){var i,s,a,l,u,c,d,p,h;let{nextauth:f,..._}=e.query;null!==(i=r.secret)&&void 0!==i||(r.secret=null!==(s=null!==(a=null===(l=r.jwt)||void 0===l?void 0:l.secret)&&void 0!==a?a:process.env.NEXTAUTH_SECRET)&&void 0!==s?s:process.env.AUTH_SECRET);let g=await (0,n.AuthHandler)({req:{body:e.body,query:_,cookies:e.cookies,headers:e.headers,method:e.method,action:null==f?void 0:f[0],providerId:null==f?void 0:f[1],error:null!==(u=e.query.error)&&void 0!==u?u:null==f?void 0:f[1]},options:r});if(t.status(null!==(c=g.status)&&void 0!==c?c:200),null===(d=g.cookies)||void 0===d||d.forEach(e=>(0,o.setCookie)(t,e)),null===(p=g.headers)||void 0===p||p.forEach(e=>t.setHeader(e.key,e.value)),g.redirect){if((null===(h=e.body)||void 0===h?void 0:h.json)!=="true"){t.status(302).setHeader("Location",g.redirect),t.end();return}return t.json({url:g.redirect})}return t.send(g.body)}async function s(e,t,i){var s,a,l,u;null!==(s=i.secret)&&void 0!==s||(i.secret=null!==(a=process.env.NEXTAUTH_SECRET)&&void 0!==a?a:process.env.AUTH_SECRET);let{headers:c,cookies:d}=r(71615),p=null===(l=await t.params)||void 0===l?void 0:l.nextauth,h=Object.fromEntries(e.nextUrl.searchParams),f=await (0,o.getBody)(e),_=await (0,n.AuthHandler)({req:{body:f,query:h,cookies:Object.fromEntries((await d()).getAll().map(e=>[e.name,e.value])),headers:Object.fromEntries(await c()),method:e.method,action:null==p?void 0:p[0],providerId:null==p?void 0:p[1],error:null!==(u=h.error)&&void 0!==u?u:null==p?void 0:p[1]},options:i}),g=(0,o.toResponse)(_),y=g.headers.get("Location");return(null==f?void 0:f.json)==="true"&&y?(g.headers.delete("Location"),g.headers.set("Content-Type","application/json"),new Response(JSON.stringify({url:y}),{status:_.status,headers:g.headers})):g}async function a(...e){var t,i,s;let l,u,c;let d=0===e.length||1===e.length;if(d){c=Object.assign({},e[0],{providers:[]});let{headers:t,cookies:n}=r(71615);l={headers:Object.fromEntries(await t()),cookies:Object.fromEntries((await n()).getAll().map(e=>[e.name,e.value]))},u={getHeader(){},setCookie(){},setHeader(){}}}else l=e[0],u=e[1],c=Object.assign({},e[2],{providers:[]});null!==(i=(t=c).secret)&&void 0!==i||(t.secret=null!==(s=process.env.NEXTAUTH_SECRET)&&void 0!==s?s:process.env.AUTH_SECRET);let{body:p,cookies:h,status:f=200}=await (0,n.AuthHandler)({options:c,req:{action:"session",method:"GET",cookies:l.cookies,headers:l.headers}});if(null==h||h.forEach(e=>(0,o.setCookie)(u,e)),p&&"string"!=typeof p&&Object.keys(p).length){if(200===f)return d&&delete p.expires,p;throw Error(p.message)}return null}async function l(...e){return await a(...e)}t.default=function(...e){var t;return 1===e.length?async(t,r)=>null!=r&&r.params?await s(t,r,e[0]):await i(t,r,e[0]):null!==(t=e[1])&&void 0!==t&&t.params?s(...e):i(...e)}},56138:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getBody=o,t.setCookie=function(e,t){var r;let o=null!==(r=e.getHeader("Set-Cookie"))&&void 0!==r?r:[];Array.isArray(o)||(o=[o]);let{name:i,value:s,options:a}=t,l=(0,n.serialize)(i,s,a);o.push(l),e.setHeader("Set-Cookie",o)},t.toResponse=function(e){var t,r,o;let i=new Headers(null===(t=e.headers)||void 0===t?void 0:t.reduce((e,{key:t,value:r})=>(e[t]=r,e),{}));null===(r=e.cookies)||void 0===r||r.forEach(e=>{let{name:t,value:r,options:o}=e,s=(0,n.serialize)(t,r,o);i.has("Set-Cookie")?i.append("Set-Cookie",s):i.set("Set-Cookie",s)});let s=e.body;"application/json"===i.get("content-type")?s=JSON.stringify(e.body):"application/x-www-form-urlencoded"===i.get("content-type")&&(s=new URLSearchParams(e.body).toString());let a=new Response(s,{headers:i,status:e.redirect?302:null!==(o=e.status)&&void 0!==o?o:200});return e.redirect&&a.headers.set("Location",e.redirect),a};var n=r(43183);async function o(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return null!=t&&t.includes("application/json")?await e.json():null!=t&&t.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}},35144:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.detectOrigin=function(e,t){var r;return(null!==(r=process.env.VERCEL)&&void 0!==r?r:process.env.AUTH_TRUST_HOST)?`${"http"===t?"http":"https"}://${e}`:process.env.NEXTAUTH_URL}},80878:(e,t,r)=>{"use strict";var n=r(262);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.proxyLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=arguments.length>1?arguments[1]:void 0;try{if("undefined"==typeof window)return e;var r={},n=function(e){var n;r[e]=(n=(0,s.default)(o.default.mark(function r(n,s){var a,d;return o.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(c[e](n,s),"error"===e&&(s=u(s)),s.client=!0,a="".concat(t,"/_log"),d=new URLSearchParams(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){(0,i.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({level:e,code:n},s)),!navigator.sendBeacon){r.next=8;break}return r.abrupt("return",navigator.sendBeacon(a,d));case 8:return r.next=10,fetch(a,{method:"POST",body:d,keepalive:!0});case 10:return r.abrupt("return",r.sent);case 11:case"end":return r.stop()}},r)})),function(e,t){return n.apply(this,arguments)})};for(var a in e)n(a);return r}catch(e){return c}},t.setLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;t||(c.debug=function(){}),e.error&&(c.error=e.error),e.warn&&(c.warn=e.warn),e.debug&&(c.debug=e.debug)};var o=n(r(81213)),i=n(r(33679)),s=n(r(85577)),a=r(85793);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){var t;return e instanceof Error&&!(e instanceof a.UnknownError)?{message:e.message,stack:e.stack,name:e.name}:(null!=e&&e.error&&(e.error=u(e.error),e.message=null!==(t=e.message)&&void 0!==t?t:e.error.message),e)}var c={error:function(e,t){t=u(t),console.error("[next-auth][error][".concat(e,"]"),"\nhttps://next-auth.js.org/errors#".concat(e.toLowerCase()),t.message,t)},warn:function(e){console.warn("[next-auth][warn][".concat(e,"]"),"\nhttps://next-auth.js.org/warnings#".concat(e.toLowerCase()))},debug:function(e,t){console.log("[next-auth][debug][".concat(e,"]"),t)}};t.default=c},86479:(e,t)=>{"use strict";function r(e){return e&&"object"==typeof e&&!Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.merge=function e(t,...n){if(!n.length)return t;let o=n.shift();if(r(t)&&r(o))for(let n in o)r(o[n])?(t[n]||Object.assign(t,{[n]:{}}),e(t[n],o[n])):Object.assign(t,{[n]:o[n]});return e(t,...n)}},33768:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!==(t=e)&&void 0!==t?t:r),o=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),i=`${n.origin}${o}`;return{origin:n.origin,host:n.host,path:o,base:i,toString:()=>i}}},71615:(e,t,r)=>{"use strict";r.r(t);var n=r(88757),o={};for(let e in n)"default"!==e&&(o[e]=()=>n[e]);r.d(t,o)},33085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return i}});let n=r(45869),o=r(6278);class i{get isEnabled(){return this._provider.isEnabled}enable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return p},draftMode:function(){return h},headers:function(){return d}});let n=r(68996),o=r(53047),i=r(92044),s=r(72934),a=r(33085),l=r(6278),u=r(45869),c=r(54580);function d(){let e="headers",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,c.getExpectedRequestStore)(e).headers}function p(){let e="cookies",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return n.RequestCookiesAdapter.seal(new i.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,c.getExpectedRequestStore)(e),o=s.actionAsyncStorage.getStore();return(null==o?void 0:o.isAction)||(null==o?void 0:o.isAppRoute)?r.mutableCookies:r.cookies}function h(){let e=(0,c.getExpectedRequestStore)("draftMode");return new a.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return o}});let n=r(38238);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,o);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==s)return n.ReflectAdapter.get(t,s,o)},set(t,r,o,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,o,i);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);return n.ReflectAdapter.set(t,a??r,o,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},38238:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},68996:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return a},appendMutableCookies:function(){return c},getModifiedCookieValues:function(){return u}});let n=r(92044),o=r(38238),i=r(45869);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new s}}class a{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function c(e,t){let r=u(t);if(0===r.length)return!1;let o=new n.ResponseCookies(e),i=o.getAll();for(let e of r)o.set(e);for(let e of i)o.set(e);return!0}class d{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let s=[],a=new Set,u=()=>{let e=i.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),s=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of s){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return s;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{u()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{u()}};default:return o.ReflectAdapter.get(e,t,r)}}})}}},66234:(e,t,r)=>{t.OAuth=r(52252).OAuth,t.OAuthEcho=r(52252).OAuthEcho,t.OAuth2=r(81788).OAuth2},50823:e=>{e.exports.isAnEarlyCloseHost=function(e){return e&&e.match(".*google(apis)?.com$")}},52252:(e,t,r)=>{var n=r(6113),o=r(24985),i=r(13685),s=r(95687),a=r(57310),l=r(63477),u=r(50823);t.OAuth=function(e,t,r,n,o,i,s,a,l){if(this._isEcho=!1,this._requestUrl=e,this._accessUrl=t,this._consumerKey=r,this._consumerSecret=this._encodeData(n),"RSA-SHA1"==s&&(this._privateKey=n),this._version=o,void 0===i?this._authorize_callback="oob":this._authorize_callback=i,"PLAINTEXT"!=s&&"HMAC-SHA1"!=s&&"RSA-SHA1"!=s)throw Error("Un-supported signature method: "+s);this._signatureMethod=s,this._nonceSize=a||32,this._headers=l||{Accept:"*/*",Connection:"close","User-Agent":"Node authentication"},this._clientOptions=this._defaultClientOptions={requestTokenHttpMethod:"POST",accessTokenHttpMethod:"POST",followRedirects:!0},this._oauthParameterSeperator=","},t.OAuthEcho=function(e,t,r,n,o,i,s,a){if(this._isEcho=!0,this._realm=e,this._verifyCredentials=t,this._consumerKey=r,this._consumerSecret=this._encodeData(n),"RSA-SHA1"==i&&(this._privateKey=n),this._version=o,"PLAINTEXT"!=i&&"HMAC-SHA1"!=i&&"RSA-SHA1"!=i)throw Error("Un-supported signature method: "+i);this._signatureMethod=i,this._nonceSize=s||32,this._headers=a||{Accept:"*/*",Connection:"close","User-Agent":"Node authentication"},this._oauthParameterSeperator=","},t.OAuthEcho.prototype=t.OAuth.prototype,t.OAuth.prototype._getTimestamp=function(){return Math.floor(new Date().getTime()/1e3)},t.OAuth.prototype._encodeData=function(e){return null==e||""==e?"":encodeURIComponent(e).replace(/\!/g,"%21").replace(/\'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")},t.OAuth.prototype._decodeData=function(e){return null!=e&&(e=e.replace(/\+/g," ")),decodeURIComponent(e)},t.OAuth.prototype._getSignature=function(e,t,r,n){var o=this._createSignatureBase(e,t,r);return this._createSignature(o,n)},t.OAuth.prototype._normalizeUrl=function(e){var t=a.parse(e,!0),r="";return t.port&&("http:"==t.protocol&&"80"!=t.port||"https:"==t.protocol&&"443"!=t.port)&&(r=":"+t.port),t.pathname&&""!=t.pathname||(t.pathname="/"),t.protocol+"//"+t.hostname+r+t.pathname},t.OAuth.prototype._isParameterNameAnOAuthParameter=function(e){var t=e.match("^oauth_");return!!t&&"oauth_"===t[0]},t.OAuth.prototype._buildAuthorizationHeaders=function(e){var t="OAuth ";this._isEcho&&(t+='realm="'+this._realm+'",');for(var r=0;r<e.length;r++)this._isParameterNameAnOAuthParameter(e[r][0])&&(t+=""+this._encodeData(e[r][0])+'="'+this._encodeData(e[r][1])+'"'+this._oauthParameterSeperator);return t.substring(0,t.length-this._oauthParameterSeperator.length)},t.OAuth.prototype._makeArrayOfArgumentsHash=function(e){var t=[];for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(Array.isArray(n))for(var o=0;o<n.length;o++)t[t.length]=[r,n[o]];else t[t.length]=[r,n]}return t},t.OAuth.prototype._sortRequestParams=function(e){return e.sort(function(e,t){return e[0]==t[0]?e[1]<t[1]?-1:1:e[0]<t[0]?-1:1}),e},t.OAuth.prototype._normaliseRequestParams=function(e){for(var t=this._makeArrayOfArgumentsHash(e),r=0;r<t.length;r++)t[r][0]=this._encodeData(t[r][0]),t[r][1]=this._encodeData(t[r][1]);t=this._sortRequestParams(t);for(var e="",r=0;r<t.length;r++)e+=t[r][0]+"="+t[r][1],r<t.length-1&&(e+="&");return e},t.OAuth.prototype._createSignatureBase=function(e,t,r){return t=this._encodeData(this._normalizeUrl(t)),r=this._encodeData(r),e.toUpperCase()+"&"+t+"&"+r},t.OAuth.prototype._createSignature=function(e,t){if(void 0===t)var t="";else t=this._encodeData(t);var r=this._consumerSecret+"&"+t,i="";return"PLAINTEXT"==this._signatureMethod?i=r:"RSA-SHA1"==this._signatureMethod?(r=this._privateKey||"",i=n.createSign("RSA-SHA1").update(e).sign(r,"base64")):i=n.Hmac?n.createHmac("sha1",r).update(e).digest("base64"):o.HMACSHA1(r,e),i},t.OAuth.prototype.NONCE_CHARS=["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","0","1","2","3","4","5","6","7","8","9"],t.OAuth.prototype._getNonce=function(e){for(var t,r=[],n=this.NONCE_CHARS,o=n.length,i=0;i<e;i++)t=Math.floor(Math.random()*o),r[i]=n[t];return r.join("")},t.OAuth.prototype._createClient=function(e,t,r,n,o,a){return(a?s:i).request({host:t,port:e,path:n,method:r,headers:o})},t.OAuth.prototype._prepareParameters=function(e,t,r,n,o){var i={oauth_timestamp:this._getTimestamp(),oauth_nonce:this._getNonce(this._nonceSize),oauth_version:this._version,oauth_signature_method:this._signatureMethod,oauth_consumer_key:this._consumerKey};if(e&&(i.oauth_token=e),this._isEcho)c=this._getSignature("GET",this._verifyCredentials,this._normaliseRequestParams(i),t);else{if(o)for(var s in o)o.hasOwnProperty(s)&&(i[s]=o[s]);var u=a.parse(n,!1);if(u.query){var c,d,p=l.parse(u.query);for(var s in p){var h=p[s];if("object"==typeof h)for(d in h)i[s+"["+d+"]"]=h[d];else i[s]=h}}c=this._getSignature(r,n,this._normaliseRequestParams(i),t)}var f=this._sortRequestParams(this._makeArrayOfArgumentsHash(i));return f[f.length]=["oauth_signature",c],f},t.OAuth.prototype._performSecureRequest=function(e,t,r,n,o,i,s,c){var d,p,h=this._prepareParameters(e,t,r,n,o);s||(s="application/x-www-form-urlencoded");var f=a.parse(n,!1);"http:"!=f.protocol||f.port||(f.port=80),"https:"!=f.protocol||f.port||(f.port=443);var _={},g=this._buildAuthorizationHeaders(h);for(var y in this._isEcho?_["X-Verify-Credentials-Authorization"]=g:_.Authorization=g,_.Host=f.host,this._headers)this._headers.hasOwnProperty(y)&&(_[y]=this._headers[y]);for(var y in o)this._isParameterNameAnOAuthParameter(y)&&delete o[y];("POST"==r||"PUT"==r)&&null==i&&null!=o&&(i=l.stringify(o).replace(/\!/g,"%21").replace(/\'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")),i?Buffer.isBuffer(i)?_["Content-length"]=i.length:_["Content-length"]=Buffer.byteLength(i):_["Content-length"]=0,_["Content-Type"]=s,f.pathname&&""!=f.pathname||(f.pathname="/"),d=f.query?f.pathname+"?"+f.query:f.pathname,p="https:"==f.protocol?this._createClient(f.port,f.hostname,r,d,_,!0):this._createClient(f.port,f.hostname,r,d,_);var v=this._clientOptions;if(!c)return("POST"==r||"PUT"==r)&&null!=i&&""!=i&&p.write(i),p;var m="",w=this,b=u.isAnEarlyCloseHost(f.hostname),k=!1,x=function(n){k||(k=!0,n.statusCode>=200&&n.statusCode<=299?c(null,m,n):(301==n.statusCode||302==n.statusCode)&&v.followRedirects&&n.headers&&n.headers.location?w._performSecureRequest(e,t,r,n.headers.location,o,i,s,c):c({statusCode:n.statusCode,data:m},m,n))};p.on("response",function(e){e.setEncoding("utf8"),e.on("data",function(e){m+=e}),e.on("end",function(){x(e)}),e.on("close",function(){b&&x(e)})}),p.on("error",function(e){k||(k=!0,c(e))}),("POST"==r||"PUT"==r)&&null!=i&&""!=i&&p.write(i),p.end()},t.OAuth.prototype.setClientOptions=function(e){var t,r={},n=Object.prototype.hasOwnProperty;for(t in this._defaultClientOptions)n.call(e,t)?r[t]=e[t]:r[t]=this._defaultClientOptions[t];this._clientOptions=r},t.OAuth.prototype.getOAuthAccessToken=function(e,t,r,n){var o={};"function"==typeof r?n=r:o.oauth_verifier=r,this._performSecureRequest(e,t,this._clientOptions.accessTokenHttpMethod,this._accessUrl,o,null,null,function(e,t,r){if(e)n(e);else{var o=l.parse(t),i=o.oauth_token;delete o.oauth_token;var s=o.oauth_token_secret;delete o.oauth_token_secret,n(null,i,s,o)}})},t.OAuth.prototype.getProtectedResource=function(e,t,r,n,o){this._performSecureRequest(r,n,t,e,null,"",null,o)},t.OAuth.prototype.delete=function(e,t,r,n){return this._performSecureRequest(t,r,"DELETE",e,null,"",null,n)},t.OAuth.prototype.get=function(e,t,r,n){return this._performSecureRequest(t,r,"GET",e,null,"",null,n)},t.OAuth.prototype._putOrPost=function(e,t,r,n,o,i,s){var a=null;return"function"==typeof i&&(s=i,i=null),"string"==typeof o||Buffer.isBuffer(o)||(i="application/x-www-form-urlencoded",a=o,o=null),this._performSecureRequest(r,n,e,t,a,o,i,s)},t.OAuth.prototype.put=function(e,t,r,n,o,i){return this._putOrPost("PUT",e,t,r,n,o,i)},t.OAuth.prototype.post=function(e,t,r,n,o,i){return this._putOrPost("POST",e,t,r,n,o,i)},t.OAuth.prototype.getOAuthRequestToken=function(e,t){"function"==typeof e&&(t=e,e={}),this._authorize_callback&&(e.oauth_callback=this._authorize_callback),this._performSecureRequest(null,null,this._clientOptions.requestTokenHttpMethod,this._requestUrl,e,null,null,function(e,r,n){if(e)t(e);else{var o=l.parse(r),i=o.oauth_token,s=o.oauth_token_secret;delete o.oauth_token,delete o.oauth_token_secret,t(null,i,s,o)}})},t.OAuth.prototype.signUrl=function(e,t,r,n){if(void 0===n)var n="GET";for(var o=this._prepareParameters(t,r,n,e,{}),i=a.parse(e,!1),s="",l=0;l<o.length;l++)s+=o[l][0]+"="+this._encodeData(o[l][1])+"&";return s=s.substring(0,s.length-1),i.protocol+"//"+i.host+i.pathname+"?"+s},t.OAuth.prototype.authHeader=function(e,t,r,n){if(void 0===n)var n="GET";var o=this._prepareParameters(t,r,n,e,{});return this._buildAuthorizationHeaders(o)}},81788:(e,t,r)=>{var n=r(63477),o=(r(6113),r(95687)),i=r(13685),s=r(57310),a=r(50823);t.OAuth2=function(e,t,r,n,o,i){this._clientId=e,this._clientSecret=t,this._baseSite=r,this._authorizeUrl=n||"/oauth/authorize",this._accessTokenUrl=o||"/oauth/access_token",this._accessTokenName="access_token",this._authMethod="Bearer",this._customHeaders=i||{},this._useAuthorizationHeaderForGET=!1,this._agent=void 0},t.OAuth2.prototype.setAgent=function(e){this._agent=e},t.OAuth2.prototype.setAccessTokenName=function(e){this._accessTokenName=e},t.OAuth2.prototype.setAuthMethod=function(e){this._authMethod=e},t.OAuth2.prototype.useAuthorizationHeaderforGET=function(e){this._useAuthorizationHeaderForGET=e},t.OAuth2.prototype._getAccessTokenUrl=function(){return this._baseSite+this._accessTokenUrl},t.OAuth2.prototype.buildAuthHeader=function(e){return this._authMethod+" "+e},t.OAuth2.prototype._chooseHttpLibrary=function(e){var t=o;return"https:"!=e.protocol&&(t=i),t},t.OAuth2.prototype._request=function(e,t,r,o,i,a){var l=s.parse(t,!0);"https:"!=l.protocol||l.port||(l.port=443);var u=this._chooseHttpLibrary(l),c={};for(var d in this._customHeaders)c[d]=this._customHeaders[d];if(r)for(var d in r)c[d]=r[d];c.Host=l.host,c["User-Agent"]||(c["User-Agent"]="Node-oauth"),o?Buffer.isBuffer(o)?c["Content-Length"]=o.length:c["Content-Length"]=Buffer.byteLength(o):c["Content-length"]=0,!i||"Authorization"in c||(l.query||(l.query={}),l.query[this._accessTokenName]=i);var p=n.stringify(l.query);p&&(p="?"+p);var h={host:l.hostname,port:l.port,path:l.pathname+p,method:e,headers:c};this._executeRequest(u,h,o,a)},t.OAuth2.prototype._executeRequest=function(e,t,r,n){var o=a.isAnEarlyCloseHost(t.host),i=!1;function s(e,t){i||(i=!0,e.statusCode>=200&&e.statusCode<=299||301==e.statusCode||302==e.statusCode?n(null,t,e):n({statusCode:e.statusCode,data:t}))}var l="";this._agent&&(t.agent=this._agent);var u=e.request(t);u.on("response",function(e){e.on("data",function(e){l+=e}),e.on("close",function(t){o&&s(e,l)}),e.addListener("end",function(){s(e,l)})}),u.on("error",function(e){i=!0,n(e)}),("POST"==t.method||"PUT"==t.method)&&r&&u.write(r),u.end()},t.OAuth2.prototype.getAuthorizeUrl=function(e){var e=e||{};return e.client_id=this._clientId,this._baseSite+this._authorizeUrl+"?"+n.stringify(e)},t.OAuth2.prototype.getOAuthAccessToken=function(e,t,r){var t=t||{};t.client_id=this._clientId,t.client_secret=this._clientSecret;var o="refresh_token"===t.grant_type?"refresh_token":"code";t[o]=e;var i=n.stringify(t);this._request("POST",this._getAccessTokenUrl(),{"Content-Type":"application/x-www-form-urlencoded"},i,null,function(e,t,o){if(e)r(e);else{try{i=JSON.parse(t)}catch(e){i=n.parse(t)}var i,s=i.access_token,a=i.refresh_token;delete i.refresh_token,r(null,s,a,i)}})},t.OAuth2.prototype.getProtectedResource=function(e,t,r){this._request("GET",e,{},"",t,r)},t.OAuth2.prototype.get=function(e,t,r){if(this._useAuthorizationHeaderForGET){var n={Authorization:this.buildAuthHeader(t)};t=null}else n={};this._request("GET",e,n,"",t,r)}},24985:(e,t)=>{function r(e){for(var t,r,n="",o=-1;++o<e.length;)t=e.charCodeAt(o),r=o+1<e.length?e.charCodeAt(o+1):0,55296<=t&&t<=56319&&56320<=r&&r<=57343&&(t=65536+((1023&t)<<10)+(1023&r),o++),t<=127?n+=String.fromCharCode(t):t<=2047?n+=String.fromCharCode(192|t>>>6&31,128|63&t):t<=65535?n+=String.fromCharCode(224|t>>>12&15,128|t>>>6&63,128|63&t):t<=2097151&&(n+=String.fromCharCode(240|t>>>18&7,128|t>>>12&63,128|t>>>6&63,128|63&t));return n}function n(e){for(var t=Array(e.length>>2),r=0;r<t.length;r++)t[r]=0;for(var r=0;r<8*e.length;r+=8)t[r>>5]|=(255&e.charCodeAt(r/8))<<24-r%32;return t}function o(e,t){e[t>>5]|=128<<24-t%32,e[(t+64>>9<<4)+15]=t;for(var r=Array(80),n=1732584193,o=-271733879,a=-1732584194,l=271733878,u=-1009589776,c=0;c<e.length;c+=16){for(var d=n,p=o,h=a,f=l,_=u,g=0;g<80;g++){g<16?r[g]=e[c+g]:r[g]=s(r[g-3]^r[g-8]^r[g-14]^r[g-16],1);var y,v,m,w,b,k=i(i(s(n,5),(y=g,v=o,m=a,w=l,y<20?v&m|~v&w:y<40?v^m^w:y<60?v&m|v&w|m&w:v^m^w)),i(i(u,r[g]),(b=g)<20?1518500249:b<40?1859775393:b<60?-1894007588:-899497514));u=l,l=a,a=s(o,30),o=n,n=k}n=i(n,d),o=i(o,p),a=i(a,h),l=i(l,f),u=i(u,_)}return[n,o,a,l,u]}function i(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function s(e,t){return e<<t|e>>>32-t}t.HMACSHA1=function(e,t){return function(e){for(var t="",r=e.length,n=0;n<r;n+=3)for(var o=e.charCodeAt(n)<<16|(n+1<r?e.charCodeAt(n+1)<<8:0)|(n+2<r?e.charCodeAt(n+2):0),i=0;i<4;i++)8*n+6*i>8*e.length?t+="=":t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(o>>>6*(3-i)&63);return t}(function(e,t){var r=n(e);r.length>16&&(r=o(r,8*e.length));for(var i=Array(16),s=Array(16),a=0;a<16;a++)i[a]=909522486^r[a],s[a]=1549556828^r[a];var l=o(i.concat(n(t)),512+8*t.length);return function(e){for(var t="",r=0;r<32*e.length;r+=8)t+=String.fromCharCode(e[r>>5]>>>24-r%32&255);return t}(o(s.concat(l),672))}(r(e),r(t)))}},75871:(e,t,r)=>{"use strict";var n=r(6113);function o(e,t){return t=a(e,t),function(e,t){if(void 0===(r="passthrough"!==t.algorithm?n.createHash(t.algorithm):new c).write&&(r.write=r.update,r.end=r.update),u(t,r).dispatch(e),r.update||r.end(""),r.digest)return r.digest("buffer"===t.encoding?void 0:t.encoding);var r,o=r.read();return"buffer"===t.encoding?o:o.toString(t.encoding)}(e,t)}(t=e.exports=o).sha1=function(e){return o(e)},t.keys=function(e){return o(e,{excludeValues:!0,algorithm:"sha1",encoding:"hex"})},t.MD5=function(e){return o(e,{algorithm:"md5",encoding:"hex"})},t.keysMD5=function(e){return o(e,{algorithm:"md5",encoding:"hex",excludeValues:!0})};var i=n.getHashes?n.getHashes().slice():["sha1","md5"];i.push("passthrough");var s=["buffer","hex","binary","base64"];function a(e,t){t=t||{};var r={};if(r.algorithm=t.algorithm||"sha1",r.encoding=t.encoding||"hex",r.excludeValues=!!t.excludeValues,r.algorithm=r.algorithm.toLowerCase(),r.encoding=r.encoding.toLowerCase(),r.ignoreUnknown=!0===t.ignoreUnknown,r.respectType=!1!==t.respectType,r.respectFunctionNames=!1!==t.respectFunctionNames,r.respectFunctionProperties=!1!==t.respectFunctionProperties,r.unorderedArrays=!0===t.unorderedArrays,r.unorderedSets=!1!==t.unorderedSets,r.unorderedObjects=!1!==t.unorderedObjects,r.replacer=t.replacer||void 0,r.excludeKeys=t.excludeKeys||void 0,void 0===e)throw Error("Object argument required.");for(var n=0;n<i.length;++n)i[n].toLowerCase()===r.algorithm.toLowerCase()&&(r.algorithm=i[n]);if(-1===i.indexOf(r.algorithm))throw Error('Algorithm "'+r.algorithm+'"  not supported. supported values: '+i.join(", "));if(-1===s.indexOf(r.encoding)&&"passthrough"!==r.algorithm)throw Error('Encoding "'+r.encoding+'"  not supported. supported values: '+s.join(", "));return r}function l(e){return"function"==typeof e&&null!=/^function\s+\w*\s*\(\s*\)\s*{\s+\[native code\]\s+}$/i.exec(Function.prototype.toString.call(e))}function u(e,t,r){r=r||[];var n=function(e){return t.update?t.update(e,"utf8"):t.write(e,"utf8")};return{dispatch:function(t){e.replacer&&(t=e.replacer(t));var r=typeof t;return null===t&&(r="null"),this["_"+r](t)},_object:function(t){var o=Object.prototype.toString.call(t),i=/\[object (.*)\]/i.exec(o);i=(i=i?i[1]:"unknown:["+o+"]").toLowerCase();var s=null;if((s=r.indexOf(t))>=0)return this.dispatch("[CIRCULAR:"+s+"]");if(r.push(t),"undefined"!=typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(t))return n("buffer:"),n(t);if("object"!==i&&"function"!==i&&"asyncfunction"!==i){if(this["_"+i])this["_"+i](t);else if(e.ignoreUnknown)return n("["+i+"]");else throw Error('Unknown object type "'+i+'"')}else{var a=Object.keys(t);e.unorderedObjects&&(a=a.sort()),!1===e.respectType||l(t)||a.splice(0,0,"prototype","__proto__","constructor"),e.excludeKeys&&(a=a.filter(function(t){return!e.excludeKeys(t)})),n("object:"+a.length+":");var u=this;return a.forEach(function(r){u.dispatch(r),n(":"),e.excludeValues||u.dispatch(t[r]),n(",")})}},_array:function(t,o){o=void 0!==o?o:!1!==e.unorderedArrays;var i=this;if(n("array:"+t.length+":"),!o||t.length<=1)return t.forEach(function(e){return i.dispatch(e)});var s=[],a=t.map(function(t){var n=new c,o=r.slice();return u(e,n,o).dispatch(t),s=s.concat(o.slice(r.length)),n.read().toString()});return r=r.concat(s),a.sort(),this._array(a,!1)},_date:function(e){return n("date:"+e.toJSON())},_symbol:function(e){return n("symbol:"+e.toString())},_error:function(e){return n("error:"+e.toString())},_boolean:function(e){return n("bool:"+e.toString())},_string:function(e){n("string:"+e.length+":"),n(e.toString())},_function:function(t){n("fn:"),l(t)?this.dispatch("[native]"):this.dispatch(t.toString()),!1!==e.respectFunctionNames&&this.dispatch("function-name:"+String(t.name)),e.respectFunctionProperties&&this._object(t)},_number:function(e){return n("number:"+e.toString())},_xml:function(e){return n("xml:"+e.toString())},_null:function(){return n("Null")},_undefined:function(){return n("Undefined")},_regexp:function(e){return n("regex:"+e.toString())},_uint8array:function(e){return n("uint8array:"),this.dispatch(Array.prototype.slice.call(e))},_uint8clampedarray:function(e){return n("uint8clampedarray:"),this.dispatch(Array.prototype.slice.call(e))},_int8array:function(e){return n("uint8array:"),this.dispatch(Array.prototype.slice.call(e))},_uint16array:function(e){return n("uint16array:"),this.dispatch(Array.prototype.slice.call(e))},_int16array:function(e){return n("uint16array:"),this.dispatch(Array.prototype.slice.call(e))},_uint32array:function(e){return n("uint32array:"),this.dispatch(Array.prototype.slice.call(e))},_int32array:function(e){return n("uint32array:"),this.dispatch(Array.prototype.slice.call(e))},_float32array:function(e){return n("float32array:"),this.dispatch(Array.prototype.slice.call(e))},_float64array:function(e){return n("float64array:"),this.dispatch(Array.prototype.slice.call(e))},_arraybuffer:function(e){return n("arraybuffer:"),this.dispatch(new Uint8Array(e))},_url:function(e){return n("url:"+e.toString(),"utf8")},_map:function(t){n("map:");var r=Array.from(t);return this._array(r,!1!==e.unorderedSets)},_set:function(t){n("set:");var r=Array.from(t);return this._array(r,!1!==e.unorderedSets)},_file:function(e){return n("file:"),this.dispatch([e.name,e.size,e.type,e.lastModfied])},_blob:function(){if(e.ignoreUnknown)return n("[blob]");throw Error('Hashing Blob objects is currently not supported\n(see https://github.com/puleos/object-hash/issues/26)\nUse "options.replacer" or "options.ignoreUnknown"\n')},_domwindow:function(){return n("domwindow")},_bigint:function(e){return n("bigint:"+e.toString())},_process:function(){return n("process")},_timer:function(){return n("timer")},_pipe:function(){return n("pipe")},_tcp:function(){return n("tcp")},_udp:function(){return n("udp")},_tty:function(){return n("tty")},_statwatcher:function(){return n("statwatcher")},_securecontext:function(){return n("securecontext")},_connection:function(){return n("connection")},_zlib:function(){return n("zlib")},_context:function(){return n("context")},_nodescript:function(){return n("nodescript")},_httpparser:function(){return n("httpparser")},_dataview:function(){return n("dataview")},_signal:function(){return n("signal")},_fsevent:function(){return n("fsevent")},_tlswrap:function(){return n("tlswrap")}}}function c(){return{buf:"",write:function(e){this.buf+=e},end:function(e){this.buf+=e},read:function(){return this.buf}}}t.writeToStream=function(e,t,r){return void 0===r&&(r=t,t={}),u(t=a(e,t),r).dispatch(e)}},87302:(e,t,r)=>{let n;let{strict:o}=r(39491),{createHash:i}=r(6113),{format:s}=r(73837),a=r(33678);if(Buffer.isEncoding("base64url"))n=e=>e.toString("base64url");else{let e=e=>e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_");n=t=>e(t.toString("base64"))}function l(e,t,r){let o=(function(e,t){switch(e){case"HS256":case"RS256":case"PS256":case"ES256":case"ES256K":return i("sha256");case"HS384":case"RS384":case"PS384":case"ES384":return i("sha384");case"HS512":case"RS512":case"PS512":case"ES512":case"Ed25519":return i("sha512");case"Ed448":if(!a)throw TypeError("Ed448 *_hash calculation is not supported in your Node.js runtime version");return i("shake256",{outputLength:114});case"EdDSA":switch(t){case"Ed25519":return i("sha512");case"Ed448":if(!a)throw TypeError("Ed448 *_hash calculation is not supported in your Node.js runtime version");return i("shake256",{outputLength:114});default:throw TypeError("unrecognized or invalid EdDSA curve provided")}default:throw TypeError("unrecognized or invalid JWS algorithm provided")}})(t,r).update(e).digest();return n(o.slice(0,o.length/2))}e.exports={validate:function(e,t,r,n,i){let a,u;if("string"!=typeof e.claim||!e.claim)throw TypeError("names.claim must be a non-empty string");if("string"!=typeof e.source||!e.source)throw TypeError("names.source must be a non-empty string");o("string"==typeof t&&t,`${e.claim} must be a non-empty string`),o("string"==typeof r&&r,`${e.source} must be a non-empty string`);try{a=l(r,n,i)}catch(t){u=s("%s could not be validated (%s)",e.claim,t.message)}u=u||s("%s mismatch, expected %s, got: %s",e.claim,a,t),o.equal(a,t,u)},generate:l}},33678:(e,t,r)=>{let n=r(6113),[o,i]=process.version.substring(1).split(".").map(e=>parseInt(e,10)),s=(o>12||12===o&&i>=8)&&n.getHashes().includes("shake256");e.exports=s},80137:(e,t,r)=>{"use strict";let n;let{inspect:o}=r(73837),i=r(13685),s=r(6113),{strict:a}=r(39491),l=r(63477),u=r(57310),{URL:c,URLSearchParams:d}=r(57310),p=r(39797),h=r(87302),f=r(74655),_=r(45820),g=r(69080),y=r(69054),v=r(15070),{assertSigningAlgValuesSupport:m,assertIssuerConfiguration:w}=r(914),b=r(58945),k=r(8140),x=r(83818),A=r(80536),{OPError:S,RPError:E}=r(80994),O=r(46438),{random:T}=r(47389),j=r(82472),{CLOCK_TOLERANCE:P}=r(77731),{keystores:C}=r(25602),R=r(56310),$=r(52936),{authenticatedPost:U,resolveResponseType:M,resolveRedirectUri:L}=r(36223),{queryKeyStore:H}=r(92792),N=r(16670),[I,D]=process.version.slice(1).split(".").map(e=>parseInt(e,10)),q=I>=17||16===I&&D>=9,z=Symbol(),W=Symbol(),J=Symbol();function B(e){return b(e,"access_token","code","error_description","error_uri","error","expires_in","id_token","iss","response","session_state","state","token_type")}function K(e,t="Bearer"){return`${t} ${e}`}function F(e){let t=u.parse(e);return t.search?l.parse(t.search.substring(1)):{}}function G(e,t,r){if(void 0===e[r])throw new E({message:`missing required JWT property ${r}`,jwt:t})}function V(e){let t={client_id:this.client_id,scope:"openid",response_type:M.call(this),redirect_uri:L.call(this),...e};return Object.entries(t).forEach(([e,r])=>{null==r?delete t[e]:"claims"===e&&"object"==typeof r?t[e]=JSON.stringify(r):"resource"===e&&Array.isArray(r)?t[e]=r:"string"!=typeof r&&(t[e]=String(r))}),t}function X(e){if(!k(e)||!Array.isArray(e.keys)||e.keys.some(e=>!k(e)||!("kty"in e)))throw TypeError("jwks must be a JSON Web Key Set formatted object");return R.fromJWKS(e,{onlyPrivate:!0})}class Y{#e;#t;#r;#n;constructor(e,t,r={},n,o){if(this.#e=new Map,this.#t=e,this.#r=t,"string"!=typeof r.client_id||!r.client_id)throw TypeError("client_id is required");let i={grant_types:["authorization_code"],id_token_signed_response_alg:"RS256",authorization_signed_response_alg:"RS256",response_types:["code"],token_endpoint_auth_method:"client_secret_basic",...this.fapi1()?{grant_types:["authorization_code","implicit"],id_token_signed_response_alg:"PS256",authorization_signed_response_alg:"PS256",response_types:["code id_token"],tls_client_certificate_bound_access_tokens:!0,token_endpoint_auth_method:void 0}:void 0,...this.fapi2()?{id_token_signed_response_alg:"PS256",authorization_signed_response_alg:"PS256",token_endpoint_auth_method:void 0}:void 0,...r};if(this.fapi())switch(i.token_endpoint_auth_method){case"self_signed_tls_client_auth":case"tls_client_auth":break;case"private_key_jwt":if(!n)throw TypeError("jwks is required");break;case void 0:throw TypeError("token_endpoint_auth_method is required");default:throw TypeError("invalid or unsupported token_endpoint_auth_method")}if(this.fapi2()&&(i.tls_client_certificate_bound_access_tokens&&i.dpop_bound_access_tokens||!i.tls_client_certificate_bound_access_tokens&&!i.dpop_bound_access_tokens))throw TypeError("either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true");if(function(e,t,r){if(t.token_endpoint_auth_method||function(e,t){try{let r=e.issuer.token_endpoint_auth_methods_supported;!r.includes(t.token_endpoint_auth_method)&&r.includes("client_secret_post")&&(t.token_endpoint_auth_method="client_secret_post")}catch(e){}}(e,r),t.redirect_uri){if(t.redirect_uris)throw TypeError("provide a redirect_uri or redirect_uris, not both");r.redirect_uris=[t.redirect_uri],delete r.redirect_uri}if(t.response_type){if(t.response_types)throw TypeError("provide a response_type or response_types, not both");r.response_types=[t.response_type],delete r.response_type}}(this,r,i),m("token",this.issuer,i),["introspection","revocation"].forEach(e=>{(function(e,t,r){if(!t[`${e}_endpoint`])return;let n=r.token_endpoint_auth_method,o=r.token_endpoint_auth_signing_alg,i=`${e}_endpoint_auth_method`,s=`${e}_endpoint_auth_signing_alg`;void 0===r[i]&&void 0===r[s]&&(void 0!==n&&(r[i]=n),void 0!==o&&(r[s]=o))})(e,this.issuer,i),m(e,this.issuer,i)}),Object.entries(i).forEach(([e,t])=>{this.#e.set(e,t),this[e]||Object.defineProperty(this,e,{get(){return this.#e.get(e)},enumerable:!0})}),void 0!==n){let e=X.call(this,n);C.set(this,e)}null!=o&&o.additionalAuthorizedParties&&(this.#n=$(o.additionalAuthorizedParties)),this[P]=0}authorizationUrl(e={}){if(!k(e))throw TypeError("params must be a plain object");w(this.issuer,"authorization_endpoint");let t=new c(this.issuer.authorization_endpoint);for(let[r,n]of Object.entries(V.call(this,e)))if(Array.isArray(n))for(let e of(t.searchParams.delete(r),n))t.searchParams.append(r,e);else t.searchParams.set(r,n);return t.href.replace(/\+/g,"%20")}authorizationPost(e={}){if(!k(e))throw TypeError("params must be a plain object");let t=V.call(this,e),r=Object.keys(t).map(e=>`<input type="hidden" name="${e}" value="${t[e]}"/>`).join("\n");return`<!DOCTYPE html>
<head>
<title>Requesting Authorization</title>
</head>
<body onload="javascript:document.forms[0].submit()">
<form method="post" action="${this.issuer.authorization_endpoint}">
  ${r}
</form>
</body>
</html>`}endSessionUrl(e={}){let t;w(this.issuer,"end_session_endpoint");let{0:r,length:n}=this.post_logout_redirect_uris||[],{post_logout_redirect_uri:o=1===n?r:void 0}=e;if({id_token_hint:t,...e}=e,t instanceof A){if(!t.id_token)throw TypeError("id_token not present in TokenSet");t=t.id_token}let i=u.parse(this.issuer.end_session_endpoint),s=y(F(this.issuer.end_session_endpoint),e,{post_logout_redirect_uri:o,client_id:this.client_id},{id_token_hint:t});return Object.entries(s).forEach(([e,t])=>{null==t&&delete s[e]}),i.search=null,i.query=s,u.format(i)}callbackParams(e){let t=e instanceof i.IncomingMessage||e&&e.method&&e.url;if("string"!=typeof e&&!t)throw TypeError("#callbackParams only accepts string urls, http.IncomingMessage or a lookalike");if(!t)return B(F(e));switch(e.method){case"GET":return B(F(e.url));case"POST":if(void 0===e.body)throw TypeError("incoming message body missing, include a body parser prior to this method call");switch(typeof e.body){case"object":case"string":if(Buffer.isBuffer(e.body))return B(l.parse(e.body.toString("utf-8")));if("string"==typeof e.body)return B(l.parse(e.body));return B(e.body);default:throw TypeError("invalid IncomingMessage body object")}default:throw TypeError("invalid IncomingMessage method")}}async callback(e,t,r={},{exchangeBody:n,clientAssertionPayload:o,DPoP:i}={}){let s=B(t);if(!r.jarm||"response"in t){if("response"in t){let e=await this.decryptJARM(s.response);s=await this.validateJARM(e)}}else throw new E({message:"expected a JARM response",checks:r,params:s});if(this.default_max_age&&!r.max_age&&(r.max_age=this.default_max_age),s.state&&!r.state)throw TypeError("checks.state argument is missing");if(!s.state&&r.state)throw new E({message:"state missing from the response",checks:r,params:s});if(r.state!==s.state)throw new E({printf:["state mismatch, expected %s, got: %s",r.state,s.state],checks:r,params:s});if("iss"in s){if(w(this.issuer,"issuer"),s.iss!==this.issuer.issuer)throw new E({printf:["iss mismatch, expected %s, got: %s",this.issuer.issuer,s.iss],params:s})}else if(this.issuer.authorization_response_iss_parameter_supported&&!("id_token"in s)&&!("response"in t))throw new E({message:"iss missing from the response",params:s});if(s.error)throw new S(s);let a={code:["code"],id_token:["id_token"],token:["access_token","token_type"]};if(r.response_type){for(let e of r.response_type.split(" "))if("none"===e){if(s.code||s.id_token||s.access_token)throw new E({message:'unexpected params encountered for "none" response',checks:r,params:s})}else for(let t of a[e])if(!s[t])throw new E({message:`${t} missing from response`,checks:r,params:s})}if(s.id_token){let e=new A(s);if(await this.decryptIdToken(e),await this.validateIdToken(e,r.nonce,"authorization",r.max_age,r.state),!s.code)return e}if(s.code){let t=await this.grant({...n,grant_type:"authorization_code",code:s.code,redirect_uri:e,code_verifier:r.code_verifier},{clientAssertionPayload:o,DPoP:i});return await this.decryptIdToken(t),await this.validateIdToken(t,r.nonce,"token",r.max_age),s.session_state&&(t.session_state=s.session_state),t}return new A(s)}async oauthCallback(e,t,r={},{exchangeBody:n,clientAssertionPayload:o,DPoP:i}={}){let s=B(t);if(!r.jarm||"response"in t){if("response"in t){let e=await this.decryptJARM(s.response);s=await this.validateJARM(e)}}else throw new E({message:"expected a JARM response",checks:r,params:s});if(s.state&&!r.state)throw TypeError("checks.state argument is missing");if(!s.state&&r.state)throw new E({message:"state missing from the response",checks:r,params:s});if(r.state!==s.state)throw new E({printf:["state mismatch, expected %s, got: %s",r.state,s.state],checks:r,params:s});if("iss"in s){if(w(this.issuer,"issuer"),s.iss!==this.issuer.issuer)throw new E({printf:["iss mismatch, expected %s, got: %s",this.issuer.issuer,s.iss],params:s})}else if(this.issuer.authorization_response_iss_parameter_supported&&!("id_token"in s)&&!("response"in t))throw new E({message:"iss missing from the response",params:s});if(s.error)throw new S(s);if("string"==typeof s.id_token&&s.id_token.length)throw new E({message:"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()",params:s});delete s.id_token;let a={code:["code"],token:["access_token","token_type"]};if(r.response_type)for(let e of r.response_type.split(" ")){if("none"===e&&(s.code||s.id_token||s.access_token))throw new E({message:'unexpected params encountered for "none" response',checks:r,params:s});if(a[e]){for(let t of a[e])if(!s[t])throw new E({message:`${t} missing from response`,checks:r,params:s})}}if(s.code){let t=await this.grant({...n,grant_type:"authorization_code",code:s.code,redirect_uri:e,code_verifier:r.code_verifier},{clientAssertionPayload:o,DPoP:i});if("string"==typeof t.id_token&&t.id_token.length)throw new E({message:"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()",params:s});return delete t.id_token,t}return new A(s)}async decryptIdToken(e){if(!this.id_token_encrypted_response_alg)return e;let t=e;if(t instanceof A){if(!t.id_token)throw TypeError("id_token not present in TokenSet");t=t.id_token}let r=this.id_token_encrypted_response_alg,n=this.id_token_encrypted_response_enc,o=await this.decryptJWE(t,r,n);return e instanceof A?(e.id_token=o,e):o}async validateJWTUserinfo(e){let t=this.userinfo_signed_response_alg;return this.validateJWT(e,t,[])}async decryptJARM(e){if(!this.authorization_encrypted_response_alg)return e;let t=this.authorization_encrypted_response_alg,r=this.authorization_encrypted_response_enc;return this.decryptJWE(e,t,r)}async decryptJWTUserinfo(e){if(!this.userinfo_encrypted_response_alg)return e;let t=this.userinfo_encrypted_response_alg,r=this.userinfo_encrypted_response_enc;return this.decryptJWE(e,t,r)}async decryptJWE(e,t,r="A128CBC-HS256"){let n;let o=JSON.parse(g.decode(e.split(".")[0]));if(o.alg!==t)throw new E({printf:["unexpected JWE alg received, expected %s, got: %s",t,o.alg],jwt:e});if(o.enc!==r)throw new E({printf:["unexpected JWE enc received, expected %s, got: %s",r,o.enc],jwt:e});let i=e=>new TextDecoder().decode(e.plaintext);if(t.match(/^(?:RSA|ECDH)/)){let t=await C.get(this),r=p.decodeProtectedHeader(e);for(let o of t.all({...r,use:"enc"}))if(n=await p.compactDecrypt(e,await o.keyObject(r.alg)).then(i,()=>{}))break}else n=await p.compactDecrypt(e,this.secretForAlg("dir"===t?r:t)).then(i,()=>{});if(!n)throw new E({message:"failed to decrypt JWE",jwt:e});return n}async validateIdToken(e,t,r,n,o){let i=e,s=this.id_token_signed_response_alg;if(i instanceof A){if(!i.id_token)throw TypeError("id_token not present in TokenSet");i=i.id_token}i=String(i);let a=O(),{protected:l,payload:u,key:c}=await this.validateJWT(i,s);if("number"==typeof n||n!==J&&this.require_auth_time){if(!u.auth_time)throw new E({message:"missing required JWT property auth_time",jwt:i});if("number"!=typeof u.auth_time)throw new E({message:"JWT auth_time claim must be a JSON numeric value",jwt:i})}if("number"==typeof n&&u.auth_time+n<a-this[P])throw new E({printf:["too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i",n,u.auth_time,a-this[P]],now:a,tolerance:this[P],auth_time:u.auth_time,jwt:i});if(t!==W&&(u.nonce||void 0!==t)&&u.nonce!==t)throw new E({printf:["nonce mismatch, expected %s, got: %s",t,u.nonce],jwt:i});if("authorization"===r){if(!u.at_hash&&e.access_token)throw new E({message:"missing required property at_hash",jwt:i});if(!u.c_hash&&e.code)throw new E({message:"missing required property c_hash",jwt:i});if(this.fapi1()&&!u.s_hash&&(e.state||o))throw new E({message:"missing required property s_hash",jwt:i});if(u.s_hash){if(!o)throw TypeError('cannot verify s_hash, "checks.state" property not provided');try{h.validate({claim:"s_hash",source:"state"},u.s_hash,o,l.alg,c.jwk&&c.jwk.crv)}catch(e){throw new E({message:e.message,jwt:i})}}}if(this.fapi()&&u.iat<a-3600)throw new E({printf:["JWT issued too far in the past, now %i, iat %i",a,u.iat],now:a,tolerance:this[P],iat:u.iat,jwt:i});if(e.access_token&&void 0!==u.at_hash)try{h.validate({claim:"at_hash",source:"access_token"},u.at_hash,e.access_token,l.alg,c.jwk&&c.jwk.crv)}catch(e){throw new E({message:e.message,jwt:i})}if(e.code&&void 0!==u.c_hash)try{h.validate({claim:"c_hash",source:"code"},u.c_hash,e.code,l.alg,c.jwk&&c.jwk.crv)}catch(e){throw new E({message:e.message,jwt:i})}return e}async validateJWT(e,t,r=["iss","sub","aud","exp","iat"]){let n,o,i;let s="https://self-issued.me"===this.issuer.issuer,l=O();try{({header:n,payload:o}=_(e,{complete:!0}))}catch(t){throw new E({printf:["failed to decode JWT (%s: %s)",t.name,t.message],jwt:e})}if(n.alg!==t)throw new E({printf:["unexpected JWT alg received, expected %s, got: %s",t,n.alg],jwt:e});if(s&&(r=[...r,"sub_jwk"]),r.forEach(G.bind(void 0,o,e)),void 0!==o.iss){let t=this.issuer.issuer;if(this.#r&&(t=this.issuer.issuer.replace("{tenantid}",o.tid)),o.iss!==t)throw new E({printf:["unexpected iss value, expected %s, got: %s",t,o.iss],jwt:e})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw new E({message:"JWT iat claim must be a JSON numeric value",jwt:e});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw new E({message:"JWT nbf claim must be a JSON numeric value",jwt:e});if(o.nbf>l+this[P])throw new E({printf:["JWT not active yet, now %i, nbf %i",l+this[P],o.nbf],now:l,tolerance:this[P],nbf:o.nbf,jwt:e})}if(void 0!==o.exp){if("number"!=typeof o.exp)throw new E({message:"JWT exp claim must be a JSON numeric value",jwt:e});if(l-this[P]>=o.exp)throw new E({printf:["JWT expired, now %i, exp %i",l-this[P],o.exp],now:l,tolerance:this[P],exp:o.exp,jwt:e})}if(void 0!==o.aud){if(Array.isArray(o.aud)){if(o.aud.length>1&&!o.azp)throw new E({message:"missing required JWT property azp",jwt:e});if(!o.aud.includes(this.client_id))throw new E({printf:["aud is missing the client_id, expected %s to be included in %j",this.client_id,o.aud],jwt:e})}else if(o.aud!==this.client_id)throw new E({printf:["aud mismatch, expected %s, got: %s",this.client_id,o.aud],jwt:e})}if(void 0!==o.azp){let t=this.#n;if(!(t="string"==typeof t?[this.client_id,t]:Array.isArray(t)?[this.client_id,...t]:[this.client_id]).includes(o.azp))throw new E({printf:["azp mismatch, got: %s",o.azp],jwt:e})}if(s){try{a(k(o.sub_jwk));let e=await p.importJWK(o.sub_jwk,n.alg);a.equal(e.type,"public"),i=[{keyObject:()=>e}]}catch(t){throw new E({message:"failed to use sub_jwk claim as an asymmetric JSON Web Key",jwt:e})}if(await p.calculateJwkThumbprint(o.sub_jwk)!==o.sub)throw new E({message:"failed to match the subject with sub_jwk",jwt:e})}else n.alg.startsWith("HS")?i=[this.secretForAlg(n.alg)]:"none"!==n.alg&&(i=await H.call(this.issuer,{...n,use:"sig"}));if(!i&&"none"===n.alg)return{protected:n,payload:o};for(let t of i){let r=await p.compactVerify(e,t instanceof Uint8Array?t:await t.keyObject(n.alg)).catch(()=>{});if(r)return{payload:o,protected:r.protectedHeader,key:t}}throw new E({message:"failed to validate JWT signature",jwt:e})}async refresh(e,{exchangeBody:t,clientAssertionPayload:r,DPoP:n}={}){let o=e;if(o instanceof A){if(!o.refresh_token)throw TypeError("refresh_token not present in TokenSet");o=o.refresh_token}let i=await this.grant({...t,grant_type:"refresh_token",refresh_token:String(o)},{clientAssertionPayload:r,DPoP:n});if(i.id_token&&(await this.decryptIdToken(i),await this.validateIdToken(i,W,"token",J),e instanceof A&&e.id_token)){let t=e.claims().sub,r=i.claims().sub;if(r!==t)throw new E({printf:["sub mismatch, expected %s, got: %s",t,r],jwt:i.id_token})}return i}async requestResource(e,t,{method:r,headers:n,body:o,DPoP:i,tokenType:s=i?"DPoP":t instanceof A?t.token_type:"Bearer"}={},a){if(t instanceof A){if(!t.access_token)throw TypeError("access_token not present in TokenSet");t=t.access_token}if(t){if("string"!=typeof t)throw TypeError("invalid access token provided")}else throw TypeError("no access token provided");let l={headers:{Authorization:K(t,s),...n},body:o},u=!!this.tls_client_certificate_bound_access_tokens,c=await j.call(this,{...l,responseType:"buffer",method:r,url:e},{accessToken:t,mTLS:u,DPoP:i}),d=c.headers["www-authenticate"];return a!==z&&d&&d.toLowerCase().startsWith("dpop ")&&"use_dpop_nonce"===v(d).error?this.requestResource(e,t,{method:r,headers:n,body:o,DPoP:i,tokenType:s}):c}async userinfo(e,{method:t="GET",via:r="header",tokenType:n,params:o,DPoP:i}={}){let s;w(this.issuer,"userinfo_endpoint");let l={tokenType:n,method:String(t).toUpperCase(),DPoP:i};if("GET"!==l.method&&"POST"!==l.method)throw TypeError("#userinfo() method can only be POST or a GET");if("body"===r&&"POST"!==l.method)throw TypeError("can only send body on POST");let u=!!(this.userinfo_signed_response_alg||this.userinfo_encrypted_response_alg);u?l.headers={Accept:"application/jwt"}:l.headers={Accept:"application/json"},this.tls_client_certificate_bound_access_tokens&&this.issuer.mtls_endpoint_aliases&&(s=this.issuer.mtls_endpoint_aliases.userinfo_endpoint),s=new c(s||this.issuer.userinfo_endpoint),"body"===r&&(l.headers.Authorization=void 0,l.headers["Content-Type"]="application/x-www-form-urlencoded",l.body=new d,l.body.append("access_token",e instanceof A?e.access_token:e)),o&&("GET"===l.method?Object.entries(o).forEach(([e,t])=>{s.searchParams.append(e,t)}):l.body?Object.entries(o).forEach(([e,t])=>{l.body.append(e,t)}):(l.body=new d,l.headers["Content-Type"]="application/x-www-form-urlencoded",Object.entries(o).forEach(([e,t])=>{l.body.append(e,t)}))),l.body&&(l.body=l.body.toString());let p=await this.requestResource(s,e,l),h=x(p,{bearer:!0});if(u){if(!/^application\/jwt/.test(p.headers["content-type"]))throw new E({message:"expected application/jwt response from the userinfo_endpoint",response:p});let e=p.body.toString(),t=await this.decryptJWTUserinfo(e);if(this.userinfo_signed_response_alg)({payload:h}=await this.validateJWTUserinfo(t));else try{h=JSON.parse(t),a(k(h))}catch(e){throw new E({message:"failed to parse userinfo JWE payload as JSON",jwt:t})}}else try{h=JSON.parse(p.body)}catch(e){throw Object.defineProperty(e,"response",{value:p}),e}if(e instanceof A&&e.id_token){let t=e.claims().sub;if(h.sub!==t)throw new E({printf:["userinfo sub mismatch, expected %s, got: %s",t,h.sub],body:h,jwt:e.id_token})}return h}encryptionSecret(e){let t=e<=256?"sha256":e<=384?"sha384":e<=512&&"sha512";if(!t)throw Error("unsupported symmetric encryption key derivation");return s.createHash(t).update(this.client_secret).digest().slice(0,e/8)}secretForAlg(e){if(!this.client_secret)throw TypeError("client_secret is required");return/^A(\d{3})(?:GCM)?KW$/.test(e)?this.encryptionSecret(parseInt(RegExp.$1,10)):/^A(\d{3})(?:GCM|CBC-HS(\d{3}))$/.test(e)?this.encryptionSecret(parseInt(RegExp.$2||RegExp.$1,10)):new TextEncoder().encode(this.client_secret)}async grant(e,{clientAssertionPayload:t,DPoP:r}={},n){let o;w(this.issuer,"token_endpoint");let i=await U.call(this,"token",{form:e,responseType:"json"},{clientAssertionPayload:t,DPoP:r});try{o=x(i)}catch(o){if(n!==z&&o instanceof S&&"use_dpop_nonce"===o.error)return this.grant(e,{clientAssertionPayload:t,DPoP:r},z);throw o}return new A(o)}async deviceAuthorization(e={},{exchangeBody:t,clientAssertionPayload:r,DPoP:n}={}){w(this.issuer,"device_authorization_endpoint"),w(this.issuer,"token_endpoint");let o=V.call(this,{client_id:this.client_id,redirect_uri:null,response_type:null,...e}),i=x(await U.call(this,"device_authorization",{responseType:"json",form:o},{clientAssertionPayload:r,endpointAuthMethod:"token"}));return new N({client:this,exchangeBody:t,clientAssertionPayload:r,response:i,maxAge:e.max_age,DPoP:n})}async revoke(e,t,{revokeBody:r,clientAssertionPayload:n}={}){if(w(this.issuer,"revocation_endpoint"),void 0!==t&&"string"!=typeof t)throw TypeError("hint must be a string");let o={...r,token:e};t&&(o.token_type_hint=t),x(await U.call(this,"revocation",{form:o},{clientAssertionPayload:n}),{body:!1})}async introspect(e,t,{introspectBody:r,clientAssertionPayload:n}={}){if(w(this.issuer,"introspection_endpoint"),void 0!==t&&"string"!=typeof t)throw TypeError("hint must be a string");let o={...r,token:e};return t&&(o.token_type_hint=t),x(await U.call(this,"introspection",{form:o,responseType:"json"},{clientAssertionPayload:n}))}static async register(e,t={}){let{initialAccessToken:r,jwks:n,...o}=t;if(w(this.issuer,"registration_endpoint"),void 0!==n&&!(e.jwks||e.jwks_uri)){let t=await X.call(this,n);e.jwks=t.toJWKS()}return new this(x(await j.call(this,{headers:{Accept:"application/json",...r?{Authorization:K(r)}:void 0},responseType:"json",json:e,url:this.issuer.registration_endpoint,method:"POST"}),{statusCode:201,bearer:!0}),n,o)}get metadata(){return $(Object.fromEntries(this.#e.entries()))}static async fromUri(e,t,r,n){return new this(x(await j.call(this,{method:"GET",url:e,responseType:"json",headers:{Authorization:K(t),Accept:"application/json"}}),{bearer:!0}),r,n)}async requestObject(e={},{sign:t=this.request_object_signing_alg||"none",encrypt:{alg:r=this.request_object_encryption_alg,enc:n=this.request_object_encryption_enc||"A128CBC-HS256"}={}}={}){let o,i;if(!k(e))throw TypeError("requestObject must be a plain object");let s=O(),a={alg:t,typ:"oauth-authz-req+jwt"},l=JSON.stringify(y({},e,{iss:this.client_id,aud:this.issuer.issuer,client_id:this.client_id,jti:T(),iat:s,exp:s+300,...this.fapi()?{nbf:s}:void 0}));if("none"===t)o=[g.encode(JSON.stringify(a)),g.encode(l),""].join(".");else{let e=t.startsWith("HS");if(e)i=this.secretForAlg(t);else{let e=await C.get(this);if(!e)throw TypeError(`no keystore present for client, cannot sign using alg ${t}`);if(!(i=e.get({alg:t,use:"sig"})))throw TypeError(`no key to sign with found for alg ${t}`)}o=await new p.CompactSign(new TextEncoder().encode(l)).setProtectedHeader({...a,kid:e?void 0:i.jwk.kid}).sign(e?i:await i.keyObject(t))}if(!r)return o;let u={alg:r,enc:n,cty:"oauth-authz-req+jwt"};return u.alg.match(/^(RSA|ECDH)/)?[i]=await H.call(this.issuer,{alg:u.alg,use:"enc"},{allowMulti:!0}):i=this.secretForAlg("dir"===u.alg?u.enc:u.alg),new p.CompactEncrypt(new TextEncoder().encode(o)).setProtectedHeader({...u,kid:i instanceof Uint8Array?void 0:i.jwk.kid}).encrypt(i instanceof Uint8Array?i:await i.keyObject(u.alg))}async pushedAuthorizationRequest(e={},{clientAssertionPayload:t}={}){w(this.issuer,"pushed_authorization_request_endpoint");let r={..."request"in e?e:V.call(this,e),client_id:this.client_id},n=await U.call(this,"pushed_authorization_request",{responseType:"json",form:r},{clientAssertionPayload:t,endpointAuthMethod:"token"}),o=x(n,{statusCode:201});if(!("expires_in"in o))throw new E({message:"expected expires_in in Pushed Authorization Successful Response",response:n});if("number"!=typeof o.expires_in)throw new E({message:"invalid expires_in value in Pushed Authorization Successful Response",response:n});if(!("request_uri"in o))throw new E({message:"expected request_uri in Pushed Authorization Successful Response",response:n});if("string"!=typeof o.request_uri)throw new E({message:"invalid request_uri value in Pushed Authorization Successful Response",response:n});return o}get issuer(){return this.#t}[o.custom](){return`${this.constructor.name} ${o(this.metadata,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}fapi(){return this.fapi1()||this.fapi2()}fapi1(){return"FAPI1Client"===this.constructor.name}fapi2(){return"FAPI2Client"===this.constructor.name}async validateJARM(e){let t=this.authorization_signed_response_alg,{payload:r}=await this.validateJWT(e,t,["iss","exp","aud"]);return B(r)}async dpopProof(e,t,r){let o;if(!k(e))throw TypeError("payload must be a plain object");if(f(t))o=t;else if("CryptoKey"===t[Symbol.toStringTag])o=t;else if("node:crypto"===p.cryptoRuntime)o=s.createPrivateKey(t);else throw TypeError("unrecognized crypto runtime");if("private"!==o.type)throw TypeError('"DPoP" option must be a private key');let i=n.call(this,o,t);if(!i)throw TypeError("could not determine DPoP JWS Algorithm");return new p.SignJWT({ath:r?g.encode(s.createHash("sha256").update(r).digest()):void 0,...e}).setProtectedHeader({alg:i,typ:"dpop+jwt",jwk:await ee(o,t)}).setIssuedAt().setJti(T()).sign(o)}}function Z(e){switch(e.algorithm.name){case"Ed25519":case"Ed448":return"EdDSA";case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512"}break;case"RSASSA-PKCS1-v1_5":return`RS${e.algorithm.hash.name.slice(4)}`;case"RSA-PSS":return`PS${e.algorithm.hash.name.slice(4)}`;default:throw TypeError("unsupported DPoP private key")}}if("node:crypto"===p.cryptoRuntime){n=function(n,s){if("CryptoKey"===s[Symbol.toStringTag])return Z(n);switch(n.asymmetricKeyType){case"ed25519":case"ed448":return"EdDSA";case"ec":return function(e,n){switch("object"==typeof n&&"object"==typeof n.key&&n.key.crv){case"P-256":return"ES256";case"secp256k1":return"ES256K";case"P-384":return"ES384";case"P-512":return"ES512"}let s=e.export({format:"der",type:"pkcs8"}),a=s[1]<128?17:18,l=s[a],u=s.slice(a+1,a+1+l);if(u.equals(t))return"ES256";if(u.equals(r))return"ES384";if(u.equals(o))return"ES512";if(u.equals(i))return"ES256K";throw TypeError("unsupported DPoP private key curve")}(n,s);case"rsa":case q&&"rsa-pss":return function(t,r,n){if("object"==typeof r&&"jwk"===r.format&&r.key&&r.key.alg)return r.key.alg;if(Array.isArray(n)){let r=n.filter(RegExp.prototype.test.bind(e));return"rsa-pss"===t.asymmetricKeyType&&(r=r.filter(e=>e.startsWith("PS"))),["PS256","PS384","PS512","RS256","RS384","RS384"].find(e=>r.includes(e))}return"PS256"}(n,s,this.issuer.dpop_signing_alg_values_supported);default:throw TypeError("unsupported DPoP private key")}};let e=/^(?:RS|PS)(?:256|384|512)$/,t=Buffer.from([42,134,72,206,61,3,1,7]),r=Buffer.from([43,129,4,0,34]),o=Buffer.from([43,129,4,0,35]),i=Buffer.from([43,129,4,0,10])}else n=Z;let Q=new WeakMap;async function ee(e,t){if("node:crypto"===p.cryptoRuntime&&"object"==typeof t&&"object"==typeof t.key&&"jwk"===t.format)return b(t.key,"kty","crv","x","y","e","n");if(Q.has(t))return Q.get(t);let r=b(await p.exportJWK(e),"kty","crv","x","y","e","n");return(f(t)||"WebCryptoAPI"===p.cryptoRuntime)&&Q.set(t,r),r}e.exports=(e,t=!1)=>class extends Y{constructor(...r){super(e,t,...r)}static get issuer(){return e}},e.exports.BaseClient=Y},16670:(e,t,r)=>{let{inspect:n}=r(73837),{RPError:o,OPError:i}=r(80994),s=r(46438);class a{#o;#i;#s;#a;#l;#u;#c;#d;#p;constructor({client:e,exchangeBody:t,clientAssertionPayload:r,response:n,maxAge:i,DPoP:a}){if(["verification_uri","user_code","device_code"].forEach(e=>{if("string"!=typeof n[e]||!n[e])throw new o(`expected ${e} string to be returned by Device Authorization Response, got %j`,n[e])}),!Number.isSafeInteger(n.expires_in))throw new o("expected expires_in number to be returned by Device Authorization Response, got %j",n.expires_in);this.#u=s()+n.expires_in,this.#i=e,this.#a=a,this.#d=i,this.#l=t,this.#s=r,this.#p=n,this.#c=1e3*n.interval||5e3}abort(){this.#o=!0}async poll({signal:e}={}){let t;if(e&&e.aborted||this.#o)throw new o("polling aborted");if(this.expired())throw new o("the device code %j has expired and the device authorization session has concluded",this.device_code);await new Promise(e=>setTimeout(e,this.#c));try{t=await this.#i.grant({...this.#l,grant_type:"urn:ietf:params:oauth:grant-type:device_code",device_code:this.device_code},{clientAssertionPayload:this.#s,DPoP:this.#a})}catch(t){switch(t instanceof i&&t.error){case"slow_down":this.#c+=5e3;case"authorization_pending":return this.poll({signal:e});default:throw t}}return"id_token"in t&&(await this.#i.decryptIdToken(t),await this.#i.validateIdToken(t,void 0,"token",this.#d)),t}get device_code(){return this.#p.device_code}get user_code(){return this.#p.user_code}get verification_uri(){return this.#p.verification_uri}get verification_uri_complete(){return this.#p.verification_uri_complete}get expires_in(){return Math.max.apply(null,[this.#u-s(),0])}expired(){return 0===this.expires_in}[n.custom](){return`${this.constructor.name} ${n(this.#p,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}}e.exports=a},80994:(e,t,r)=>{let{format:n}=r(73837);class o extends Error{constructor({error_description:e,error:t,error_uri:r,session_state:n,state:o,scope:i},s){super(e?`${t} (${e})`:t),Object.assign(this,{error:t},e&&{error_description:e},r&&{error_uri:r},o&&{state:o},i&&{scope:i},n&&{session_state:n}),s&&Object.defineProperty(this,"response",{value:s}),this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor)}}class i extends Error{constructor(...e){if("string"==typeof e[0])super(n(...e));else{let{message:t,printf:r,response:o,...i}=e[0];r?super(n(...r)):super(t),Object.assign(this,i),o&&Object.defineProperty(this,"response",{value:o})}this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor)}}e.exports={OPError:o,RPError:i}},914:e=>{e.exports={assertSigningAlgValuesSupport:function(e,t,r){if(!t[`${e}_endpoint`])return;let n=`${e}_endpoint_auth_method`,o=`${e}_endpoint_auth_signing_alg`,i=`${e}_endpoint_auth_signing_alg_values_supported`;if(r[n]&&r[n].endsWith("_jwt")&&!r[o]&&!t[i])throw TypeError(`${i} must be configured on the issuer if ${o} is not defined on a client`)},assertIssuerConfiguration:function(e,t){if(!e[t])throw TypeError(`${t} must be configured on the issuer`)}}},69080:e=>{let t;if(Buffer.isEncoding("base64url"))t=(e,t="utf8")=>Buffer.from(e,t).toString("base64url");else{let e=e=>e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_");t=(t,r="utf8")=>e(Buffer.from(t,r).toString("base64"))}e.exports.decode=e=>Buffer.from(e,"base64"),e.exports.encode=t},36223:(e,t,r)=>{let n=r(39797),{RPError:o}=r(80994),{assertIssuerConfiguration:i}=r(914),{random:s}=r(47389),a=r(46438),l=r(82472),{keystores:u}=r(25602),c=r(49366),d=e=>encodeURIComponent(e).replace(/%20/g,"+");async function p(e,t){let r=this[`${e}_endpoint_auth_signing_alg`];if(r||i(this.issuer,`${e}_endpoint_auth_signing_alg_values_supported`),"client_secret_jwt"===this[`${e}_endpoint_auth_method`]){if(!r){let t=this.issuer[`${e}_endpoint_auth_signing_alg_values_supported`];r=Array.isArray(t)&&t.find(e=>/^HS(?:256|384|512)/.test(e))}if(!r)throw new o(`failed to determine a JWS Algorithm to use for ${this[`${e}_endpoint_auth_method`]} Client Assertion`);return new n.CompactSign(Buffer.from(JSON.stringify(t))).setProtectedHeader({alg:r}).sign(this.secretForAlg(r))}let s=await u.get(this);if(!s)throw TypeError("no client jwks provided for signing a client assertion with");if(!r){let t=this.issuer[`${e}_endpoint_auth_signing_alg_values_supported`];r=Array.isArray(t)&&t.find(e=>s.get({alg:e,use:"sig"}))}if(!r)throw new o(`failed to determine a JWS Algorithm to use for ${this[`${e}_endpoint_auth_method`]} Client Assertion`);let a=s.get({alg:r,use:"sig"});if(!a)throw new o(`no key found in client jwks to sign a client assertion with using alg ${r}`);return new n.CompactSign(Buffer.from(JSON.stringify(t))).setProtectedHeader({alg:r,kid:a.jwk&&a.jwk.kid}).sign(await a.keyObject(r))}async function h(e,{clientAssertionPayload:t}={}){switch(this[`${e}_endpoint_auth_method`]){case"self_signed_tls_client_auth":case"tls_client_auth":case"none":return{form:{client_id:this.client_id}};case"client_secret_post":if("string"!=typeof this.client_secret)throw TypeError("client_secret_post client authentication method requires a client_secret");return{form:{client_id:this.client_id,client_secret:this.client_secret}};case"private_key_jwt":case"client_secret_jwt":{let r=a(),n=await p.call(this,e,{iat:r,exp:r+60,jti:s(),iss:this.client_id,sub:this.client_id,aud:this.issuer.issuer,...t});return{form:{client_id:this.client_id,client_assertion:n,client_assertion_type:"urn:ietf:params:oauth:client-assertion-type:jwt-bearer"}}}case"client_secret_basic":{if("string"!=typeof this.client_secret)throw TypeError("client_secret_basic client authentication method requires a client_secret");let e=`${d(this.client_id)}:${d(this.client_secret)}`,t=Buffer.from(e).toString("base64");return{headers:{Authorization:`Basic ${t}`}}}default:throw TypeError(`missing, or unsupported, ${e}_endpoint_auth_method`)}}async function f(e,t,{clientAssertionPayload:r,endpointAuthMethod:n=e,DPoP:o}={}){let i;let s=c(t,await h.call(this,n,{clientAssertionPayload:r})),a=this[`${n}_endpoint_auth_method`].includes("tls_client_auth")||"token"===e&&this.tls_client_certificate_bound_access_tokens;if(a&&this.issuer.mtls_endpoint_aliases&&(i=this.issuer.mtls_endpoint_aliases[`${e}_endpoint`]),i=i||this.issuer[`${e}_endpoint`],"form"in s)for(let[e,t]of Object.entries(s.form))void 0===t&&delete s.form[e];return l.call(this,{...s,method:"POST",url:i,headers:{..."revocation"!==e?{Accept:"application/json"}:void 0,...s.headers}},{mTLS:a,DPoP:o})}e.exports={resolveResponseType:function(){let{length:e,0:t}=this.response_types;if(1===e)return t},resolveRedirectUri:function(){let{length:e,0:t}=this.redirect_uris||[];if(1===e)return t},authFor:h,authenticatedPost:f}},77731:e=>{let t=Symbol(),r=Symbol();e.exports={CLOCK_TOLERANCE:r,HTTP_OPTIONS:t}},45820:(e,t,r)=>{let n=r(69080);e.exports=e=>{if("string"!=typeof e||!e)throw TypeError("JWT must be a string");let{0:t,1:r,2:o,length:i}=e.split(".");if(5===i)throw TypeError("encrypted JWTs cannot be decoded");if(3!==i)throw Error("JWTs must have three components");try{return{header:JSON.parse(n.decode(t)),payload:JSON.parse(n.decode(r)),signature:o}}catch(e){throw Error("JWT is malformed")}}},52936:e=>{e.exports=globalThis.structuredClone||(e=>JSON.parse(JSON.stringify(e)))},69054:(e,t,r)=>{let n=r(8140);function o(e,t,...r){for(let i of r)if(n(i))for(let[r,s]of Object.entries(i))"__proto__"!==r&&"constructor"!==r&&(void 0===t[r]&&void 0!==s&&(t[r]=s),e&&n(t[r])&&n(s)&&o(!0,t[r],s));return t}e.exports=o.bind(void 0,!1),e.exports.deep=o.bind(void 0,!0)},47389:(e,t,r)=>{let{createHash:n,randomBytes:o}=r(6113),i=r(69080),s=(e=32)=>i.encode(o(e));e.exports={random:s,state:s,nonce:s,codeVerifier:s,codeChallenge:e=>i.encode(n("sha256").update(e).digest())}},74655:(e,t,r)=>{let n=r(73837),o=r(6113);e.exports=n.types.isKeyObject||(e=>e&&e instanceof o.KeyObject)},8140:e=>{e.exports=e=>!!e&&e.constructor===Object},92792:(e,t,r)=>{let n=r(75871),o=r(50901),{RPError:i}=r(80994),{assertIssuerConfiguration:s}=r(914),a=r(56310),{keystores:l}=r(25602),u=r(83818),c=r(82472),d=new WeakMap,p=new WeakMap,h=e=>(p.has(e)||p.set(e,new o({max:100})),p.get(e));async function f(e=!1){s(this,"jwks_uri");let t=l.get(this),r=h(this);return e||!t?(d.has(this)||(r.reset(),d.set(this,(async()=>{let e=u(await c.call(this,{method:"GET",responseType:"json",url:this.jwks_uri,headers:{Accept:"application/json, application/jwk-set+json"}}).finally(()=>{d.delete(this)})),t=a.fromJWKS(e,{onlyPublic:!0});return r.set("throttle",!0,6e4),l.set(this,t),t})())),d.get(this)):t}async function _({kid:e,kty:t,alg:r,use:o},{allowMulti:s=!1}={}){let a=h(this),l={kid:e,kty:t,alg:r,use:o},u=n(l,{algorithm:"sha256",ignoreUnknown:!0,unorderedArrays:!0,unorderedSets:!0,respectType:!1}),c=a.get(u)||a.get("throttle"),d=await f.call(this,!c),p=d.all(l);if(delete l.use,0===p.length)throw new i({printf:["no valid key found in issuer's jwks_uri for key parameters %j",l],jwks:d});if(!s&&p.length>1&&!e)throw new i({printf:["multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case",l],jwks:d});return a.set(u,!0),p}e.exports.queryKeyStore=_,e.exports.keystore=f},56310:(e,t,r)=>{let n=r(39797),o=r(52936),i=r(8140),s=Symbol(),a=(e,{alg:t,use:r})=>{let n=0;return t&&e.alg&&n++,r&&e.use&&n++,n};e.exports=class{#h;constructor(e,t){if(e!==s)throw Error("invalid constructor call");this.#h=t}toJWKS(){return{keys:this.map(({jwk:{d:e,p:t,q:r,dp:n,dq:o,qi:i,...s}})=>s)}}all({alg:e,kid:t,use:r}={}){if(!r||!e)throw Error();let n=function(e){switch("string"==typeof e&&e.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:return}}(e),o={alg:e,use:r};return this.filter(o=>{let i=!0;return void 0!==n&&o.jwk.kty!==n&&(i=!1),i&&void 0!==t&&o.jwk.kid!==t&&(i=!1),i&&void 0!==r&&void 0!==o.jwk.use&&o.jwk.use!==r&&(i=!1),i&&o.jwk.alg&&o.jwk.alg!==e?i=!1:o.algorithms.has(e)||(i=!1),i}).sort((e,t)=>a(t,o)-a(e,o))}get(...e){return this.all(...e)[0]}static async fromJWKS(e,{onlyPublic:t=!1,onlyPrivate:r=!1}={}){if(!i(e)||!Array.isArray(e.keys)||e.keys.some(e=>!i(e)||!("kty"in e)))throw TypeError("jwks must be a JSON Web Key Set formatted object");let a=[];for(let i of e.keys){let{kty:e,kid:s,crv:l}=i=o(i),{alg:u,use:c}=i;if("string"==typeof e&&e&&(void 0===c||"sig"===c||"enc"===c)&&("string"==typeof u||void 0===u)&&("string"==typeof s||void 0===s)){if("EC"===e&&"sig"===c)switch(l){case"P-256":u="ES256";break;case"P-384":u="ES384";break;case"P-521":u="ES512"}if("secp256k1"===l&&(c="sig",u="ES256K"),"OKP"===e)switch(l){case"Ed25519":case"Ed448":c="sig",u="EdDSA";break;case"X25519":case"X448":c="enc"}if(u&&!c)switch(!0){case u.startsWith("ECDH"):case u.startsWith("RSA"):c="enc"}if(r&&("oct"===i.kty||!i.d))throw Error("jwks must only contain private keys");t&&(i.d||i.k)||a.push({jwk:{...i,alg:u,use:c},async keyObject(e){if(this[e])return this[e];let t=await n.importJWK(this.jwk,e);return this[e]=t,t},get algorithms(){return Object.defineProperty(this,"algorithms",{value:function(e,t,r,o){if(t)return new Set([t]);switch(r){case"EC":{let t=[];if(("enc"===e||void 0===e)&&(t=t.concat(["ECDH-ES","ECDH-ES+A128KW","ECDH-ES+A192KW","ECDH-ES+A256KW"])),"sig"===e||void 0===e)switch(o){case"P-256":case"P-384":t=t.concat([`ES${o.slice(-3)}`]);break;case"P-521":t=t.concat(["ES512"]);break;case"secp256k1":"node:crypto"===n.cryptoRuntime&&(t=t.concat(["ES256K"]))}return new Set(t)}case"OKP":return new Set(["ECDH-ES","ECDH-ES+A128KW","ECDH-ES+A192KW","ECDH-ES+A256KW"]);case"RSA":{let t=[];return("enc"===e||void 0===e)&&(t=t.concat(["RSA-OAEP","RSA-OAEP-256","RSA-OAEP-384","RSA-OAEP-512"]),"node:crypto"===n.cryptoRuntime&&(t=t.concat(["RSA1_5"]))),("sig"===e||void 0===e)&&(t=t.concat(["PS256","PS384","PS512","RS256","RS384","RS512"])),new Set(t)}default:throw Error("unreachable")}}(this.jwk.use,this.jwk.alg,this.jwk.kty,this.jwk.crv),enumerable:!0,configurable:!1}),this.algorithms}})}}return new this(s,a)}filter(...e){return this.#h.filter(...e)}find(...e){return this.#h.find(...e)}every(...e){return this.#h.every(...e)}some(...e){return this.#h.some(...e)}map(...e){return this.#h.map(...e)}forEach(...e){return this.#h.forEach(...e)}reduce(...e){return this.#h.reduce(...e)}sort(...e){return this.#h.sort(...e)}*[Symbol.iterator](){for(let e of this.#h)yield e}}},49366:(e,t,r)=>{let n=r(8140);e.exports=function e(t,...r){for(let o of r)if(n(o))for(let[r,i]of Object.entries(o))"__proto__"!==r&&"constructor"!==r&&(n(t[r])&&n(i)?t[r]=e(t[r],i):void 0!==i&&(t[r]=i));return t}},58945:e=>{e.exports=function(e,...t){let r={};for(let n of t)void 0!==e[n]&&(r[n]=e[n]);return r}},83818:(e,t,r)=>{let{STATUS_CODES:n}=r(13685),{format:o}=r(73837),{OPError:i}=r(80994),s=r(15070),a=e=>{let t=s(e.headers["www-authenticate"]);if(t.error)throw new i(t,e)},l=e=>{let t=!1;try{let r;"object"!=typeof e.body||Buffer.isBuffer(e.body)?r=JSON.parse(e.body):r=e.body,(t="string"==typeof r.error&&r.error.length)&&Object.defineProperty(e,"body",{value:r,configurable:!0})}catch(e){}return t};e.exports=function(e,{statusCode:t=200,body:r=!0,bearer:s=!1}={}){if(e.statusCode!==t){if(s&&a(e),l(e))throw new i(e.body,e);throw new i({error:o("expected %i %s, got: %i %s",t,n[t],e.statusCode,n[e.statusCode])},e)}if(r&&!e.body)throw new i({error:o("expected %i %s with body but no body was returned",t,n[t])},e);return e.body}},82472:(e,t,r)=>{let n;let o=r(39491),i=r(63477),s=r(13685),a=r(95687),{once:l}=r(82361),{URL:u}=r(57310),c=r(50901),d=r(87658),{RPError:p}=r(80994),h=r(58945),{deep:f}=r(69054),{HTTP_OPTIONS:_}=r(77731),g=/^[\x21\x23-\x5B\x5D-\x7E]+$/,y=["agent","ca","cert","crl","headers","key","lookup","passphrase","pfx","timeout"],v=(e,t)=>{n=f({},e.length?h(t,...e):t,n)};function m(e,t,r){r&&(e.removeHeader("content-type"),e.setHeader("content-type",r)),t&&(e.removeHeader("content-length"),e.setHeader("content-length",Buffer.byteLength(t)),e.write(t)),e.end()}v([],{headers:{"User-Agent":`${d.name}/${d.version} (${d.homepage})`,"Accept-Encoding":"identity"},timeout:3500});let w=new c({max:100});e.exports=async function(e,{accessToken:t,mTLS:r=!1,DPoP:c}={}){let d,v,b,k,x,A,S;try{d=new u(e.url),delete e.url,o(/^(https?:)$/.test(d.protocol))}catch(e){throw TypeError("only valid absolute URLs can be requested")}let E=this[_],O=e,T=`${d.origin}${d.pathname}`;if(c&&"dpopProof"in this&&(O.headers=O.headers||{},O.headers.DPoP=await this.dpopProof({htu:`${d.origin}${d.pathname}`,htm:e.method||"GET",nonce:w.get(T)},c,t)),E&&(v=h(E.call(this,d,f({},O,n)),...y)),O=f({},v,O,n),r&&!O.pfx&&!(O.key&&O.cert))throw TypeError("mutual-TLS certificate and key not set");if(O.searchParams)for(let[e,t]of Object.entries(O.searchParams))d.searchParams.delete(e),d.searchParams.set(e,t);for(let[e,t]of({form:k,responseType:b,json:x,body:A,...O}=O,Object.entries(O.headers||{})))void 0===t&&delete O.headers[e];let j=("https:"===d.protocol?a.request:s.request)(d.href,O);return(async()=>{if(x?m(j,JSON.stringify(x),"application/json"):k?m(j,i.stringify(k),"application/x-www-form-urlencoded"):A?m(j,A):m(j),[S]=await Promise.race([l(j,"response"),l(j,"timeout")]),!S)throw j.destroy(),new p(`outgoing request timed out after ${O.timeout}ms`);let e=[];for await(let t of S)e.push(t);if(e.length)switch(b){case"json":Object.defineProperty(S,"body",{get(){let t=Buffer.concat(e);try{t=JSON.parse(t)}catch(e){throw Object.defineProperty(e,"response",{value:S}),e}finally{Object.defineProperty(S,"body",{value:t,configurable:!0})}return t},configurable:!0});break;case void 0:case"buffer":Object.defineProperty(S,"body",{get(){let t=Buffer.concat(e);return Object.defineProperty(S,"body",{value:t,configurable:!0}),t},configurable:!0});break;default:throw TypeError("unsupported responseType request option")}return S})().catch(e=>{throw S&&Object.defineProperty(e,"response",{value:S}),e}).finally(()=>{let e=S&&S.headers["dpop-nonce"];e&&g.test(e)&&w.set(T,e)})},e.exports.setDefaults=v.bind(void 0,y)},46438:e=>{e.exports=()=>Math.floor(Date.now()/1e3)},25602:e=>{e.exports.keystores=new WeakMap},91578:e=>{let t=/^\d+$/;e.exports=function(e){if("string"!=typeof e)throw TypeError("input must be a string");return(!function(e){if(e.includes("://"))return!0;let r=e.replace(/(\/|\?)/g,"#").split("#")[0];if(r.includes(":")){let e=r.indexOf(":"),n=r.slice(e+1);if(!t.test(n))return!0}return!1}(e)?!function(e){if(!e.includes("@"))return!1;let t=e.split("@"),r=t[t.length-1];return!(r.includes(":")||r.includes("/")||r.includes("?"))}(e)?`https://${e}`:`acct:${e}`:e).split("#")[0]}},15070:e=>{let t=/(\w+)=("[^"]*")/g;e.exports=e=>{let r={};try{for(;null!==t.exec(e);)RegExp.$1&&RegExp.$2&&(r[RegExp.$1]=RegExp.$2.slice(1,-1))}catch(e){}return r}},77838:(e,t,r)=>{let n=r(99197),{OPError:o,RPError:i}=r(80994),s=r(74202),a=r(80536),{CLOCK_TOLERANCE:l,HTTP_OPTIONS:u}=r(77731),c=r(47389),{setDefaults:d}=r(82472);e.exports={Issuer:n,Strategy:s,TokenSet:a,errors:{OPError:o,RPError:i},custom:{setHttpOptionsDefaults:d,http_options:u,clock_tolerance:l},generators:c}},99197:(e,t,r)=>{let{inspect:n}=r(73837),o=r(57310),{RPError:i}=r(80994),s=r(80137),a=r(89123),l=r(83818),u=r(91578),c=r(82472),d=r(52936),{keystore:p}=r(92792),h=["https://login.microsoftonline.com/common/.well-known/openid-configuration","https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration","https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration","https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration"],f=Symbol(),_={claim_types_supported:["normal"],claims_parameter_supported:!1,grant_types_supported:["authorization_code","implicit"],request_parameter_supported:!1,request_uri_parameter_supported:!0,require_request_uri_registration:!1,response_modes_supported:["query","fragment"],token_endpoint_auth_methods_supported:["client_secret_basic"]};class g{#e;constructor(e={}){let t=e[f];delete e[f],["introspection","revocation"].forEach(t=>{e[`${t}_endpoint`]&&void 0===e[`${t}_endpoint_auth_methods_supported`]&&void 0===e[`${t}_endpoint_auth_signing_alg_values_supported`]&&(e.token_endpoint_auth_methods_supported&&(e[`${t}_endpoint_auth_methods_supported`]=e.token_endpoint_auth_methods_supported),e.token_endpoint_auth_signing_alg_values_supported&&(e[`${t}_endpoint_auth_signing_alg_values_supported`]=e.token_endpoint_auth_signing_alg_values_supported))}),this.#e=new Map,Object.entries(e).forEach(([e,t])=>{this.#e.set(e,t),this[e]||Object.defineProperty(this,e,{get(){return this.#e.get(e)},enumerable:!0})}),a.set(this.issuer,this);let r=s(this,t);Object.defineProperties(this,{Client:{value:r,enumerable:!0},FAPI1Client:{value:class extends r{},enumerable:!0},FAPI2Client:{value:class extends r{},enumerable:!0}})}get metadata(){return d(Object.fromEntries(this.#e.entries()))}static async webfinger(e){let t=u(e),{host:r}=o.parse(t),n=`https://${r}/.well-known/webfinger`,s=l(await c.call(this,{method:"GET",url:n,responseType:"json",searchParams:{resource:t,rel:"http://openid.net/specs/connect/1.0/issuer"},headers:{Accept:"application/json"}})),d=Array.isArray(s.links)&&s.links.find(e=>"object"==typeof e&&"http://openid.net/specs/connect/1.0/issuer"===e.rel&&e.href);if(!d)throw new i({message:"no issuer found in webfinger response",body:s});if("string"!=typeof d.href||!d.href.startsWith("https://"))throw new i({printf:["invalid issuer location %s",d.href],body:s});let p=d.href;if(a.has(p))return a.get(p);let h=await this.discover(p);if(h.issuer!==p)throw a.del(h.issuer),new i("discovered issuer mismatch, expected %s, got: %s",p,h.issuer);return h}static async discover(e){let t=function(e){let t=o.parse(e);if(t.pathname.includes("/.well-known/"))return e;{let e;return e=t.pathname.endsWith("/")?`${t.pathname}.well-known/openid-configuration`:`${t.pathname}/.well-known/openid-configuration`,o.format({...t,pathname:e})}}(e),r=l(await c.call(this,{method:"GET",responseType:"json",url:t,headers:{Accept:"application/json"}}));return new g({..._,...r,[f]:!!h.find(e=>t.startsWith(e))})}async reloadJwksUri(){await p.call(this,!0)}[n.custom](){return`${this.constructor.name} ${n(this.metadata,{depth:1/0,colors:process.stdout.isTTY,compact:!1,sorted:!0})}`}}e.exports=g},89123:(e,t,r)=>{let n=r(50901);e.exports=new n({max:100})},74202:(e,t,r)=>{let n=r(57310),{format:o}=r(73837),i=r(52936),{RPError:s,OPError:a}=r(80994),{BaseClient:l}=r(80137),{random:u,codeChallenge:c}=r(47389),d=r(58945),{resolveResponseType:p,resolveRedirectUri:h}=r(36223);function f(e,t,r={}){e?this.error(e):t?this.success(t,r):this.fail(r)}function _({client:e,params:t={},passReqToCallback:r=!1,sessionKey:o,usePKCE:s=!0,extras:a={}}={},u){if(!(e instanceof l))throw TypeError("client must be an instance of openid-client Client");if("function"!=typeof u)throw TypeError("verify callback must be a function");if(!e.issuer||!e.issuer.issuer)throw TypeError("client must have an issuer with an identifier");if(this._client=e,this._issuer=e.issuer,this._verify=u,this._passReqToCallback=r,this._usePKCE=s,this._key=o||`oidc:${n.parse(this._issuer.issuer).hostname}`,this._params=i(t),delete this._params.state,delete this._params.nonce,this._extras=i(a),this._params.response_type||(this._params.response_type=p.call(e)),this._params.redirect_uri||(this._params.redirect_uri=h.call(e)),this._params.scope||(this._params.scope="openid"),!0===this._usePKCE){let e=!!Array.isArray(this._issuer.code_challenge_methods_supported)&&this._issuer.code_challenge_methods_supported;if(e&&e.includes("S256"))this._usePKCE="S256";else if(e&&e.includes("plain"))this._usePKCE="plain";else if(e)throw TypeError("neither code_challenge_method supported by the client is supported by the issuer");else this._usePKCE="S256"}else if("string"==typeof this._usePKCE&&!["plain","S256"].includes(this._usePKCE))throw TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);this.name=n.parse(e.issuer.issuer).hostname}_.prototype.authenticate=function(e,t){(async()=>{let r=this._client;if(!e.session)throw TypeError("authentication requires session support");let n=r.callbackParams(e),i=this._key,{0:a,length:l}=Object.keys(n);if(0===l||1===l&&"iss"===a){let n={state:u(),...this._params,...t};if(!n.nonce&&n.response_type.includes("id_token")&&(n.nonce=u()),e.session[i]=d(n,"nonce","state","max_age","response_type"),this._usePKCE&&n.response_type.includes("code")){let t=u();switch(e.session[i].code_verifier=t,this._usePKCE){case"S256":n.code_challenge=c(t),n.code_challenge_method="S256";break;case"plain":n.code_challenge=t}}this.redirect(r.authorizationUrl(n));return}let p=e.session[i];if(0===Object.keys(p||{}).length)throw Error(o('did not find expected authorization request details in session, req.session["%s"] is %j',i,p));let{state:h,nonce:_,max_age:g,code_verifier:y,response_type:v}=p;try{delete e.session[i]}catch(e){}let m={redirect_uri:this._params.redirect_uri,...t},w=await r.callback(m.redirect_uri,n,{state:h,nonce:_,max_age:g,code_verifier:y,response_type:v},this._extras),b=this._passReqToCallback,k=this._verify.length>(b?3:2)&&r.issuer.userinfo_endpoint,x=[w,f.bind(this)];if(k){if(!w.access_token)throw new s({message:"expected access_token to be returned when asking for userinfo in verify callback",tokenset:w});let e=await r.userinfo(w);x.splice(1,0,e)}b&&x.unshift(e),this._verify(...x)})().catch(e=>{e instanceof a&&"server_error"!==e.error&&!e.error.startsWith("invalid")||e instanceof s?this.fail(e):this.error(e)})},e.exports=_},80536:(e,t,r)=>{let n=r(69080),o=r(46438);class i{constructor(e){Object.assign(this,e);let{constructor:t,...r}=Object.getOwnPropertyDescriptors(this.constructor.prototype);Object.defineProperties(this,r)}set expires_in(e){this.expires_at=o()+Number(e)}get expires_in(){return Math.max.apply(null,[this.expires_at-o(),0])}expired(){return 0===this.expires_in}claims(){if(!this.id_token)throw TypeError("id_token not present in TokenSet");return JSON.parse(n.decode(this.id_token.split(".")[1]))}}e.exports=i},50901:(e,t,r)=>{"use strict";let n=r(10517),o=Symbol("max"),i=Symbol("length"),s=Symbol("lengthCalculator"),a=Symbol("allowStale"),l=Symbol("maxAge"),u=Symbol("dispose"),c=Symbol("noDisposeOnSet"),d=Symbol("lruList"),p=Symbol("cache"),h=Symbol("updateAgeOnGet"),f=()=>1;class _{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw TypeError("max must be a non-negative number");this[o]=e.max||1/0;let t=e.length||f;if(this[s]="function"!=typeof t?f:t,this[a]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw TypeError("maxAge must be a number");this[l]=e.maxAge||0,this[u]=e.dispose,this[c]=e.noDisposeOnSet||!1,this[h]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw TypeError("max must be a non-negative number");this[o]=e||1/0,v(this)}get max(){return this[o]}set allowStale(e){this[a]=!!e}get allowStale(){return this[a]}set maxAge(e){if("number"!=typeof e)throw TypeError("maxAge must be a non-negative number");this[l]=e,v(this)}get maxAge(){return this[l]}set lengthCalculator(e){"function"!=typeof e&&(e=f),e!==this[s]&&(this[s]=e,this[i]=0,this[d].forEach(e=>{e.length=this[s](e.value,e.key),this[i]+=e.length})),v(this)}get lengthCalculator(){return this[s]}get length(){return this[i]}get itemCount(){return this[d].length}rforEach(e,t){t=t||this;for(let r=this[d].tail;null!==r;){let n=r.prev;b(this,e,r,t),r=n}}forEach(e,t){t=t||this;for(let r=this[d].head;null!==r;){let n=r.next;b(this,e,r,t),r=n}}keys(){return this[d].toArray().map(e=>e.key)}values(){return this[d].toArray().map(e=>e.value)}reset(){this[u]&&this[d]&&this[d].length&&this[d].forEach(e=>this[u](e.key,e.value)),this[p]=new Map,this[d]=new n,this[i]=0}dump(){return this[d].map(e=>!y(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)}).toArray().filter(e=>e)}dumpLru(){return this[d]}set(e,t,r){if((r=r||this[l])&&"number"!=typeof r)throw TypeError("maxAge must be a number");let n=r?Date.now():0,a=this[s](t,e);if(this[p].has(e)){if(a>this[o])return m(this,this[p].get(e)),!1;let s=this[p].get(e).value;return this[u]&&!this[c]&&this[u](e,s.value),s.now=n,s.maxAge=r,s.value=t,this[i]+=a-s.length,s.length=a,this.get(e),v(this),!0}let h=new w(e,t,a,n,r);return h.length>this[o]?(this[u]&&this[u](e,t),!1):(this[i]+=h.length,this[d].unshift(h),this[p].set(e,this[d].head),v(this),!0)}has(e){return!!this[p].has(e)&&!y(this,this[p].get(e).value)}get(e){return g(this,e,!0)}peek(e){return g(this,e,!1)}pop(){let e=this[d].tail;return e?(m(this,e),e.value):null}del(e){m(this,this[p].get(e))}load(e){this.reset();let t=Date.now();for(let r=e.length-1;r>=0;r--){let n=e[r],o=n.e||0;if(0===o)this.set(n.k,n.v);else{let e=o-t;e>0&&this.set(n.k,n.v,e)}}}prune(){this[p].forEach((e,t)=>g(this,t,!1))}}let g=(e,t,r)=>{let n=e[p].get(t);if(n){let t=n.value;if(y(e,t)){if(m(e,n),!e[a])return}else r&&(e[h]&&(n.value.now=Date.now()),e[d].unshiftNode(n));return t.value}},y=(e,t)=>{if(!t||!t.maxAge&&!e[l])return!1;let r=Date.now()-t.now;return t.maxAge?r>t.maxAge:e[l]&&r>e[l]},v=e=>{if(e[i]>e[o])for(let t=e[d].tail;e[i]>e[o]&&null!==t;){let r=t.prev;m(e,t),t=r}},m=(e,t)=>{if(t){let r=t.value;e[u]&&e[u](r.key,r.value),e[i]-=r.length,e[p].delete(r.key),e[d].removeNode(t)}};class w{constructor(e,t,r,n,o){this.key=e,this.value=t,this.length=r,this.now=n,this.maxAge=o||0}}let b=(e,t,r,n)=>{let o=r.value;y(e,o)&&(m(e,r),e[a]||(o=void 0)),o&&t.call(n,o.value,o.key,e)};e.exports=_},8907:e=>{"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},10517:(e,t,r)=>{"use strict";function n(e){var t=this;if(t instanceof n||(t=new n),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach(function(e){t.push(e)});else if(arguments.length>0)for(var r=0,o=arguments.length;r<o;r++)t.push(arguments[r]);return t}function o(e,t,r,n){if(!(this instanceof o))return new o(e,t,r,n);this.list=n,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}e.exports=n,n.Node=o,n.create=n,n.prototype.removeNode=function(e){if(e.list!==this)throw Error("removing node which does not belong to this list");var t=e.next,r=e.prev;return t&&(t.prev=r),r&&(r.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=r),e.list.length--,e.next=null,e.prev=null,e.list=null,t},n.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},n.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},n.prototype.push=function(){for(var e,t=0,r=arguments.length;t<r;t++)e=arguments[t],this.tail=new o(e,this.tail,null,this),this.head||(this.head=this.tail),this.length++;return this.length},n.prototype.unshift=function(){for(var e,t=0,r=arguments.length;t<r;t++)e=arguments[t],this.head=new o(e,null,this.head,this),this.tail||(this.tail=this.head),this.length++;return this.length},n.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},n.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},n.prototype.forEach=function(e,t){t=t||this;for(var r=this.head,n=0;null!==r;n++)e.call(t,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(e,t){t=t||this;for(var r=this.tail,n=this.length-1;null!==r;n--)e.call(t,r.value,n,this),r=r.prev},n.prototype.get=function(e){for(var t=0,r=this.head;null!==r&&t<e;t++)r=r.next;if(t===e&&null!==r)return r.value},n.prototype.getReverse=function(e){for(var t=0,r=this.tail;null!==r&&t<e;t++)r=r.prev;if(t===e&&null!==r)return r.value},n.prototype.map=function(e,t){t=t||this;for(var r=new n,o=this.head;null!==o;)r.push(e.call(t,o.value,this)),o=o.next;return r},n.prototype.mapReverse=function(e,t){t=t||this;for(var r=new n,o=this.tail;null!==o;)r.push(e.call(t,o.value,this)),o=o.prev;return r},n.prototype.reduce=function(e,t){var r,n=this.head;if(arguments.length>1)r=t;else if(this.head)n=this.head.next,r=this.head.value;else throw TypeError("Reduce of empty list with no initial value");for(var o=0;null!==n;o++)r=e(r,n.value,o),n=n.next;return r},n.prototype.reduceReverse=function(e,t){var r,n=this.tail;if(arguments.length>1)r=t;else if(this.tail)n=this.tail.prev,r=this.tail.value;else throw TypeError("Reduce of empty list with no initial value");for(var o=this.length-1;null!==n;o--)r=e(r,n.value,o),n=n.prev;return r},n.prototype.toArray=function(){for(var e=Array(this.length),t=0,r=this.head;null!==r;t++)e[t]=r.value,r=r.next;return e},n.prototype.toArrayReverse=function(){for(var e=Array(this.length),t=0,r=this.tail;null!==r;t++)e[t]=r.value,r=r.prev;return e},n.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var o=0,i=this.head;null!==i&&o<e;o++)i=i.next;for(;null!==i&&o<t;o++,i=i.next)r.push(i.value);return r},n.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var o=this.length,i=this.tail;null!==i&&o>t;o--)i=i.prev;for(;null!==i&&o>e;o--,i=i.prev)r.push(i.value);return r},n.prototype.splice=function(e,t,...r){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var n=0,i=this.head;null!==i&&n<e;n++)i=i.next;for(var s=[],n=0;i&&n<t;n++)s.push(i.value),i=this.removeNode(i);null===i&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var n=0;n<r.length;n++)i=function(e,t,r){var n=t===e.head?new o(r,null,t,e):new o(r,t,t.next,e);return null===n.next&&(e.tail=n),null===n.prev&&(e.head=n),e.length++,n}(this,i,r[n]);return s},n.prototype.reverse=function(){for(var e=this.head,t=this.tail,r=e;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=t,this.tail=e,this};try{r(8907)(n)}catch(e){}},61316:function(e,t,r){(function(e,t){var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,n=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,o=/[\s\n\\/='"\0<>]/,i=/^xlink:?./,s=/["&<]/;function a(e){if(!1===s.test(e+=""))return e;for(var t=0,r=0,n="",o="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=o,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var l=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},u=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},c={},d=/([A-Z])/g;function p(e){var t="";for(var n in e){var o=e[n];null!=o&&""!==o&&(t&&(t+=" "),t+="-"==n[0]?n:c[n]||(c[n]=n.replace(d,"-$1").toLowerCase()),t="number"==typeof o&&!1===r.test(n)?t+": "+o+"px;":t+": "+o+";")}return t||void 0}function h(e,t){return Array.isArray(t)?t.reduce(h,e):null!=t&&!1!==t&&e.push(t),e}function f(){this.__d=!0}function _(e,t){return{__v:e,context:t,props:e.props,setState:f,forceUpdate:f,__d:!0,__h:[]}}function g(e,t){var r=e.contextType,n=r&&t[r.__c];return null!=r?n?n.props.value:r.__:t}var y=[],v={shallow:!0};b.render=b;var m=function(e,t){return b(e,t,v)},w=[];function b(e,r,s){r=r||{};var c=t.options.__s;t.options.__s=!0;var d,f=t.h(t.Fragment,null);return f.__k=[e],d=s&&(s.pretty||s.voidElements||s.sortAttributes||s.shallow||s.allAttributes||s.xml||s.attributeHook)?function e(r,s,c,d,f,v){if(null==r||"boolean"==typeof r)return"";if("object"!=typeof r)return"function"==typeof r?"":a(r);var m=c.pretty,w=m&&"string"==typeof m?m:"	";if(Array.isArray(r)){for(var b="",k=0;k<r.length;k++)m&&k>0&&(b+="\n"),b+=e(r[k],s,c,d,f,v);return b}if(void 0!==r.constructor)return"";var x,A=r.type,S=r.props,E=!1;if("function"==typeof A){if(E=!0,!c.shallow||!d&&!1!==c.renderRootComponent){if(A===t.Fragment){var O=[];return h(O,r.props.children),e(O,s,c,!1!==c.shallowHighOrder,f,v)}var T,j=r.__c=_(r,s);t.options.__b&&t.options.__b(r);var P=t.options.__r;if(A.prototype&&"function"==typeof A.prototype.render){var C=g(A,s);(j=r.__c=new A(S,C)).__v=r,j._dirty=j.__d=!0,j.props=S,null==j.state&&(j.state={}),null==j._nextState&&null==j.__s&&(j._nextState=j.__s=j.state),j.context=C,A.getDerivedStateFromProps?j.state=Object.assign({},j.state,A.getDerivedStateFromProps(j.props,j.state)):j.componentWillMount&&(j.componentWillMount(),j.state=j._nextState!==j.state?j._nextState:j.__s!==j.state?j.__s:j.state),P&&P(r),T=j.render(j.props,j.state,j.context)}else for(var R=g(A,s),$=0;j.__d&&$++<25;)j.__d=!1,P&&P(r),T=A.call(r.__c,S,R);return j.getChildContext&&(s=Object.assign({},s,j.getChildContext())),t.options.diffed&&t.options.diffed(r),e(T,s,c,!1!==c.shallowHighOrder,f,v)}A=(x=A).displayName||x!==Function&&x.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,n=y.length;n--;)if(y[n]===e){r=n;break}r<0&&(r=y.push(e)-1),t="UnnamedComponent"+r}return t}(x)}var U,M,L="<"+A;if(S){var H=Object.keys(S);c&&!0===c.sortAttributes&&H.sort();for(var N=0;N<H.length;N++){var I=H[N],D=S[I];if("children"!==I){if(!o.test(I)&&(c&&c.allAttributes||"key"!==I&&"ref"!==I&&"__self"!==I&&"__source"!==I)){if("defaultValue"===I)I="value";else if("defaultChecked"===I)I="checked";else if("defaultSelected"===I)I="selected";else if("className"===I){if(void 0!==S.class)continue;I="class"}else f&&i.test(I)&&(I=I.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===I){if(S.for)continue;I="for"}"style"===I&&D&&"object"==typeof D&&(D=p(D)),"a"===I[0]&&"r"===I[1]&&"boolean"==typeof D&&(D=String(D));var q=c.attributeHook&&c.attributeHook(I,D,s,c,E);if(q||""===q)L+=q;else if("dangerouslySetInnerHTML"===I)M=D&&D.__html;else if("textarea"===A&&"value"===I)U=D;else if((D||0===D||""===D)&&"function"!=typeof D){if(!(!0!==D&&""!==D||(D=I,c&&c.xml))){L=L+" "+I;continue}if("value"===I){if("select"===A){v=D;continue}"option"===A&&v==D&&void 0===S.selected&&(L+=" selected")}L=L+" "+I+'="'+a(D)+'"'}}}else U=D}}if(m){var z=L.replace(/\n\s*/," ");z===L||~z.indexOf("\n")?m&&~L.indexOf("\n")&&(L+="\n"):L=z}if(L+=">",o.test(A))throw Error(A+" is not a valid HTML tag name in "+L);var W,J=n.test(A)||c.voidElements&&c.voidElements.test(A),B=[];if(M)m&&u(M)&&(M="\n"+w+l(M,w)),L+=M;else if(null!=U&&h(W=[],U).length){for(var K=m&&~L.indexOf("\n"),F=!1,G=0;G<W.length;G++){var V=W[G];if(null!=V&&!1!==V){var X=e(V,s,c,!0,"svg"===A||"foreignObject"!==A&&f,v);if(m&&!K&&u(X)&&(K=!0),X){if(m){var Y=X.length>0&&"<"!=X[0];F&&Y?B[B.length-1]+=X:B.push(X),F=Y}else B.push(X)}}}if(m&&K)for(var Z=B.length;Z--;)B[Z]="\n"+w+l(B[Z],w)}if(B.length||M)L+=B.join("");else if(c&&c.xml)return L.substring(0,L.length-1)+" />";return!J||W||M?(m&&~L.indexOf("\n")&&(L+="\n"),L=L+"</"+A+">"):L=L.replace(/>$/," />"),L}(e,r,s):function e(r,s,l,u,c){if(null==r||!0===r||!1===r||""===r)return"";if("object"!=typeof r)return"function"==typeof r?"":a(r);if(x(r)){var d="";c.__k=r;for(var h=0;h<r.length;h++)d+=e(r[h],s,l,u,c),r[h]=k(r[h]);return d}if(void 0!==r.constructor)return"";r.__=c,t.options.__b&&t.options.__b(r);var f=r.type,y=r.props;if("function"==typeof f){if(f===t.Fragment)E=y.children;else{E=f.prototype&&"function"==typeof f.prototype.render?(v=s,w=g(m=r.type,v),b=new m(r.props,w),r.__c=b,b.__v=r,b.__d=!0,b.props=r.props,null==b.state&&(b.state={}),null==b.__s&&(b.__s=b.state),b.context=w,m.getDerivedStateFromProps?b.state=A({},b.state,m.getDerivedStateFromProps(b.props,b.state)):b.componentWillMount&&(b.componentWillMount(),b.state=b.__s!==b.state?b.__s:b.state),(S=t.options.__r)&&S(r),b.render(b.props,b.state,b.context)):function(e,r){var n,o=_(e,r),i=g(e.type,r);e.__c=o;for(var s=t.options.__r,a=0;o.__d&&a++<25;)o.__d=!1,s&&s(e),n=e.type.call(o,e.props,i);return n}(r,s);var v,m,w,b,S,E,O=r.__c;O.getChildContext&&(s=A({},s,O.getChildContext()))}var T=e(E=null!=E&&E.type===t.Fragment&&null==E.key?E.props.children:E,s,l,u,r);return t.options.diffed&&t.options.diffed(r),r.__=void 0,t.options.unmount&&t.options.unmount(r),T}var j,P,C="<";if(C+=f,y)for(var R in j=y.children,y){var $,U,M,L=y[R];if(!("key"===R||"ref"===R||"__self"===R||"__source"===R||"children"===R||"className"===R&&"class"in y||"htmlFor"===R&&"for"in y||o.test(R))){if(U=R="className"===($=R)?"class":"htmlFor"===$?"for":"defaultValue"===$?"value":"defaultChecked"===$?"checked":"defaultSelected"===$?"selected":l&&i.test($)?$.toLowerCase().replace(/^xlink:?/,"xlink:"):$,M=L,L="style"===U&&null!=M&&"object"==typeof M?p(M):"a"===U[0]&&"r"===U[1]&&"boolean"==typeof M?String(M):M,"dangerouslySetInnerHTML"===R)P=L&&L.__html;else if("textarea"===f&&"value"===R)j=L;else if((L||0===L||""===L)&&"function"!=typeof L){if(!0===L||""===L){L=R,C=C+" "+R;continue}if("value"===R){if("select"===f){u=L;continue}"option"!==f||u!=L||"selected"in y||(C+=" selected")}C=C+" "+R+'="'+a(L)+'"'}}}var H=C;if(C+=">",o.test(f))throw Error(f+" is not a valid HTML tag name in "+C);var N="",I=!1;if(P)N+=P,I=!0;else if("string"==typeof j)N+=a(j),I=!0;else if(x(j)){r.__k=j;for(var D=0;D<j.length;D++){var q=j[D];if(j[D]=k(q),null!=q&&!1!==q){var z=e(q,s,"svg"===f||"foreignObject"!==f&&l,u,r);z&&(N+=z,I=!0)}}}else if(null!=j&&!1!==j&&!0!==j){r.__k=[k(j)];var W=e(j,s,"svg"===f||"foreignObject"!==f&&l,u,r);W&&(N+=W,I=!0)}if(t.options.diffed&&t.options.diffed(r),r.__=void 0,t.options.unmount&&t.options.unmount(r),I)C+=N;else if(n.test(f))return H+" />";return C+"</"+f+">"}(e,r,!1,void 0,f),t.options.__c&&t.options.__c(e,w),t.options.__s=c,w.length=0,d}function k(e){return null==e||"boolean"==typeof e?null:"string"==typeof e||"number"==typeof e||"bigint"==typeof e?t.h(null,null,e):e}var x=Array.isArray,A=Object.assign;b.shallowRender=m,e.default=b,e.render=b,e.renderToStaticMarkup=b,e.renderToString=b,e.shallowRender=m})(t,r(97983))},23935:(e,t,r)=>{e.exports=r(61316).default},97983:(e,t)=>{var r,n,o,i,s,a,l,u,c,d,p,h,f={},_=[],g=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,y=Array.isArray;function v(e,t){for(var r in t)e[r]=t[r];return e}function m(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function w(e,t,n){var o,i,s,a={};for(s in t)"key"==s?o=t[s]:"ref"==s?i=t[s]:a[s]=t[s];if(arguments.length>2&&(a.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===a[s]&&(a[s]=e.defaultProps[s]);return b(e,a,o,i,null)}function b(e,t,r,i,s){var a={type:e,props:t,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==s?++o:s,__i:-1,__u:0};return null==s&&null!=n.vnode&&n.vnode(a),a}function k(e){return e.children}function x(e,t){this.props=e,this.context=t}function A(e,t){if(null==t)return e.__?A(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?A(e):null}function S(e){(!e.__d&&(e.__d=!0)&&s.push(e)&&!E.__r++||a!==n.debounceRendering)&&((a=n.debounceRendering)||l)(E)}function E(){var e,t,r,o,i,a,l,c;for(s.sort(u);e=s.shift();)e.__d&&(t=s.length,o=void 0,a=(i=(r=e).__v).__e,l=[],c=[],r.__P&&((o=v({},i)).__v=i.__v+1,n.vnode&&n.vnode(o),C(r.__P,o,i,r.__n,r.__P.namespaceURI,32&i.__u?[a]:null,l,null==a?A(i):a,!!(32&i.__u),c),o.__v=i.__v,o.__.__k[o.__i]=o,R(l,o,c),o.__e!=a&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(o)),s.length>t&&s.sort(u));E.__r=0}function O(e,t,r,o,i,s,a,l,u,c,d){var p,h,g,v,w,x=o&&o.__k||_,S=t.length;for(r.__d=u,function(e,t,r){var o,i,s,a,l,u=t.length,c=r.length,d=c,p=0;for(e.__k=[],o=0;o<u;o++)null!=(i=t[o])&&"boolean"!=typeof i&&"function"!=typeof i?(a=o+p,(i=e.__k[o]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?b(null,i,null,null,null):y(i)?b(k,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?b(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,s=null,-1!==(l=i.__i=function(e,t,r,n){var o=e.key,i=e.type,s=r-1,a=r+1,l=t[r];if(null===l||l&&o==l.key&&i===l.type&&0==(131072&l.__u))return r;if(n>(null!=l&&0==(131072&l.__u)?1:0))for(;s>=0||a<t.length;){if(s>=0){if((l=t[s])&&0==(131072&l.__u)&&o==l.key&&i===l.type)return s;s--}if(a<t.length){if((l=t[a])&&0==(131072&l.__u)&&o==l.key&&i===l.type)return a;a++}}return -1}(i,r,a,d))&&(d--,(s=r[l])&&(s.__u|=131072)),null==s||null===s.__v?(-1==l&&p--,"function"!=typeof i.type&&(i.__u|=65536)):l!==a&&(l==a-1?p--:l==a+1?p++:(l>a?p--:p++,i.__u|=65536))):i=e.__k[o]=null;if(d)for(o=0;o<c;o++)null!=(s=r[o])&&0==(131072&s.__u)&&(s.__e==e.__d&&(e.__d=A(s)),function e(t,r,o){var i,s;if(n.unmount&&n.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||$(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){n.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(s=0;s<i.length;s++)i[s]&&e(i[s],r,o||"function"!=typeof t.type);o||m(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(s,s))}(r,t,x),u=r.__d,p=0;p<S;p++)null!=(g=r.__k[p])&&(h=-1===g.__i?f:x[g.__i]||f,g.__i=p,C(e,g,h,i,s,a,l,u,c,d),v=g.__e,g.ref&&h.ref!=g.ref&&(h.ref&&$(h.ref,null,g),d.push(g.ref,g.__c||v,g)),null==w&&null!=v&&(w=v),65536&g.__u||h.__k===g.__k?u=function e(t,r,n){var o,i;if("function"==typeof t.type){for(o=t.__k,i=0;o&&i<o.length;i++)o[i]&&(o[i].__=t,r=e(o[i],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=A(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(g,u,e):"function"==typeof g.type&&void 0!==g.__d?u=g.__d:v&&(u=v.nextSibling),g.__d=void 0,g.__u&=-196609);r.__d=u,r.__e=w}function T(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||g.test(t)?r:r+"px"}function j(e,t,r,n,o){var i;e:if("style"===t){if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||T(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||T(e.style,t,r[t])}}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase() in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.t=n.t:(r.t=c,e.addEventListener(t,i?p:d,i)):e.removeEventListener(t,i?p:d,i);else{if("http://www.w3.org/2000/svg"==o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function P(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.u)t.u=c++;else if(t.u<r.t)return;return r(n.event?n.event(t):t)}}}function C(e,t,o,i,s,a,l,u,c,d){var p,h,_,g,w,b,S,E,T,P,C,R,$,M,L,H,N=t.type;if(void 0!==t.constructor)return null;128&o.__u&&(c=!!(32&o.__u),a=[u=t.__e=o.__e]),(p=n.__b)&&p(t);e:if("function"==typeof N)try{if(E=t.props,T="prototype"in N&&N.prototype.render,P=(p=N.contextType)&&i[p.__c],C=p?P?P.props.value:p.__:i,o.__c?S=(h=t.__c=o.__c).__=h.__E:(T?t.__c=h=new N(E,C):(t.__c=h=new x(E,C),h.constructor=N,h.render=U),P&&P.sub(h),h.props=E,h.state||(h.state={}),h.context=C,h.__n=i,_=h.__d=!0,h.__h=[],h._sb=[]),T&&null==h.__s&&(h.__s=h.state),T&&null!=N.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=v({},h.__s)),v(h.__s,N.getDerivedStateFromProps(E,h.__s))),g=h.props,w=h.state,h.__v=t,_)T&&null==N.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),T&&null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(T&&null==N.getDerivedStateFromProps&&E!==g&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(E,C),!h.__e&&(null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(E,h.__s,C)||t.__v===o.__v)){for(t.__v!==o.__v&&(h.props=E,h.state=h.__s,h.__d=!1),t.__e=o.__e,t.__k=o.__k,t.__k.some(function(e){e&&(e.__=t)}),R=0;R<h._sb.length;R++)h.__h.push(h._sb[R]);h._sb=[],h.__h.length&&l.push(h);break e}null!=h.componentWillUpdate&&h.componentWillUpdate(E,h.__s,C),T&&null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(g,w,b)})}if(h.context=C,h.props=E,h.__P=e,h.__e=!1,$=n.__r,M=0,T){for(h.state=h.__s,h.__d=!1,$&&$(t),p=h.render(h.props,h.state,h.context),L=0;L<h._sb.length;L++)h.__h.push(h._sb[L]);h._sb=[]}else do h.__d=!1,$&&$(t),p=h.render(h.props,h.state,h.context),h.state=h.__s;while(h.__d&&++M<25);h.state=h.__s,null!=h.getChildContext&&(i=v(v({},i),h.getChildContext())),T&&!_&&null!=h.getSnapshotBeforeUpdate&&(b=h.getSnapshotBeforeUpdate(g,w)),O(e,y(H=null!=p&&p.type===k&&null==p.key?p.props.children:p)?H:[H],t,o,i,s,a,l,u,c,d),h.base=t.__e,t.__u&=-161,h.__h.length&&l.push(h),S&&(h.__E=h.__=null)}catch(e){if(t.__v=null,c||null!=a){for(t.__u|=c?160:128;u&&8===u.nodeType&&u.nextSibling;)u=u.nextSibling;a[a.indexOf(u)]=null,t.__e=u}else t.__e=o.__e,t.__k=o.__k;n.__e(e,t,o)}else null==a&&t.__v===o.__v?(t.__k=o.__k,t.__e=o.__e):t.__e=function(e,t,o,i,s,a,l,u,c){var d,p,h,_,g,v,w,b=o.props,k=t.props,x=t.type;if("svg"===x?s="http://www.w3.org/2000/svg":"math"===x?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),null!=a){for(d=0;d<a.length;d++)if((g=a[d])&&"setAttribute"in g==!!x&&(x?g.localName===x:3===g.nodeType)){e=g,a[d]=null;break}}if(null==e){if(null===x)return document.createTextNode(k);e=document.createElementNS(s,x,k.is&&k),u&&(n.__m&&n.__m(t,a),u=!1),a=null}if(null===x)b===k||u&&e.data===k||(e.data=k);else{if(a=a&&r.call(e.childNodes),b=o.props||f,!u&&null!=a)for(b={},d=0;d<e.attributes.length;d++)b[(g=e.attributes[d]).name]=g.value;for(d in b)if(g=b[d],"children"==d);else if("dangerouslySetInnerHTML"==d)h=g;else if(!(d in k)){if("value"==d&&"defaultValue"in k||"checked"==d&&"defaultChecked"in k)continue;j(e,d,null,g,s)}for(d in k)g=k[d],"children"==d?_=g:"dangerouslySetInnerHTML"==d?p=g:"value"==d?v=g:"checked"==d?w=g:u&&"function"!=typeof g||b[d]===g||j(e,d,g,b[d],s);if(p)u||h&&(p.__html===h.__html||p.__html===e.innerHTML)||(e.innerHTML=p.__html),t.__k=[];else if(h&&(e.innerHTML=""),O(e,y(_)?_:[_],t,o,i,"foreignObject"===x?"http://www.w3.org/1999/xhtml":s,a,l,a?a[0]:o.__k&&A(o,0),u,c),null!=a)for(d=a.length;d--;)m(a[d]);u||(d="value","progress"===x&&null==v?e.removeAttribute("value"):void 0===v||v===e[d]&&("progress"!==x||v)&&("option"!==x||v===b[d])||j(e,d,v,b[d],s),d="checked",void 0!==w&&w!==e[d]&&j(e,d,w,b[d],s))}return e}(o.__e,t,o,i,s,a,l,c,d);(p=n.diffed)&&p(t)}function R(e,t,r){t.__d=void 0;for(var o=0;o<r.length;o++)$(r[o],r[++o],r[++o]);n.__c&&n.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){n.__e(e,t.__v)}})}function $(e,t,r){try{if("function"==typeof e){var o="function"==typeof e.__u;o&&e.__u(),o&&null==t||(e.__u=e(t))}else e.current=t}catch(e){n.__e(e,r)}}function U(e,t,r){return this.constructor(e,r)}function M(e,t,o){var i,s,a,l;n.__&&n.__(e,t),s=(i="function"==typeof o)?null:o&&o.__k||t.__k,a=[],l=[],C(t,e=(!i&&o||t).__k=w(k,null,[e]),s||f,f,t.namespaceURI,!i&&o?[o]:s?null:t.firstChild?r.call(t.childNodes):null,a,!i&&o?o:s?s.__e:t.firstChild,i,l),R(a,e,l)}r=_.slice,n={__e:function(e,t,r,n){for(var o,i,s;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),s=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,n||{}),s=o.__d),s)return o.__E=o}catch(t){e=t}throw e}},o=0,i=function(e){return null!=e&&null==e.constructor},x.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=v({},this.state),"function"==typeof e&&(e=e(v({},r),this.props)),e&&v(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),S(this))},x.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),S(this))},x.prototype.render=k,s=[],l="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,u=function(e,t){return e.__v.__b-t.__v.__b},E.__r=0,c=0,d=P(!1),p=P(!0),h=0,t.Component=x,t.Fragment=k,t.cloneElement=function(e,t,n){var o,i,s,a,l=v({},e.props);for(s in e.type&&e.type.defaultProps&&(a=e.type.defaultProps),t)"key"==s?o=t[s]:"ref"==s?i=t[s]:l[s]=void 0===t[s]&&void 0!==a?a[s]:t[s];return arguments.length>2&&(l.children=arguments.length>3?r.call(arguments,2):n),b(e.type,l,o||e.key,i||e.ref,null)},t.createContext=function(e,t){var r={__c:t="__cC"+h++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var r,n;return this.getChildContext||(r=new Set,(n={})[t]=this,this.getChildContext=function(){return n},this.componentWillUnmount=function(){r=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&r.forEach(function(e){e.__e=!0,S(e)})},this.sub=function(e){r.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){r&&r.delete(e),t&&t.call(e)}}),e.children}};return r.Provider.__=r.Consumer.contextType=r},t.createElement=w,t.createRef=function(){return{current:null}},t.h=w,t.hydrate=function e(t,r){M(t,r,e)},t.isValidElement=i,t.options=n,t.render=M,t.toChildArray=function e(t,r){return r=r||[],null==t||"boolean"==typeof t||(y(t)?t.some(function(t){e(t,r)}):r.push(t)),r}},18999:e=>{e.exports=function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},85577:e=>{function t(e,t,r,n,o,i,s){try{var a=e[i](s),l=a.value}catch(e){return void r(e)}a.done?t(l):Promise.resolve(l).then(n,o)}e.exports=function(e){return function(){var r=this,n=arguments;return new Promise(function(o,i){var s=e.apply(r,n);function a(e){t(s,o,i,a,l,"next",e)}function l(e){t(s,o,i,a,l,"throw",e)}a(void 0)})}},e.exports.__esModule=!0,e.exports.default=e.exports},8908:e=>{e.exports=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},15182:(e,t,r)=>{var n=r(93762),o=r(20805);e.exports=function(e,t,r){if(n())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,t);var s=new(e.bind.apply(e,i));return r&&o(s,r.prototype),s},e.exports.__esModule=!0,e.exports.default=e.exports},85925:(e,t,r)=>{var n=r(38627);function o(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},33679:(e,t,r)=>{var n=r(38627);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},4239:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},83968:e=>{function t(r){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},23006:(e,t,r)=>{var n=r(20805);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},58089:e=>{e.exports=function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}},e.exports.__esModule=!0,e.exports.default=e.exports},93762:e=>{function t(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e.exports=t=function(){return!!r},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},64702:(e,t,r)=>{var n=r(73897).default,o=r(18999);e.exports=function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},77165:(e,t,r)=>{var n=r(73897).default;function o(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=o=function(){return r},e.exports.__esModule=!0,e.exports.default=e.exports;var t,r={},i=Object.prototype,s=i.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},l="function"==typeof Symbol?Symbol:{},u=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",d=l.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function h(e,r,n,o){var i,s,l=Object.create((r&&r.prototype instanceof m?r:m).prototype);return a(l,"_invoke",{value:(i=new P(o||[]),s=_,function(r,o){if(s===g)throw Error("Generator is already running");if(s===y){if("throw"===r)throw o;return{value:t,done:!0}}for(i.method=r,i.arg=o;;){var a=i.delegate;if(a){var l=function e(r,n){var o=n.method,i=r.iterator[o];if(i===t)return n.delegate=null,"throw"===o&&r.iterator.return&&(n.method="return",n.arg=t,e(r,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=TypeError("The iterator does not provide a '"+o+"' method")),v;var s=f(i,r.iterator,n.arg);if("throw"===s.type)return n.method="throw",n.arg=s.arg,n.delegate=null,v;var a=s.arg;return a?a.done?(n[r.resultName]=a.value,n.next=r.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=TypeError("iterator result is not an object"),n.delegate=null,v)}(a,i);if(l){if(l===v)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(s===_)throw s=y,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);s=g;var u=f(e,n,i);if("normal"===u.type){if(s=i.done?y:"suspendedYield",u.arg===v)continue;return{value:u.arg,done:i.done}}"throw"===u.type&&(s=y,i.method="throw",i.arg=u.arg)}})}),l}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}r.wrap=h;var _="suspendedStart",g="executing",y="completed",v={};function m(){}function w(){}function b(){}var k={};p(k,u,function(){return this});var x=Object.getPrototypeOf,A=x&&x(x(C([])));A&&A!==i&&s.call(A,u)&&(k=A);var S=b.prototype=m.prototype=Object.create(k);function E(e){["next","throw","return"].forEach(function(t){p(e,t,function(e){return this._invoke(t,e)})})}function O(e,t){var r;a(this,"_invoke",{value:function(o,i){function a(){return new t(function(r,a){!function r(o,i,a,l){var u=f(e[o],e,i);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==n(d)&&s.call(d,"__await")?t.resolve(d.__await).then(function(e){r("next",e,a,l)},function(e){r("throw",e,a,l)}):t.resolve(d).then(function(e){c.value=e,a(c)},function(e){return r("throw",e,a,l)})}l(u.arg)}(o,i,r,a)})}return r=r?r.then(a,a):a()}})}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function C(e){if(e||""===e){var r=e[u];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(s.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw TypeError(n(e)+" is not iterable")}return w.prototype=b,a(S,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:w,configurable:!0}),w.displayName=p(b,d,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,p(e,d,"GeneratorFunction")),e.prototype=Object.create(S),e},r.awrap=function(e){return{__await:e}},E(O.prototype),p(O.prototype,c,function(){return this}),r.AsyncIterator=O,r.async=function(e,t,n,o,i){void 0===i&&(i=Promise);var s=new O(h(e,t,n,o),i);return r.isGeneratorFunction(t)?s:s.next().then(function(e){return e.done?e.value:s.next()})},E(S),p(S,d,"Generator"),p(S,u,function(){return this}),p(S,"toString",function(){return"[object Generator]"}),r.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},r.values=C,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&s.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,o){return a.type="throw",a.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var l=s.call(i,"catchLoc"),u=s.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&s.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:C(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},20805:e=>{function t(r,n){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},43560:(e,t,r)=>{var n=r(73897).default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},38627:(e,t,r)=>{var n=r(73897).default,o=r(43560);e.exports=function(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},73897:e=>{function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},16041:(e,t,r)=>{var n=r(83968),o=r(20805),i=r(58089),s=r(15182);function a(t){var r="function"==typeof Map?new Map:void 0;return e.exports=a=function(e){if(null===e||!i(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return s(e,arguments,n(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),o(t,e)},e.exports.__esModule=!0,e.exports.default=e.exports,a(t)}e.exports=a,e.exports.__esModule=!0,e.exports.default=e.exports},81213:(e,t,r)=>{var n=r(77165)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},87658:e=>{"use strict";e.exports=JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}')}};