"use strict";exports.id=5072,exports.ids=[5072],exports.modules={51557:(e,t,r)=>{r.d(t,{e:()=>a,w:()=>i});var o=r(52972),s=r(43895);class a{shouldUseOAuth(){return"OAUTH_MODE"===process.env.MCP_GITHUB_TOKEN&&!!process.env.AUTH_GITHUB_CLIENT_ID&&!!process.env.AUTH_GITHUB_CLIENT_SECRET}async getOAuthToken(){return Buffer.from(`${process.env.AUTH_GITHUB_CLIENT_ID}:${process.env.AUTH_GITHUB_CLIENT_SECRET}`).toString("base64")}constructor(e){this.baseUrl="https://api.github.com",this.graphqlUrl="https://api.github.com/graphql",this.token=e?.token||o.Vi.GITHUB_TOKEN||"";let t=e?.owner||o.Vi.GITHUB_OWNER;t&&(this.owner=t);let r=e?.repo||o.Vi.GITHUB_REPO;r&&(this.repo=r),this.token||s.kg.warn("GitHub token n\xe3o configurado")}async restRequest(e,t={}){if(!this.token)throw Error("GitHub token n\xe3o configurado");let r=`${this.baseUrl}${e}`,o={Authorization:`Bearer ${this.token}`,Accept:"application/vnd.github+json","X-GitHub-Api-Version":"2022-11-28",...t.headers};try{let e=await fetch(r,{...t,headers:o});if(!e.ok){let t=await e.text();throw Error(`GitHub API error: ${e.status} - ${t}`)}return await e.json()}catch(t){throw s.kg.error("Erro na requisi\xe7\xe3o GitHub API",{endpoint:e,error:t}),t}}async graphqlQuery(e,t){if(!this.token)throw Error("GitHub token n\xe3o configurado");let r=await fetch(this.graphqlUrl,{method:"POST",headers:{Authorization:`Bearer ${this.token}`,"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t})});if(!r.ok)throw Error(`GitHub GraphQL error: ${r.status} ${r.statusText}`);let o=await r.json();if(o.errors)throw Error(`GitHub GraphQL error: ${o.errors.map(e=>e.message).join(", ")}`);return o.data}async getRateLimit(){return(await this.restRequest("/rate_limit")).rate}async getRepositories(e={}){let{type:t="owner",sort:r="updated",direction:o="desc",per_page:s=30,page:a=1}=e,i=this.owner?`/orgs/${this.owner}/repos`:"/user/repos",n=new URLSearchParams({type:t,sort:r,direction:o,per_page:s.toString(),page:a.toString()}),l=await this.restRequest(`${i}?${n}`);return{repositories:l,total:l.length}}async getRepository(e,t){return await this.restRequest(`/repos/${e}/${t}`)}async getIssues(e={}){let t=e.owner||this.owner,r=e.repo||this.repo;if(!t||!r)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let{state:o="open",sort:s="updated",direction:a="desc",per_page:i=30,page:n=1}=e,l=new URLSearchParams({state:o,sort:s,direction:a,per_page:i.toString(),page:n.toString()});e.labels&&l.append("labels",e.labels),e.since&&l.append("since",e.since);let u=(await this.restRequest(`/repos/${t}/${r}/issues?${l}`)).filter(e=>!("pull_request"in e));return{issues:u,total:u.length}}async getPullRequests(e={}){let t=e.owner||this.owner,r=e.repo||this.repo;if(!t||!r)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let{state:o="open",sort:s="updated",direction:a="desc",per_page:i=30,page:n=1}=e,l=new URLSearchParams({state:o,sort:s,direction:a,per_page:i.toString(),page:n.toString()});e.head&&l.append("head",e.head),e.base&&l.append("base",e.base);let u=await this.restRequest(`/repos/${t}/${r}/pulls?${l}`);return{pullRequests:u,total:u.length}}async getWorkflowRuns(e={}){let t=e.owner||this.owner,r=e.repo||this.repo;if(!t||!r)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let{per_page:o=30,page:s=1}=e,a=new URLSearchParams({per_page:o.toString(),page:s.toString()});e.workflow_id&&a.append("workflow_id",e.workflow_id),e.actor&&a.append("actor",e.actor),e.branch&&a.append("branch",e.branch),e.event&&a.append("event",e.event),e.status&&a.append("status",e.status);let i=await this.restRequest(`/repos/${t}/${r}/actions/runs?${a}`);return{workflowRuns:i.workflow_runs,total:i.total_count}}async getAuthenticatedUser(){return await this.restRequest("/user")}async checkHealth(){try{if(!this.token)return{configured:!1,tokenValid:!1,repositoryAccessible:!1,rateLimitRemaining:0,rateLimitReset:new Date().toISOString(),repositoryCount:0,issueCount:0,pullRequestCount:0,recentActivity:0};let e=await this.getRateLimit();await this.getAuthenticatedUser();let t=!0,r=0,o=0,a=0,i=0;try{if(this.owner&&this.repo){await this.getRepository(this.owner,this.repo),r=1;let[e,t]=await Promise.all([this.getIssues({per_page:10}),this.getPullRequests({per_page:10})]);o=e.total,a=t.total;let s=new Date(Date.now()-864e5);i=[...e.issues,...t.pullRequests].filter(e=>new Date(e.updated_at)>s).length}else r=(await this.getRepositories({per_page:10})).total}catch(e){s.kg.warn("Erro ao acessar reposit\xf3rio espec\xedfico:",e),t=!1}return{configured:!0,tokenValid:!0,repositoryAccessible:t,rateLimitRemaining:e.remaining,rateLimitReset:new Date(1e3*e.reset).toISOString(),lastSync:new Date().toISOString(),repositoryCount:r,issueCount:o,pullRequestCount:a,recentActivity:i}}catch(e){return s.kg.error("GitHub health check failed:",e),{configured:!!this.token,tokenValid:!1,repositoryAccessible:!1,rateLimitRemaining:0,rateLimitReset:new Date().toISOString(),repositoryCount:0,issueCount:0,pullRequestCount:0,recentActivity:0}}}}class i{constructor(e){this.client=new a(e)}async getRepositoryDashboard(e,t){let r=e||o.Vi.GITHUB_OWNER,s=t||o.Vi.GITHUB_REPO;if(!r||!s)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let[a,i,n,l]=await Promise.all([this.client.getRepository(r,s),this.client.getIssues({owner:r,repo:s,state:"open",per_page:5}),this.client.getPullRequests({owner:r,repo:s,state:"open",per_page:5}),this.client.getWorkflowRuns({owner:r,repo:s,per_page:5})]),u=[...i.issues,...n.pullRequests].sort((e,t)=>new Date(t.updated_at).getTime()-new Date(e.updated_at).getTime()).slice(0,10),c="healthy",p=l.workflowRuns.filter(e=>"failure"===e.conclusion).length;return p>2?c="critical":p>0&&(c="warning"),{repository:a,openIssues:i.total,openPullRequests:n.total,recentWorkflowRuns:l.workflowRuns,recentActivity:u,healthStatus:c}}async getCICDMetrics(e,t){let r=e||o.Vi.GITHUB_OWNER,s=t||o.Vi.GITHUB_REPO;if(!r||!s)throw Error("Owner e repo s\xe3o obrigat\xf3rios");let a=await this.client.getWorkflowRuns({owner:r,repo:s,per_page:100}),i=new Date(Date.now()-2592e6),n=a.workflowRuns.filter(e=>new Date(e.created_at)>i),l=n.length,u=n.filter(e=>"success"===e.conclusion).length,c=n.filter(e=>"completed"===e.status&&e.run_started_at),p=c.length>0?c.reduce((e,t)=>{let r=new Date(t.run_started_at).getTime();return e+(new Date(t.updated_at).getTime()-r)},0)/c.length/1e3/60:0,h=n.filter(e=>"failure"===e.conclusion).slice(0,5),g=new Map;return n.forEach(e=>{if(!e.created_at)return;let t=new Date(e.created_at).toISOString().split("T")[0];if(!t)return;let r=g.get(t)||{runs:0,failures:0};r.runs++,"failure"===e.conclusion&&r.failures++,g.set(t,r)}),{totalRuns:l,successRate:l>0?u/l*100:0,averageDuration:p,recentFailures:h,trendsLast30Days:Array.from(g.entries()).map(([e,t])=>({date:e,...t})).sort((e,t)=>e.date.localeCompare(t.date))}}}},43895:(e,t,r)=>{let o;r.d(t,{kg:()=>c});var s=r(99557),a=r.n(s);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function n(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let t=["name","message","stack"],r={};return Object.keys(e).forEach(o=>{t.includes(o)||(r[o]=e[o])}),{normalizedError:e,extractedMetadata:r}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=u.production;o=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=a()({level:"info",formatters:{level:e=>({level:e})}})}let c={trace:(e,t)=>{o.trace(t||{},e)},debug:(e,t)=>{o.debug(t||{},e)},info:(e,t)=>{o.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:r}=n(t);o.warn(r,e)}else o.warn(l(t)||{},e)},error:(e,t,r)=>{let{normalizedError:s,extractedMetadata:a}=n(t),i={...r||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};o.error(i,e)},fatal:(e,t,r)=>{let{normalizedError:s,extractedMetadata:a}=n(t),i={...r||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};o.fatal(i,e)},createChild:e=>{let t=o.child(e);return{trace:(e,r)=>{t.trace(r||{},e)},debug:(e,r)=>{t.debug(r||{},e)},info:(e,r)=>{t.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:o}=n(r);t.warn(o,e)}else t.warn(l(r)||{},e)},error:(e,r,o)=>{let{normalizedError:s,extractedMetadata:a}=n(r),i={...o||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.error(i,e)},fatal:(e,r,o)=>{let{normalizedError:s,extractedMetadata:a}=n(r),i={...o||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},82840:(e,t,r)=>{r.d(t,{R:()=>a});var o=r(87070),s=r(43895);let a={success(e,t,r=200){let s={data:e,...t&&{meta:t}};return o.NextResponse.json(s,{status:r})},error(e,t="INTERNAL_ERROR",r=500,a){let i={code:t,message:e,timestamp:new Date().toISOString(),...void 0!==a&&{details:a}};return s.kg.error(`API Error [${t}]: ${e}`,{details:a}),o.NextResponse.json(i,{status:r})},unauthorized(e="N\xe3o autorizado",t){return this.error(e,"UNAUTHORIZED",401,t)},badRequest(e,t){return this.error(e,"BAD_REQUEST",400,t)},notFound(e="Recurso n\xe3o encontrado",t){return this.error(e,"NOT_FOUND",404,t)},forbidden(e="Acesso negado",t){return this.error(e,"FORBIDDEN",403,t)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",t){let r={};return t&&(r["Retry-After"]=t.toString()),o.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:r})}}}};