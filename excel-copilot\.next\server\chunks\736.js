exports.id=736,exports.ids=[736],exports.modules={36147:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,12994,23)),Promise.resolve().then(i.t.bind(i,96114,23)),Promise.resolve().then(i.t.bind(i,9727,23)),Promise.resolve().then(i.t.bind(i,79671,23)),Promise.resolve().then(i.t.bind(i,41868,23)),Promise.resolve().then(i.t.bind(i,84759,23))},31010:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,79404,23)),Promise.resolve().then(i.bind(i,91664))},2280:(e,t,i)=>{Promise.resolve().then(i.bind(i,14831)),Promise.resolve().then(i.t.bind(i,79404,23)),Promise.resolve().then(i.bind(i,80939)),Promise.resolve().then(i.bind(i,83067)),Promise.resolve().then(i.bind(i,48253)),Promise.resolve().then(i.bind(i,31631)),Promise.resolve().then(i.bind(i,70042))},80939:(e,t,i)=>{"use strict";i.d(t,{Providers:()=>O});var r,s,a=i(10326),n=i(87012),o=i(36293),l=i(49690),c=i(77109),d=i(14831),u=i(17577),E=i.n(u),m=i(2282);i(4679);var p=i(71701),h=i(51641);let g={info:(...e)=>{},warn:(...e)=>{},error:(...e)=>{},debug:(...e)=>{}};!function(e){e[e.CRITICAL=0]="CRITICAL",e[e.HIGH=1]="HIGH",e[e.MEDIUM=2]="MEDIUM",e[e.LOW=3]="LOW",e[e.LAZY=4]="LAZY"}(r||(r={}));class f{constructor(){this.services={},this.initialized=!1,this.initializationStartTime=0,this.initializationEndTime=0}static getInstance(){return f.instance||(f.instance=new f),f.instance}registerService(e,t,i=[],r=2){return this.services[e]&&g.warn(`Servi\xe7o '${e}' j\xe1 estava registrado e ser\xe1 substitu\xeddo.`),this.services[e]={name:e,dependencies:i,initialized:!1,instance:t,priority:r},this}getService(e){let t=this.services[e];return t?t.instance:null}isInitialized(){return this.initialized}setInitialized(e){if(this.initialized=e,e){this.initializationEndTime=performance.now();let e=this.initializationEndTime-this.initializationStartTime;g.info(`Inicializa\xe7\xe3o completa do ServiceManager em ${e.toFixed(2)}ms`)}else this.initializationStartTime=performance.now()}isServiceInitialized(e){return!!this.services[e]?.initialized}setServiceInitialized(e,t){if(this.services[e]){let i=performance.now();if(t&&!this.services[e].initialized){if(this.services[e].initEndTime=i,this.services[e].initStartTime){let t=i-this.services[e].initStartTime;this.services[e].initDuration=t,t>5&&g.debug(`Servi\xe7o '${e}' inicializado em ${t.toFixed(2)}ms`)}}else!t&&this.services[e].initialized&&(this.services[e].initStartTime=i,this.services[e].initEndTime=void 0,this.services[e].initDuration=void 0);this.services[e].initialized=t}else g.warn(`Tentativa de marcar servi\xe7o n\xe3o registrado '${e}' como inicializado`)}markServiceInitializationStart(e){this.services[e]&&(this.services[e].initStartTime=performance.now())}getServicesByPriority(e){return Object.keys(this.services).filter(t=>{let i=this.services[t];return i&&i.priority===e})}getTotalInitializationTime(){return this.initialized&&this.initializationEndTime&&this.initializationStartTime?this.initializationEndTime-this.initializationStartTime:0}getAllDependencies(e,t=new Set){let i=this.services[e];if(!i)return[];if(t.has(e))return g.warn(`Depend\xeancia circular detectada para o servi\xe7o '${e}'`),[];t.add(e);let r=[...i.dependencies];for(let e of i.dependencies)for(let i of this.getAllDependencies(e,new Set(t)))r.includes(i)||r.push(i);return r}getInitializationOrder(e){let t=new Set,i=[],r=s=>{if(t.has(s))return;t.add(s);let a=this.services[s];if(a){if(void 0!==e&&a.priority!==e)return;for(let e of a.dependencies)r(e);(void 0===e||a.priority===e)&&i.push(s)}};return Object.keys(this.services).forEach(r),i}getShutdownOrder(){return this.getInitializationOrder().reverse()}async shutdown(){g.info("Iniciando finaliza\xe7\xe3o ordenada dos servi\xe7os...");let e=this.getShutdownOrder(),t=[];for(let i of e)try{let e=this.getService(i);if(!e)continue;g.debug(`Finalizando servi\xe7o: ${i}`);let t=e=>!!(e&&"function"==typeof e.shutdown),r=e=>!!(e&&"function"==typeof e.dispose),s=e=>!!(e&&"function"==typeof e.close),a=e=>!!(e&&"function"==typeof e.terminate),n=e=>!!(e&&"function"==typeof e.destroy);t(e)?await e.shutdown():r(e)?await e.dispose():s(e)?await e.close():a(e)?await e.terminate():n(e)?await e.destroy():e instanceof Map||e instanceof Set?e.clear():e&&"object"==typeof e&&"clearInterval"in globalThis&&!!(e&&"object"==typeof e&&"_timer"in e)&&e._timer&&clearInterval(e._timer),this.setServiceInitialized(i,!1)}catch(r){let e=r instanceof Error?r.message:String(r);g.error(`Erro ao finalizar servi\xe7o '${i}': ${e}`,r),t.push({service:i,error:r})}this.initialized=!1,t.length>0?g.warn(`Finaliza\xe7\xe3o de servi\xe7os conclu\xedda com ${t.length} erros`):g.info("Todos os servi\xe7os finalizados com sucesso")}reset(e=!0){if(e){g.warn("Tentativa de reset do ServiceManager em produ\xe7\xe3o bloqueada.");return}this.services={},this.initialized=!1}listServices(){return Object.values(this.services)}getInitializationMetrics(){let e=this.listServices(),t=this.getTotalInitializationTime(),i=e.filter(e=>e.initialized).length;return{totalServices:e.length,initializedServices:i,uninitializedServices:e.length-i,totalInitTime:t,serviceMetrics:e.map(e=>({name:e.name,initialized:e.initialized,initTime:e.initDuration||0,priority:e.priority}))}}}i(37178);class _{constructor(){this._initialized=!1,this._initializationResult=null,this._initializationPromise=null,this._shutdownPromise=null,this._isShuttingDown=!1}static getInstance(){return _.instance||(_.instance=new _),_.instance}get initialized(){return this._initialized}get initializationResult(){return this._initializationResult}set initializationResult(e){this._initializationResult=e,this._initialized=e?.success||!1}get initializationPromise(){return this._initializationPromise}set initializationPromise(e){this._initializationPromise=e}get isShuttingDown(){return this._isShuttingDown}set isShuttingDown(e){this._isShuttingDown=e}get shutdownPromise(){return this._shutdownPromise}set shutdownPromise(e){this._shutdownPromise=e}}async function T(e,t){let r=performance.now();for(let r of t){if(e.isServiceInitialized(r)){logger.debug(`Servi\xe7o '${r}' j\xe1 inicializado, pulando.`);continue}try{let{initLogger:t}=await Promise.resolve().then(i.bind(i,51641));t.info(`Inicializando servi\xe7o: ${r}`);let s=e.getService(r);s&&"function"==typeof s.initialize&&await s.initialize(),e.setServiceInitialized(r,!0)}catch(t){let e=t instanceof Error?t.message:String(t);throw logError(`Falha ao inicializar servi\xe7o '${r}': ${e}`,t),t}}let s=performance.now()-r;logger.debug(`Inicializa\xe7\xe3o de servi\xe7os [${t.join(", ")}] conclu\xedda em ${s.toFixed(2)}ms`)}_.getInstance(),function(e){e.NETWORK="network",e.WEBSOCKET="websocket",e.BRIDGE="bridge",e.UI="ui",e.GENERAL="general"}(s||(s={}));class v extends Error{constructor(e,t={}){super(e),this.name=this.constructor.name,this.level=t.level||"error",this.code=t.code||"UNKNOWN_ERROR",this.context=t.context||{},this.shouldReport=!1!==t.shouldReport,this.originalError=t.originalError,this.userId=t.userId,this.sessionId=t.sessionId,Object.setPrototypeOf(this,new.target.prototype)}toJSON(){return{name:this.name,message:this.message,code:this.code,level:this.level,stack:this.stack,context:this.context,originalError:this.originalError?{message:this.originalError.message,stack:this.originalError.stack}:void 0,userId:this.userId,sessionId:this.sessionId}}report(){if(!this.shouldReport)return;let e=this.toJSON();"warn"===this.level?h.logger.warn("Aviso da aplica\xe7\xe3o",e):"error"===this.level?h.logger.error("Erro da aplica\xe7\xe3o",this):"fatal"===this.level&&h.logger.fatal("Erro fatal da aplica\xe7\xe3o",this)}}class x{static async handle(e,t){x.normalizeError(e,t).report()}static normalizeError(e,t){if(e instanceof v)return t?new v(e.message,{level:e.level,code:e.code,originalError:e.originalError,userId:e.userId,sessionId:e.sessionId,shouldReport:e.shouldReport,context:{...e.context,...t}}):e;if(e instanceof Error)return logger.debug("Normalizando erro nativo",{errorName:e.name}),new v(e.message,{originalError:e,context:t||void 0,code:"Error"===e.name?"UNKNOWN_ERROR":e.name});if("string"==typeof e)return new v(e,{context:t||void 0});try{let i=JSON.stringify(e);return new v(`Erro n\xe3o estruturado: ${i}`,{context:t||void 0})}catch{return new v("Erro desconhecido n\xe3o serializ\xe1vel",{context:t||void 0})}}static async handleApiError(e,t,i){let r=this.getUserIdFromRequest(t),s=this.getSessionIdFromRequest(t),a=x.normalizeError(e,{url:t.url,method:t.method,userId:r,sessionId:s}),n=new v(a.message,{level:a.level,code:a.code,context:a.context,originalError:a.originalError,shouldReport:a.shouldReport,userId:r||void 0,sessionId:s||void 0});return n.report(),logger.debug("API Error",{errorId:i,code:n.code,message:n.message}),{message:this.getSafeErrorMessage(n),code:n.code,id:i}}static getSafeErrorMessage(e){return"fatal"===e.level||"INTERNAL_SERVER_ERROR"===e.code?"Ocorreu um erro inesperado no servidor.":e.message}static getUserIdFromRequest(e){try{if(!e.headers.get("authorization"))return;return"user-id-extracted"}catch(e){logger.warn("Erro ao extrair ID do usu\xe1rio",{error:e});return}}static getSessionIdFromRequest(e){try{return e.headers.get("x-session-id")||void 0}catch(e){logger.warn("Erro ao extrair ID da sess\xe3o",{error:e});return}}}function I({children:e}){let[t,i]=(0,u.useState)(!1),[r,s]=(0,u.useState)(!0),[n,o]=(0,u.useState)(null),[l,c]=(0,u.useState)("not-started"),[d,E]=(0,u.useState)(0);return((0,u.useRef)(0),(0,u.useRef)(null),r)?a.jsx("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-6 max-w-md p-8",children:[(0,a.jsxs)("div",{className:"relative h-10 w-10",children:[a.jsx("div",{className:"absolute h-10 w-10 animate-ping rounded-full bg-primary opacity-75"}),a.jsx("div",{className:"relative flex h-10 w-10 items-center justify-center rounded-full bg-primary",children:(0,a.jsxs)("svg",{className:"h-5 w-5 text-white",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{d:"M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3"}),a.jsx("path",{d:"M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3"}),a.jsx("path",{d:"M4 12H2"}),a.jsx("path",{d:"M10 12H8"}),a.jsx("path",{d:"M16 12h-2"}),a.jsx("path",{d:"M22 12h-2"})]})})]}),(0,a.jsxs)("div",{className:"space-y-4 text-center w-full",children:[a.jsx("h3",{className:"text-xl font-medium",children:"Inicializando Excel Copilot"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:(e=>{switch(e){case"not-started":return"Preparando ambiente...";case"critical-services":return"Inicializando servi\xe7os essenciais...";case"high-priority":return"Preparando interface de usu\xe1rio...";case"medium-priority":return"Carregando funcionalidades avan\xe7adas...";case"low-priority":return"Inicializando recursos de IA...";case"health-checks":return"Verificando conex\xf5es externas...";case"complete":return"Inicializa\xe7\xe3o conclu\xedda!";case"error":return"Erro de inicializa\xe7\xe3o";default:return"Carregando..."}})(l)}),a.jsx("div",{className:"w-full bg-secondary rounded-full h-2.5 overflow-hidden",children:a.jsx("div",{className:"bg-primary h-2.5 rounded-full transition-all duration-300 ease-out",style:{width:`${d}%`}})}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground pt-1",children:[a.jsx("span",{className:"not-started"!==l?"text-primary font-medium":"",children:"Essenciais"}),a.jsx("span",{className:"high-priority"===l||"medium-priority"===l||"low-priority"===l||"health-checks"===l||"complete"===l?"text-primary font-medium":"",children:"Interface"}),a.jsx("span",{className:"medium-priority"===l||"low-priority"===l||"health-checks"===l||"complete"===l?"text-primary font-medium":"",children:"Avan\xe7ados"}),a.jsx("span",{className:"low-priority"===l||"health-checks"===l||"complete"===l?"text-primary font-medium":"",children:"IA"}),a.jsx("span",{className:"health-checks"===l||"complete"===l?"text-primary font-medium":"",children:"Verifica\xe7\xe3o"})]})]})]})}):n?a.jsx("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 max-w-md p-6",children:[a.jsx("div",{className:"h-10 w-10 rounded-full bg-destructive flex items-center justify-center",children:(0,a.jsxs)("svg",{className:"h-5 w-5 text-white",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{d:"M18 6 6 18"}),a.jsx("path",{d:"m6 6 12 12"})]})}),(0,a.jsxs)("div",{className:"space-y-2 text-center",children:[a.jsx("h3",{className:"text-xl font-medium",children:"Erro de inicializa\xe7\xe3o"}),a.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:n}),a.jsx("button",{className:"inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary",onClick:()=>window.location.reload(),children:"Tentar novamente"})]})]})}):t?a.jsx(a.Fragment,{children:e}):a.jsx("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{children:"Estado de inicializa\xe7\xe3o inconsistente. Atualize a p\xe1gina."}),a.jsx("button",{className:"mt-4 inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90",onClick:()=>window.location.reload(),children:"Atualizar"})]})})}function A({children:e,fallback:t=null}){let[i,r]=(0,u.useState)(!1);return i?a.jsx(a.Fragment,{children:e}):a.jsx(a.Fragment,{children:t})}let C=(0,u.memo)(({providers:e,children:t,fallback:i})=>{let r=(0,u.useMemo)(()=>e.filter(({condition:e=!0})=>e),[e]),s=(0,u.useMemo)(()=>r.reduceRight((e,{provider:t,props:i={},key:r})=>r?E().cloneElement(a.jsx(t,{...i,children:e}),{key:r}):a.jsx(t,{...i,children:e}),t),[r,t]);return 0===r.length?a.jsx(a.Fragment,{children:i||t}):a.jsx(a.Fragment,{children:s})});C.displayName="ProviderComposer",(0,u.memo)(({provider:e,props:t={},children:i,condition:r=!0,fallback:s})=>r?a.jsx(e,{...t,children:i}):a.jsx(a.Fragment,{children:s||i})).displayName="SingleProvider";var R=i(79355);let N=(0,i(2325).ec)();function S({children:e}){let t=(0,u.useMemo)(()=>new n.S({defaultOptions:{queries:{refetchOnWindowFocus:!1,staleTime:3e5,retry:1,cacheTime:6e5}}}),[]),i=(0,u.useMemo)(()=>{let e=process.env.NEXT_PUBLIC_APP_URL?process.env.NEXT_PUBLIC_APP_URL:"http://localhost:3000";return N.createClient({links:[(0,R.N8)({url:`${e}/api/trpc`,headers:()=>({"x-trpc-source":"react"})})],transformer:m.ZP})},[]);return a.jsx(N.Provider,{client:i,queryClient:t,children:a.jsx(o.aH,{client:t,children:e})})}let b=(0,u.lazy)(()=>i.e(8667).then(i.bind(i,68667)).then(e=>({default:e.TourProvider}))),D=(0,u.lazy)(()=>i.e(6709).then(i.bind(i,6709)).then(e=>({default:e.CSRFProvider}))),L=(0,u.lazy)(()=>Promise.all([i.e(180),i.e(1011)]).then(i.bind(i,1011)).then(e=>({default:e.ToastProvider}))),U=(0,u.lazy)(()=>Promise.all([i.e(180),i.e(4818)]).then(i.bind(i,84818)).then(e=>({default:e.Toaster}))),P=(0,u.lazy)(()=>i.e(5999).then(i.bind(i,85999)).then(e=>({default:e.Toaster})));function O({children:e,locale:t="pt-BR"}){let[i,r]=(0,u.useState)(!1),[s]=(0,u.useState)(()=>new n.S({defaultOptions:{queries:{staleTime:6e4,cacheTime:3e5,refetchOnWindowFocus:!1,retry:1,queryFn:({queryKey:e})=>m.ZP.stringify(e)}}})),E=[{provider:c.SessionProvider,key:"session"},{provider:d.ThemeProvider,props:{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0},key:"theme"},{provider:({children:e})=>a.jsx(o.aH,{client:s,children:e}),key:"query"},{provider:S,key:"trpc"}].map(({provider:e,props:t,key:i,condition:r})=>{var s;return s={key:i||void 0,condition:r??!0},{provider:e,props:t||{},key:s?.key,condition:s?.condition??!0}}),p=a.jsx("div",{className:"flex h-screen w-full items-center justify-center bg-background",children:a.jsx("div",{className:"text-center",children:"Carregando..."})});return a.jsx(C,{providers:E,children:a.jsx(A,{fallback:p,children:(0,a.jsxs)(I,{children:[a.jsx(u.Suspense,{fallback:null,children:a.jsx(D,{children:a.jsx(u.Suspense,{fallback:null,children:(0,a.jsxs)(L,{children:[a.jsx(u.Suspense,{fallback:null,children:i&&a.jsx(b,{children:e})}),a.jsx(l.c,{})]})})})}),a.jsx(u.Suspense,{fallback:null,children:a.jsx(U,{})}),a.jsx(u.Suspense,{fallback:null,children:a.jsx(P,{position:"top-right",toastOptions:{style:{fontSize:"0.875rem"},duration:4e3}})})]})})})}(0,p.xi)()},48253:(e,t,i)=>{"use strict";i.d(t,{default:()=>s});var r=i(10326);function s({children:e,className:t,interClassName:i}){return(0,r.jsxs)(r.Fragment,{children:[e,r.jsx("div",{id:"portal-root","aria-hidden":"true"})]})}i(17577)},83067:(e,t,i)=>{"use strict";function r(){return null}i.d(t,{ClientScripts:()=>r}),i(17577)},31631:(e,t,i)=>{"use strict";i.r(t),i.d(t,{NavBar:()=>g});var r=i(10326),s=i(95920),a=i(24319),n=i(28916),o=i(35351),l=i(71876),c=i(90434),d=i(35047),u=i(17577),E=i(50337),m=i(51223);let p=[{href:"/",label:"In\xedcio",icon:s.Z,ariaLabel:"P\xe1gina inicial"},{href:"/dashboard",label:"Painel",icon:a.Z,ariaLabel:"Painel de controle"},{href:"/pricing",label:"Pre\xe7os",icon:n.Z,ariaLabel:"Planos e pre\xe7os"},{href:"/help",label:"Ajuda",icon:o.Z,ariaLabel:"Central de ajuda"},{href:"/api-docs",label:"API",icon:l.Z,ariaLabel:"Documenta\xe7\xe3o da API"}],h=({href:e,children:t,className:i})=>r.jsx(c.default,{href:e,className:(0,m.cn)("flex items-center text-lg font-semibold text-foreground hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary",i),children:t});function g(){let e=(0,d.usePathname)(),[t,i]=(0,u.useState)(!1),[s,a]=(0,u.useState)(!1);!function(e){let[t,i]=(0,u.useState)(()=>!1)}(0);let n=(0,u.useRef)(null),o=(0,u.useRef)(null),l=(0,u.useRef)(null),g=(0,u.useRef)(null);(0,u.useCallback)(()=>{a(window.scrollY>5)},[]);let f=()=>{i(!t)},_=E.OK.transition.medium;return(0,r.jsxs)(r.Fragment,{children:[r.jsx("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:outline-ring",children:"Skip to main content"}),(0,r.jsxs)("div",{className:"flex items-center w-full",children:[r.jsx("div",{className:"mr-4 flex items-center md:hidden",children:(0,r.jsxs)("button",{ref:o,type:"button",onClick:()=>f(),className:"inline-flex items-center justify-center rounded-md p-2 text-foreground hover:bg-muted focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","aria-controls":"mobile-menu","aria-expanded":t,children:[r.jsx("span",{className:"sr-only",children:t?"Close main menu":"Open main menu"}),r.jsx("svg",{className:(0,m.cn)("h-6 w-6",t?"hidden":"block"),fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})}),r.jsx("svg",{className:(0,m.cn)("h-6 w-6",t?"block":"hidden"),fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})})]})}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(h,{href:"/",className:(0,m.cn)("flex items-center text-lg font-semibold text-foreground","hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary",_),children:"Excel Copilot"}),(0,r.jsxs)("div",{className:"hidden md:flex md:items-center md:gap-6 ml-10",children:[r.jsx(h,{href:"/",className:(0,m.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/"===e?"text-primary font-semibold":"text-foreground/80",_),"aria-current":"/"===e?"page":void 0,children:"In\xedcio"}),r.jsx(h,{href:"/dashboard",className:(0,m.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/dashboard"===e?"text-primary font-semibold":"text-foreground/80",_),"aria-current":"/dashboard"===e?"page":void 0,children:"Painel"}),r.jsx(h,{href:"/pricing",className:(0,m.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/pricing"===e?"text-primary font-semibold":"text-foreground/80",_),"aria-current":"/pricing"===e?"page":void 0,children:"Pre\xe7os"}),r.jsx(h,{href:"/help",className:(0,m.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/help"===e?"text-primary font-semibold":"text-foreground/80",_),"aria-current":"/help"===e?"page":void 0,children:"Ajuda"}),r.jsx(h,{href:"/api-docs",className:(0,m.cn)("text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary","/api-docs"===e?"text-primary font-semibold":"text-foreground/80",_),"aria-current":"/api-docs"===e?"page":void 0,children:"API"})]})]}),r.jsx("div",{className:"flex-grow min-w-[120px] md:min-w-[200px] lg:min-w-[300px]"}),r.jsx("div",{className:"flex items-center gap-3 md:gap-5"})]}),t&&r.jsx("div",{ref:n,id:"mobile-menu",className:"md:hidden fixed inset-0 top-16 z-40 bg-background/80 backdrop-blur-sm","aria-label":"Mobile Navigation",children:(0,r.jsxs)("div",{className:"p-4 bg-background border-b shadow-md flex flex-col space-y-4",children:[p.map((t,i)=>(0,r.jsxs)(c.default,{href:t.href,className:(0,m.cn)("flex items-center py-2 text-base font-medium rounded-md",e===t.href?"text-primary font-semibold":"text-foreground/70 hover:text-primary",_),"aria-current":e===t.href?"page":void 0,ref:0===i?l:void 0,children:[r.jsx(t.icon,{className:"mr-3 h-5 w-5","aria-hidden":"true"}),t.label]},t.href)),r.jsx("div",{className:"pt-4 border-t border-border",children:r.jsx("button",{ref:g,onClick:()=>f(),className:"w-full flex items-center justify-center px-4 py-2 text-base font-medium text-foreground bg-muted rounded-md hover:bg-muted/80",children:"Fechar Menu"})})]})})]})}},3594:(e,t,i)=>{"use strict";i.d(t,{F$:()=>l,Q5:()=>c,qE:()=>o});var r=i(10326),s=i(42851),a=i(17577),n=i(51223);let o=a.forwardRef(({className:e,...t},i)=>r.jsx(s.fC,{ref:i,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));o.displayName=s.fC.displayName;let l=a.forwardRef(({className:e,...t},i)=>r.jsx(s.Ee,{ref:i,className:(0,n.cn)("aspect-square h-full w-full",e),...t}));l.displayName=s.Ee.displayName;let c=a.forwardRef(({className:e,...t},i)=>r.jsx(s.NY,{ref:i,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));c.displayName=s.NY.displayName},91664:(e,t,i)=>{"use strict";i.d(t,{Button:()=>u,d:()=>d});var r=i(10326),s=i(34214),a=i(79360),n=i(31722),o=i(17577),l=i(45365),c=i(51223);let d=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),u=o.forwardRef(({className:e,variant:t,size:i,rounded:a,cssFeedback:o,asChild:u=!1,animated:E=!1,icon:m,iconPosition:p="left",children:h,...g},f)=>{let _=u?s.g7:"button",T=(0,r.jsxs)("span",{className:"inline-flex items-center justify-center",children:[m&&"left"===p&&r.jsx("span",{className:"mr-2",children:m}),h,m&&"right"===p&&r.jsx("span",{className:"ml-2",children:m})]});if(E){let s={whileTap:{scale:.97},whileHover:["link","ghost"].includes(t)?void 0:{y:-2},transition:{duration:.67*l.zn,ease:l.d}},o=(0,c.cn)(d({variant:t,size:i,rounded:a,cssFeedback:"none",className:e})),u={...g,className:o,...s};return r.jsx(n.E.button,{ref:f,...u,children:T})}return r.jsx(_,{className:(0,c.cn)(d({variant:t,size:i,rounded:a,cssFeedback:o,className:e})),ref:f,...g,children:T})});u.displayName="Button"},10143:(e,t,i)=>{"use strict";i.d(t,{$F:()=>u,AW:()=>m,Ju:()=>h,Qk:()=>E,VD:()=>g,Xi:()=>p,h_:()=>d});var r=i(10326),s=i(79313),a=i(39183),n=i(32933),o=i(53982),l=i(17577),c=i(51223);let d=s.fC,u=s.xz,E=s.ZA;s.Uv,s.Tr,s.Ee,l.forwardRef(({className:e,inset:t,children:i,...n},o)=>(0,r.jsxs)(s.fF,{ref:o,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...n,children:[i,r.jsx(a.Z,{className:"ml-auto h-4 w-4"})]})).displayName=s.fF.displayName,l.forwardRef(({className:e,...t},i)=>r.jsx(s.tu,{ref:i,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=s.tu.displayName;let m=l.forwardRef(({className:e,sideOffset:t=4,...i},a)=>r.jsx(s.Uv,{children:r.jsx(s.VY,{ref:a,sideOffset:t,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...i})}));m.displayName=s.VY.displayName;let p=l.forwardRef(({className:e,inset:t,...i},a)=>r.jsx(s.ck,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...i}));p.displayName=s.ck.displayName,l.forwardRef(({className:e,children:t,checked:i,...a},o)=>(0,r.jsxs)(s.oC,{ref:o,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:i??!1,...a,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(s.wU,{children:r.jsx(n.Z,{className:"h-4 w-4"})})}),t]})).displayName=s.oC.displayName,l.forwardRef(({className:e,children:t,...i},a)=>(0,r.jsxs)(s.Rk,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...i,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(s.wU,{children:r.jsx(o.Z,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=s.Rk.displayName;let h=l.forwardRef(({className:e,inset:t,...i},a)=>r.jsx(s.__,{ref:a,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...i}));h.displayName=s.__.displayName;let g=l.forwardRef(({className:e,...t},i)=>r.jsx(s.Z0,{ref:i,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));g.displayName=s.Z0.displayName},70042:(e,t,i)=>{"use strict";i.r(t),i.d(t,{UserNav:()=>E});var r=i(10326),s=i(60850),a=i(72607),n=i(35047),o=i(77109),l=i(14831),c=i(3594),d=i(91664),u=i(10143);function E(){var e;let{data:t}=(0,o.useSession)(),i=(0,n.useRouter)(),{theme:E,setTheme:m}=(0,l.F)(),p=t?.user,h=async()=>{await (0,o.signOut)({redirect:!1}),i.push("/auth/signin")},g=()=>{m("dark"===E?"light":"dark")};return(0,r.jsxs)(u.h_,{children:[r.jsx(u.$F,{asChild:!0,children:r.jsx(d.Button,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsxs)(c.qE,{className:"h-8 w-8",children:[r.jsx(c.F$,{src:p?.image||"",alt:p?.name||"Usu\xe1rio"}),r.jsx(c.Q5,{children:(e=p?.name)?e.split(" ").map(e=>e[0]).join("").toUpperCase().substring(0,2):"?"})]})})}),(0,r.jsxs)(u.AW,{className:"w-56",align:"end",forceMount:!0,children:[r.jsx(u.Ju,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[r.jsx("p",{className:"text-sm font-medium leading-none",children:p?.name}),r.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:p?.email})]})}),r.jsx(u.VD,{}),(0,r.jsxs)(u.Qk,{children:[r.jsx(u.Xi,{onClick:()=>i.push("/dashboard"),children:"Painel"}),r.jsx(u.Xi,{onClick:()=>i.push("/profile"),children:"Perfil"}),r.jsx(u.Xi,{onClick:()=>i.push("/settings"),children:"Configura\xe7\xf5es"}),r.jsx(u.Xi,{onClick:()=>i.push("/dashboard/account"),children:"Conta"})]}),r.jsx(u.VD,{}),(0,r.jsxs)(u.Xi,{onClick:()=>g(),children:["dark"===E?r.jsx(s.Z,{className:"mr-2 h-4 w-4"}):r.jsx(a.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"dark"===E?"Tema claro":"Tema escuro"})]}),r.jsx(u.VD,{}),r.jsx(u.Xi,{onClick:()=>h(),children:"Sair"})]})]})}},4679:(e,t,i)=>{"use strict";i.d(t,{Vi:()=>E});var r=i(27256);let s=r.z.string().url("Deve ser uma URL v\xe1lida"),a=r.z.string().min(8,"Deve ter pelo menos 8 caracteres"),n=r.z.string().min(1,"N\xe3o pode estar vazio"),o=r.z.object({NODE_ENV:r.z.enum(["development","production","test"]),APP_NAME:r.z.string().default("Excel Copilot"),APP_VERSION:r.z.string().default("1.0.0"),APP_URL:s,AUTH_NEXTAUTH_SECRET:a,AUTH_NEXTAUTH_URL:s,AUTH_GOOGLE_CLIENT_ID:n.optional(),AUTH_GOOGLE_CLIENT_SECRET:a.optional(),AUTH_GITHUB_CLIENT_ID:n.optional(),AUTH_GITHUB_CLIENT_SECRET:a.optional(),AUTH_SKIP_PROVIDERS:r.z.boolean().default(!1),DB_DATABASE_URL:r.z.string().min(1,"URL do banco \xe9 obrigat\xf3ria"),DB_DIRECT_URL:r.z.string().optional(),DB_PROVIDER:r.z.enum(["postgresql","sqlite"]).default("postgresql"),AI_ENABLED:r.z.boolean().default(!0),AI_USE_MOCK:r.z.boolean().default(!1),AI_VERTEX_PROJECT_ID:r.z.string().optional(),AI_VERTEX_LOCATION:r.z.string().default("us-central1"),AI_VERTEX_MODEL:r.z.string().default("gemini-2.0-flash-001"),STRIPE_ENABLED:r.z.boolean().default(!0),STRIPE_SECRET_KEY:r.z.string().optional(),STRIPE_WEBHOOK_SECRET:r.z.string().optional(),STRIPE_PUBLISHABLE_KEY:r.z.string().optional(),SUPABASE_URL:s.optional(),SUPABASE_ANON_KEY:r.z.string().optional(),SUPABASE_SERVICE_ROLE_KEY:r.z.string().optional(),MCP_VERCEL_TOKEN:r.z.string().optional(),MCP_VERCEL_PROJECT_ID:r.z.string().optional(),MCP_VERCEL_TEAM_ID:r.z.string().optional(),MCP_LINEAR_API_KEY:r.z.string().optional(),MCP_GITHUB_TOKEN:r.z.string().optional(),DEV_DISABLE_VALIDATION:r.z.boolean().default(!1),DEV_FORCE_PRODUCTION:r.z.boolean().default(!1),DEV_LOG_LEVEL:r.z.enum(["debug","info","warn","error"]).default("info"),SECURITY_CSRF_SECRET:r.z.string().optional(),SECURITY_RATE_LIMIT_ENABLED:r.z.boolean().default(!0),SECURITY_CORS_ORIGINS:r.z.string().optional()});function l(e,t){return process.env[e]||t}function c(e,t=!1){return e?["true","1","yes","on"].includes(e.toLowerCase()):t}class d{constructor(){this.initialized=!1,this.config=this.loadConfiguration(),this.validationResult=this.validateConfiguration()}static getInstance(){return d.instance||(d.instance=new d),d.instance}loadConfiguration(){return{NODE_ENV:l("NODE_ENV","development"),APP_NAME:l("APP_NAME","Excel Copilot"),APP_VERSION:l("APP_VERSION","1.0.0"),APP_URL:l("NEXT_PUBLIC_APP_URL")||l("APP_URL","http://localhost:3000"),AUTH_NEXTAUTH_SECRET:l("AUTH_NEXTAUTH_SECRET"),AUTH_NEXTAUTH_URL:l("AUTH_NEXTAUTH_URL"),AUTH_GOOGLE_CLIENT_ID:l("AUTH_GOOGLE_CLIENT_ID"),AUTH_GOOGLE_CLIENT_SECRET:l("AUTH_GOOGLE_CLIENT_SECRET"),AUTH_GITHUB_CLIENT_ID:l("AUTH_GITHUB_CLIENT_ID"),AUTH_GITHUB_CLIENT_SECRET:l("AUTH_GITHUB_CLIENT_SECRET"),AUTH_SKIP_PROVIDERS:c(l("AUTH_SKIP_PROVIDERS")),DB_DATABASE_URL:l("DB_DATABASE_URL"),DB_DIRECT_URL:l("DB_DIRECT_URL"),DB_PROVIDER:l("DB_PROVIDER","postgresql"),AI_ENABLED:this.resolveAIConfiguration(),AI_USE_MOCK:this.resolveAIMockConfiguration(),AI_VERTEX_PROJECT_ID:l("AI_VERTEX_PROJECT_ID"),AI_VERTEX_LOCATION:l("AI_VERTEX_LOCATION","us-central1"),AI_VERTEX_MODEL:l("AI_VERTEX_MODEL","gemini-2.0-flash-001"),STRIPE_ENABLED:!c(l("DISABLE_STRIPE")),STRIPE_SECRET_KEY:l("STRIPE_SECRET_KEY"),STRIPE_WEBHOOK_SECRET:l("STRIPE_WEBHOOK_SECRET"),STRIPE_PUBLISHABLE_KEY:l("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"),SUPABASE_URL:l("SUPABASE_URL")||l("NEXT_PUBLIC_SUPABASE_URL"),SUPABASE_ANON_KEY:l("SUPABASE_ANON_KEY")||l("NEXT_PUBLIC_SUPABASE_ANON_KEY"),SUPABASE_SERVICE_ROLE_KEY:l("SUPABASE_SERVICE_ROLE_KEY"),MCP_VERCEL_TOKEN:l("MCP_VERCEL_TOKEN"),MCP_VERCEL_PROJECT_ID:l("MCP_VERCEL_PROJECT_ID"),MCP_VERCEL_TEAM_ID:l("MCP_VERCEL_TEAM_ID"),MCP_LINEAR_API_KEY:l("MCP_LINEAR_API_KEY"),MCP_GITHUB_TOKEN:l("MCP_GITHUB_TOKEN"),DEV_DISABLE_VALIDATION:!1,DEV_FORCE_PRODUCTION:c(l("DEV_FORCE_PRODUCTION")),DEV_LOG_LEVEL:l("DEV_LOG_LEVEL","info"),SECURITY_CSRF_SECRET:l("SECURITY_CSRF_SECRET"),SECURITY_RATE_LIMIT_ENABLED:!c(l("SECURITY_RATE_LIMIT_ENABLED")),SECURITY_CORS_ORIGINS:l("SECURITY_CORS_ORIGINS")}}resolveAIConfiguration(){if(c(l("NEXT_PUBLIC_DISABLE_VERTEX_AI")))return!1;let e=l("AI_ENABLED");return void 0!==e?c(e,!0):!!l("AI_VERTEX_PROJECT_ID")||"production"===l("NODE_ENV","development")}resolveAIMockConfiguration(){return!!(c(l("FORCE_GOOGLE_MOCKS"))||c(l("AI_USE_MOCK")))||!this.resolveAIConfiguration()||!(l("AI_VERTEX_PROJECT_ID")||l("VERTEX_AI_CREDENTIALS"))||"development"===l("NODE_ENV","development")}validateConfiguration(){let e={valid:!0,errors:[],warnings:[],missing:[],conflicts:[]};try{o.parse(this.config),this.validateByEnvironment(e),this.validateDependencies(e),this.validateConflicts(e),this.validateSecurity(e)}catch(t){t instanceof r.z.ZodError?e.errors.push(...t.errors.map(e=>`${e.path.join(".")}: ${e.message}`)):e.errors.push(`Erro de valida\xe7\xe3o: ${t}`)}return e.valid=0===e.errors.length,e}validateByEnvironment(e){let t=this.config.NODE_ENV;if("production"===t){for(let t of["AUTH_NEXTAUTH_SECRET","AUTH_NEXTAUTH_URL","DB_DATABASE_URL"])this.config[t]||e.errors.push(`${t} \xe9 obrigat\xf3ria em produ\xe7\xe3o`);this.config.AUTH_GOOGLE_CLIENT_ID||this.config.AUTH_GITHUB_CLIENT_ID||e.errors.push("Pelo menos um provider OAuth deve estar configurado em produ\xe7\xe3o"),this.config.DEV_DISABLE_VALIDATION&&e.errors.push("CR\xcdTICO: DISABLE_ENV_VALIDATION est\xe1 ativo em produ\xe7\xe3o - RISCO DE SEGURAN\xc7A"),(l("DISABLE_ENV_VALIDATION")||l("DEV_DISABLE_VALIDATION"))&&e.errors.push("CR\xcdTICO: Tentativa de bypass de valida\xe7\xe3o detectada em produ\xe7\xe3o")}"development"!==t||this.config.AUTH_GOOGLE_CLIENT_ID||this.config.AUTH_GITHUB_CLIENT_ID||e.warnings.push("Nenhum provider OAuth configurado - usando modo de desenvolvimento")}validateDependencies(e){this.config.STRIPE_ENABLED&&(this.config.STRIPE_SECRET_KEY||e.errors.push("STRIPE_SECRET_KEY \xe9 obrigat\xf3ria quando Stripe est\xe1 habilitado"),this.config.STRIPE_PUBLISHABLE_KEY||e.errors.push("STRIPE_PUBLISHABLE_KEY \xe9 obrigat\xf3ria quando Stripe est\xe1 habilitado")),!this.config.AI_ENABLED||this.config.AI_USE_MOCK||this.config.AI_VERTEX_PROJECT_ID||e.warnings.push("AI habilitada sem VERTEX_AI_PROJECT_ID - usando modo mock"),this.config.DB_DATABASE_URL?.includes("supabase")&&(this.config.SUPABASE_URL||e.warnings.push("Usando Supabase mas SUPABASE_URL n\xe3o configurada"),this.config.SUPABASE_ANON_KEY||e.warnings.push("Usando Supabase mas SUPABASE_ANON_KEY n\xe3o configurada"))}validateConflicts(e){this.config.AI_ENABLED&&c(l("FORCE_GOOGLE_MOCKS"))&&e.conflicts.push("AI_ENABLED=true mas FORCE_GOOGLE_MOCKS=true - usando mocks"),[l("FORCE_GOOGLE_MOCKS"),l("AI_USE_MOCK"),l("NEXT_PUBLIC_DISABLE_VERTEX_AI")].filter(Boolean).length>1&&e.conflicts.push("M\xfaltiplas flags de configura\xe7\xe3o de IA detectadas - usando hierarquia de preced\xeancia"),"production"===this.config.NODE_ENV&&this.config.AUTH_SKIP_PROVIDERS&&e.conflicts.push("SKIP_AUTH_PROVIDERS=true em produ\xe7\xe3o - pode causar problemas de autentica\xe7\xe3o")}validateSecurity(e){this.config.AUTH_NEXTAUTH_SECRET&&this.config.AUTH_NEXTAUTH_SECRET.length<32&&e.warnings.push("NEXTAUTH_SECRET deveria ter pelo menos 32 caracteres para m\xe1xima seguran\xe7a"),"production"===this.config.NODE_ENV&&(this.config.AUTH_NEXTAUTH_URL?.includes("localhost")&&e.errors.push("NEXTAUTH_URL n\xe3o pode ser localhost em produ\xe7\xe3o"),this.config.APP_URL?.includes("localhost")&&e.errors.push("APP_URL n\xe3o pode ser localhost em produ\xe7\xe3o")),"production"!==this.config.NODE_ENV||this.config.SECURITY_CORS_ORIGINS||e.warnings.push("CORS_ORIGINS n\xe3o configurado em produ\xe7\xe3o - pode causar problemas de seguran\xe7a")}getConfig(){return{...this.config}}getValidationResult(){return{...this.validationResult}}isValid(){return this.validationResult.valid}getAuthConfig(){return{enabled:!this.config.AUTH_SKIP_PROVIDERS,status:this.config.AUTH_SKIP_PROVIDERS?"disabled":"enabled",credentials:{nextAuthSecret:this.config.AUTH_NEXTAUTH_SECRET||"",nextAuthUrl:this.config.AUTH_NEXTAUTH_URL||"",googleClientId:this.config.AUTH_GOOGLE_CLIENT_ID||"",googleClientSecret:this.config.AUTH_GOOGLE_CLIENT_SECRET||"",githubClientId:this.config.AUTH_GITHUB_CLIENT_ID||"",githubClientSecret:this.config.AUTH_GITHUB_CLIENT_SECRET||""}}}getAIConfig(){let e=this.config.AI_ENABLED,t=this.config.AI_USE_MOCK,i="enabled";return e?t&&(i="mock"):i="disabled",{enabled:e,status:i,credentials:{projectId:this.config.AI_VERTEX_PROJECT_ID||"",location:this.config.AI_VERTEX_LOCATION,model:this.config.AI_VERTEX_MODEL}}}getDatabaseConfig(){return{enabled:!!this.config.DB_DATABASE_URL,status:this.config.DB_DATABASE_URL?"enabled":"disabled",credentials:{databaseUrl:this.config.DB_DATABASE_URL||"",directUrl:this.config.DB_DIRECT_URL||"",provider:this.config.DB_PROVIDER}}}getStripeConfig(){return{enabled:this.config.STRIPE_ENABLED,status:this.config.STRIPE_ENABLED?"enabled":"disabled",credentials:{secretKey:this.config.STRIPE_SECRET_KEY||"",webhookSecret:this.config.STRIPE_WEBHOOK_SECRET||"",publishableKey:this.config.STRIPE_PUBLISHABLE_KEY||""}}}getMCPConfig(){return{vercel:{enabled:!!this.config.MCP_VERCEL_TOKEN,status:this.config.MCP_VERCEL_TOKEN?"enabled":"disabled",credentials:{token:this.config.MCP_VERCEL_TOKEN||"",projectId:this.config.MCP_VERCEL_PROJECT_ID||"",teamId:this.config.MCP_VERCEL_TEAM_ID||""}},linear:{enabled:!!this.config.MCP_LINEAR_API_KEY,status:this.config.MCP_LINEAR_API_KEY?"enabled":"disabled",credentials:{apiKey:this.config.MCP_LINEAR_API_KEY||""}},github:{enabled:!!this.config.MCP_GITHUB_TOKEN,status:this.config.MCP_GITHUB_TOKEN?"enabled":"disabled",credentials:{token:this.config.MCP_GITHUB_TOKEN||""}}}}revalidate(){return this.config=this.loadConfiguration(),this.validationResult=this.validateConfiguration(),this.getValidationResult()}generateReport(){let e=this.validationResult,t=this.config,i="\uD83D\uDD27 RELAT\xd3RIO DE CONFIGURA\xc7\xc3O - EXCEL COPILOT\n";i+="=".repeat(60)+"\n\n"+`✅ Status Geral: ${e.valid?"V\xc1LIDA":"INV\xc1LIDA"}
`+`🌍 Ambiente: ${t.NODE_ENV}
`+`📱 Aplica\xe7\xe3o: ${t.APP_NAME} v${t.APP_VERSION}
`+`🔗 URL: ${t.APP_URL}

`+"\uD83D\uDCCB STATUS DOS SERVI\xc7OS:\n"+"-".repeat(30)+"\n";let r=this.getAuthConfig();i+=`🔐 Autentica\xe7\xe3o: ${r.status.toUpperCase()}
`;let s=this.getAIConfig();i+=`🤖 Intelig\xeancia Artificial: ${s.status.toUpperCase()}
`;let a=this.getDatabaseConfig();i+=`🗄️  Banco de Dados: ${a.status.toUpperCase()}
`;let n=this.getStripeConfig();i+=`💳 Stripe: ${n.status.toUpperCase()}
`;let o=this.getMCPConfig();return i+=`🔌 Vercel MCP: ${o.vercel?.status?.toUpperCase()||"DISABLED"}
🔌 Linear MCP: ${o.linear?.status?.toUpperCase()||"DISABLED"}
🔌 GitHub MCP: ${o.github?.status?.toUpperCase()||"DISABLED"}

`,e.errors.length>0&&(i+="❌ ERROS:\n",e.errors.forEach(e=>i+=`  • ${e}
`),i+="\n"),e.warnings.length>0&&(i+="⚠️  AVISOS:\n",e.warnings.forEach(e=>i+=`  • ${e}
`),i+="\n"),e.conflicts.length>0&&(i+="\uD83D\uDD04 CONFLITOS RESOLVIDOS:\n",e.conflicts.forEach(e=>i+=`  • ${e}
`),i+="\n"),i}}let u=d.getInstance(),E={NODE_ENV:u.getConfig().NODE_ENV,IS_DEVELOPMENT:"development"===u.getConfig().NODE_ENV,IS_PRODUCTION:"production"===u.getConfig().NODE_ENV,IS_TEST:"test"===u.getConfig().NODE_ENV,IS_SERVER:!0,APP:{NAME:u.getConfig().APP_NAME,VERSION:u.getConfig().APP_VERSION,URL:u.getConfig().APP_URL},NEXTAUTH_SECRET:u.getConfig().AUTH_NEXTAUTH_SECRET,NEXTAUTH_URL:u.getConfig().AUTH_NEXTAUTH_URL,API_KEYS:{GOOGLE_CLIENT_ID:u.getConfig().AUTH_GOOGLE_CLIENT_ID||"",GOOGLE_CLIENT_SECRET:u.getConfig().AUTH_GOOGLE_CLIENT_SECRET||"",GITHUB_CLIENT_ID:u.getConfig().AUTH_GITHUB_CLIENT_ID||"",GITHUB_CLIENT_SECRET:u.getConfig().AUTH_GITHUB_CLIENT_SECRET||""},DATABASE_URL:u.getConfig().DB_DATABASE_URL,VERTEX_AI:{ENABLED:u.getAIConfig().enabled,PROJECT_ID:u.getConfig().AI_VERTEX_PROJECT_ID||"",LOCATION:u.getConfig().AI_VERTEX_LOCATION,MODEL_NAME:u.getConfig().AI_VERTEX_MODEL,CREDENTIALS_PATH:process.env.VERTEX_AI_CREDENTIALS_PATH||process.env.GOOGLE_APPLICATION_CREDENTIALS},TIMEOUTS:{API_CALL:3e4,HEALTH_CHECK:{DATABASE:5e3,AI_SERVICE:1e4,EXTERNAL_DEPS:15e3}},CACHE:{DEFAULT_TTL:300,EXCEL_CACHE_SIZE:50,EXCEL_CACHE_TTL:1800,AI_CACHE_SIZE:200,AI_CACHE_TTL:86400},LIMITS:{API_RATE_LIMIT:"production"===u.getConfig().NODE_ENV?60:120},FEATURES:{USE_MOCK_AI:"mock"===u.getAIConfig().status,SKIP_AUTH_PROVIDERS:u.getConfig().AUTH_SKIP_PROVIDERS,TELEMETRY_SAMPLE_RATE:.1,ENABLE_REALTIME_COLLABORATION:!0,ENABLE_DESKTOP_INTEGRATION:!0,ENABLE_STRIPE_INTEGRATION:u.getStripeConfig().enabled},VERCEL_API_TOKEN:u.getConfig().MCP_VERCEL_TOKEN,VERCEL_PROJECT_ID:u.getConfig().MCP_VERCEL_PROJECT_ID,VERCEL_TEAM_ID:u.getConfig().MCP_VERCEL_TEAM_ID,LINEAR_API_KEY:u.getConfig().MCP_LINEAR_API_KEY,GITHUB_TOKEN:u.getConfig().MCP_GITHUB_TOKEN,GITHUB_OWNER:process.env.GITHUB_OWNER||process.env.MCP_GITHUB_OWNER,GITHUB_REPO:process.env.GITHUB_REPO||process.env.MCP_GITHUB_REPO||"excel-copilot",SUPABASE_SERVICE_ROLE_KEY:u.getConfig().SUPABASE_SERVICE_ROLE_KEY,SUPABASE_URL:u.getConfig().SUPABASE_URL,validate:()=>u.getValidationResult()}},45365:(e,t,i)=>{"use strict";i.d(t,{Ph:()=>o,d:()=>s,q:()=>a,zn:()=>r});let r=.5,s=[.23,.1,.36,1],a={hover:{y:-10,scale:1.02,boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},tap:{scale:.98}},n={fadeIn:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.5,ease:s}},slideIn:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.5,ease:s}},list:{initial:{},animate:{transition:{staggerChildren:.1}}},listItem:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0},transition:{duration:.5,ease:s}},card:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0},transition:{duration:.5,ease:s}},page:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3,ease:s}}};function o(e){return n[e]}},50337:(e,t,i)=>{"use strict";i.d(t,{OK:()=>a,z6:()=>s});let r={padding:{xs:"p-1",sm:"p-2",md:"p-4",lg:"p-6",xl:"p-8",card:"p-6",section:"py-16 px-6",inputField:"px-4 py-2"}},s={radius:{sm:"rounded",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl",full:"rounded-full"},width:{none:"border-0",thin:"border",medium:"border-2",thick:"border-4"},card:"border rounded-lg",input:"border rounded-md",button:"rounded-md"},a={transition:{fast:"transition-all duration-150 ease-in-out",medium:"transition-all duration-300 ease-in-out",slow:"transition-all duration-500 ease-in-out"},hover:{scale:"hover:scale-105",brightness:"hover:brightness-110",opacity:"hover:opacity-90"},spin:"animate-spin",pulse:"animate-pulse",loading:"animate-pulse",shake:"animate-[shake_0.82s_cubic-bezier(.36,.07,.19,.97)_both]"},n={intent:{primary:{base:"bg-primary text-primary-foreground",hover:"hover:bg-primary/90",light:"bg-primary/10 text-primary"},secondary:{base:"bg-secondary text-secondary-foreground",hover:"hover:bg-secondary/90",light:"bg-secondary/10 text-secondary"},accent:{base:"bg-accent text-accent-foreground",hover:"hover:bg-accent/90",light:"bg-accent/20 text-accent-foreground"},destructive:{base:"bg-destructive text-destructive-foreground",hover:"hover:bg-destructive/90",light:"bg-destructive/10 text-destructive"},success:{base:"bg-green-600 text-white dark:bg-green-500",hover:"hover:bg-green-700 dark:hover:bg-green-600",light:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"},warning:{base:"bg-yellow-500 text-white dark:bg-yellow-600",hover:"hover:bg-yellow-600 dark:hover:bg-yellow-700",light:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"}},focus:"focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"};s.button,a.transition.fast,n.intent.primary.base,n.intent.primary.hover,n.intent.secondary.base,n.intent.secondary.hover,n.intent.destructive.base,n.intent.destructive.hover,s.input,r.padding.inputField,n.focus,n.intent.destructive.light,s.radius.md,r.padding.sm,n.intent.success.light,s.radius.md,r.padding.sm,n.intent.warning.light,s.radius.md,r.padding.sm,n.intent.primary.light,s.radius.md,r.padding.sm,r.padding.sm,s.radius.md,a.transition.fast,r.padding.sm,s.radius.md},71701:(e,t,i)=>{"use strict";i.d(t,{xi:()=>l});var r=i(4679),s=i(51641);process.env.AUTH_GOOGLE_CLIENT_ID,process.env.AUTH_GOOGLE_CLIENT_SECRET,process.env.AUTH_GITHUB_CLIENT_ID,process.env.AUTH_GITHUB_CLIENT_SECRET,process.env.AUTH_NEXTAUTH_SECRET,process.env.AUTH_NEXTAUTH_URL,process.env.DB_DATABASE_URL,process.env.AI_VERTEX_PROJECT_ID,process.env.AI_VERTEX_LOCATION;let a={DATABASE_URL:{required:!0,validate:e=>!!e&&["mysql://","postgresql://","postgres://"].some(t=>e.startsWith(t)),errorMessage:"\uD83D\uDD34 DATABASE_URL deve come\xe7ar com mysql://, postgresql:// ou postgres:// e conter credenciais v\xe1lidas"},NEXTAUTH_SECRET:{required:!0,validate:e=>!!e&&!(e.length<32)&&!["your-secret-here","change-me","secret","nextauth-secret"].includes(e.toLowerCase()),errorMessage:"\uD83D\uDD34 NEXTAUTH_SECRET deve ter pelo menos 32 caracteres e n\xe3o pode ser um valor padr\xe3o inseguro"},NEXTAUTH_URL:{required:!0,validate:e=>{if(!e)return!1;try{let t=new URL(e);if("localhost"===t.hostname)return!1;return"http:"===t.protocol||"https:"===t.protocol}catch{return!1}},errorMessage:"\uD83D\uDD34 NEXTAUTH_URL deve ser uma URL v\xe1lida (https:// em produ\xe7\xe3o, n\xe3o localhost)"},GOOGLE_CLIENT_ID:{required:!1,production:!0,validate:e=>!!e&&e.endsWith(".apps.googleusercontent.com"),errorMessage:"\uD83D\uDD34 GOOGLE_CLIENT_ID deve terminar com .apps.googleusercontent.com"},GOOGLE_CLIENT_SECRET:{required:!1,production:!0,validate:e=>!!e&&e.startsWith("GOCSPX-")&&e.length>20,errorMessage:"\uD83D\uDD34 GOOGLE_CLIENT_SECRET deve come\xe7ar com GOCSPX- e ter mais de 20 caracteres"},GITHUB_CLIENT_ID:{required:!1,production:!0,validate:e=>!!e&&/^[a-f0-9]{20}$/.test(e),errorMessage:"\uD83D\uDD34 GITHUB_CLIENT_ID deve ser um hash hexadecimal de 20 caracteres"},GITHUB_CLIENT_SECRET:{required:!1,production:!0,validate:e=>!!e&&/^[a-f0-9]{40}$/.test(e),errorMessage:"\uD83D\uDD34 GITHUB_CLIENT_SECRET deve ser um hash hexadecimal de 40 caracteres"},VERTEX_AI_PROJECT_ID:{required:!1,validate:e=>!e||/^[a-z][-a-z0-9]{4,28}[a-z0-9]$/.test(e),errorMessage:"\uD83D\uDD34 VERTEX_AI_PROJECT_ID deve seguir o formato do Google Cloud (6-30 chars, lowercase, h\xedfens)"},VERTEX_AI_LOCATION:{required:!1,validate:e=>!e||/^[a-z]+-[a-z]+\d+$/.test(e),errorMessage:"\uD83D\uDD34 VERTEX_AI_LOCATION deve ser uma regi\xe3o v\xe1lida do Google Cloud (ex: us-central1)"},STRIPE_SECRET_KEY:{required:!1,production:!0,validate:e=>!!e&&!!("local"===process.env.BUILD_MODE||"1"!==process.env.VERCEL||e.startsWith("sk_live_"))&&e.startsWith("sk_")&&e.length>20,errorMessage:"\uD83D\uDD34 STRIPE_SECRET_KEY deve come\xe7ar com sk_live_ em produ\xe7\xe3o e ter mais de 20 caracteres"},NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:{required:!1,production:!0,validate:e=>!!e&&!!("local"===process.env.BUILD_MODE||"1"!==process.env.VERCEL||e.startsWith("pk_live_"))&&e.startsWith("pk_")&&e.length>20,errorMessage:"\uD83D\uDD34 NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY deve come\xe7ar com pk_live_ em produ\xe7\xe3o"},NEXT_PUBLIC_SUPABASE_URL:{required:!1,production:!0,validate:e=>{if(!e)return!1;try{let t=new URL(e);return t.hostname.endsWith(".supabase.co")&&"https:"===t.protocol}catch{return!1}},errorMessage:"\uD83D\uDD34 NEXT_PUBLIC_SUPABASE_URL deve ser uma URL v\xe1lida do Supabase (https://*.supabase.co)"},NEXT_PUBLIC_SUPABASE_ANON_KEY:{required:!1,production:!0,validate:e=>!!e&&e.startsWith("eyJ")&&e.length>100,errorMessage:"\uD83D\uDD34 NEXT_PUBLIC_SUPABASE_ANON_KEY deve ser um JWT v\xe1lido (come\xe7a com eyJ)"},UPSTASH_REDIS_REST_URL:{required:!1,validate:e=>!e||e.startsWith("https://")&&e.includes(".upstash.io"),errorMessage:"\uD83D\uDD34 UPSTASH_REDIS_REST_URL deve ser uma URL v\xe1lida do Upstash"},UPSTASH_REDIS_REST_TOKEN:{required:!1,validate:e=>!e||e.length>20&&/^[A-Za-z0-9]+$/.test(e),errorMessage:"\uD83D\uDD34 UPSTASH_REDIS_REST_TOKEN deve ser um token alfanum\xe9rico v\xe1lido"}},n={BASIC:{name:"\uD83C\uDFD7️ Configura\xe7\xf5es B\xe1sicas",vars:["NODE_ENV","APP_NAME","APP_VERSION","APP_URL"],required:!0},AUTH:{name:"\uD83D\uDD10 Autentica\xe7\xe3o (NextAuth)",vars:["AUTH_NEXTAUTH_SECRET","AUTH_NEXTAUTH_URL","AUTH_GOOGLE_CLIENT_ID","AUTH_GOOGLE_CLIENT_SECRET","AUTH_GITHUB_CLIENT_ID","AUTH_GITHUB_CLIENT_SECRET"],required:!0},DATABASE:{name:"\uD83D\uDDC4️ Banco de Dados",vars:["DB_DATABASE_URL","DB_DIRECT_URL","SUPABASE_URL","SUPABASE_ANON_KEY","SUPABASE_SERVICE_ROLE_KEY"],required:!0},PAYMENTS:{name:"\uD83D\uDCB3 Pagamentos (Stripe)",vars:["STRIPE_SECRET_KEY","NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY","STRIPE_WEBHOOK_SECRET"],required:!0},AI:{name:"\uD83E\uDD16 Intelig\xeancia Artificial",vars:["AI_ENABLED","AI_USE_MOCK","AI_VERTEX_PROJECT_ID","AI_VERTEX_LOCATION","AI_VERTEX_MODEL"],required:!1},CACHE:{name:"⚡ Cache (Redis)",vars:["UPSTASH_REDIS_REST_URL","UPSTASH_REDIS_REST_TOKEN"],required:!1},MCP_VERCEL:{name:"\uD83D\uDE80 MCP Vercel",vars:["MCP_VERCEL_TOKEN","MCP_VERCEL_PROJECT_ID","MCP_VERCEL_TEAM_ID"],required:!1},MCP_LINEAR:{name:"\uD83D\uDCCB MCP Linear",vars:["MCP_LINEAR_API_KEY"],required:!1},MCP_GITHUB:{name:"\uD83D\uDC19 MCP GitHub",vars:["MCP_GITHUB_TOKEN"],required:!1},MONITORING:{name:"\uD83D\uDCCA Monitoramento (Sentry)",vars:["SENTRY_DSN","SENTRY_ORG","SENTRY_PROJECT"],required:!1}};function o(e){let t={name:e.name,configured:0,total:e.vars.length,missing:[],present:[],isComplete:!1,isRequired:e.required};return e.vars.forEach(e=>{let i=process.env[e];i&&""!==i.trim()?(t.configured++,t.present.push(e)):t.missing.push(e)}),t.isComplete=t.configured===t.total,t}function l(){try{if(function(){let e="development"===r.Vi.NODE_ENV,t="production"===r.Vi.NODE_ENV,i=Object.values(n).map(o);i.forEach((t,i)=>{let r=Math.round(t.configured/t.total*100);t.isComplete||t.isRequired,t.isComplete||t.isRequired,e&&t.missing.length,t.name.includes("Autentica\xe7\xe3o")&&t.present.length>0&&(t.present.some(e=>e.includes("GOOGLE")),t.present.some(e=>e.includes("GITHUB"))),t.name.includes("Intelig\xeancia Artificial")&&t.present.length>0&&(process.env.AI_ENABLED,process.env.AI_USE_MOCK,process.env.AI_VERTEX_MODEL),t.name.includes("MCP")&&t.present.length>0&&t.name.replace(/🚀 MCP |📋 MCP |🐙 MCP /g,""),"█".repeat(Math.floor(r/5)),"░".repeat(20-Math.floor(r/5))});let a=i.filter(e=>e.isRequired),l=a.filter(e=>e.isComplete),c=i.filter(e=>!e.isRequired).filter(e=>e.isComplete);if(l.length===a.length)s.logger.info("Variaveis de ambiente validadas com sucesso"),c.length>0&&s.logger.info(`Bonus: ${c.length} integracoes opcionais ativas!`);else{let e=a.filter(e=>!e.isComplete).map(e=>e.name.replace(/🔐|🗄️|💳|🤖|⚡|🚀|📋|🐙|📊|🏗️/g,"").trim());t?s.logger.error(`❌ Configura\xe7\xe3o incompleta para produ\xe7\xe3o: ${e.join(", ")}`):s.logger.warn(`⚠️ Algumas configura\xe7\xf5es obrigat\xf3rias ausentes (modo desenvolvimento): ${e.join(", ")}`)}}(),!function(){let e=[],t="true"===process.env.DEV_FORCE_PRODUCTION||!0,i="1"===process.env.VERCEL,n=t&&!i&&!process.env.RAILWAY&&!process.env.HEROKU&&!process.env.NETLIFY;if(i)return s.logger.warn("Ambiente Vercel detectado. Ignorando valida\xe7\xf5es cr\xedticas para permitir build."),!0;let o=process.env.GOOGLE_APPLICATION_CREDENTIALS||process.env.VERTEX_AI_CREDENTIALS_PATH,l=process.env;for(let[i,s]of Object.entries(a))if(!(s.production&&!t||i.startsWith("VERTEX_AI_")&&r.Vi.FEATURES.USE_MOCK_AI)&&("AI_VERTEX_PROJECT_ID"!==i||!o)){if(s.required&&(!l[i]||""===l[i])){e.push(`Vari\xe1vel de ambiente ${i} \xe9 obrigat\xf3ria mas n\xe3o est\xe1 definida`);continue}l[i]&&s.validate&&!s.validate(l[i])&&e.push(s.errorMessage||`Vari\xe1vel de ambiente ${i} tem valor inv\xe1lido`)}if(!r.Vi.VERTEX_AI.ENABLED||r.Vi.FEATURES.USE_MOCK_AI||o||process.env.AI_VERTEX_PROJECT_ID&&process.env.AI_VERTEX_LOCATION||e.push("VERTEX_AI_PROJECT_ID e VERTEX_AI_LOCATION s\xe3o obrigat\xf3rios quando VERTEX_AI_ENABLED=true e USE_MOCK_AI=false"),t&&!n&&(r.Vi.FEATURES.USE_MOCK_AI&&e.push("USE_MOCK_AI deve ser false em ambiente de produ\xe7\xe3o"),process.env.AUTH_NEXTAUTH_URL&&process.env.AUTH_NEXTAUTH_URL.includes("localhost")&&e.push("NEXTAUTH_URL n\xe3o deve apontar para localhost em ambiente de produ\xe7\xe3o")),t&&n&&(process.env.AUTH_NEXTAUTH_URL&&process.env.AUTH_NEXTAUTH_URL.includes("localhost")&&s.logger.warn("⚠️ Build local detectado com NEXTAUTH_URL=localhost - configure para produ\xe7\xe3o real"),r.Vi.FEATURES.USE_MOCK_AI&&s.logger.warn("⚠️ Build local detectado com USE_MOCK_AI=true - configure para produ\xe7\xe3o real")),e.length>0){if(s.logger.error("Problemas com vari\xe1veis de ambiente cr\xedticas:"),e.forEach(e=>s.logger.error(`- ${e}`)),s.logger.error("Verifique suas configura\xe7\xf5es e tente novamente."),t&&!i&&!n)throw Error("Vari\xe1veis de ambiente inv\xe1lidas em produ\xe7\xe3o");return!!n&&(s.logger.warn("⚠️ Build local com configura\xe7\xf5es de desenvolvimento - OK para testes"),!0)}return!0}()&&r.Vi.IS_PRODUCTION)throw s.logger.error("\uD83D\uDD34 Falha cr\xedtica na valida\xe7\xe3o de vari\xe1veis de ambiente"),Error("Configura\xe7\xe3o de ambiente inv\xe1lida para produ\xe7\xe3o")}catch(e){throw s.logger.error("\uD83D\uDD34 Falha cr\xedtica na valida\xe7\xe3o de vari\xe1veis de ambiente"),e}}},51641:(e,t,i)=>{"use strict";let r;i.r(t),i.d(t,{default:()=>_,devLogger:()=>f,extractErrorData:()=>o,initLogger:()=>h,logger:()=>u,safeConsoleError:()=>E,safeConsoleInfo:()=>p,safeConsoleLog:()=>c,safeConsoleWarn:()=>m,serviceLogger:()=>g,toError:()=>n,toMetadata:()=>l});var s=i(67337),a=i.n(s);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function o(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let t=["name","message","stack"],i={};return Object.keys(e).forEach(r=>{t.includes(r)||(i[r]=e[r])}),{normalizedError:e,extractedMetadata:i}}return"object"==typeof e&&null!==e?{normalizedError:n(e),extractedMetadata:e}:{normalizedError:n(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}function c(...e){}let d={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=d.production;r=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),r=a()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:i}=o(t);r.warn(i,e)}else r.warn(l(t)||{},e)},error:(e,t,i)=>{let{normalizedError:s,extractedMetadata:a}=o(t),n={...i||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};r.error(n,e)},fatal:(e,t,i)=>{let{normalizedError:s,extractedMetadata:a}=o(t),n={...i||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};r.fatal(n,e)},createChild:e=>{let t=r.child(e);return{trace:(e,i)=>{t.trace(i||{},e)},debug:(e,i)=>{t.debug(i||{},e)},info:(e,i)=>{t.info(i||{},e)},warn:(e,i)=>{if(i instanceof Error||"object"==typeof i&&null!==i){let{extractedMetadata:r}=o(i);t.warn(r,e)}else t.warn(l(i)||{},e)},error:(e,i,r)=>{let{normalizedError:s,extractedMetadata:a}=o(i),n={...r||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.error(n,e)},fatal:(e,i,r)=>{let{normalizedError:s,extractedMetadata:a}=o(i),n={...r||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.fatal(n,e)}}},child:function(e){return this.createChild(e)}},E=()=>{},m=()=>{},p=()=>{},h={info:(e,...t)=>{},warn:(e,...t)=>{},error:(e,...t)=>{console.error(`[INIT] ${e}`,...t)},debug:(e,...t)=>{}},g={environment:()=>{},rateLimiters:()=>{},telemetry:()=>{},aiService:()=>{},logger:()=>{}},f={startup:(e,t)=>{},config:(e,t,i)=>{},section:e=>{},progress:(e,t,i)=>{},error:(e,t)=>{},success:(e,t)=>{}},_=u},51223:(e,t,i)=>{"use strict";i.d(t,{WH:()=>n,cn:()=>a,x0:()=>o});var r=i(41135),s=i(31009);function a(...e){return(0,s.m6)((0,r.W)(e))}function n(e){let t=0;for(let i=0;i<e.length;i++)t=26*t+e.charCodeAt(i)-64;return t-1}function o(){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="";for(let i=0;i<10;i++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}i(71701)},37178:(e,t,i)=>{"use strict";i.d(t,{H:()=>s,KE:()=>a});var r=i(51641);function s(e,t,i){let s=function(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{let t=JSON.stringify(e);return Error(t)}catch{return Error("Unknown error")}}}(t);r.logger.error(e,s)}function a(e,t,i){let s=null==t?void 0:"object"==typeof t?t:{value:t},a=i?{...i,...s}:s;r.logger.warn(e,a)}},65675:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>P,metadata:()=>N,viewport:()=>S});var r=i(19510),s=i(13460),a=i.n(s),n=i(57371),o=i(19552),l=i(71159),c=i(68570);let d=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\client-scripts.tsx`),{__esModule:u,$$typeof:E}=d;d.default;let m=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\client-scripts.tsx#ClientScripts`),p=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\ClientLayoutWrapper.tsx`),{__esModule:h,$$typeof:g}=p;p.default;let f=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\ClientLayoutWrapper.tsx#default`);var _=i(40644);let T=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\providers.tsx`),{__esModule:v,$$typeof:x}=T;T.default,(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\providers.tsx#AppProviders`);let I=(0,c.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\providers.tsx#Providers`);i(5023);let A=(0,l.lazy)(()=>i.e(1110).then(i.bind(i,1110)).then(e=>({default:e.NavBar}))),C=(0,l.lazy)(()=>i.e(850).then(i.bind(i,90850)).then(e=>({default:e.UserNav}))),R=a().className,N={title:{default:"Excel Copilot - Automatize planilhas com IA e linguagem natural",template:"%s | Excel Copilot - Automa\xe7\xe3o inteligente de planilhas"},description:"Excel Copilot transforma comandos em linguagem natural em planilhas poderosas. Crie tabelas, gr\xe1ficos e an\xe1lises complexas sem f\xf3rmulas ou c\xf3digos complicados.",keywords:["Excel Copilot","automa\xe7\xe3o de planilhas","IA para Excel","planilhas inteligentes","Excel com IA","an\xe1lise de dados simplificada","gr\xe1ficos autom\xe1ticos","linguagem natural para Excel","transformar texto em planilha","Microsoft Excel assistente","an\xe1lise de dados sem c\xf3digo","ferramenta produtividade planilhas"],applicationName:"Excel Copilot",authors:[{name:"Excel Copilot Team",url:"https://excel-copilot.vercel.app"}],creator:"Excel Copilot Team",publisher:"Excel Copilot",formatDetection:{telephone:!1,email:!1,address:!1},alternates:{canonical:"/",languages:{"pt-BR":"/","en-US":"/en"}},openGraph:{title:"Excel Copilot - Automatize suas planilhas com IA e linguagem natural",description:"Transforme comandos em texto simples em planilhas profissionais com gr\xe1ficos, an\xe1lises e c\xe1lculos autom\xe1ticos. Economize tempo e aumente sua produtividade.",url:"https://excel-copilot.app",siteName:"Excel Copilot",locale:"pt_BR",type:"website",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Excel Copilot - Automatize suas planilhas com IA"}]},twitter:{card:"summary_large_image",title:"Excel Copilot - Automatize suas planilhas com IA",description:"Transforme comandos em texto simples em planilhas profissionais. Integr\xe1vel com Microsoft Excel e Google Sheets.",creator:"@excelcopilot",images:{url:"/twitter-image.jpg",alt:"Excel Copilot - Interface da aplica\xe7\xe3o mostrando comandos sendo transformados em planilhas"}},icons:{icon:[{url:"/favicon.ico",sizes:"any"},{url:"/icon.png",sizes:"32x32"},{url:"/icon-192.png",sizes:"192x192"},{url:"/icon-512.png",sizes:"512x512"}],shortcut:"/favicon.ico",apple:[{url:"/apple-touch-icon.png",sizes:"180x180"}],other:[{rel:"mask-icon",url:"/safari-pinned-tab.svg",color:"#2563eb"}]},metadataBase:new URL(process.env.NEXT_PUBLIC_APP_URL||"https://excel-copilot.vercel.app"),manifest:"/manifest.json",category:"productivity",other:{"web-vitals-spec":"https://wicg.github.io/web-vitals-spec/","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","format-detection":"telephone=no","mobile-web-app-capable":"yes","msapplication-TileColor":"#2563eb","msapplication-config":"/browserconfig.xml"},robots:{index:!0,follow:!0,"max-image-preview":"large","max-video-preview":-1,"max-snippet":-1,nocache:!1,googleBot:{index:!0,follow:!0,"max-image-preview":"large","max-video-preview":-1,"max-snippet":-1,noimageindex:!1}}},S={width:"device-width",initialScale:1,maximumScale:5,userScalable:!0,viewportFit:"cover",themeColor:[{media:"(prefers-color-scheme: light)",color:"#ffffff"},{media:"(prefers-color-scheme: dark)",color:"#0f172a"}]},b=(0,l.lazy)(()=>Promise.resolve({default:()=>r.jsx("footer",{className:"py-6 md:py-8 border-t",children:(0,r.jsxs)("div",{className:"container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row",children:[(0,r.jsxs)("p",{className:"text-center text-sm leading-loose text-muted-foreground md:text-left",children:["\xa9 ",new Date().getFullYear()," Excel Copilot. Todos os direitos reservados."]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[r.jsx(n.default,{href:"/terms",className:"hover:underline hover:text-foreground transition-colors",children:"Termos"}),r.jsx(n.default,{href:"/privacy",className:"hover:underline hover:text-foreground transition-colors",children:"Privacidade"})]})]})})})),D=()=>r.jsx("div",{className:"h-10 w-36 bg-muted animate-pulse rounded"}),L=()=>r.jsx("div",{className:"flex items-center space-x-2",children:r.jsx("div",{className:"h-8 w-8 bg-muted animate-pulse rounded-full"})}),U=()=>r.jsx("div",{className:"h-16 w-full bg-muted/30 animate-pulse"});function P({children:e}){return(0,r.jsxs)("html",{lang:"pt-BR",suppressHydrationWarning:!0,children:[(0,r.jsxs)("head",{children:[r.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0"}),r.jsx("meta",{name:"theme-color",content:"#2563eb"}),r.jsx("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                document.documentElement.classList.add('js');
              })();
            `}}),r.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"Excel Copilot",applicationCategory:"BusinessApplication",operatingSystem:"Web, Windows",offers:{"@type":"Offer",price:"0",priceCurrency:"BRL"},description:"Excel Copilot transforma comandos em linguagem natural em planilhas poderosas.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"127",reviewCount:"68"}})}})]}),(0,r.jsxs)("body",{className:(0,_.cn)("min-h-screen bg-background antialiased",R),children:[r.jsx(m,{}),r.jsx(o.f,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:r.jsx(I,{children:r.jsx(f,{interClassName:R,children:(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx("header",{className:"sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 w-full shadow-sm",children:(0,r.jsxs)("div",{className:"container flex h-14 items-center justify-between",children:[r.jsx("div",{className:"flex items-center",children:r.jsx(l.Suspense,{fallback:r.jsx(D,{}),children:r.jsx(A,{})})}),r.jsx("div",{className:"flex items-center space-x-3",children:r.jsx(l.Suspense,{fallback:r.jsx(L,{}),children:r.jsx(C,{})})})]})}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(l.Suspense,{fallback:r.jsx(U,{}),children:r.jsx(b,{})})]})})})})]})]})}},12523:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n});var r=i(19510),s=i(57371),a=i(27039);function n(){return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center space-y-6 max-w-md mx-auto px-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h1",{className:"text-6xl font-bold text-primary",children:"404"}),r.jsx("h2",{className:"text-2xl font-semibold text-foreground",children:"P\xe1gina n\xe3o encontrada"}),r.jsx("p",{className:"text-muted-foreground",children:"A p\xe1gina que voc\xea est\xe1 procurando n\xe3o existe ou foi movida."})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(a.z,{asChild:!0,className:"w-full",children:r.jsx(s.default,{href:"/",children:"Voltar ao in\xedcio"})}),r.jsx(a.z,{variant:"outline",asChild:!0,className:"w-full",children:r.jsx(s.default,{href:"/dashboard",children:"Ir para o Dashboard"})})]}),r.jsx("div",{className:"text-sm text-muted-foreground",children:r.jsx("p",{children:"Se voc\xea acredita que isso \xe9 um erro, entre em contato com o suporte."})})]})})}},27039:(e,t,i)=>{"use strict";i.d(t,{z:()=>o});var r=i(68570);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\ui\button.tsx`),{__esModule:a,$$typeof:n}=s;s.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\ui\button.tsx#Button`);(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\ui\button.tsx#buttonVariants`)},40644:(e,t,i)=>{"use strict";i.d(t,{WH:()=>n,cn:()=>a});var r=i(55761),s=i(62386);function a(...e){return(0,s.m6)((0,r.W)(e))}function n(e){let t=0;for(let i=0;i<e.length;i++)t=26*t+e.charCodeAt(i)-64;return t-1}i(24433)},5023:()=>{}};