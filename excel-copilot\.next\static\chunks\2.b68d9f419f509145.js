"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2],{74697:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},44504:function(e,t,r){r.d(t,{Dx:function(){return $},aU:function(){return et},dk:function(){return ee},fC:function(){return Q},l_:function(){return J},x8:function(){return er},zt:function(){return G}});var n=r(2265),o=r(54887),i=r(78149),a=r(1584),s=r(38620),l=r(98324),u=r(53938),c=r(7715),d=r(31383),p=r(25171),f=r(75137),v=r(91715),w=r(1336),m=r(31725),y=r(57437),x="ToastProvider",[E,h,T]=(0,s.B)("Toast"),[g,b]=(0,l.b)("Toast",[T]),[C,P]=g(x),R=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:s}=e,[l,u]=n.useState(null),[c,d]=n.useState(0),p=n.useRef(!1),f=n.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(x,"`. Expected non-empty `string`.")),(0,y.jsx)(E.Provider,{scope:t,children:(0,y.jsx)(C,{scope:t,label:r,duration:o,swipeDirection:i,swipeThreshold:a,toastCount:c,viewport:l,onViewportChange:u,onToastAdd:n.useCallback(()=>d(e=>e+1),[]),onToastRemove:n.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:f,children:s})})};R.displayName=x;var j="ToastViewport",L=["F8"],N="toast.viewportPause",k="toast.viewportResume",D=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=L,label:i="Notifications ({hotkey})",...s}=e,l=P(j,r),c=h(r),d=n.useRef(null),f=n.useRef(null),v=n.useRef(null),w=n.useRef(null),m=(0,a.e)(t,w,l.onViewportChange),x=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),T=l.toastCount>0;n.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=w.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=d.current,t=w.current;if(T&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(N);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(k);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},i=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",i),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[T,l.isClosePausedRef]);let g=n.useCallback(e=>{let{tabbingDirection:t}=e,r=c().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[c]);return n.useEffect(()=>{let e=w.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,i;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(n=f.current)||void 0===n||n.focus();return}let s=g({tabbingDirection:a?"backwards":"forwards"}),l=s.findIndex(e=>e===r);B(s.slice(l+1))?t.preventDefault():a?null===(o=f.current)||void 0===o||o.focus():null===(i=v.current)||void 0===i||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,g]),(0,y.jsxs)(u.I0,{ref:d,role:"region","aria-label":i.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:T?void 0:"none"},children:[T&&(0,y.jsx)(F,{ref:f,onFocusFromOutsideViewport:()=>{B(g({tabbingDirection:"forwards"}))}}),(0,y.jsx)(E.Slot,{scope:r,children:(0,y.jsx)(p.WV.ol,{tabIndex:-1,...s,ref:m})}),T&&(0,y.jsx)(F,{ref:v,onFocusFromOutsideViewport:()=>{B(g({tabbingDirection:"backwards"}))}})]})});D.displayName=j;var M="ToastFocusProxy",F=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,i=P(M,r);return(0,y.jsx)(m.TX,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=i.viewport)||void 0===t?void 0:t.contains(r))||n()}})});F.displayName=M;var S="Toast",I=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:a,...s}=e,[l,u]=(0,v.T)({prop:n,defaultProp:null==o||o,onChange:a,caller:S});return(0,y.jsx)(d.z,{present:r||l,children:(0,y.jsx)(K,{open:l,...s,ref:t,onClose:()=>u(!1),onPause:(0,f.W)(e.onPause),onResume:(0,f.W)(e.onResume),onSwipeStart:(0,i.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,i.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),u(!1)})})})});I.displayName=S;var[A,W]=g(S,{onClose(){}}),K=n.forwardRef((e,t)=>{let{__scopeToast:r,type:s="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:v,onPause:w,onResume:m,onSwipeStart:x,onSwipeMove:h,onSwipeCancel:T,onSwipeEnd:g,...b}=e,C=P(S,r),[R,j]=n.useState(null),L=(0,a.e)(t,e=>j(e)),D=n.useRef(null),M=n.useRef(null),F=l||C.duration,I=n.useRef(0),W=n.useRef(F),K=n.useRef(0),{onToastAdd:_,onToastRemove:X}=C,O=(0,f.W)(()=>{var e;(null==R?void 0:R.contains(document.activeElement))&&(null===(e=C.viewport)||void 0===e||e.focus()),d()}),H=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(K.current),I.current=new Date().getTime(),K.current=window.setTimeout(O,e))},[O]);n.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{H(W.current),null==m||m()},r=()=>{let e=new Date().getTime()-I.current;W.current=W.current-e,window.clearTimeout(K.current),null==w||w()};return e.addEventListener(N,r),e.addEventListener(k,t),()=>{e.removeEventListener(N,r),e.removeEventListener(k,t)}}},[C.viewport,F,w,m,H]),n.useEffect(()=>{c&&!C.isClosePausedRef.current&&H(F)},[c,F,C.isClosePausedRef,H]),n.useEffect(()=>(_(),()=>X()),[_,X]);let U=n.useMemo(()=>R?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(R):null,[R]);return C.viewport?(0,y.jsxs)(y.Fragment,{children:[U&&(0,y.jsx)(V,{__scopeToast:r,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:U}),(0,y.jsx)(A,{scope:r,onClose:O,children:o.createPortal((0,y.jsx)(E.ItemSlot,{scope:r,children:(0,y.jsx)(u.fC,{asChild:!0,onEscapeKeyDown:(0,i.M)(v,()=>{C.isFocusedToastEscapeKeyDownRef.current||O(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,y.jsx)(p.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":C.swipeDirection,...b,ref:L,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.M)(e.onKeyDown,e=>{"Escape"!==e.key||(null==v||v(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,O()))}),onPointerDown:(0,i.M)(e.onPointerDown,e=>{0===e.button&&(D.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.M)(e.onPointerMove,e=>{if(!D.current)return;let t=e.clientX-D.current.x,r=e.clientY-D.current.y,n=!!M.current,o=["left","right"].includes(C.swipeDirection),i=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,s=o?0:i(0,r),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};n?(M.current=u,Y("toast.swipeMove",h,c,{discrete:!1})):Z(u,C.swipeDirection,l)?(M.current=u,Y("toast.swipeStart",x,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(D.current=null)}),onPointerUp:(0,i.M)(e.onPointerUp,e=>{let t=M.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),M.current=null,D.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Z(t,C.swipeDirection,C.swipeThreshold)?Y("toast.swipeEnd",g,n,{discrete:!0}):Y("toast.swipeCancel",T,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),V=e=>{let{__scopeToast:t,children:r,...o}=e,i=P(S,t),[a,s]=n.useState(!1),[l,u]=n.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,f.W)(e);(0,w.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>s(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,y.jsx)(c.h,{asChild:!0,children:(0,y.jsx)(m.TX,{...o,children:a&&(0,y.jsxs)(y.Fragment,{children:[i.label," ",r]})})})},_=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(p.WV.div,{...n,ref:t})});_.displayName="ToastTitle";var X=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(p.WV.div,{...n,ref:t})});X.displayName="ToastDescription";var O="ToastAction",H=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,y.jsx)(q,{altText:r,asChild:!0,children:(0,y.jsx)(z,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(O,"`. Expected non-empty `string`.")),null)});H.displayName=O;var U="ToastClose",z=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=W(U,r);return(0,y.jsx)(q,{asChild:!0,children:(0,y.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,i.M)(e.onClick,o.onClose)})})});z.displayName=U;var q=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,y.jsx)(p.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function Y(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,p.jH)(i,a):i.dispatchEvent(a)}var Z=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),i=n>o;return"left"===t||"right"===t?i&&n>r:!i&&o>r};function B(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var G=R,J=D,Q=I,$=_,ee=X,et=H,er=z},31725:function(e,t,r){r.d(t,{C2:function(){return a},TX:function(){return s},fC:function(){return l}});var n=r(2265),o=r(25171),i=r(57437),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),s=n.forwardRef((e,t)=>(0,i.jsx)(o.WV.span,{...e,ref:t,style:{...a,...e.style}}));s.displayName="VisuallyHidden";var l=s}}]);