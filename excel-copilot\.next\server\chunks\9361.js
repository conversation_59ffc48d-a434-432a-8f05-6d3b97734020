"use strict";exports.id=9361,exports.ids=[9361],exports.modules={38443:(e,t,r)=>{r.d(t,{C:()=>o});var a=r(10326),s=r(79360);r(17577);var n=r(51223);let i=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return a.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...r})}},29752:(e,t,r)=>{r.d(t,{Ol:()=>l,SZ:()=>m,Zb:()=>d,aY:()=>f,eW:()=>u,ll:()=>c});var a=r(10326),s=r(31722),n=r(17577),i=r(45365),o=r(51223);let d=(0,n.forwardRef)(({className:e,children:t,hoverable:r=!1,variant:n="default",noPadding:d=!1,animated:l=!1,...c},m)=>{let f=(0,o.cn)("rounded-xl border shadow-sm",{"p-6":!d,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":r&&!l,"border-border bg-card":"default"===n,"border-border/50 bg-transparent":"outline"===n,"bg-card/90 backdrop-blur-md border-border/50":"glass"===n,"bg-gradient-primary text-primary-foreground border-none":"gradient"===n},e);return l?a.jsx(s.E.div,{ref:m,className:f,...(0,i.Ph)("card"),whileHover:r?i.q.hover:void 0,whileTap:r?i.q.tap:void 0,...c,children:t}):a.jsx("div",{ref:m,className:f,...c,children:t})});d.displayName="Card";let l=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,o.cn)("mb-4 flex flex-col space-y-1.5",e),...t}));l.displayName="CardHeader";let c=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("h3",{ref:r,className:(0,o.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let m=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));m.displayName="CardDescription";let f=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,o.cn)("card-content",e),...t}));f.displayName="CardContent";let u=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,o.cn)("flex items-center pt-4 mt-auto",e),...t}));u.displayName="CardFooter"},62734:(e,t,r)=>{r.d(t,{RM:()=>d,aF:()=>l});var a=r(17577),s=r.n(a),n=r(51223);let i={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},o={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function d(e="default",t="md",r=!1,a){return(0,n.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i[e],o[t],r&&"min-h-[80px] resize-vertical",a)}function l(e,t){return t?s().createElement("div",{className:t},e):e}},41190:(e,t,r)=>{r.d(t,{I:()=>i,Z:()=>o});var a=r(10326),s=r(17577),n=r(62734);let i=s.forwardRef(({className:e,type:t,wrapperClassName:r,variant:s="default",fieldSize:i="md",inputSize:o,...d},l)=>{let c=a.jsx("input",{type:t,className:(0,n.RM)(s,o||i,!1,e),ref:l,...d});return(0,n.aF)(c,r)});i.displayName="Input";let o=i},44794:(e,t,r)=>{r.d(t,{_:()=>l});var a=r(10326),s=r(34478),n=r(79360),i=r(17577),o=r(51223);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=i.forwardRef(({className:e,...t},r)=>a.jsx(s.f,{ref:r,className:(0,o.cn)(d(),e),...t}));l.displayName=s.f.displayName},50258:(e,t,r)=>{r.d(t,{Kk:()=>u,MD:()=>f});var a=r(10326),s=r(17577),n=r.n(s);class i{constructor(){this.metrics=new Map,this.renderEvents=[],this.isEnabled=!1,this.isEnabled=!1}recordRender(e,t,r=!1,a){if(!this.isEnabled)return;let s=Date.now();this.renderEvents.push({componentName:e,timestamp:s,renderTime:t,props:a||{},isOptimized:r});let n=this.metrics.get(e);n?(n.renderCount++,n.lastRenderTime=t,n.totalRenderTime+=t,n.averageRenderTime=n.totalRenderTime/n.renderCount):this.metrics.set(e,{componentName:e,renderCount:1,lastRenderTime:t,averageRenderTime:t,totalRenderTime:t,isOptimized:r}),this.renderEvents.length>1e3&&(this.renderEvents=this.renderEvents.slice(-1e3))}getComponentMetrics(e){return this.metrics.get(e)}getAllMetrics(){return Array.from(this.metrics.values())}getRecentRenderEvents(e=100){return this.renderEvents.slice(-e)}getPerformanceComparison(){let e=this.getAllMetrics().filter(e=>e.isOptimized),t=this.getAllMetrics().filter(e=>!e.isOptimized),r=e.reduce((e,t)=>e+t.averageRenderTime,0)/e.length||0,a=t.reduce((e,t)=>e+t.averageRenderTime,0)/t.length||0,s=e.reduce((e,t)=>e+t.renderCount,0)/e.length||0,n=t.reduce((e,t)=>e+t.renderCount,0)/t.length||0;return{optimized:e,nonOptimized:t,improvement:{averageRenderTimeReduction:a>0?(a-r)/a*100:0,renderCountReduction:n>0?(n-s)/n*100:0}}}generateReport(){let e=this.getPerformanceComparison(),t=this.metrics.size,r=e.optimized.length,a=e.nonOptimized.length;return`
📊 RELAT\xd3RIO DE PERFORMANCE - COMPONENTES UI

📈 Resumo Geral:
- Total de componentes monitorados: ${t}
- Componentes otimizados: ${r}
- Componentes n\xe3o otimizados: ${a}

🚀 Melhorias de Performance:
- Redu\xe7\xe3o m\xe9dia no tempo de renderiza\xe7\xe3o: ${e.improvement.averageRenderTimeReduction.toFixed(2)}%
- Redu\xe7\xe3o m\xe9dia no n\xfamero de renderiza\xe7\xf5es: ${e.improvement.renderCountReduction.toFixed(2)}%

🔝 Top 5 Componentes Mais Renderizados:
${this.getAllMetrics().sort((e,t)=>t.renderCount-e.renderCount).slice(0,5).map((e,t)=>`${t+1}. ${e.componentName}: ${e.renderCount} renders (${e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado"})`).join("\n")}

⚡ Top 5 Componentes Mais Lentos:
${this.getAllMetrics().sort((e,t)=>t.averageRenderTime-e.averageRenderTime).slice(0,5).map((e,t)=>`${t+1}. ${e.componentName}: ${e.averageRenderTime.toFixed(2)}ms (${e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado"})`).join("\n")}
    `.trim()}clear(){this.metrics.clear(),this.renderEvents=[]}exportData(){let e=this.getPerformanceComparison();return{metrics:this.getAllMetrics(),events:this.renderEvents,summary:e}}}let o=new i;function d(e,t=!1){return n().useRef(0),{recordCustomMetric:(r,a)=>{o.recordRender(`${e}.${r}`,a,t)}}}var l=r(91664);function c(e,t){for(let r of["variant","size","disabled","children","className","animated","icon","iconPosition","asChild"])if(e[r]!==t[r])return!1;if("object"==typeof e.children&&"object"==typeof t.children)return e.children===t.children;for(let r of["onClick","onMouseEnter","onMouseLeave","onFocus","onBlur"]){let a=e[r],s=t[r];if((a||s)&&(!a||!s||a!==s))return!1}return!0}let m=(0,s.memo)(l.Button,c),f=n().forwardRef((e,t)=>(d("OptimizedButton",!0),a.jsx(m,{...e,ref:t})));f.displayName="OptimizedButton";let u=(0,s.memo)(({onAction:e,actionId:t,...r})=>{d("ActionButton",!0);let s=n().useCallback(()=>{e()},[e]);return a.jsx(l.Button,{...r,onClick:s})},(e,t)=>c(e,t)&&e.actionId===t.actionId&&e.onAction===t.onAction);u.displayName="ActionButton"},51027:(e,t,r)=>{r.d(t,{J2:()=>o,xo:()=>d,yk:()=>l});var a=r(10326),s=r(74964),n=r(17577),i=r(51223);let o=s.fC,d=s.xz,l=n.forwardRef(({className:e,align:t="center",sideOffset:r=4,...n},o)=>a.jsx(s.h_,{children:a.jsx(s.VY,{ref:o,align:t,sideOffset:r,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));l.displayName=s.VY.displayName},29280:(e,t,r)=>{r.d(t,{Bw:()=>g,Ph:()=>c,Ql:()=>x,i4:()=>f,ki:()=>m});var a=r(10326),s=r(44875),n=r(941),i=r(96633),o=r(32933),d=r(17577),l=r(51223);let c=s.fC;s.ZA;let m=s.B4,f=d.forwardRef(({className:e,children:t,...r},i)=>(0,a.jsxs)(s.xz,{ref:i,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,a.jsx(s.JO,{asChild:!0,children:a.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=s.xz.displayName;let u=d.forwardRef(({className:e,...t},r)=>a.jsx(s.u_,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(i.Z,{className:"h-4 w-4"})}));u.displayName=s.u_.displayName;let p=d.forwardRef(({className:e,...t},r)=>a.jsx(s.$G,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=s.$G.displayName;let g=d.forwardRef(({className:e,children:t,position:r="popper",...n},i)=>a.jsx(s.h_,{children:(0,a.jsxs)(s.VY,{ref:i,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[a.jsx(u,{}),a.jsx(s.l_,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(p,{})]})}));g.displayName=s.VY.displayName,d.forwardRef(({className:e,...t},r)=>a.jsx(s.__,{ref:r,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=s.__.displayName;let x=d.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(s.ck,{ref:n,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(s.wU,{children:a.jsx(o.Z,{className:"h-4 w-4"})})}),a.jsx(s.eT,{children:t})]}));x.displayName=s.ck.displayName,d.forwardRef(({className:e,...t},r)=>a.jsx(s.Z0,{ref:r,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=s.Z0.displayName},50384:(e,t,r)=>{r.d(t,{SP:()=>l,dr:()=>d,mQ:()=>o,nU:()=>c});var a=r(10326),s=r(28407),n=r(17577),i=r(51223);let o=s.fC,d=n.forwardRef(({className:e,...t},r)=>a.jsx(s.aV,{ref:r,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=s.aV.displayName;let l=n.forwardRef(({className:e,...t},r)=>a.jsx(s.xz,{ref:r,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));l.displayName=s.xz.displayName;let c=n.forwardRef(({className:e,...t},r)=>a.jsx(s.VY,{ref:r,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=s.VY.displayName}};