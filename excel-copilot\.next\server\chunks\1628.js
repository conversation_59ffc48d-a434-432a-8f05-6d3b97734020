"use strict";exports.id=1628,exports.ids=[1628],exports.modules={53797:(e,r)=>{r.Z=function(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},77210:(e,r)=>{r.Z=function(e){return{id:"github",name:"GitHub",type:"oauth",authorization:{url:"https://github.com/login/oauth/authorize",params:{scope:"read:user user:email"}},token:"https://github.com/login/oauth/access_token",userinfo:{url:"https://api.github.com/user",async request({client:e,tokens:r}){let t=await e.userinfo(r.access_token);if(!t.email){let e=await fetch("https://api.github.com/user/emails",{headers:{Authorization:`token ${r.access_token}`}});if(e.ok){var a;let r=await e.json();t.email=(null!==(a=r.find(e=>e.primary))&&void 0!==a?a:r[0]).email}}return t}},profile(e){var r;return{id:e.id.toString(),name:null!==(r=e.name)&&void 0!==r?r:e.login,email:e.email,image:e.avatar_url}},style:{logo:"/github.svg",bg:"#24292f",text:"#fff"},options:e}}},77234:(e,r)=>{r.Z=function(e){return{id:"google",name:"Google",type:"oauth",wellKnown:"https://accounts.google.com/.well-known/openid-configuration",authorization:{params:{scope:"openid email profile"}},idToken:!0,checks:["pkce","state"],profile:e=>({id:e.sub,name:e.name,email:e.email,image:e.picture}),style:{logo:"/google.svg",bg:"#fff",text:"#000"},options:e}}},43895:(e,r,t)=>{let a;t.d(r,{kg:()=>c});var i=t(99557),n=t.n(i);function o(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(a=>{r.includes(a)||(t[a]=e[a])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:o(e),extractedMetadata:e}:{normalizedError:o(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:n().stdSerializers.err,error:n().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:n().stdSerializers.err,error:n().stdSerializers.err}}};try{let e=u.production;a=n()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=n()({level:"info",formatters:{level:e=>({level:e})}})}let c={trace:(e,r)=>{a.trace(r||{},e)},debug:(e,r)=>{a.debug(r||{},e)},info:(e,r)=>{a.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=s(r);a.warn(t,e)}else a.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:i,extractedMetadata:n}=s(r),o={...t||{},...n,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};a.error(o,e)},fatal:(e,r,t)=>{let{normalizedError:i,extractedMetadata:n}=s(r),o={...t||{},...n,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};a.fatal(o,e)},createChild:e=>{let r=a.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:a}=s(t);r.warn(a,e)}else r.warn(l(t)||{},e)},error:(e,t,a)=>{let{normalizedError:i,extractedMetadata:n}=s(t),o={...a||{},...n,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};r.error(o,e)},fatal:(e,t,a)=>{let{normalizedError:i,extractedMetadata:n}=s(t),o={...a||{},...n,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};r.fatal(o,e)}}},child:function(e){return this.createChild(e)}}},81628:(e,r,t)=>{t.d(r,{L:()=>d});var a=t(4530);function i(e){let r={};for(let t in e)void 0!==e[t]&&(r[t]=e[t]);return{data:r}}var n=t(53797),o=t(77210),s=t(77234),l=t(43895),u=t(52972),c=t(63841);let d={adapter:function(e){return{createUser:({id:r,...t})=>e.user.create(i(t)),getUser:r=>e.user.findUnique({where:{id:r}}),getUserByEmail:r=>e.user.findUnique({where:{email:r}}),async getUserByAccount(r){let t=await e.account.findUnique({where:{provider_providerAccountId:r},include:{user:!0}});return t?.user??null},updateUser:({id:r,...t})=>e.user.update({where:{id:r},...i(t)}),deleteUser:r=>e.user.delete({where:{id:r}}),linkAccount:r=>e.account.create({data:r}),unlinkAccount:r=>e.account.delete({where:{provider_providerAccountId:r}}),async getSessionAndUser(r){let t=await e.session.findUnique({where:{sessionToken:r},include:{user:!0}});if(!t)return null;let{user:a,...i}=t;return{user:a,session:i}},createSession:r=>e.session.create(i(r)),updateSession:r=>e.session.update({where:{sessionToken:r.sessionToken},...i(r)}),deleteSession:r=>e.session.delete({where:{sessionToken:r}}),async createVerificationToken(r){let t=await e.verificationToken.create(i(r));return"id"in t&&t.id&&delete t.id,t},async useVerificationToken(r){try{let t=await e.verificationToken.delete({where:{identifier_token:r}});return"id"in t&&t.id&&delete t.id,t}catch(e){if(e instanceof a.PrismaClientKnownRequestError&&"P2025"===e.code)return null;throw e}},getAccount:async(r,t)=>e.account.findFirst({where:{providerAccountId:r,provider:t}}),createAuthenticator:async r=>e.authenticator.create(i(r)),getAuthenticator:async r=>e.authenticator.findUnique({where:{credentialID:r}}),listAuthenticatorsByUserId:async r=>e.authenticator.findMany({where:{userId:r}}),updateAuthenticatorCounter:async(r,t)=>e.authenticator.update({where:{credentialID:r},data:{counter:t}})}}(c.prisma),session:{strategy:"jwt",maxAge:86400,updateAge:3600},pages:{signIn:"/auth/signin",error:"/auth/signin?error=AuthError"},cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:u.Vi.IS_PRODUCTION}},callbackUrl:{name:"next-auth.callback-url",options:{sameSite:"lax",path:"/",secure:u.Vi.IS_PRODUCTION}},csrfToken:{name:"next-auth.csrf-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:u.Vi.IS_PRODUCTION}}},debug:u.Vi.IS_DEVELOPMENT,logger:{error(e,...r){l.kg.error("AUTH ERROR",{code:e,message:r})},warn(e,...r){l.kg.warn("AUTH WARNING",{code:e,message:r})},debug(e,...r){u.Vi.IS_DEVELOPMENT&&l.kg.debug("AUTH DEBUG",{code:e,message:r})}},providers:u.Vi.IS_PRODUCTION?[(0,s.Z)({clientId:u.Vi.API_KEYS.GOOGLE_CLIENT_ID,clientSecret:u.Vi.API_KEYS.GOOGLE_CLIENT_SECRET,allowDangerousEmailAccountLinking:!1,authorization:{params:{scope:"openid email profile",prompt:"consent",access_type:"offline",response_type:"code"}}}),(0,o.Z)({clientId:u.Vi.API_KEYS.GITHUB_CLIENT_ID,clientSecret:u.Vi.API_KEYS.GITHUB_CLIENT_SECRET,allowDangerousEmailAccountLinking:!1})]:u.Vi.FEATURES.SKIP_AUTH_PROVIDERS||"true"===process.env.AUTH_SKIP_PROVIDERS?[(0,n.Z)({name:"Desenvolvimento",credentials:{email:{label:"Email",type:"text"},password:{label:"Password",type:"password"}},authorize:async()=>({id:"dev-user",name:"Usu\xe1rio Desenvolvimento",email:"<EMAIL>"})})]:[(0,s.Z)({clientId:u.Vi.API_KEYS.GOOGLE_CLIENT_ID,clientSecret:u.Vi.API_KEYS.GOOGLE_CLIENT_SECRET,allowDangerousEmailAccountLinking:!0,authorization:{params:{scope:"openid email profile",prompt:"consent",access_type:"offline",response_type:"code"}}}),(0,o.Z)({clientId:u.Vi.API_KEYS.GITHUB_CLIENT_ID,clientSecret:u.Vi.API_KEYS.GITHUB_CLIENT_SECRET,allowDangerousEmailAccountLinking:!0})],callbacks:{async signIn({user:e,account:r,profile:t}){try{if(l.kg.info("\uD83D\uDD10 Tentativa de login",{userId:e?.id,email:e?.email?e.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",provider:r?.provider,type:r?.type,timestamp:new Date().toISOString(),userAgent:"N/A"}),e?.id){let r=await c.prisma.user.findUnique({where:{id:e.id},select:{isBanned:!0,banReason:!0,banDate:!0}});if(r?.isBanned)return l.kg.warn("\uD83D\uDEAB Tentativa de login de usu\xe1rio banido",{userId:e.id,email:e.email?e.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",banReason:r.banReason,banDate:r.banDate}),!1}if(r?.type==="oauth")return l.kg.info("✅ Login OAuth autorizado",{provider:r.provider,email:e?.email?e.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",userId:e?.id,timestamp:new Date().toISOString()}),!0;if(r?.type==="credentials")return l.kg.info("✅ Login por credenciais autorizado",{email:e?.email?e.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",userId:e?.id,timestamp:new Date().toISOString()}),!0;return l.kg.warn("❌ Tentativa de login n\xe3o autorizada",{accountType:r?.type,provider:r?.provider,email:e?.email?e.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",timestamp:new Date().toISOString(),reason:"Tipo de conta n\xe3o suportado"}),!0}catch(t){return l.kg.error("\uD83D\uDCA5 Erro no callback signIn",{error:t instanceof Error?t.message:"Erro desconhecido",stack:t instanceof Error?t.stack:void 0,userId:e?.id,email:e?.email?e.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",provider:r?.provider,timestamp:new Date().toISOString()}),!0}},async session({session:e,token:r}){try{if(e.user&&r.sub){let t=e.user;t.id=r.sub;let a=await c.prisma.user.findUnique({where:{id:r.sub},select:{id:!0,isBanned:!0,email:!0,banReason:!0,banDate:!0}});if(!a)return l.kg.warn("\uD83D\uDEAB Sess\xe3o para usu\xe1rio inexistente",{userId:r.sub,email:e.user.email?e.user.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",timestamp:new Date().toISOString()}),{...e,user:null};if(a.isBanned)return l.kg.warn("\uD83D\uDEAB Sess\xe3o para usu\xe1rio banido",{userId:r.sub,email:e.user.email?e.user.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",banReason:a.banReason,banDate:a.banDate,timestamp:new Date().toISOString()}),{...e,user:null};t.isBanned=a.isBanned,"development"===u.Vi.NODE_ENV&&l.kg.info("\uD83D\uDD10 Sess\xe3o ativa verificada",{userId:r.sub,email:e.user.email?e.user.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",timestamp:new Date().toISOString()})}return e}catch(t){return l.kg.error("\uD83D\uDCA5 Erro no callback de sess\xe3o",{error:t instanceof Error?t.message:"Erro desconhecido",userId:r?.sub,email:e?.user?.email?e.user.email.replace(/(.{2}).*(@.*)/,"$1***$2"):"N/A",timestamp:new Date().toISOString()}),e}},jwt:async({token:e,user:r})=>(r?.id&&(e.sub=r.id),e),async redirect({url:e,baseUrl:r}){let t=["/dashboard","/workbook","/account","/pricing","/"];try{if(e.startsWith("/")){let a=e.split("?")[0]||"/";if(t.some(e=>a.startsWith(e)))return`${r}${e}`;return`${r}/dashboard`}if(e.startsWith(r)){let a=e.replace(r,"").split("?")[0]||"/";if(t.some(e=>a.startsWith(e)))return e}return`${r}/dashboard`}catch{return`${r}/dashboard`}}},secret:u.Vi.NEXTAUTH_SECRET,trustHost:!0,useSecureCookies:u.Vi.IS_PRODUCTION}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>s});var a=t(53524);let i={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},n={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},o=[],s=global.prisma||new a.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...n,activeConnections:Math.min(Math.floor(5*Math.random())+1,n.maxPoolSize),poolSize:n.poolSize}}async function u(){try{await s.$disconnect(),i.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){i.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{n.totalQueries++,e.duration&&(o.push(e.duration),o.length>100&&o.shift(),n.averageQueryTime=o.reduce((e,r)=>e+r,0)/o.length),e.duration&&e.duration>500&&i.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{n.failedQueries++,n.connectionFailures++,n.lastConnectionFailure=new Date().toISOString(),i.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{u()})}};