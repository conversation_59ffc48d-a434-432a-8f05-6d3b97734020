"use strict";(()=>{var e={};e.id=6649,e.ids=[6649],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},95534:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>td,patchFetch:()=>tp,requestAsyncStorage:()=>tu,routeModule:()=>ta,serverHooks:()=>tc,staticGenerationAsyncStorage:()=>tl});var n={};r.r(n),r.d(n,{GET:()=>ti,POST:()=>ti,dynamic:()=>to,runtime:()=>ts});var o=r(49303),s=r(88716),i=r(60670);function a(e){let t=Object.create(null);for(let r in e)t[e[r]]=r;return t}let u={PARSE_ERROR:-32700,BAD_REQUEST:-32600,INTERNAL_SERVER_ERROR:-32603,NOT_IMPLEMENTED:-32603,UNAUTHORIZED:-32001,FORBIDDEN:-32003,NOT_FOUND:-32004,METHOD_NOT_SUPPORTED:-32005,TIMEOUT:-32008,CONFLICT:-32009,PRECONDITION_FAILED:-32012,PAYLOAD_TOO_LARGE:-32013,UNPROCESSABLE_CONTENT:-32022,TOO_MANY_REQUESTS:-32029,CLIENT_CLOSED_REQUEST:-32099};a(u);let l=a(u),c={PARSE_ERROR:400,BAD_REQUEST:400,UNAUTHORIZED:401,NOT_FOUND:404,FORBIDDEN:403,METHOD_NOT_SUPPORTED:405,TIMEOUT:408,CONFLICT:409,PRECONDITION_FAILED:412,PAYLOAD_TOO_LARGE:413,UNPROCESSABLE_CONTENT:422,TOO_MANY_REQUESTS:429,CLIENT_CLOSED_REQUEST:499,INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501};function d(e){return c[e.code]??500}let p=()=>{},f=e=>(function e(t,r){return new Proxy(p,{get(n,o){if("string"==typeof o&&"then"!==o)return e(t,[...r,o])},apply(e,n,o){let s="apply"===r[r.length-1];return t({args:s?o.length>=2?o[1]:[]:o,path:s?r.slice(0,-1):r})}})})(e,[]),m=e=>new Proxy(p,{get(t,r){if("string"==typeof r&&"then"!==r)return e(r)}});class h extends Error{}function y(e){if(e instanceof g||e instanceof Error&&"TRPCError"===e.name)return e;let t=new g({code:"INTERNAL_SERVER_ERROR",cause:e});return e instanceof Error&&e.stack&&(t.stack=e.stack),t}class g extends Error{constructor(e){let t=function(e){if(e instanceof Error)return e;let t=typeof e;if("undefined"!==t&&"function"!==t&&null!==e){if("object"!==t)return Error(String(e));if(e&&!Array.isArray(e)&&"object"==typeof e){let t=new h;for(let r in e)t[r]=e[r];return t}}}(e.cause);super(e.message??t?.message??e.code,{cause:t}),this.code=e.code,this.name="TRPCError",this.cause||(this.cause=t)}}let w={_default:!0,input:{serialize:e=>e,deserialize:e=>e},output:{serialize:e=>e,deserialize:e=>e}},b=({shape:e})=>e,E=["query","mutation","subscription"],_={_ctx:null,_errorShape:null,_meta:null,queries:{},mutations:{},subscriptions:{},errorFormatter:b,transformer:w},O=["then"];function v(e){return function(t){var r;let n=new Set(Object.keys(t).filter(e=>O.includes(e)));if(n.size>0)throw Error("Reserved words used in `router({})` call: "+Array.from(n).join(", "));let o=(r={},Object.assign(Object.create(null),r));!function e(t,r=""){for(let[n,s]of Object.entries(t??{})){let t=`${r}${n}`;if("router"in s._def){e(s._def.procedures,`${t}.`);continue}if(o[t])throw Error(`Duplicate key: ${t}`);o[t]=s}}(t);let s={_config:e,router:!0,procedures:o,..._,record:t,queries:Object.entries(o).filter(e=>e[1]._def.query).reduce((e,[t,r])=>({...e,[t]:r}),{}),mutations:Object.entries(o).filter(e=>e[1]._def.mutation).reduce((e,[t,r])=>({...e,[t]:r}),{}),subscriptions:Object.entries(o).filter(e=>e[1]._def.subscription).reduce((e,[t,r])=>({...e,[t]:r}),{})},i={...t,_def:s,createCaller:e=>R()(i)(e),getErrorShape(t){let{path:r,error:n}=t,{code:o}=t.error,s={message:n.message,code:u[o],data:{code:o,httpStatus:d(n)}};return e.isDev&&"string"==typeof t.error.stack&&(s.data.stack=t.error.stack),"string"==typeof r&&(s.data.path=r),this._def._config.errorFormatter({...t,shape:s})}};return i}}function k(e){let{type:t,path:r}=e;if(!(r in e.procedures)||!e.procedures[r]?._def[t])throw new g({code:"NOT_FOUND",message:`No "${t}"-procedure on path "${r}"`});return(0,e.procedures[r])(e)}function R(){return function(e){let t=e._def;return function(e){return f(({path:r,args:n})=>{if(1===r.length&&E.includes(r[0]))return k({procedures:t.procedures,path:n[0],rawInput:n[1],ctx:e,type:r[0]});let o=r.join("."),s=t.procedures[o],i="query";return s._def.mutation?i="mutation":s._def.subscription&&(i="subscription"),s({path:o,rawInput:n[0],ctx:e,type:i})})}}}let S="undefined"==typeof window||"Deno"in window||globalThis.process?.env?.NODE_ENV==="test"||!!globalThis.process?.env?.JEST_WORKER_ID||!!globalThis.process?.env?.VITEST_WORKER_ID;function x(e){let{path:t,error:r,config:n}=e,{code:o}=e.error,s={message:r.message,code:u[o],data:{code:o,httpStatus:d(r)}};return n.isDev&&"string"==typeof e.error.stack&&(s.data.stack=e.error.stack),"string"==typeof t&&(s.data.path=t),n.errorFormatter({...e,shape:s})}function I(e,t){return"error"in t?{...t,error:e.transformer.output.serialize(t.error)}:"data"in t.result?{...t,result:{...t.result,data:e.transformer.output.serialize(t.result.data)}}:t}function T(e,t){return Array.isArray(t)?t.map(t=>I(e,t)):I(e,t)}let P=(e,t)=>void 0!==e?t.input.deserialize(e):e,A={GET:"query",POST:"mutation"},N={getInputs:e=>{let t=function(e){let{req:t}=e;try{if("GET"===t.method){if(!t.query.has("input"))return;let e=t.query.get("input");return JSON.parse(e)}if(!e.preprocessedBody&&"string"==typeof t.body)return 0===t.body.length?void 0:JSON.parse(t.body);return t.body}catch(e){throw new g({code:"PARSE_ERROR",cause:e})}}(e),r=e.router._def._config.transformer;if(!e.isBatchCall)return{0:P(t,r)};if(null==t||"object"!=typeof t||Array.isArray(t))throw new g({code:"BAD_REQUEST",message:'"input" needs to be an object when doing a batch call'});let n={};for(let e in t){let o=P(t[e],r);n[e]=o}return n}};function j(e){let{ctx:t,paths:r,type:n,responseMeta:o,untransformedJSON:s,errors:i=[]}=e,a=s?function(e){let t=new Set((Array.isArray(e)?e:[e]).map(e=>{if("error"in e){let t=e.error.data;return"number"==typeof t.httpStatus?t.httpStatus:c[l[e.error.code]]??500}return 200}));return 1!==t.size?207:t.values().next().value}(s):200,u={"Content-Type":"application/json"},d=!s,p=d?[]:Array.isArray(s)?s:[s],f=o?.({ctx:t,paths:r,type:n,data:p,errors:i,eagerGeneration:d})??{};for(let[e,t]of Object.entries(f.headers??{}))u[e]=t;return f.status&&(a=f.status),{status:a,headers:u}}async function C(e){let{opts:t,ctx:r,type:n,input:o,path:s}=e;try{return{result:{data:await k({procedures:t.router._def.procedures,path:s,rawInput:o,ctx:r,type:n})}}}catch(i){let e=y(i);return t.onError?.({error:e,path:s,input:o,ctx:r,type:n,req:t.req}),{error:x({config:t.router._def._config,error:e,type:n,path:s,input:o,ctx:r})}}}function D(e,t){let{router:r,req:n,onError:o}=t.opts,s=y(e);o?.({error:s,path:t.path,input:t.input,ctx:t.ctx,type:t.type,req:n});let i={error:x({config:r._def._config,error:s,type:t.type,path:t.path,input:t.input,ctx:t.ctx})},a=JSON.stringify(T(r._def._config,i));return{error:s,untransformedJSON:i,body:a}}async function q(e){let t,r;let{router:n,req:o,unstable_onHead:s,unstable_onChunk:i}=e;if("HEAD"===o.method){let e={status:204};return s?.(e,!1),i?.([-1,""]),e}let a=e.contentTypeHandler??N,u=e.batching?.enabled??!0,l=A[o.method]??"unknown",c=!!o.query.get("batch"),d=c&&s&&i&&"stream"===o.headers["trpc-batch-mode"];try{if(r=await e.createContext(),e.error)throw e.error;if(c&&!u)throw Error("Batching is not enabled on the server");/* istanbul ignore if -- @preserve */if("subscription"===l)throw new g({message:"Subscriptions should use wsLink",code:"METHOD_NOT_SUPPORTED"});if("unknown"===l)throw new g({message:`Unexpected request method ${o.method}`,code:"METHOD_NOT_SUPPORTED"});let p=await a.getInputs({isBatchCall:c,req:o,router:n,preprocessedBody:e.preprocessedBody??!1}),f=(t=c?decodeURIComponent(e.path).split(","):[e.path]).map((t,n)=>C({opts:e,ctx:r,type:l,input:p[n],path:t}));if(!d){let o=await Promise.all(f),a=o.flatMap(e=>"error"in e?[e.error]:[]),u=j({ctx:r,paths:t,type:l,responseMeta:e.responseMeta,untransformedJSON:o,errors:a});s?.(u,!1);let d=c?o:o[0],p=T(n._def._config,d),m=JSON.stringify(p);return i?.([-1,m]),{status:u.status,headers:u.headers,body:m}}let m=j({ctx:r,paths:t,type:l,responseMeta:e.responseMeta});s(m,!0);let h=new Map(f.map((e,t)=>[t,e.then(e=>[t,e])]));for(let o of t){let[o,s]=await Promise.race(h.values());h.delete(o);try{let e=T(n._def._config,s),t=JSON.stringify(e);i([o,t])}catch(u){let n=t[o],s=p[o],{body:a}=D(u,{opts:e,ctx:r,type:l,path:n,input:s});i([o,a])}}return}catch(c){let{error:n,untransformedJSON:o,body:a}=D(c,{opts:e,ctx:r,type:l}),u=j({ctx:r,paths:t,type:l,responseMeta:e.responseMeta,untransformedJSON:o,errors:[n]});return s?.(u,!1),i?.([-1,a]),{status:u.status,headers:u.headers,body:a}}}let U=e=>e=(e=e.startsWith("/")?e.slice(1):e).endsWith("/")?e.slice(0,-1):e;async function M(e){var t;let r,n,o,s;let i=new Headers,a=async()=>e.createContext?.({req:e.req,resHeaders:i}),u=new URL((t=e.req.url).startsWith("/")?`http://127.0.0.1${t}`:t),l=U(u.pathname),c=U(e.endpoint),d=U(l.slice(c.length)),p={query:u.searchParams,method:e.req.method,headers:Object.fromEntries(e.req.headers),body:e.req.headers.get("content-type")?.startsWith("application/json")?await e.req.text():""},f=new Promise(e=>r=e),m=200,h=!1;return q({req:p,createContext:a,path:d,router:e.router,batching:e.batching,responseMeta:e.responseMeta,onError(t){e?.onError?.({...t,req:e.req})},unstable_onHead:(e,t)=>{for(let[t,r]of Object.entries(e.headers??{}))/* istanbul ignore if -- @preserve */if(void 0!==r){if("string"==typeof r){i.set(t,r);continue}for(let e of r)i.append(t,e)}if(m=e.status,t){i.set("Transfer-Encoding","chunked"),i.append("Vary","trpc-batch-mode");let e=new Response(new ReadableStream({start(e){n=e}}),{status:m,headers:i});r(e),o=new TextEncoder,s=function(){let e=!0;function t(t,r){let n=e?"{":",";return e=!1,`${n}"${t}":${r}
`}return t.end=()=>"}",t}(),h=!0}},unstable_onChunk:([e,t])=>{if(-1===e){let e=new Response(t||null,{status:m,headers:i});r(e)}else n.enqueue(o.encode(s(e,t)))}}).then(()=>{h&&(n.enqueue(o.encode(s.end())),n.close())}).catch(()=>{h&&n.close()}),f}var F=r(7410),z=r(21954);let $="middlewareMarker";function B(e){if("function"==typeof e)return e;if("function"==typeof e.parseAsync)return e.parseAsync.bind(e);if("function"==typeof e.parse)return e.parse.bind(e);if("function"==typeof e.validateSync)return e.validateSync.bind(e);if("function"==typeof e.create)return e.create.bind(e);throw Error("Could not find a validator fn")}class V{_def(){return{middlewares:this.middlewares,resolver:this.resolver,inputParser:this.inputParser,outputParser:this.outputParser,meta:this.meta}}async parseInput(e){try{return await this.parseInputFn(e)}catch(e){throw new TRPCError({code:"BAD_REQUEST",cause:e})}}async parseOutput(e){try{return await this.parseOutputFn(e)}catch(e){throw new TRPCError({code:"INTERNAL_SERVER_ERROR",cause:e,message:"Output validation failed"})}}async call(e){let t=this.middlewares.concat([async({ctx:t})=>{let r=await this.parseInput(e.rawInput),n=await this.resolver({...e,ctx:t,input:r});return{marker:$,ok:!0,data:await this.parseOutput(n),ctx:t}}]),r=async(n={index:0,ctx:e.ctx})=>{try{return await t[n.index]({ctx:n.ctx,type:e.type,path:e.path,rawInput:e.rawInput,meta:this.meta,next:async e=>await r({index:n.index+1,ctx:e?e.ctx:n.ctx})})}catch(e){return{ctx:n.ctx,ok:!1,error:getTRPCErrorFromUnknown(e),marker:$}}},n=await r();if(!n)throw new TRPCError({code:"INTERNAL_SERVER_ERROR",message:"No result from middlewares - did you forget to `return next()`?"});if(!n.ok)throw n.error;return n.data}inheritMiddlewares(e){return new this.constructor({middlewares:[...e,...this.middlewares],resolver:this.resolver,inputParser:this.inputParser,outputParser:this.outputParser,meta:this.meta})}constructor(e){this.middlewares=e.middlewares,this.resolver=e.resolver,this.inputParser=e.inputParser,this.parseInputFn=B(this.inputParser),this.outputParser=e.outputParser,this.parseOutputFn=B(this.outputParser),this.meta=e.meta}}function L(e){let t="input"in e?e.input:e=>{if(null!=e)throw new TRPCError({code:"BAD_REQUEST",message:"No input expected"})},r="output"in e&&e.output?e.output:e=>e;return new V({inputParser:t,resolver:e.resolve,middlewares:[],outputParser:r,meta:e.meta})}function H(e){if("function"==typeof e)return e;if("function"==typeof e.parseAsync)return e.parseAsync.bind(e);if("function"==typeof e.parse)return e.parse.bind(e);if("function"==typeof e.validateSync)return e.validateSync.bind(e);if("function"==typeof e.create)return e.create.bind(e);if("function"==typeof e.assert)return t=>(e.assert(t),t);throw Error("Could not find a validator fn")}function Q(e,...t){let r=Object.assign(Object.create(null),e);for(let e of t)for(let t in e){if(t in r&&r[t]!==e[t])throw Error(`Duplicate key ${t}`);r[t]=e[t]}return r}function G(e){return e&&"object"==typeof e&&!Array.isArray(e)}function J(e){let t=async({next:t,rawInput:r,input:n})=>{let o;try{o=await e(r)}catch(e){throw new g({code:"BAD_REQUEST",cause:e})}return t({input:G(n)&&G(o)?{...n,...o}:o})};return t._type="input",t}function Y(e){let t=async({next:t})=>{let r=await t();if(!r.ok)return r;try{let t=await e(r.data);return{...r,data:t}}catch(e){throw new g({message:"Output validation failed",code:"INTERNAL_SERVER_ERROR",cause:e})}};return t._type="output",t}let W="middlewareMarker";function K(e,t){let{middlewares:r=[],inputs:n,meta:o,...s}=t;return X({...Q(e,s),inputs:[...e.inputs,...n??[]],middlewares:[...e.middlewares,...r],meta:e.meta&&o?{...e.meta,...o}:o??e.meta})}function X(e={}){let t={inputs:[],middlewares:[],...e};return{_def:t,input(e){let r=H(e);return K(t,{inputs:[e],middlewares:[J(r)]})},output(e){let r=H(e);return K(t,{output:e,middlewares:[Y(r)]})},meta:e=>K(t,{meta:e}),unstable_concat:e=>K(t,e._def),use:e=>K(t,{middlewares:"_middlewares"in e?e._middlewares:[e]}),query:e=>Z({...t,query:!0},e),mutation:e=>Z({...t,mutation:!0},e),subscription:e=>Z({...t,subscription:!0},e)}}function Z(e,t){return function(e){let t=async function(t){if(!t||!("rawInput"in t))throw Error(ee);let r=async(n={index:0,ctx:t.ctx})=>{try{let o=e.middlewares[n.index];return await o({ctx:n.ctx,type:t.type,path:t.path,rawInput:n.rawInput??t.rawInput,meta:e.meta,input:n.input,next:e=>r({index:n.index+1,ctx:e&&"ctx"in e?{...n.ctx,...e.ctx}:n.ctx,input:e&&"input"in e?e.input:n.input,rawInput:e&&"rawInput"in e?e.rawInput:n.rawInput})})}catch(e){return{ok:!1,error:y(e),marker:W}}},n=await r();if(!n)throw new g({code:"INTERNAL_SERVER_ERROR",message:"No result from middlewares - did you forget to `return next()`?"});if(!n.ok)throw n.error;return n.data};return t._def=e,t.meta=e.meta,t}(K(e,{resolver:t,middlewares:[async function(e){return{marker:W,ok:!0,data:await t(e),ctx:e.ctx}}]})._def)}let ee=`
This is a client-only function.
If you want to call this function on the server, see https://trpc.io/docs/server/server-side-calls
`.trim();function et(e,t){var r,n;let o=e._def(),s=(r=o.inputParser)?H(r):e=>e,i=(n=o.outputParser)?H(n):e=>e,a=J(s);return X({inputs:[o.inputParser],middlewares:[...o.middlewares,a,Y(i)],meta:o.meta,output:o.outputParser,mutation:"mutation"===t,query:"query"===t,subscription:"subscription"===t})[t](e=>o.resolver(e))}let er={query:"queries",mutation:"mutations",subscription:"subscriptions"};function en(...e){return Object.assign(Object.create(null),...e)}class eo{static prefixProcedures(e,t){let r=en();for(let[n,o]of Object.entries(e))r[t+n]=o;return r}query(e,t){let r=new eo({queries:en({[e]:L(t)})});return this.merge(r)}mutation(e,t){let r=new eo({mutations:en({[e]:L(t)})});return this.merge(r)}subscription(e,t){let r=new eo({subscriptions:en({[e]:L(t)})});return this.merge(r)}merge(e,t){let r,n="";if("string"==typeof e&&t instanceof eo)n=e,r=t;else if(e instanceof eo)r=e;else throw Error("Invalid args");let o=[...Object.keys(r._def.queries).filter(e=>!!this._def.queries[n+e]),...Object.keys(r._def.mutations).filter(e=>!!this._def.mutations[n+e]),...Object.keys(r._def.subscriptions).filter(e=>!!this._def.subscriptions[n+e])];if(o.length)throw Error(`Duplicate endpoint(s): ${o.join(", ")}`);let s=e=>{let t=en();for(let[r,n]of Object.entries(e)){let e=n.inheritMiddlewares(this._def.middlewares);t[r]=e}return eo.prefixProcedures(t,n)};return new eo({...this._def,queries:en(this._def.queries,s(r._def.queries)),mutations:en(this._def.mutations,s(r._def.mutations)),subscriptions:en(this._def.subscriptions,s(r._def.subscriptions))})}async call(e){let{type:t,path:r}=e,n=er[t],o=this._def[n][r];if(!o)throw new TRPCError({code:"NOT_FOUND",message:`No "${t}"-procedure on path "${r}"`});return o.call(e)}createCaller(e){return{query:(t,...r)=>this.call({type:"query",ctx:e,path:t,rawInput:r[0]}),mutation:(t,...r)=>this.call({type:"mutation",ctx:e,path:t,rawInput:r[0]}),subscription:(t,...r)=>this.call({type:"subscription",ctx:e,path:t,rawInput:r[0]})}}middleware(e){return new eo({...this._def,middlewares:[...this._def.middlewares,e]})}formatError(e){if(this._def.errorFormatter!==defaultFormatter)throw Error("You seem to have double `formatError()`-calls in your router tree");return new eo({...this._def,errorFormatter:e})}getErrorShape(e){let{path:t,error:r}=e,{code:n}=e.error,o={message:r.message,code:TRPC_ERROR_CODES_BY_KEY[n],data:{code:n,httpStatus:getHTTPStatusCodeFromError(r)}};return globalThis.process?.env?.NODE_ENV!=="production"&&"string"==typeof e.error.stack&&(o.data.stack=e.error.stack),"string"==typeof t&&(o.data.path=t),this._def.errorFormatter({...e,shape:o})}transformer(e){if(this._def.transformer!==defaultTransformer)throw Error("You seem to have double `transformer()`-calls in your router tree");return new eo({...this._def,transformer:"input"in e?e:{input:e,output:e}})}flat(){return this}interop(){return function(e){let t=e._def.errorFormatter,r=e._def.transformer,n={},o={},s={};for(let[t,r]of Object.entries(e._def.queries))n[t]=et(r,"query");for(let[t,r]of Object.entries(e._def.mutations))o[t]=et(r,"mutation");for(let[t,r]of Object.entries(e._def.subscriptions))s[t]=et(r,"subscription");let i=Q(n,o,s);return createRouterFactory({transformer:r,errorFormatter:t,isDev:!1})(i)}(this)}constructor(e){this._def={queries:e?.queries??en(),mutations:e?.mutations??en(),subscriptions:e?.subscriptions??en(),middlewares:e?.middlewares??[],errorFormatter:e?.errorFormatter??defaultFormatter,transformer:e?.transformer??defaultTransformer}}}function es(...e){let t=Q({},...e.map(e=>e._def.record));return v({errorFormatter:e.reduce((e,t)=>{if(t._def._config.errorFormatter&&t._def._config.errorFormatter!==b){if(e!==b&&e!==t._def._config.errorFormatter)throw Error("You seem to have several error formatters");return t._def._config.errorFormatter}return e},b),transformer:e.reduce((e,t)=>{if(t._def._config.transformer&&t._def._config.transformer!==w){if(e!==w&&e!==t._def._config.transformer)throw Error("You seem to have several transformers");return t._def._config.transformer}return e},w),isDev:e.some(e=>e._def._config.isDev),allowOutsideOfServer:e.some(e=>e._def._config.allowOutsideOfServer),isServer:e.some(e=>e._def._config.isServer),$types:e[0]?._def._config.$types})(t)}class ei{context(){return new ei}meta(){return new ei}create(e){return function(e){var t;let r=e?.errorFormatter??b,n={transformer:"input"in(t=e?.transformer??w)?t:{input:t,output:t},isDev:e?.isDev??globalThis.process?.env?.NODE_ENV!=="production",allowOutsideOfServer:e?.allowOutsideOfServer??!1,errorFormatter:r,isServer:e?.isServer??S,$types:m(e=>{throw Error(`Tried to access "$types.${e}" which is not available at runtime`)})};if(!(e?.isServer??S)&&e?.allowOutsideOfServer!==!0)throw Error("You're trying to use @trpc/server in a non-server environment. This is not supported by default.");return{_config:n,procedure:X({meta:e?.defaultMeta}),middleware:function(e){return function e(t){return{_middlewares:t,unstable_pipe:r=>e([...t,..."_middlewares"in r?r._middlewares:[r]])}}([e])},router:v(n),mergeRouters:es,createCallerFactory:R()}}(e)}}let ea=new ei;var eu=r(45609);class el{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class ec{constructor(e){this.generateIdentifier=e,this.kv=new el}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}}class ed extends ec{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){"object"==typeof t?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}}function ep(e,t){Object.entries(e).forEach(([e,r])=>t(r,e))}function ef(e,t){return -1!==e.indexOf(t)}function em(e,t){for(let r=0;r<e.length;r++){let n=e[r];if(t(n))return n}}class eh{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return function(e,t){let r=function(e){if("values"in Object)return Object.values(e);let t=[];for(let r in e)e.hasOwnProperty(r)&&t.push(e[r]);return t}(e);if("find"in r)return r.find(t);for(let e=0;e<r.length;e++){let n=r[e];if(t(n))return n}}(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}}let ey=e=>Object.prototype.toString.call(e).slice(8,-1),eg=e=>void 0===e,ew=e=>null===e,eb=e=>"object"==typeof e&&null!==e&&e!==Object.prototype&&(null===Object.getPrototypeOf(e)||Object.getPrototypeOf(e)===Object.prototype),eE=e=>eb(e)&&0===Object.keys(e).length,e_=e=>Array.isArray(e),eO=e=>"string"==typeof e,ev=e=>"number"==typeof e&&!isNaN(e),ek=e=>"boolean"==typeof e,eR=e=>e instanceof Map,eS=e=>e instanceof Set,ex=e=>"Symbol"===ey(e),eI=e=>"number"==typeof e&&isNaN(e),eT=e=>ek(e)||ew(e)||eg(e)||ev(e)||eO(e)||ex(e),eP=e=>e===1/0||e===-1/0,eA=e=>e.replace(/\./g,"\\."),eN=e=>e.map(String).map(eA).join("."),ej=e=>{let t=[],r="";for(let n=0;n<e.length;n++){let o=e.charAt(n);if("\\"===o&&"."===e.charAt(n+1)){r+=".",n++;continue}if("."===o){t.push(r),r="";continue}r+=o}let n=r;return t.push(n),t};function eC(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let eD=[eC(eg,"undefined",()=>null,()=>void 0),eC(e=>"bigint"==typeof e,"bigint",e=>e.toString(),e=>"undefined"!=typeof BigInt?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),eC(e=>e instanceof Date&&!isNaN(e.valueOf()),"Date",e=>e.toISOString(),e=>new Date(e)),eC(e=>e instanceof Error,"Error",(e,t)=>{let r={name:e.name,message:e.message};return t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r},(e,t)=>{let r=Error(e.message);return r.name=e.name,r.stack=e.stack,t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r}),eC(e=>e instanceof RegExp,"regexp",e=>""+e,e=>new RegExp(e.slice(1,e.lastIndexOf("/")),e.slice(e.lastIndexOf("/")+1))),eC(eS,"set",e=>[...e.values()],e=>new Set(e)),eC(eR,"map",e=>[...e.entries()],e=>new Map(e)),eC(e=>eI(e)||eP(e),"number",e=>eI(e)?"NaN":e>0?"Infinity":"-Infinity",Number),eC(e=>0===e&&1/e==-1/0,"number",()=>"-0",Number),eC(e=>e instanceof URL,"URL",e=>e.toString(),e=>new URL(e))];function eq(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let eU=eq((e,t)=>!!ex(e)&&!!t.symbolRegistry.getIdentifier(e),(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,r)=>{let n=r.symbolRegistry.getValue(t[1]);if(!n)throw Error("Trying to deserialize unknown symbol");return n}),eM=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),eF=eq(e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{let r=eM[t[1]];if(!r)throw Error("Trying to deserialize unknown typed array");return new r(e)});function ez(e,t){return!!e?.constructor&&!!t.classRegistry.getIdentifier(e.constructor)}let e$=eq(ez,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{let r=t.classRegistry.getAllowedProps(e.constructor);if(!r)return{...e};let n={};return r.forEach(t=>{n[t]=e[t]}),n},(e,t,r)=>{let n=r.classRegistry.getValue(t[1]);if(!n)throw Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(n.prototype),e)}),eB=eq((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,r)=>{let n=r.customTransformerRegistry.findByName(t[1]);if(!n)throw Error("Trying to deserialize unknown custom value");return n.deserialize(e)}),eV=[e$,eU,eB,eF],eL=(e,t)=>{let r=em(eV,r=>r.isApplicable(e,t));if(r)return{value:r.transform(e,t),type:r.annotation(e,t)};let n=em(eD,r=>r.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation}},eH={};eD.forEach(e=>{eH[e.annotation]=e});let eQ=(e,t,r)=>{if(e_(t))switch(t[0]){case"symbol":return eU.untransform(e,t,r);case"class":return e$.untransform(e,t,r);case"custom":return eB.untransform(e,t,r);case"typed-array":return eF.untransform(e,t,r);default:throw Error("Unknown transformation: "+t)}else{let n=eH[t];if(!n)throw Error("Unknown transformation: "+t);return n.untransform(e,r)}},eG=(e,t)=>{if(t>e.size)throw Error("index out of bounds");let r=e.keys();for(;t>0;)r.next(),t--;return r.next().value};function eJ(e){if(ef(e,"__proto__"))throw Error("__proto__ is not allowed as a property");if(ef(e,"prototype"))throw Error("prototype is not allowed as a property");if(ef(e,"constructor"))throw Error("constructor is not allowed as a property")}let eY=(e,t)=>{eJ(t);for(let r=0;r<t.length;r++){let n=t[r];if(eS(e))e=eG(e,+n);else if(eR(e)){let o=+n,s=0==+t[++r]?"key":"value",i=eG(e,o);switch(s){case"key":e=i;break;case"value":e=e.get(i)}}else e=e[n]}return e},eW=(e,t,r)=>{if(eJ(t),0===t.length)return r(e);let n=e;for(let e=0;e<t.length-1;e++){let r=t[e];if(e_(n))n=n[+r];else if(eb(n))n=n[r];else if(eS(n))n=eG(n,+r);else if(eR(n)){if(e===t.length-2)break;let o=+r,s=0==+t[++e]?"key":"value",i=eG(n,o);switch(s){case"key":n=i;break;case"value":n=n.get(i)}}}let o=t[t.length-1];if(e_(n)?n[+o]=r(n[+o]):eb(n)&&(n[o]=r(n[o])),eS(n)){let e=eG(n,+o),t=r(e);e!==t&&(n.delete(e),n.add(t))}if(eR(n)){let e=eG(n,+t[t.length-2]);switch(0==+o?"key":"value"){case"key":{let t=r(e);n.set(t,n.get(e)),t!==e&&n.delete(e);break}case"value":n.set(e,r(n.get(e)))}}return e},eK=(e,t)=>eb(e)||e_(e)||eR(e)||eS(e)||ez(e,t),eX=(e,t,r,n,o=[],s=[],i=new Map)=>{let a=eT(e);if(!a){!function(e,t,r){let n=r.get(e);n?n.push(t):r.set(e,[t])}(e,o,t);let r=i.get(e);if(r)return n?{transformedValue:null}:r}if(!eK(e,r)){let t=eL(e,r),n=t?{transformedValue:t.value,annotations:[t.type]}:{transformedValue:e};return a||i.set(e,n),n}if(ef(s,e))return{transformedValue:null};let u=eL(e,r),l=u?.value??e,c=e_(l)?[]:{},d={};ep(l,(a,u)=>{if("__proto__"===u||"constructor"===u||"prototype"===u)throw Error(`Detected property ${u}. This is a prototype pollution risk, please remove it from your object.`);let l=eX(a,t,r,n,[...o,u],[...s,e],i);c[u]=l.transformedValue,e_(l.annotations)?d[u]=l.annotations:eb(l.annotations)&&ep(l.annotations,(e,t)=>{d[eA(u)+"."+t]=e})});let p=eE(d)?{transformedValue:c,annotations:u?[u.type]:void 0}:{transformedValue:c,annotations:u?[u.type,d]:d};return a||i.set(e,p),p};function eZ(e){return Object.prototype.toString.call(e).slice(8,-1)}function e0(e){return"Array"===eZ(e)}class e1{constructor({dedupe:e=!1}={}){this.classRegistry=new ed,this.symbolRegistry=new ec(e=>e.description??""),this.customTransformerRegistry=new eh,this.allowedErrorProps=[],this.dedupe=e}serialize(e){let t=new Map,r=eX(e,t,this,this.dedupe),n={json:r.transformedValue};r.annotations&&(n.meta={...n.meta,values:r.annotations});let o=function(e,t){let r;let n={};return(e.forEach(e=>{if(e.length<=1)return;t||(e=e.map(e=>e.map(String)).sort((e,t)=>e.length-t.length));let[o,...s]=e;0===o.length?r=s.map(eN):n[eN(o)]=s.map(eN)}),r)?eE(n)?[r]:[r,n]:eE(n)?void 0:n}(t,this.dedupe);return o&&(n.meta={...n.meta,referentialEqualities:o}),n}deserialize(e){let{json:t,meta:r}=e,n=function e(t,r={}){return e0(t)?t.map(t=>e(t,r)):!function(e){if("Object"!==eZ(e))return!1;let t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}(t)?t:[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)].reduce((n,o)=>{if(e0(r.props)&&!r.props.includes(o))return n;let s=e(t[o],r);return function(e,t,r,n,o){let s=({}).propertyIsEnumerable.call(n,t)?"enumerable":"nonenumerable";"enumerable"===s&&(e[t]=r),o&&"nonenumerable"===s&&Object.defineProperty(e,t,{value:r,enumerable:!1,writable:!0,configurable:!0})}(n,o,s,t,r.nonenumerable),n},{})}(t);if(r?.values){var o,s,i;o=n,s=r.values,i=this,function e(t,r,n=[]){if(!t)return;if(!e_(t)){ep(t,(t,o)=>e(t,r,[...n,...ej(o)]));return}let[o,s]=t;s&&ep(s,(t,o)=>{e(t,r,[...n,...ej(o)])}),r(o,n)}(s,(e,t)=>{o=eW(o,t,t=>eQ(t,e,i))}),n=o}return r?.referentialEqualities&&(n=function(e,t){function r(t,r){let n=eY(e,ej(r));t.map(ej).forEach(t=>{e=eW(e,t,()=>n)})}if(e_(t)){let[n,o]=t;n.forEach(t=>{e=eW(e,ej(t),()=>e)}),o&&ep(o,r)}else ep(t,r);return e}(n,r.referentialEqualities)),n}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}}e1.defaultInstance=new e1,e1.serialize=e1.defaultInstance.serialize.bind(e1.defaultInstance),e1.deserialize=e1.defaultInstance.deserialize.bind(e1.defaultInstance),e1.stringify=e1.defaultInstance.stringify.bind(e1.defaultInstance),e1.parse=e1.defaultInstance.parse.bind(e1.defaultInstance),e1.registerClass=e1.defaultInstance.registerClass.bind(e1.defaultInstance),e1.registerSymbol=e1.defaultInstance.registerSymbol.bind(e1.defaultInstance),e1.registerCustom=e1.defaultInstance.registerCustom.bind(e1.defaultInstance),e1.allowErrorProps=e1.defaultInstance.allowErrorProps.bind(e1.defaultInstance),e1.serialize,e1.deserialize,e1.stringify,e1.parse,e1.registerClass,e1.registerCustom,e1.registerSymbol,e1.allowErrorProps;var e2=r(43895);let e3={USE_DEMO_USER:!1,DEMO_USER:{id:"demo-user",name:"Usu\xe1rio Demo",email:"<EMAIL>"},DEMO_SESSION_EXPIRY:2592e6,VERBOSE_LOGGING:"true"===process.env.AUTH_VERBOSE_LOGGING,IS_DEVELOPMENT:!1,IS_TEST:!1,IS_PRODUCTION:!0};var e5=r(24433),e4=r(63841);let e9=(0,e5.OI)();if(!e9.valid){e2.kg.error(`Erro na configura\xe7\xe3o do servidor tRPC: Vari\xe1veis de ambiente ausentes: ${e9.missing.join(", ")}`);let e="phase-production-build"===process.env.NEXT_PHASE;if("true"===process.env.AI_USE_MOCK||e)e2.kg.warn("Usando configura\xe7\xe3o incompleta com modo mock ativado em produ\xe7\xe3o");else throw Error("Configura\xe7\xe3o de ambiente incompleta para ambiente de produ\xe7\xe3o")}let e7=e=>({session:e.session,prisma:e4.prisma}),e6=async e=>{let t=null;try{if(e&&("object"!=typeof e||null===e||"req"in e&&"res"in e)){if(t=await (0,eu.getServerSession)(),t?.user){let e="object"==typeof t.user&&null!==t.user&&"id"in t.user?t.user.id:void 0;t.user.id=e||"unknown-id"}}else if(t=await (0,eu.getServerSession)(),t?.user){let e="object"==typeof t.user&&null!==t.user&&"id"in t.user?t.user.id:void 0;t.user.id=e||"unknown-id"}}catch(e){e2.kg.error("Erro ao obter sess\xe3o:",e)}return e7({session:t})},e8=ea.context().create({transformer:e1,errorFormatter:({shape:e})=>e}),te=e8.router;e8.procedure;let tt=e8.middleware(({ctx:e,next:t})=>{let r=!!e.session?.user;if(!r&&e3.USE_DEMO_USER)return e3.VERBOSE_LOGGING&&e2.kg.info("Autentica\xe7\xe3o com usu\xe1rio demo ativada: Usando credenciais de demonstra\xe7\xe3o"),t({ctx:{session:{user:e3.DEMO_USER,expires:new Date(Date.now()+e3.DEMO_SESSION_EXPIRY).toISOString()}}});if(!r)throw new g({code:"UNAUTHORIZED",message:"Voc\xea precisa estar autenticado para acessar este recurso"});return t({ctx:{session:{...e.session,user:e.session?.user}}})}),tr=e8.procedure.use(tt),tn=te({getWorkbooks:tr.query(async({ctx:e})=>e.prisma.workbook.findMany({where:{userId:e.session.user.id},orderBy:{updatedAt:"desc"},include:{sheets:{select:{id:!0,name:!0}}}})),workbook_getAll:tr.query(async({ctx:e})=>e.prisma.workbook.findMany({where:{userId:e.session.user.id},orderBy:{updatedAt:"desc"},include:{sheets:{select:{id:!0,name:!0}}}})),workbook_getTemplates:tr.query(async({ctx:e})=>{try{return(await e.prisma.template.findMany({where:{isActive:!0,isPublic:!0},orderBy:[{isFeatured:"desc"},{popularity:"desc"}],take:12,include:{categories:{select:{id:!0,name:!0,slug:!0}},_count:{select:{reviews:!0,usage:!0}}}})).map(e=>({id:e.id,title:e.title,description:e.description,icon:e.icon,category:e.categories[0]?.name||"Geral",popularity:e.popularity,sheets:1,isFeatured:e.isFeatured,isNew:e.isNew,usageCount:e._count.usage,reviewCount:e._count.reviews}))}catch(e){return console.error("Erro ao buscar templates do banco:",e),[{id:"vendas",title:"Controle de Vendas",description:"Template para controle de vendas com campos para produto, quantidade, valor e total.",icon:"bar-chart",category:"Neg\xf3cios"},{id:"financas",title:"Finan\xe7as Pessoais",description:"Controle suas receitas e despesas com categorias predefinidas.",icon:"piggy-bank",category:"Finan\xe7as"},{id:"projeto",title:"Gerenciamento de Projeto",description:"Acompanhe tarefas, respons\xe1veis e prazos do seu projeto.",icon:"file-text",category:"Produtividade"},{id:"inventario",title:"Controle de Estoque",description:"Registre produtos, quantidades e informa\xe7\xf5es de fornecedores.",icon:"table",category:"Neg\xf3cios"}]}}),workbook_get:tr.input(F.z.object({id:F.z.string()})).query(async({ctx:e,input:t})=>{let r=await e.prisma.workbook.findUnique({where:{id:t.id},include:{sheets:!0}});if(!r)throw Error("Workbook n\xe3o encontrado");if(r.userId!==e.session.user.id&&!r.isPublic)throw Error("Voc\xea n\xe3o tem permiss\xe3o para acessar este workbook");return r}),workbook_delete:tr.input(F.z.object({id:F.z.string()})).mutation(async({ctx:e,input:t})=>{if(!await e.prisma.workbook.findFirst({where:{id:t.id,userId:e.session.user.id}}))throw Error("Workbook n\xe3o encontrado ou n\xe3o pertence ao usu\xe1rio");return e.prisma.workbook.delete({where:{id:t.id}})}),workbook_create:tr.input(F.z.object({name:F.z.string().min(1).max(100),description:F.z.string().max(500).optional()})).mutation(async({ctx:e,input:t})=>e.prisma.workbook.create({data:{name:t.name,description:t.description||"",userId:e.session.user.id,sheets:{create:[{name:"Planilha 1",data:JSON.stringify({headers:[],rows:[]})}]}}})),workbook_createFromTemplate:tr.input(F.z.object({templateId:F.z.string()})).mutation(async({ctx:e,input:t})=>{try{let r;let n=await e.prisma.template.findUnique({where:{id:t.templateId},include:{categories:!0}});if(!n)throw Error("Template n\xe3o encontrado");if(!n.isActive)throw Error("Template n\xe3o est\xe1 ativo");if(!n.isPublic&&n.createdBy!==e.session.user.id)throw Error("Acesso negado ao template");try{r=JSON.parse(n.data)}catch(e){throw Error("Dados do template inv\xe1lidos")}if(!r.sheets||!Array.isArray(r.sheets))throw Error("Estrutura do template inv\xe1lida");return await e.prisma.$transaction(async t=>{let o=await t.workbook.create({data:(0,z.fp)(`${n.title} - ${new Date().toLocaleDateString()}`,e.session.user.id,n.description||void 0,r.sheets.map((e,t)=>({name:e.name||`Sheet ${t+1}`,data:JSON.stringify(e.data||{headers:[],rows:[]})}))),include:{sheets:!0}});return await t.templateUsage.create({data:{templateId:n.id,userId:e.session.user.id,workbookId:o.id}}),await t.template.update({where:{id:n.id},data:{usageCount:{increment:1},popularity:{increment:1}}}),o})}catch(a){console.error("Erro ao criar workbook a partir do template:",a);let r={vendas:{sheets:[{name:"Vendas",data:{headers:["Data","Produto","Quantidade","Valor Unit\xe1rio","Total"],rows:[["2023-01-01","Produto A",5,100,500],["2023-01-02","Produto B",3,200,600],["2023-01-03","Produto C",10,50,500]]}}]},financas:{sheets:[{name:"Or\xe7amento",data:{headers:["Categoria","Descri\xe7\xe3o","Valor","Data","Tipo"],rows:[["Moradia","Aluguel",1200,"2023-01-01","Despesa"],["Transporte","Combust\xedvel",300,"2023-01-05","Despesa"],["Sal\xe1rio","Mensal",5e3,"2023-01-10","Receita"]]}}]},projeto:{sheets:[{name:"Tarefas",data:{headers:["Tarefa","Respons\xe1vel","Prazo","Status","Prioridade"],rows:[["Definir escopo","Jo\xe3o","2023-01-15","Conclu\xeddo","Alta"],["Desenvolver front-end","Maria","2023-02-10","Em andamento","M\xe9dia"],["Testar aplica\xe7\xe3o","Pedro","2023-02-25","N\xe3o iniciado","Baixa"]]}}]},inventario:{sheets:[{name:"Estoque",data:{headers:["Produto","Quantidade","Pre\xe7o Unit\xe1rio","Fornecedor","Data Entrada"],rows:[["Item A",100,10,"Fornecedor X","2023-01-05"],["Item B",50,25,"Fornecedor Y","2023-01-10"],["Item C",200,5,"Fornecedor Z","2023-01-15"]]}}]}};if(!r[t.templateId])throw Error("Template n\xe3o encontrado");let n=r[t.templateId],o={vendas:{name:"Controle de Vendas",description:"Planilha para controle de vendas com registros de produtos, quantidades e valores."},financas:{name:"Finan\xe7as Pessoais",description:"Controle de finan\xe7as com categorias para receitas e despesas."},projeto:{name:"Gerenciamento de Projeto",description:"Acompanhamento de tarefas e respons\xe1veis do projeto."},inventario:{name:"Controle de Estoque",description:"Registro de produtos e quantidades em estoque."}},s=`Template ${t.templateId}`,i="";return["vendas","financas","projeto","inventario"].includes(t.templateId)&&(s=o[t.templateId].name,i=o[t.templateId].description),await e.prisma.workbook.create({data:(0,z.fp)(s,e.session.user.id,i,(n?.sheets||[]).map(e=>({name:e.name,data:JSON.stringify(e.data)}))),include:{sheets:!0}})}}),createWorkbook:tr.input(F.z.object({name:F.z.string().min(1,"O nome \xe9 obrigat\xf3rio"),description:F.z.string().optional()})).mutation(async({ctx:e,input:t})=>await e.prisma.workbook.create({data:(0,z.fp)(t.name,e.session.user.id,t.description,[{name:"Planilha1",data:JSON.stringify({headers:[],rows:[]})}]),include:{sheets:!0}})),saveChatHistory:tr.input(F.z.object({message:F.z.string(),response:F.z.string(),workbookId:F.z.string().optional()})).mutation(async({ctx:e,input:t})=>e.prisma.chatHistory.create({data:(0,z.PL)(e.session.user.id,t.message,t.response,t.workbookId)})),getWorkbookChatHistory:tr.input(F.z.object({workbookId:F.z.string()})).query(async({ctx:e,input:t})=>e.prisma.chatHistory.findMany({where:{userId:e.session.user.id,workbookId:t.workbookId},orderBy:{createdAt:"desc"},take:50}))}),to="force-dynamic",ts="nodejs",ti=async e=>M({router:tn,createContext:e6,req:e,endpoint:"/api/trpc"}),ta=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/trpc/[trpc]/route",pathname:"/api/trpc/[trpc]",filename:"route",bundlePath:"app/api/trpc/[trpc]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\trpc\\[trpc]\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:tu,staticGenerationAsyncStorage:tl,serverHooks:tc}=ta,td="/api/trpc/[trpc]/route";function tp(){return(0,i.patchFetch)({serverHooks:tc,staticGenerationAsyncStorage:tl})}},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,s={};function i(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[n,o],...s]=a(e),{domain:i,expires:u,httponly:d,maxage:p,path:f,samesite:m,secure:h,partitioned:y,priority:g}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(o),domain:i,...u&&{expires:new Date(u)},...d&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:f,...m&&{sameSite:l.includes(t=(t=m).toLowerCase())?t:void 0},...h&&{secure:!0},...g&&{priority:c.includes(r=(r=g).toLowerCase())?r:void 0},...y&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>a,parseSetCookie:()=>u,stringifyCookie:()=>i}),e.exports=((e,s,i,a)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let i of n(s))o.call(e,i)||void 0===i||t(e,i,{get:()=>s[i],enumerable:!(a=r(s,i))||a.enumerable});return e})(t({},"__esModule",{value:!0}),s);var l=["strict","lax","none"],c=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>i(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>i(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let o=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(o)?o:function(e){if(!e)return[];var t,r,n,o,s,i=[],a=0;function u(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;u();)if(","===(r=e.charAt(a))){for(n=a,a+=1,u(),o=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=o,i.push(e.substring(t,n)),t=a):a=n+1}else a+=1;(!s||a>=e.length)&&i.push(e.substring(t,e.length))}return i}(o)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=i(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},49303:(e,t,r)=>{e.exports=r(30517)},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies}});let n=r(79925)},63841:(e,t,r)=>{r.d(t,{P:()=>u,prisma:()=>a});var n=r(53524);let o={info:(e,...t)=>{},error:(e,...t)=>{console.error(`[DB ERROR] ${e}`,...t)},warn:(e,...t)=>{console.warn(`[DB WARNING] ${e}`,...t)}},s={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],a=global.prisma||new n.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function u(){return{...s,activeConnections:Math.min(Math.floor(5*Math.random())+1,s.maxPoolSize),poolSize:s.poolSize}}async function l(){try{await a.$disconnect(),o.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){o.error("Erro ao desconectar do banco de dados",e)}}a.$on("query",e=>{s.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),s.averageQueryTime=i.reduce((e,t)=>e+t,0)/i.length),e.duration&&e.duration>500&&o.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),a.$on("error",e=>{s.failedQueries++,s.connectionFailures++,s.lastConnectionFailure=new Date().toISOString(),o.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{l()})},21954:(e,t,r)=>{function n(e,t,r,n,o){return{userId:e,endpoint:t,count:r,workbookId:n||null,billable:o}}function o(e,t,r,n){return{name:e,userId:t,description:r||null,sheets:{create:n}}}function s(e,t,r,n){return{userId:e,message:t,response:r,workbookId:n||null}}r.d(t,{PL:()=>s,ZR:()=>n,fp:()=>o})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,9557,7410,330,5609,2972,4433],()=>r(95534));module.exports=n})();