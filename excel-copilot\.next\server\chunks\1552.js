"use strict";exports.id=1552,exports.ids=[1552],exports.modules={79381:(e,t,a)=>{a.d(t,{cb:()=>_,Xf:()=>R,Al:()=>E,DI:()=>g,Ag:()=>A});var r,i="https://js.stripe.com",n="".concat(i,"/").concat("basil","/stripe.js"),o=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,s=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,c=function(){for(var e=document.querySelectorAll('script[src^="'.concat(i,'"]')),t=0;t<e.length;t++){var a,r=e[t];if(a=r.src,o.test(a)||s.test(a))return r}return null},l=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",a=document.createElement("script");a.src="".concat(n).concat(t);var r=document.head||document.body;if(!r)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(a),a},d=null,u=null,m=null;Promise.resolve().then(function(){return r||(r=(null!==d?d:(d=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var a,r=c();r?r&&null!==m&&null!==u&&(r.removeEventListener("load",m),r.removeEventListener("error",u),null===(a=r.parentNode)||void 0===a||a.removeChild(r),r=l(null)):r=l(null),m=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},u=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},r.addEventListener("load",m),r.addEventListener("error",u)}catch(e){t(e);return}})).catch(function(e){return d=null,Promise.reject(e)})).catch(function(e){return r=null,Promise.reject(e)}))}).catch(function(e){console.warn(e)});var p=a(31059);let f={PRO_MONTHLY:"price_1RJeZYRrKLXtzZkMf1SS2CRR",PRO_ANNUAL:"price_1RJecORrKLXtzZkMy1RSRpMV"},R={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"},_={[R.FREE]:50,[R.PRO_MONTHLY]:500,[R.PRO_ANNUAL]:1e3},w=process.env.STRIPE_SECRET_KEY||"",A=w?new p.Z(w,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}}):null;function E(e){switch(e){case R.PRO_MONTHLY:return f.PRO_MONTHLY;case R.PRO_ANNUAL:return f.PRO_ANNUAL;default:return f.PRO_MONTHLY}}function g(e){switch(e){case"active":case"trialing":return"active";case"canceled":case"unpaid":case"incomplete_expired":return"canceled";case"past_due":return"past_due";case"incomplete":return"incomplete";default:return"unknown"}}},51552:(e,t,a)=>{a.d(t,{J0:()=>_,mB:()=>w});var r=a(43895),i=a(79381);let n=a(92647).s.fromEnv();async function o(e,t=30,a=60){let r=`ratelimit:${e}`,i=Date.now(),o=1e3*a;try{let e=await n.pipeline().zremrangebyscore(r,0,i-o).zadd(r,{score:i,member:i.toString()}).zcount(r,i-o,"+inf").pexpire(r,o).exec(),a=e?.[2]||0,s=await n.zrange(r,0,0,{withScores:!0}),c=s.length>0?s[0].score+o:i+o;return{success:a<=t,limit:t,remaining:Math.max(0,t-a),reset:c}}catch(e){return console.error("[RATE_LIMIT_ERROR]",e),{success:!1,limit:t,remaining:0,reset:i+o,error:"Erro ao verificar limites de taxa. Limite tempor\xe1rio aplicado por seguran\xe7a."}}}let s=new Map,c=process.env.REDIS_URL||process.env.UPSTASH_REDIS_REST_URL?o:function(e,t=30,a=60){let r=Date.now(),i=1e3*a;s.has(e)||s.set(e,{timestamps:[],reset:r+i});let n=s.get(e);n.timestamps=n.timestamps.filter(e=>e>r-i),n.timestamps.push(r),1===n.timestamps.length&&(n.reset=r+i);let o=n.timestamps.length<=t,c=Math.max(0,t-n.timestamps.length);if(s.size>1e4)for(let e of[...s.entries()].filter(([e,t])=>t.reset<r).map(([e])=>e))s.delete(e);return{success:o,limit:t,remaining:c,reset:n.reset}};var l=a(63841);let d={MAX_WORKBOOKS:{[i.Xf.FREE]:5,[i.Xf.PRO_MONTHLY]:1/0,[i.Xf.PRO_ANNUAL]:1/0},MAX_CELLS:{[i.Xf.FREE]:1e3,[i.Xf.PRO_MONTHLY]:5e4,[i.Xf.PRO_ANNUAL]:1/0},MAX_CHARTS:{[i.Xf.FREE]:1,[i.Xf.PRO_MONTHLY]:1/0,[i.Xf.PRO_ANNUAL]:1/0},ADVANCED_AI_COMMANDS:{[i.Xf.FREE]:!1,[i.Xf.PRO_MONTHLY]:!0,[i.Xf.PRO_ANNUAL]:!0},RATE_LIMITS:{[i.Xf.FREE]:30,[i.Xf.PRO_MONTHLY]:120,[i.Xf.PRO_ANNUAL]:240}},u=["an\xe1lise preditiva","previs\xe3o","machine learning","regress\xe3o","correla\xe7\xe3o avan\xe7ada","modelo estat\xedstico","tend\xeancia futura","clustering","classifica\xe7\xe3o","s\xe9rie temporal","forecast","prediction"],m=new Map;async function p(e){try{let t=m.get(e),a=Date.now();if(t&&a-t.timestamp<18e5)return t.plan;let r=await l.prisma.subscription.findFirst({where:{userId:e,OR:[{status:"active"},{status:"trialing"}],AND:[{OR:[{currentPeriodEnd:{gt:new Date}},{currentPeriodEnd:null}]}]},orderBy:{createdAt:"desc"}});await f(e);let n=r?.plan||i.Xf.FREE;return m.set(e,{plan:n,timestamp:a}),n}catch(t){throw r.kg.error("[GET_USER_PLAN_ERROR]",t),await R(e,"plan_verification_failure",t),Error("N\xe3o foi poss\xedvel verificar seu plano de assinatura. Tente novamente mais tarde.")}}async function f(e){try{let t=await l.prisma.user.findUnique({where:{id:e}});if(!t)return;if("lastIpAddress"in t&&t.lastIpAddress){let a=await l.prisma.user.findMany({where:{lastIpAddress:t.lastIpAddress,id:{not:e}}}),r=new Date;r.setDate(r.getDate()-30);let i=a.filter(e=>"createdAt"in e&&e.createdAt&&e.createdAt>r).length;if(i>3){let r=a.map(e=>"email"in e&&e.email&&e.email.split("@")[1]||""),n=new Set(r),o=n.size>1&&n.size<=3;await R(e,"multiple_accounts_same_ip",{ipAddress:t.lastIpAddress,accountCount:a.length,recentAccountsCount:i,suspiciousDomains:o,creationDates:a.map(e=>"createdAt"in e?e.createdAt:null).filter(Boolean),severity:i>5?"high":"medium"});let s={};if(s.isSuspicious=!0,await l.prisma.user.update({where:{id:e},data:s}),i>5){let t={isBanned:!0,banReason:"M\xfaltiplas contas com mesmo IP detectadas (poss\xedvel abuso)",banDate:new Date};await l.prisma.user.update({where:{id:e},data:t}),await l.prisma.session.deleteMany({where:{userId:e}})}}}if("createdAt"in t&&t.createdAt){let a=t.createdAt.getTime(),r=(Date.now()-a)/36e5;if(r<24){let a=await l.prisma.userActionLog.count({where:{userId:e,action:{in:["attempt_create_workbook","attempt_add_cells","attempt_advanced_ai"]},timestamp:{gte:new Date(Date.now()-72e5)}}});a>50&&(await R(e,"high_activity_new_account",{accountAgeHours:r,recentActivityCount:a,ipAddress:t.lastIpAddress,severity:"high"}),await l.prisma.user.update({where:{id:e},data:{isSuspicious:!0}}))}}}catch(t){r.kg.error("[ABUSE_DETECTION_ERROR]",{userId:e,error:t})}}async function R(e,t,a){try{await l.prisma.securityLog.create({data:{userId:e,eventType:t,details:JSON.stringify(a),timestamp:new Date}})}catch(i){r.kg.error("[SECURITY_LOG_ERROR]",{userId:e,eventType:t,details:a,error:i})}}async function _(e,t,a){try{let t=await p(e),r=d.MAX_CHARTS[i.Xf.FREE]??1,n=d.MAX_CHARTS[t]??r,o=a<n,s=await A(e,t,"add_chart");if(!s.allowed)return{allowed:!1,message:`Limite de taxa excedido. Tente novamente em ${s.timeRemaining??60} segundos.`,limit:n===1/0?-1:n};return{allowed:o,message:o?void 0:`Voc\xea atingiu o limite de ${n} ${1===n?"gr\xe1fico":"gr\xe1ficos"} para este plano. Fa\xe7a upgrade para adicionar mais.`,limit:n===1/0?-1:n}}catch(a){throw r.kg.error("[CAN_ADD_CHART_ERROR]",{userId:e,sheetId:t,error:a}),Error("N\xe3o foi poss\xedvel verificar seus limites de uso. Tente novamente mais tarde.")}}async function w(e,t){if(!function(e){let t=e.toLowerCase();return u.some(e=>t.includes(e))}(t))return{allowed:!0,message:void 0};try{let a=await p(e),r=d.ADVANCED_AI_COMMANDS[a]||!1,i=await A(e,a,"advanced_ai_command");if(!i.allowed)return{allowed:!1,message:i.message||"Limite de taxa excedido para comandos avan\xe7ados de IA. Tente novamente mais tarde."};return await E(e,"attempt_advanced_ai",{allowed:r,command:t}),{allowed:r,message:r?void 0:`Recursos avan\xe7ados de IA requerem um plano Premium. Fa\xe7a upgrade para desbloquear esta funcionalidade.`}}catch(a){return r.kg.error("[CHECK_ADVANCED_AI_ERROR]",{userId:e,command:t,error:a}),{allowed:!1,message:"N\xe3o foi poss\xedvel verificar seu acesso a recursos avan\xe7ados de IA. Tente novamente mais tarde."}}}async function A(e,t,a){try{let r=d.RATE_LIMITS[i.Xf.FREE]||10,n=d.RATE_LIMITS[t]||r,{success:o,limit:s,remaining:l,reset:u,error:m}=await c(e,n);if(!o){if(m)return await R(e,"rate_limit_error",{action:a,error:m}),{allowed:!1,message:m};let t=Math.ceil((u-Date.now())/1e3);return{allowed:!1,message:`Voc\xea excedeu o limite de a\xe7\xf5es por minuto. Tente novamente em ${t} segundos.`,timeRemaining:t}}return l<=Math.floor(.2*n)&&l>0&&await E(e,"approaching_rate_limit",{action:a,remaining:l,limit:n,percentRemaining:Math.round(l/n*100)}),{allowed:!0}}catch(i){return r.kg.error("[RATE_LIMIT_CHECK_ERROR]",{userId:e,userPlan:t,action:a,error:i}),await R(e,"rate_limit_verification_error",{action:a,error:String(i)}),{allowed:!1,message:"N\xe3o foi poss\xedvel verificar limites de uso. Tente novamente em alguns instantes."}}}async function E(e,t,a){try{await l.prisma.userActionLog.create({data:{userId:e,action:t,details:JSON.stringify(a),timestamp:new Date}})}catch(i){r.kg.error("[USER_ACTION_LOG_ERROR]",{userId:e,action:t,details:a,error:i})}}},63841:(e,t,a)=>{a.d(t,{P:()=>c,prisma:()=>s});var r=a(53524);let i={info:(e,...t)=>{},error:(e,...t)=>{console.error(`[DB ERROR] ${e}`,...t)},warn:(e,...t)=>{console.warn(`[DB WARNING] ${e}`,...t)}},n={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},o=[],s=global.prisma||new r.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function c(){return{...n,activeConnections:Math.min(Math.floor(5*Math.random())+1,n.maxPoolSize),poolSize:n.poolSize}}async function l(){try{await s.$disconnect(),i.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){i.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{n.totalQueries++,e.duration&&(o.push(e.duration),o.length>100&&o.shift(),n.averageQueryTime=o.reduce((e,t)=>e+t,0)/o.length),e.duration&&e.duration>500&&i.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{n.failedQueries++,n.connectionFailures++,n.lastConnectionFailure=new Date().toISOString(),i.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{l()})}};