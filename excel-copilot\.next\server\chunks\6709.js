"use strict";exports.id=6709,exports.ids=[6709],exports.modules={6709:(e,t,r)=>{r.r(t),r.d(t,{CSRFProvider:()=>i,useCSRF:()=>a,useFetchWithCSRF:()=>c});var o=r(10326),n=r(17577);let s=(0,n.createContext)({csrfToken:null,isLoading:!0,refreshToken:async()=>null}),a=()=>(0,n.useContext)(s);function i({children:e}){let[t,r]=(0,n.useState)(null),[a,i]=(0,n.useState)(!0),c=async()=>{try{i(!0);let e=0;for(;e<3;)try{e++;let t=await fetch("/api/csrf",{method:"GET",credentials:"include"});if(t.ok){let e=await t.json();return r(e.csrfToken),i(!1),e.csrfToken}if(console.warn(`Tentativa ${e}/3 falhou ao obter token CSRF: ${t.status}`),e<3){await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e-1)));continue}throw Error(`Falha ao obter token CSRF: ${t.status}`)}catch(t){if(e>=3)throw t;await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e-1)))}throw Error("M\xe1ximo de tentativas excedido")}catch(e){return console.error("Erro ao obter token CSRF:",e),i(!1),null}};return o.jsx(s.Provider,{value:{csrfToken:t,isLoading:a,refreshToken:c},children:e})}function c(){let{csrfToken:e,isLoading:t,refreshToken:r}=a();return{fetchWithCSRF:async(o,n={})=>{t&&await new Promise(e=>setTimeout(e,100));let s=new Headers(n.headers||{});if(e)s.set("x-csrf-token",e);else{let e=await r();e&&s.set("x-csrf-token",e)}return fetch(o,{...n,credentials:"include",headers:s})},csrfToken:e,isLoading:t}}}};